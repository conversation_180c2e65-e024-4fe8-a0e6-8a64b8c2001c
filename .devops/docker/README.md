#Docker installation guide
## 1. Repository:
If you haven't got repository yet, clone it by command

```sh
<NAME_EMAIL>:tylkocom/cstm.git
```

## 2. Environment variables:
Before building images populate environment variable file:

```bash
cp .env.dist .env
```

and switch some variables to work with docker (default .env.dist works with non-dockerized build)
```
POSTGRES_HOST=db
CACHE_REDIS_URL=redis://redis:6379/0
HUEY_CONNECTION_HOST=redis
GOOGLE_CHROME_URL=http://**********:9222
CELERY_BROKER_URL=redis://redis:6379
```

**If you are using MacOS** and you will be using Docker you have uncomment and set environment variable depends on project path
```
# DOCKER
REPOSITORY_PATH=<your local cstm repository path>
```
ex.:

```REPOSITORY_PATH=:/Users/<USER>/PycharmProjects/cstm```


For basic local development following variables need to be set:

```
PRODUCTION_SYSTEM_TOKEN=
```

## 3. Build app:

### Installing pre-commit

Even when working with docker, you will still need to install pre-commit locally,
so it can format/lint the files before they're committed.

```bash
pip install pre-commit==2.11.1
pre-commit install --install-hooks
```

### Building app with docker

`docker-compose` is required to run backend server in container.

Create `tylko_global` network to be able to connect to production_system or
other apps working in containers:

```bash
bash .devops/docker/create_tylko_global_network.sh
```

Next, setup docker-compose properly:

#### **If you are Linux User**

```bash
cp \
    .devops/docker/docker-compose.override.yml.example \
    docker-compose.override.yml
```

#### **If you are MacOs User**
You should set volume mount by following steps to set NFS mount
(it speed up working with Docker on MacOS):

**Note that it was configured for MacOS Catalina**

1. Edit file `sudo nano /etc/exports`
and add line:

   `/System/Volumes/Data -alldirs -mapall=<UID>:20 localhost`

    Remember to replace `<UID>` with your user ID (you can check it by running `echo $UID`)
    (for older version than Catalina change `/System/Volumes/Data` for `/Users`/)


2. Edit file `sudo nano /etc/nfs.conf`
and add line:

    `nfs.server.mount.require_resv_port = 0`


3. Restart service `sudo nfsd restart`

Now you should have NFS configured

So continue with next steps

```bash
cp \
    .devops/docker/docker-compose.override.yml.example-macos \
    docker-compose.override.yml
```


To build `Dockerfile` that won't mess up with your local file permissions
you need to find your current user id - `UID`, by running command ```echo $UID``` and then replace it in
`docker-compose.override.yml` file in `!!UID!!` occurrences.

Now let's build docker images:

```bash
docker-compose build
```

And run containers (but the website won't work yet):
```bash
docker-compose up
```

NOTE: Please don't run `migrate`. If you do, the database from step 4 won't load properly.

## 4. Load database:

**It's not recommended unless you have necessity to do that** ex. using Big database dump but
If you want to use your local db from app container, look at section "Use local database in Docker"

### Database dump

https://cstm-tasks.atlassian.net/wiki/spaces/~816323402/pages/2239529004/Load+mini+db+script



### Use local database in Docker
If you want to use local database you have to set value:

```
POSTGRES_USER=cstm
POSTGRES_PASSWORD=cstm
POSTGRES_PORT=5432
POSTGRES_DB=cstm
POSTGRES_HOST=host.docker.internal
```
or for Linux users (only `POSTGRES_HOST` is changed compared to above) :
```
POSTGRES_HOST=**********
```

Note that your user, password and db can be different than `cstm`


------
##### After these steps you should have working app with data on `http://localhost:8000/`.

## 5. Backend development:

To run Django's server (only for API, admin and celery worker) use following
command:

```bash
docker-compose up app celeryworker
```

To run basic tests suite use one of following commands:

+ when using `pytest`

```bash
docker-compose run --rm app pytest
```

+ when all tests suite is required, override markers expression from
`setup.cfg` with empty one:

```bash
docker-compose run --rm app pytest -m=""
```

or use `--run-all-tests` cli option:

```bash
docker-compose run --rm app pytest --run-all-tests
```

+ when coverage report is required:

```bash
docker-compose run --rm app pytest --cov=.
```

Above listed command will generate term and XML report located in
`src/.test_reports/` directory, to generate HTML report use following flags:

```bash
docker-compose run --rm app pytest --cov=. --cov-report=html --cov-report=xml
```

### Add admin role
`docker-compose run --rm app ./manage.py createsuperuser`
You can now log into your local django admin

### Regenerate DNA entries
`docker-compose run --rm app ./manage.py regenerate_dna_entries`

## 6. Frontend development:

If you want to track gulp changes without reloading, run command:

```
docker-compose run --rm web gulp django
```

To develop quasar app redirect to folder including app and run:
```bash
npm i
quasar dev
```


## 7. Setup local S3 storage in docker

To run minio (local S3 storage) in docker you need to copy minio sections to your
`docker-compose.override.yml` (`minio`, `minio-client` services and add them to
`depends_on` section in `app` service)

You also need to copy and uncomment env variables listed in `.env.dist` file:

```bash
# Local S3 bucket
USE_AWS_S3_MEDIA_STORAGE=True
MINIO_ROOT_USER=access-key
MINIO_ROOT_PASSWORD=secret-key
LOCAL_BUCKET_NAME=my-local-bucket
LOCAL_STORAGE_URL=http://minio:9000
DEFAULT_FILE_STORAGE=storages.backends.s3boto3.S3Boto3Storage
```

After changes restart your docker-compose service.


## 8. Nuxt development

In the `nuxt` directory run

```bash
cp .env.default .env
```

and in `.env` set:
```
BASE_URL=http://app:8000
```

You can leave other variables unchanged.
