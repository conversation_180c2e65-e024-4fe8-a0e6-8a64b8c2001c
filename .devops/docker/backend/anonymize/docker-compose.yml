---
version: '3.8'
services:
  db:
    image: ${DOCKER_POSTGRES_IMAGE}
    expose:
      - 5432
    env_file:
      - .env
    volumes:
      - postgres:/var/lib/postgresql/data
    shm_size: 1g
    command: postgres -c shared_preload_libraries='pglogical'

  app:
    user: "${USER_ID}"
    image: ${DOCKER_IMAGE}
    depends_on:
      - db
    env_file:
      - .env
    volumes:
      - app_volume:/app
      - media_volume:/app/media
      - error_volume:/app/errors
      - shared_volume:/app/shared

  logistic:
    user: "${USER_ID}"
    image: ${DOCKER_LOGISTIC_IMAGE}
    depends_on:
      - db
    env_file:
      - .env_logistic
    volumes:
      - app_logistic_volume:/app
      - error_volume:/app/errors
      - shared_volume:/app/shared

  ps:
    user: "${USER_ID}"
    image: ${DOCKER_IMAGE}
    command: ["bash", "-c", "./minify_ps_db.sh 2> >(tee -a ./errors/$(date +'%F'.log)) || (c=$?; ./anonymize_slack_notification.sh; (exit $c))"]
    depends_on:
      - db
    env_file:
      - .env
    volumes:
      - app_ps_volume:/app
      - error_volume:/app/errors
      - shared_volume:/app/shared
      - ./minify_ps_db.sh:/app/minify_ps_db.sh


volumes:
  app_volume: {}
  app_logistic_volume: {}
  app_ps_volume: {}
  media_volume: {}
  postgres: {}
  error_volume: {}
  shared_volume: {}
