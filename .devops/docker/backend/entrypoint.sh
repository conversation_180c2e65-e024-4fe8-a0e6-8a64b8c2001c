#!/usr/bin/env bash

set -o errexit
set -o pipefail

cmd="$@"

# Remove `DJANGO_SETTINGS_MODULE` from environment variables to take Django
# settings from setup.cfg when running `pytest`
if [[ $cmd = *"pytest"* ]]; then
  unset DJANGO_SETTINGS_MODULE
fi

case "$cmd" in
    # TODO: add frequently used commands here as `case` options, e.g.
    # `run_CI_tests` or `setup_environment`.
    *)
        $cmd
    ;;
esac
