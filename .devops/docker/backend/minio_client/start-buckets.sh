#!/usr/bin/env bash

set -o errexit
set -o pipefail
set -o nounset
set -o xtrace

apk add nc &&
while ! nc -z minio 9000; do echo 'Wait minio to startup...' && sleep 0.1; done; sleep 5 &&
/usr/bin/mc config host add myminio http://minio:9000 "${MINIO_ROOT_USER}" "${MINIO_ROOT_PASSWORD}";
/usr/bin/mc mb "myminio/${LOCAL_BUCKET_NAME}";
/usr/bin/mc policy download "myminio/${LOCAL_BUCKET_NAME}";
exit 0;
