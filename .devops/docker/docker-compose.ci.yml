---
version: '3.8'
services:
  db:
    image: postgres:14-bullseye
    env_file:
      - ../../.env

  app:
    init: true
    image: "cstm-backend:ci-${BUILD_SLUG}"
    user: "root"
    build:
      context: ../..
      dockerfile: ./.devops/docker/backend/Dockerfile
      target: production_build
      args:
        DJANGO_ENV: "development"
      cache_from:
        - "tylko/cache:latest"
    env_file:
      - ../../.env
    volumes:
      - ../../.test_reports:/app/.test_reports
    depends_on:
      - db
