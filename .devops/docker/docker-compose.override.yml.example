---
version: '3.8'
services:
  db:
    user: "!!UID!!"
    ports:
      - "5500:5432"
    env_file:
      - .env

  app: &app
    user: "!!UID!!"
    build:
      target: development_build
      args:
        DJANGO_ENV: development
        USER_ID: "!!UID!!"
    ports:
      - "8000:8000"
    expose:
      - 8000
    depends_on:
      - redis
      - db
      - chrome-headless
      - mailhog
      - minio
      - minio_client
    environment:
      - VIRTUAL_HOST=cstm.local.tylko.com
    env_file:
      - .env
    volumes:
      - ./src/:/app
      - ./shared/static_files:/app/r_static
      - ./shared/media:/app/media
    stdin_open: true
    tty: true
    networks:
      default: {}
      tylko_global:
        aliases:
          - cstm-app

  celeryworker:
    <<: *app
    command: /start-celeryworker.sh
    ports: [ ]

  celerybeat:
    <<: *app
    command: /start-celerybeat.sh
    ports: [ ]

  flower:
    <<: *app
    expose:
      - "5555"
    command: /start-flower.sh
    ports: [ ]

  web:
    user: "!!UID!!"
    build:
      context: .
      dockerfile: .devops/docker/frontend/Dockerfile
    ports:
      - "8080:8080"
    env_file:
      - .env
    volumes:
      - ./frontend_src/:/frontend_src
      - /frontend_src/node_modules
      - ./src/frontend_cms:/src/frontend_cms
    networks:
      default: { }
      tylko_global:
        aliases:
          - cstm-web

  nuxt:
    build:
      context: ./nuxt
      target: development_build
      dockerfile: ../.devops/docker/nuxt/Dockerfile
    ports:
      - "3333:3333"
    env_file:
      - .env
    volumes:
      - ./nuxt:/nuxt
      - /nuxt/node_modules/

  nginx:
    image: nginx:latest
    depends_on:
      - nuxt
      - app
    volumes:
      - ./.devops/docker/nginx/nginx.docker.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "80:80"
      - "443:443"

  mailhog:
    ports:
      # SMTP server
      - "1025:1025"
      # WEB UI
      - "8025:8025"

  minio:
    image: minio/minio
    ports:
      - "9000:9000"
      - "9001:9001"
    env_file:
      - .env
    command: server /export --console-address ":9001"
    volumes:
      - ./shared/minio:/export

  minio_client:
    image: minio/mc
    build:
      context: ./.devops
      dockerfile: ./docker/backend/minio_client/Dockerfile
    env_file:
      - .env
    depends_on:
      - minio
    entrypoint: /start-buckets.sh

networks:
  tylko_global:
    external: true
