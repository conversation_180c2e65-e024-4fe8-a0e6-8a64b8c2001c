---
version: '3.8'
services:
  db:
    user: "!!UID!!"
    ports:
      - "5500:5432"
    env_file:
      - .env
    volumes:
      - volume_db:/var/lib/postgresql/data

  app: &app
    user: "!!UID!!"
    build:
      target: development_build
      args:
        DJANGO_ENV: development
        USER_ID: "!!UID!!"
    ports:
      - "8000:8000"
    expose:
      - 8000
    depends_on:
      - redis
      - db
      - chrome-headless
      - mailhog
    environment:
      - VIRTUAL_HOST=cstm.local.tylko.com
    env_file:
      - .env
    volumes:
      - volume_src:/app
      - volume_static_files:/app/r_static
      - volume_media:/app/media
    stdin_open: true
    tty: true
    networks:
      default: {}
      tylko_global:
        aliases:
          - cstm-app

  celeryworker:
    <<: *app
    command: /start-celeryworker.sh
    ports: [ ]

  celerybeat:
    <<: *app
    command: /start-celerybeat.sh
    ports: [ ]

  flower:
    <<: *app
    expose:
      - "5555"
    command: /start-flower.sh
    ports: [ ]

  web:
    user: "!!UID!!"
    build:
      context: .
      dockerfile: .devops/docker/frontend/Dockerfile
    ports:
      - "8080:8080"
    env_file:
      - .env
    volumes:
      - volume_frontend_src:/frontend_src
      - /frontend_src/node_modules
      - volume_src:/src
    stdin_open: true
    tty: true
    networks:
      default: { }
      tylko_global:
        aliases:
          - cstm-web

  mailhog:
    ports:
      # SMTP server
      - "1025:1025"
      # WEB UI
      - "8025:8025"

  nuxt:
    user: "root"
    build:
      context: ./nuxt
      target: development_build
      dockerfile: ../.devops/docker/nuxt/Dockerfile
    ports:
      - "3333:3333"
    env_file:
      - .env
    volumes:
      - volume_nuxt:/nuxt
      - /nuxt/node_modules/
    networks:
      default: { }
      tylko_global:
        aliases:
          - cstm-nuxt

  nginx:
    user: "!!UID!!"
    image: nginx:latest
    depends_on:
      - nuxt
      - app
    volumes:
      - ./.devops/docker/nginx/nginx.docker.conf:/etc/nginx/nginx.conf:ro
    ports:
      - "80:80"
      - "443:443"
    networks:
      - tylko_global

networks:
  tylko_global:
    external: true

volumes:
  volume_src:
    driver: local
    driver_opts:
      type: nfs
      o: addr=host.docker.internal,rw,nolock,hard,nointr,nfsvers=3
      device: ':${REPOSITORY_PATH}/src'

  volume_db:
    driver: local
    driver_opts:
      type: nfs
      o: addr=host.docker.internal,rw,nolock,hard,nointr,nfsvers=3
      device: ':${REPOSITORY_PATH}/shared/postgresql'

  volume_frontend_src:
    driver: local
    driver_opts:
      type: nfs
      o: addr=host.docker.internal,rw,nolock,hard,nointr,nfsvers=3
      device: ':${REPOSITORY_PATH}/frontend_src'

  volume_nuxt:
    driver: local
    driver_opts:
      type: nfs
      o: addr=host.docker.internal,rw,nolock,hard,nointr,nfsvers=3
      device: ':${REPOSITORY_PATH}/nuxt'

  volume_static_files:
    driver: local
    driver_opts:
      type: nfs
      o: addr=host.docker.internal,rw,nolock,hard,nointr,nfsvers=3
      device: ':${REPOSITORY_PATH}/shared/static_files'

  volume_media:
    driver: local
    driver_opts:
      type: nfs
      o: addr=host.docker.internal,rw,nolock,hard,nointr,nfsvers=3
      device: ':${REPOSITORY_PATH}/shared/media'
