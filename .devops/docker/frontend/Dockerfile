FROM node:16.20.0

ENV DOCKER_BUILDKIT=1
ENV CHOKIDAR_USEPOLLING=true

COPY ./frontend_src/package.json /frontend_src/
COPY ./frontend_src/package-lock.json /frontend_src/

SHELL ["/bin/bash", "--login", "-c"]

WORKDIR /frontend_src

RUN \
  apt-get update -y && \
  apt-get install xserver-xorg-dev libxi-dev libxext-dev -y

RUN npm root
RUN npm install --legacy-peer-deps
RUN npm install --global gulp-cli
RUN npm rebuild node-sass

COPY ./.devops/docker/frontend/start.sh /start.sh
RUN sed -i 's/\r//' /start.sh && chmod +x /start.sh
CMD ["/start.sh"]
