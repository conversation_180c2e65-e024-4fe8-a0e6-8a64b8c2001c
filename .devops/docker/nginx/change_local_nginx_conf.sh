#!/bin/bash

nginx_test_command=$(sudo nginx -t 2>&1)
# set -e must be here coz nginx -t sends output to stderr
set -e

if [ "${BASH_SOURCE[0]}" ]
then
  SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )
else
  SCRIPT_DIR="$(pwd)/$(dirname "$0")"
fi

(
  nginx_conf_path=$(echo "$nginx_test_command" | grep -oE '\/(.*)\/nginx\.conf' | head -n 1)
  nginx_dir_path="${nginx_conf_path%/nginx.conf}"
  echo "Copy nginx config"
  if [ "$(ls "$nginx_dir_path" | grep -c sites-available)" -eq 1 ]
  then
    sudo cp "$SCRIPT_DIR/nginx.local.conf" "$nginx_dir_path/sites-available/default"
  elif [ "$(ls "$nginx_dir_path" | grep -c servers)" -eq 1 ]
  then
    cp "$SCRIPT_DIR/nginx.local.conf" "$nginx_dir_path/servers/default"
  else
    echo "Failed to copy nginx config file"
    exit 1
  fi
)


echo "Attempt to restart service"
sudo nginx -s reload
echo "Service restarted"
