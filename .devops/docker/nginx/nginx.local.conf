server {
  listen 80;
  server_name localhost;

  location ~* ^/(api|internal-api|admin|pricing|product_feed|product_feed_new|cs|pages|r_static) {
    proxy_pass http://localhost:8000;
    proxy_set_header HOST $host;
    proxy_buffer_size   1M;
    proxy_buffers   4 1M;
    proxy_set_header Connection keep-alive;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  }

  location ~* "^/((en|de|fr|nl|es|pl|it|sv|da)(-[A-Z]{2})?/)?(order|adyen_redirect|email24|assembly-service|complaint-service|delivery-time-frames|editor)" {
    proxy_pass http://localhost:8000;
    proxy_set_header HOST $host;
    proxy_buffer_size   1M;
    proxy_buffers   4 1M;
    proxy_set_header Connection keep-alive;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  }

  location / {
    proxy_pass http://localhost:3333;
    proxy_set_header HOST $host;
    proxy_buffer_size   1M;
    proxy_buffers   4 1M;
    proxy_set_header Connection keep-alive;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  }

  location ~* "^/((en|de|fr|nl|es|pl|it|sv|da)(-[A-Z]{2})?/)?(about-tylko|mobili-c|furniture-c|mobel-c|meubles-c|muebles-c|meubels-c|gallery|tylko-for-business|tylko-fur-unternehmen|tylko-business|tylko-para-empresas|tylko-for-foretag|checkout|payment_methods|confirmation_fake|confirmation_pending|confirmation|furniture|mobel|mueble|meuble|mobili|meubel|mobler|material-samples|muster|echantillon-de-matiere|material-tester|product-lines|gammes-de-produits|produktlinien|lineas-de-productos|productlijnen|produktlinjer|tylko-pro|mobler-c|tylko-til-erhverv|mobel|materiale-prover|produktlinjer|om-tylko|collaborations/reisinger|cart|crm-2|investor-relationst|terms|faq)" {
    proxy_pass http://localhost:3000;
    proxy_set_header HOST $host;
    proxy_buffer_size   1M;
    proxy_buffers   4 1M;
    proxy_set_header Connection keep-alive;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  }

  location ~* "^/((en|de|fr|nl|es|pl|it|sv|da)(-[A-Z]{2})?)?(/)?$" {
    proxy_pass http://localhost:3000;
    proxy_set_header HOST $host;
    proxy_buffer_size   1M;
    proxy_buffers   4 1M;
    proxy_set_header Connection keep-alive;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  }

  location ~* ^/(_nuxt3|nuxt-api|nuxt3-statics) {
    proxy_pass http://localhost:3000;
    proxy_set_header HOST $host;
    proxy_buffer_size   1M;
    proxy_buffers   4 1M;
    proxy_set_header Connection keep-alive;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  }

  location ~* ^/(configurator)/ {
    proxy_pass http://localhost:5173;
    proxy_set_header HOST $host;
    proxy_buffer_size   1M;
    proxy_buffers   4 1M;
    proxy_set_header Connection keep-alive;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
  }
}

