FROM node:16.20.0 AS development_build

ARG SENTRY_URL
ARG SENTRY_PROJECT
ARG SENTRY_ORG
ARG SENTRY_ENV
ARG SENTRY_AUTH_TOKEN
ARG SENTRY_DSN
ARG ENVIRONMENT
ARG SENTRY_RELEASE
ARG GITHUB_TOKEN

ENV DOCKER_BUILDKIT=1
ENV CHOKIDAR_USEPOLLING=true
ENV SENTRY_URL=${SENTRY_URL} \
 SENTRY_PROJECT=${SENTRY_PROJECT} \
 SENTRY_ORG=${SENTRY_ORG} \
 SENTRY_ENV=${SENTRY_ENV} \
 SENTRY_AUTH_TOKEN=${SENTRY_AUTH_TOKEN} \
 SENTRY_DSN=${SENTRY_DSN} \
 SENTRY_RELEASE=${SENTRY_RELEASE}
ENV ENVIRONMENT=${ENVIRONMENT}
ENV GITHUB_TOKEN=${GITHUB_TOKEN}

WORKDIR /nuxt

COPY ./package.json /nuxt/
COPY ./package-lock.json /nuxt/

SHELL ["/bin/bash", "--login", "-c"]

RUN printf "@tylkocom:registry=https://npm.pkg.github.com/\n//npm.pkg.github.com/:_authToken=$GITHUB_TOKEN" > .npmrc
RUN npm root
RUN npm install

RUN npm rebuild node-sass

COPY . /nuxt/

EXPOSE 3333

CMD ["npm", "run", "dev"]

FROM development_build as production_build

RUN npm run build
RUN npm install -g pm2@latest

CMD ["npm", "run", "start-pm2"]
