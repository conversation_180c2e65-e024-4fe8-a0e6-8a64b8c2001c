# PYTHON
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1

# !!! LOCAL DEVELOPMENT - CHECK IF VALUES ARE CORRECT !!!

# POSTGRES DB
POSTGRES_USER=cstm
POSTGRES_PASSWORD=cstm
POSTGRES_PORT=5432
POSTGRES_DB=cstm
POSTGRES_HOST=127.0.0.1
# POSTGRES_CONN_MAX_AGE=60
DJANGO_POSTGRES_ENGINE=django.db.backends.postgresql

# CSTM
CSTM_PROD_TOKEN=

# PRODUCTION SYSTEM
PRODUCTION_SYSTEM_API_URL=https://ps-dev-00.oklyt.pl/api/v1
PRODUCTION_SYSTEM_TOKEN=
PRODUCTION_SYSTEM_DEFAULT_TIMEOUT=60

# LOGISTIC
LOGISTIC_URL=https://testing-logistic.oklyt.pl
LOGISTIC_MOCK_REQUEST_STRATEGY=False

# DEBUGGING TOOLS
DEBUG_TOOLBAR=False
# NOTE: run migrations after enabling silk
USE_SILK=False

# DJANGO
DJANGO_SETTINGS_MODULE=cstm_be.settings.local
DJANGO_DEBUG=True
# DJANGO_SECRET_KEY=!!!UNCOMMENT_AND_CHANGE_ME!!!
DJANGO_ALLOWED_HOSTS=*
SITE_URL=http://localhost:8000/


# EMAILS
DJANGO_EMAIL_HOST=mailhog
DJANGO_EMAIL_PORT=1025
DJANGO_EMAIL_HOST_USER=
DJANGO_EMAIL_HOST_PASSWORD=
DJANGO_EMAIL_SUBJECT_PREFIX=
DJANGO_DEFAULT_FROM_EMAIL=<EMAIL>


# MEDIA FILES
# DJANGO_MEDIA_ROOT=

# CACHES
# CACHE_KEY_PREFIX=
CACHE_REDIS_URL=redis://127.0.0.1:6379/0


# DATADOG
# DATADOG_APP_NAME=
# DATADOG_API_KEY=
# DATADOG_APP_KEY=
# DATADOG_HOST=https://app.datadoghq.com
# DATADOG_STATSD_HOST=localhost
# DATADOG_STATSD_PORT=8125

# SENTRY
# SENTRY_DSN=

# GOOGLE CHROME HEADLESS
# GOOGLE_CHROME_URL=
# GOOGLE_RECAPTCHA_SECRET=

# LOCAL
# GENERAL
# PROJECT_NAME=
# AB TESTS
# AB_TESTS_TOKEN_SECRET_KEY=

# FRONTEND CMS
# CONTACT_FILE_SIZE_LIMIT=
# CONTACT_FILE_CLEANUP_TIME=

# FACEBOOK
# FB_APP_ID=
# FB_APP_SECRET=
# FB_APP_TOKEN=
FB_API_VERSION=v5.0

# BRAZE
# BRAZE_API_KEY=
# BRAZE_API_URL=
# BRAZE_APP_ID=

# MAILCHIMP
# MAILCHIMP_API_KEY=

# TRUSTED SHOPS
# TRUSTEDSHOPS_TS_ID=
# TRUSTEDSHOPS_USER=
# TRUSTEDSHOPS_PASSWORD=

# TRUSTPILOT
TRUSTPILOT_ID=tylko.com


# GOOGLE ANALYTICS
# GA_ACCOUNT=
# GA_ACCOUNT_KEY_PATH=
GA_PROFILE_ID_WEB=*********

# CUSTOMER SERVICE
# USER_HMAC_HASH_SECRET=

# SLACK
# SLACK_CS_WEBHOOK_URL=

# TYPEFORM
# TYPEFORM_TOKEN=
# TYPEFORM_FORMS=

# ADYEN
# ADYEN_MERCHANT_ACCOUNT=
# ADYEN_MERCHANT_SECRET=
# ADYEN_MERCHANT_WS_ACCOUNT=
# ADYEN_MERCHANT_WS_SECRET=
# ADYEN_PUBLIC_KEY=
# ADYEN_REPORT_USER=
# ADYEN_REPORT_PASSWORD=
# ADYEN_HMAC_KEY=
# CHECKOUT_API_KEY=
# CHECKOUT_SETUP_URL=

# MAILING
MAILING_FLOW_PERIOD=10
MAILING_FLOW_THRESHOLD=100
MANDRILL_REPORT_CHECK_DAYS_BACK=30

# DEVELOPMENT
IPYTHON_ARGUMENTS=

# BAG
BAG_URL=http://bag-app:8000

# CELERY
CELERY_BROKER_URL=redis://127.0.0.1:6379

# Flower
CELERY_FLOWER_USER=
CELERY_FLOWER_PASSWORD=

# Local S3 bucket
# uncomment to run minio and use storage locally
# USE_AWS_S3_MEDIA_STORAGE=True
# MINIO_ROOT_USER=access-key
# MINIO_ROOT_PASSWORD=secret-key
# LOCAL_BUCKET_NAME=my-local-bucket
# LOCAL_STORAGE_URL=http://minio:9000
# DEFAULT_FILE_STORAGE=storages.backends.s3boto3.S3Boto3Storage

# SELENIUM_USER_PASSWORD=

GOOGLE_CLIENT_ID=************-m10tnqifsipcmpk7al20csvevqinj60p.apps.googleusercontent.com
GOOGLE_SECRET=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

FACEBOOK_CLIENT_ID=****************
FACEBOOK_SECRET=********************************

APPLE_CLIENT_ID=service.pl.oklyt.ecom.lambert
APPLE_SECRET=NC2CQ4WJPQ
APPLE_KEY=J83F7S37UZ
APPLE_CERTIFICATE_KEY=MIGTAgEAMBMGByqGSM49AgEGCCqGSM49AwEHBHkwdwIBAQQgAoIYZwgtF4lIJVO4L0cv3z83QVFWgF6GP9AGoagVxdOgCgYIKoZIzj0DAQehRANCAAQ/AQqBe7WqHw6R78EiQqxvdYvDLZgjA57RfMzD7l2owb3djjfJBPbG/pD7Lm8rccMXl29xDfRjk6eEXsVWV2/2
