#!/bin/bash

# Git post-checkout hook to warn about invalid branch names
# This runs after switching to or creating a branch

# Get the current branch name
current_branch=$(git rev-parse --abbrev-ref HEAD)

# Skip validation for HEAD (detached state) or master branches
if [[ "$current_branch" == "HEAD" || "$current_branch" == "release" || "$current_branch" == "master" ]]; then
    exit 0
fi

# Define the pattern: (feature|fix|quality)/TICKET-123/description-with-dashes-or-underscores
pattern="^(feature|fix|quality)/[A-Za-z]+-[0-9]+/[a-zA-Z0-9_.-]+$"

if [[ ! $current_branch =~ $pattern ]]; then
    echo "⚠️  WARNING: Branch name '$current_branch' does not follow naming convention!"
    echo ""
    echo "Expected format: (feature|fix|quality)/TICKET-123/description-with-dashes"
    echo "Examples:"
    echo "  feature/PROJ-123/add-user-authentication"
    echo "  fix/PROJ-456/resolve_login_bug"
    echo "  quality/PROJ-789/refactor-payment-service"
    echo ""
    echo "Consider renaming your branch with:"
    echo "  git branch -m $current_branch <new-branch-name>"
    echo ""
fi

exit 0
