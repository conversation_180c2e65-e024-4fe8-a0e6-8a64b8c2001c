#!/bin/bash

# Git pre-push hook to enforce branch naming conventions
# This runs before pushing and can block the push

# Get the current branch name
current_branch=$(git rev-parse --abbrev-ref HEAD)

# Skip validation for main/master branches
if [[ "$current_branch" == "release" || "$current_branch" == "master" ]]; then
    exit 0
fi

# Define the pattern: (feature|fix|quality)/TICKET-123/description-with-dashes-or-underscores
pattern="^(feature|fix|quality)/[A-Za-z]+-[0-9]+/[a-zA-Z0-9_.-]+$"

if [[ ! $current_branch =~ $pattern ]]; then
    echo "❌ ERROR: Branch name '$current_branch' does not follow naming convention!"
    echo ""
    echo "Expected format: (feature|fix|quality)/TICKET-123/description-with-dashes"
    echo "Examples:"
    echo "  feature/PROJ-123/add-user-authentication"
    echo "  fix/PROJ-456/resolve_login_bug"
    echo "  quality/PROJ-789/refactor-payment-service"
    echo ""
    echo "Please rename your branch before pushing:"
    echo "  git branch -m $current_branch <new-branch-name>"
    echo ""
    echo "Push rejected."
    exit 1
fi

echo "✅ Branch name '$current_branch' follows naming convention."
exit 0
