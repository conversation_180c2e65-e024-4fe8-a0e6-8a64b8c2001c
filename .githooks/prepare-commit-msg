#!/bin/bash

FILE=$1
COMMIT_SOURCE=$2
if [ "${COMMIT_SOURCE}" = "merge" ];then
    exit 0
fi
MESSAGE=$(cat $FILE)
BRANCH_UPPER=$(git rev-parse --abbrev-ref HEAD | tr "[:lower:]" "[:upper:]")

PATTERN_FOR_JIRA_ID='[A-Z]{1,5}-[0-9]+'
PATTERN_FOR_RELEASE='^[0-9]{1,3}\.[0-9]{1,3}'

JIRA_IDS=[$(echo $BRANCH_UPPER | grep -Eo $PATTERN_FOR_JIRA_ID)]
RELEASE_NUMBER=[$(echo $BRANCH_UPPER | grep -Eo $PATTERN_FOR_RELEASE)]

if [ "$JIRA_IDS" != "[]" ] && [ "$RELEASE_NUMBER" != "[]" ]; then
	COMMIT_MESSAGE="${RELEASE_NUMBER}${JIRA_IDS}"
elif [ "$JIRA_IDS" != "[]" ]; then
	COMMIT_MESSAGE="${JIRA_IDS}"
elif [ "$RELEASE_NUMBER" != "[]" ]; then
	COMMIT_MESSAGE="${RELEASE_NUMBER}"
else
	COMMIT_MESSAGE=""
fi

echo "$COMMIT_MESSAGE $MESSAGE" > $FILE
