name: Auto merge release to master

#on closed PRs to release merge to master
on:
  pull_request:
    branches:
      - release
    types: [closed]

jobs:
  master-merge:
    runs-on: ubuntu-latest
    if: ${{ github.event.pull_request.merged }}
    steps:
      - uses: actions/checkout@master

      - name: Merge release -> master
        uses: devmasx/merge-branch@master
        with:
          type: now
          from_branch: release
          target_branch: master
          message: Merge release into master
          github_token: ${{ secrets.GITHUB_TOKEN }}
