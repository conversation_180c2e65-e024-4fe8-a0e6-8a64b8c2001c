name: Check branch name
on: pull_request

jobs:
  check_name:
    runs-on: ubuntu-latest

    steps:
      - name: Check if branch name is valid
        run: |
          if [[ ! "${{ github.head_ref }}" =~ ^(feature|fix|quality)/[A-Z]+-[0-9]+/[a-zA-Z0-9_.-]+$ ]]; then
            echo "Invalid branch name: ${{ github.head_ref }}"
            echo "Expected format: (feature|fix|quality)/TICKET-123/description-with-dashes"
            exit 1
          else
            echo "Branch name is valid: ${{ github.head_ref }}"
          fi
        shell: bash
