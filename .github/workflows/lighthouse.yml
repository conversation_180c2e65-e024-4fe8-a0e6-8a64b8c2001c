name: Lighthouse Score Summary

on:
  schedule:
    - cron: '0 17 * * *'  # 19:00 CEST (17 UTC)
  workflow_dispatch:

env:
  BASE_URL: https://tylko.com/de-de/configure/739,s
  PRODUCTION_LINE: Sofa

jobs:
  lighthouse:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repo
        uses: actions/checkout@v3

      - name: Setup Node
        uses: actions/setup-node@v3
        with:
          node-version: 18

      - name: Install dependencies
        run: npm install -g lighthouse@latest

      - name: Create report directory
        run: mkdir -p $GITHUB_WORKSPACE/lhci-reports

      - name: Run Lighthouse for Desktop
        run: |          
          lighthouse ${{ env.BASE_URL }} \
            --preset=desktop \
            --output=json \
            --output-path=$GITHUB_WORKSPACE/lhci-reports/desktop.report.json \
            --chrome-flags="--headless=new --no-sandbox --enable-gpu"


      - name: Run Lighthouse for Mobile
        run: |
          lighthouse ${{ env.BASE_URL }} \
            --output=json \
            --output-path=$GITHUB_WORKSPACE/lhci-reports/mobile.report.json \
            --chrome-flags="--headless=new --no-sandbox --enable-gpu" \
            --emulated-form-factor=none \
            --user-agent="Mozilla/5.0 (iPhone; CPU iPhone OS 18_1_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/18.1.1 Mobile/15E148 Safari/604.1" \
            --screen-width=390 \
            --screen-height=844 \
            --device-scale-factor=3

      - name: Send scores to Slack
        run: |
          color() {
            if (( $(echo "$1 >= 90" | bc -l) )); then echo "🟢 $1"; \
            elif (( $(echo "$1 >= 50" | bc -l) )); then echo "🟡 $1"; \
            else echo "🔴 $1"; fi
          }

          M=$GITHUB_WORKSPACE/lhci-reports/mobile.report.json
          D=$GITHUB_WORKSPACE/lhci-reports/desktop.report.json

          echo "Checking if files exist:"
          ls -la $M
          ls -la $D

          M_PERF=$(jq '.categories.performance.score * 100' "$M")
          M_ACCESS=$(jq '.categories.accessibility.score * 100' "$M")
          M_BEST=$(jq '.categories["best-practices"].score * 100' "$M")
          M_SEO=$(jq '.categories.seo.score * 100' "$M")

          D_PERF=$(jq '.categories.performance.score * 100' "$D")
          D_ACCESS=$(jq '.categories.accessibility.score * 100' "$D")
          D_BEST=$(jq '.categories["best-practices"].score * 100' "$D")
          D_SEO=$(jq '.categories.seo.score * 100' "$D")

          MESSAGE="
            Production Line: ${PRODUCTION_LINE}\n
            URL Tested: ${BASE_URL}\n
            📱 *Mobile Report*\n
              • Performance: $(color $M_PERF)
              • Accessibility: $(color $M_ACCESS)
              • Best Practices: $(color $M_BEST)
              • SEO: $(color $M_SEO)

            🖥️ *Desktop Report*\n
              • Performance: $(color $D_PERF)
              • Accessibility: $(color $D_ACCESS)
              • Best Practices: $(color $D_BEST)
              • SEO: $(color $D_SEO)"

          echo "$MESSAGE"
          curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\": \"$MESSAGE\"}" \
            ${{ secrets.SLACK_LIGHTHOUSE_WEBHOOK_URL }}