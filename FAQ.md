# Error when changing region:

When you change region to for example `Germany` you could see the message `Something went wrong. Please try again.`

The problem may be related to the settings of `adyen` - our payments provider. If you are working on a local development
environment make sure to deselect `Is live payment` option in the admin panel. This setting can be found on:
`All apps` > `CUSTOM` > `Global Settings`.

# django.db.utils.OperationalError: FATAL: sorry, too many clients already

When you see the error like above, it is very likely that you don't set max session length to `0`. Set `POSTGRES_CONN_MAX_AGE=0` in your .env
file, and you shouldn't have that problem again.
