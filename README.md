# REPO

 - [Setup podstawowy](#setup-podstawowy)
 - [R<PERSON>czy specyficzne dla MACów](#rzeczy-specyficzne-dla-macw)
 - [Setup backendu](src/README.md)
 - [Setup frontendu](frontend_src/README.md)
 - [Setup środowiska dockerowego](.devops/docker/README.md)
 - [Tylko GIT Flow](#tylko-git-flow)

## Setup podstawowy

### Instalacja i setup github
 - tl;dr: zainstaluj github, wygeneruj i wgraj klucz ssh
 - instrukcja: https://kbroman.org/github_tutorial/pages/first_time.html
 - w Github włącz 2FA

#### MAC
Wpisz git w terminalu, mac spyta się o instalację dev tools,
kliknij pobierz, jeśli nie to znaczy że już masz

#### Ubuntu
```bash
sudo apt install git
```

### Clone repo
```
<NAME_EMAIL>:tylkocom/cstm.git
```

### Setup git hooks
```
cp .githooks/* .git/hooks/
```
## Rzeczy specyficzne dla MACów

  - zainstaluj homebrew
  ```
  /usr/bin/ruby -e "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/master/install)"
```
 - Wykonaj w konsoli polecenie które zbierze od nowa nagłówki xcode:
    ```export CPATH=`xcrun --show-sdk-path`/usr/include```

## Rzeczy specyficzne dla MACów z układem Apple M1

  - zainstaluj homebrew
  ```
  /bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"
```
 - Dodaj do pliku ``.zshrc``

    ```export PATH=/opt/homebrew/bin:$PATH```

## Tylko GIT flow

See: [the documentation](https://docs.oklyt.pl/doc/0003-branch-and-commit-naming-lWBTxEMNPN)

### Pull Requests

+ Minimum number of reviewers - 2
+ PR creator should be the person that merge PR
+ PR branch should be removed after merge

## E2E tests in cypress

### Folder ```cypress``` is located under ```CSTM``` (on the same level as ```frontend_src```)

### How to run cypress test in view mode
In order to see the execution of tests you have to open cypress:

```
$cstm: cd cypress
$cstm/cypress: npx cypress open
```
now you are able to select browser and spec to be launched for test via cypress app

### How to run cypress e2e tests in headless mode
+ To run all tests in headless mode (pattern)
```
$cstm/cypress: npx cypress run --spec "e2e/e2e/*.cy.js"
```

+ To run specific test spec (pattern)
```
$cstm/cypress: npx cypress run --spec "path/to/my-spec.cy.js"
```

+ More options on https://docs.cypress.io/guides/guides/command-line

### How to run goldenpath
+ on localhost
```
$cstm/cypress: npm run cypress:local:goldenpath:small
```
what is the same as
```
$cstm/cypress: npx cypress run --config baseUrl=http://localhost:8000 --spec e2e/e2e_small/golden_path_spec_small.cy.js
```

+ on staging
```
$cstm/cypress: npm run cypress:staging:goldenpath
```
