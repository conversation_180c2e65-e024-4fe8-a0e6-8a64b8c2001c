# CSTM Installation guide

# Requirements
You need to install few things before we start

Instructions how to install are in links below

Pyenv
~~~
https://github.com/pyenv/pyenv?tab=readme-ov-file#installation
~~~

NVM - Node version manager
~~~
https://github.com/nvm-sh/nvm?tab=readme-ov-file#installing-and-updating
~~~

AWS cli
~~~
https://docs.aws.amazon.com/cli/latest/userguide/getting-started-install.html
~~~

Postgresql 14 VERSION IS IMPORTANT HERE!
~~~
https://www.postgresql.org/download/linux/debian/
~~~

Nginx
~~~ 
https://docs.nginx.com/nginx/admin-guide/installing-nginx/installing-nginx-open-source/
~~~


# Add aliases

Run this commands in main cstm directory

For bash shell (Ubuntu)
```
echo "CSTM_HOME_DIR=$(pwd)" >> ~/.bashrc
echo "alias start_cstm='. $CSTM_HOME_DIR/src/install_and_start.sh'" >> ~/.bashrc
echo "alias start_nuxt='. $CSTM_HOME_DIR/nuxt/install_and_start.sh'" >> ~/.bashrc
echo "alias start_nuxt3='. $CSTM_HOME_DIR/nuxt3/install_and_start.sh'" >> ~/.bashrc
echo "alias update_nginx='. $CSTM_HOME_DIR/.devops/docker/nginx/change_local_nginx_conf.sh'" >> ~/.bashrc
```

For zsh shell (MacOS)
```
echo "CSTM_HOME_DIR=$(pwd)" >> ~/.zshrc
echo "alias start_cstm='. $CSTM_HOME_DIR/src/install_and_start.sh'" >> ~/.zshrc
echo "alias start_nuxt='. $CSTM_HOME_DIR/nuxt/install_and_start.sh'" >> ~/.zshrc
echo "alias start_nuxt3='. $CSTM_HOME_DIR/nuxt3/install_and_start.sh'" >> ~/.zshrc
echo "alias update_nginx='. $CSTM_HOME_DIR/.devops/docker/nginx/change_local_nginx_conf.sh'" >> ~/.zshrc
```

# Setup postgresql

Open postgres cli

For macos your role is your "username"\
For ubuntu its "postgres"

```
psql -U role_name
```

Create role

```
CREATE ROLE cstm WITH LOGIN SUPERUSER PASSWORD 'cstm'
```

Load minidb script will create database ^^

# Setup Minidb script

Setup environment variables
```
POSTGRES_HOST default 127.0.0.1
POSTGRES_PORT default 5500 (docker option)
POSTGRES_USER default cstm
POSTGRES_DB default cstm
AWS_ACCESS_KEY_ID ask devops team on slack
AWS_SECRET_ACCESS_KEY ask devops team on slack
```

You can do it that way

```
Linux add to bashrc echo "export AWS_ACCESS_KEY_ID=ask_devops_for_password">>~/.bashrc

Macos add to zshrc echo "export AWS_ACCESS_KEY_ID=ask_devops_for_password">>~/.zshrc
```

# Setup backend
Copy .env.dist to src directory 
```
cp .env.dist src/.env
```

Setup all needed env variables in new .env  

Run command (its from alias)
```
start_cstm [options]

Options:
-h, --help                     show brief help
--reinstall                    reinstall virtual environment
--load-mini-db                 run load_mini_db script
-m, --migrations               run database migrations
```

For first time you should run like that
```
start_cstm --load-mini-db -m
```

# Setup nuxt2

Run command (its from alias)
```
start_nuxt [options]

Options:
-h, --help                     show brief help
-sp, --skip-install-packages   run without npm install
-sn, --skip-install-node       run without nvm install
```

Flags are only for frontends. If you want only working nuxt2 you should script without options
```
start_nuxt
```

# Setup nuxt3
Create .npmrc file in nuxt3 directory with code below
```
@tylkocom:registry= "https://npm.pkg.github.com/"
//npm.pkg.github.com/:_authToken=${TYLKO_PKG_INSTALL_TOKEN}
```

Create github token with option read:packages
```
https://github.com/settings/tokens
```

Add TYLKO_PKG_INSTALL_TOKEN environment variable
```
Linux add to bashrc echo "export TYLKO_PKG_INSTALL_TOKEN=paste_token_here">>~/.bashrc
Macos add to zshrc echo "export TYLKO_PKG_INSTALL_TOKEN=paste_token_here">>~/.zshrc
```


Run command (its from alias)
```
start_nuxt [options]

Options:
-h, --help                             show brief help
-sp, --skip-install-packages           run without npm install
-sn, --skip-install-node               run without nvm install
-ic, --install-old-configurators       install old configurators
```

For first time start nuxt with flag --install-old-configurators
```
start_nuxt3 --install-old-configurators
```
Then start it without flags

# Setup nginx

Run command (its from alias)
```
update_nginx
```

It will ask you for your password