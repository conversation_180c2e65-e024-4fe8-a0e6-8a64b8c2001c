#!/bin/bash

cd src || exit 1

if ! command -v poetry &> /dev/null; then
    output=$(docker compose run --rm app python manage.py makemigrations --check --dry-run 2>&1)
else
    output=$(poetry run python manage.py makemigrations --check --dry-run 2>&1)
fi
status=$?

if [ $status -ne 0 ]; then
    echo "❌ Error: There are model changes that are not reflected in migrations!"
    echo "$output"
    exit 1
else
    echo "✅ Migrations are up to date."
    exit 0
fi
