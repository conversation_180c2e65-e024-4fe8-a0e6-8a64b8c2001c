const fs = require('fs')

module.exports = {
    chromeWebSecurity: false,
    viewportWidth: 1440,
    viewportHeight: 850,
    e2e: {
        retries: {
            runMode: 2,
            openMode: 0,
        },
        supportFile: './support/e2e.js',
        specPattern: './e2e/**/*.cy.js',
        fixturesFolder: 'fixtures',
        screenshotsFolder: './screenshots',
        videosFolder: './videos',
        video: true,
        videoCompression: true,
        setupNodeEvents(on, config) {
            on('after:spec', (spec, results) => {
                if (results && results.video) {
                    const failures = results.tests.some((test) =>
                        test.attempts.some((attempt) => attempt.state === 'failed')
                    )
                    if (!failures) {
                        fs.unlinkSync(results.video)
                    }
                }
            })
            return require('./plugins/index.js')(on, config);
        },

        baseUrl: 'https://tylko-rules.dev',
        prodUrl: 'https://tylko.com',

        user: {
            name: "Test",
            email: "",
            password: "",
            wrong_email: "xxx",
            wrong_password: "xxx"
        },

        orderData: {
            first_name: '<PERSON><PERSON>',
            surname: '<PERSON>',
            street: 'S<PERSON>fkowa 4',
            postal_code: '55-166',
            city: 'Warszawa',
            email: '<EMAIL>',
            phone_number: '222222222',
        },

        testCardValid: {
            card_number: '****************',
            expiry_date: '03/30',
            security_code: '737',
        },

        testCardInvalidExpiryDate: {
            card_number: '****************',
            expiry_date: '03/99',
            security_code: '737',
        },

        testCardInvalidCvc: {
            card_number: '****************',
            expiry_date: '03/30',
            security_code: '999',
        },

        promoCodes:{
            valid_promocode: 'gosia20',
            invalid_promocode: 'xxxxxx',
            invalid_promocode_space: 'xxx xxx',
            promo_percent: 20,
        },

        defaultCommandTimeout: 4000,
    },
};
