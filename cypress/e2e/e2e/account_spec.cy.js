import * as accountTests from "../../test_modules/account_tests"

const devices = ['iphone-se2']

describe ('Account tests', () => {
    devices.forEach(device => 
        context(`${device} resolution`, () => {
            beforeEach(() => {
                accountTests.prepareTest(device)
            })
            accountTests.accessToRegisterPageFromHP()
            accountTests.loginUserWithWrongPassword()
            const urls = [
                `/de-de/mobel/sideboard/3464597,j,tiefes-weisse-multiplex-platte-sideboard-mit-turen-schubladen-ruckwanden-und-sockel-multiplexplatten-170x73x40cm`,
                '/',
            ]
            accountTests.loginUser(urls)
            accountTests.registerUserAcceptedNewsletter()
            accountTests.registerUserUnacceptedNewsletter()
            accountTests.registerUserWithTooShortPassword()
            accountTests.checkIfDirectRegistrationIsNotPossible()
            accountTests.passwordReminder()
        })
    )
})
