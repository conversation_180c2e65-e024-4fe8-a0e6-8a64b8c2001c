import cart from "../../pages/cart_page"
import hp from "../../pages/home_page"
import { 
    extractPriceValue,
    isProdEnv,
    addToCart
} from "../../test_modules/common_functions"

describe('Test usage of promocodes', () => {
    const url = Cypress.config('baseUrl') + '/de-de'
    const {
        valid_promocode,
        invalid_promocode,
        invalid_promocode_space,
        promo_percent,
    } = Cypress.config('promoCodes')
    const priceFactorAfterPromo = (100 - promo_percent) / 100

    beforeEach(() => {
        cy.visit(url)
        cy.agreeCookies()
    })

    it(`apply promocode on everything in cart`, () => {
        addToCart(['OriginalBookcase', 'ModernBookcase', 'ToneWardrobe'])
        hp.goToCart()
        cy.wait(1000)
        cart.removePromoIfApplied()
        cy.wait(1000)
        cart.getCartTotalPriceAsString().then((prePrice) => {
            cart.addPromoCodeWithEnter(invalid_promocode)
            hp.getNotificationNuxt3().as('notification').should('be.visible')
                //.and('contain', 'Gib bitte einen gültigen Promo-Code ein')
            cart.removePromoIfApplied()
            cart.addPromoCodeWithEnter(invalid_promocode_space)
            cy.get('@notification', {timeout: 6000}).should('be.visible')
                //.and('contain', 'Gib bitte einen gültigen Promo-Code ein')
            if (!isProdEnv()) {      
                cy.get('@notification', { timeout:10000 }).should('not.exist')
                cart.removePromoIfApplied()
                cart.addPromoCodeWithButton(valid_promocode)
                cy.get('@notification').first()//.should('contain', 'Rabattcode akzeptiert')
                cart.getCartTotalPriceAsString().then((postPrice) => {
                    expect(extractPriceValue(postPrice)).to.eql(Math.round(priceFactorAfterPromo * extractPriceValue(prePrice)))
                })
                cy.wait(1000)
                cart.removePromoIfApplied()
                cy.wait(1000)
                cart.getCartTotalPriceAsString().then((price) => {
                    expect(extractPriceValue(price)).to.eql(extractPriceValue(prePrice))
                })
            }
        })
    })
})
