const username = Cypress.env('CY_USER')
const password = Cypress.env('CY_PASS')
const url = Cypress.config('baseUrl')

describe("As admin I'm able to access CS and admin panels", () => {

    it("login to ADMIN panel", () => {
        cy.visit('/admin')
        cy.url().should('contain', 'login')
        cy.get('#id_username').type(username)
        cy.get('#id_password').type(password)
        cy.get('[type=submit]').click()
        cy.url().should('eq', `${url}/admin/`)
        cy.visit('/admin/all_index/')
        cy.get('#user-tools').should('contain.text', username)
    })

    it("login to CS panel", () => {
        cy.visit('/cs')
        cy.url().should('contain', 'login')
        cy.get('#id_username').type(username)
        cy.get('#id_password').type(password)
        cy.get('[type=submit]').click()
        cy.url().should('eq', `${url}/cs/`)
        cy.get('.navbar-brand').should('contain.text', 'Tylko CS')
    })
})
