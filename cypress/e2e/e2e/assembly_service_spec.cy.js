import cart from "../../pages/cart_page"
import hp from "../../pages/home_page"
import { 
    extractPriceValue,
    addToCart
} from "../../test_modules/common_functions"

describe('Test assembly service', () => {
    const baseUrl = Cypress.config('baseUrl')
    const desktop = 'macbook-15'
    const mobile = 'iphone-se2' // TODO: add test on mobile
    const url = `${baseUrl}/de-de`

    it('Assembly service is applicable to "modern" furniture', () => {
        cy.viewport(desktop)
        cy.visit(url)
        cy.agreeCookies()
        addToCart(['ModernBookcase'])
        hp.goToCart()
        cy.get(cart.selectors.cart_summary_assembly).should('be.visible')
    })

    it('Assembly service is applicable to "original" furniture', () => {
        cy.viewport(desktop)
        cy.visit(url)
        cy.agreeCookies()
        addToCart(['OriginalBookcase'])
        hp.goToCart()
        cy.get(cart.selectors.cart_summary_assembly).should('be.visible')
    })

    it('Assembly service is not applicable to Tone and samples', () => {
        cy.viewport(desktop)
        cy.visit(url)
        cy.agreeCookies()
        addToCart(['ToneWardrobe', 'OriginalSample', 'ToneSample', 'EdgeSample'])
        hp.goToCart()
        cy.wait(1000)
        cy.get(cart.selectors.cart_summary_assembly).should('not.exist')
    })

    it('Price of assembly service is correctly added to purchase - cart (DESKTOP)', () => {
        cy.viewport(desktop)
        cy.visit(url)
        cy.agreeCookies()
        //Tone + samples
        addToCart(['ToneWardrobe', 'OriginalSample'])
        hp.goToCart()
        cy.wait(2000)
        cy.get(cart.selectors.cart_summary_assembly).should('not.exist')
        //Other furniture
        addToCart(['OriginalBookcase'])
        cart.getCartTotalPriceAsString().then((prePrice) => {
            cy.get(cart.selectors.cart_summary_assembly).should('be.visible')
            cart.switchAssemblyServiceCheckbox()
            cart.getCartTotalPriceAsString().should('not.be.equal', prePrice).then((postPriceWithAS) => {
                cy.get(cart.selectors.cart_summary_assembly_price).invoke('text').then((ASPrice) => {
                    expect(extractPriceValue(postPriceWithAS)).to.eql(extractPriceValue(prePrice) + extractPriceValue(ASPrice))
                    //Second furniture
                    addToCart(['OriginalBookcase'])
                    cart.getCartTotalPriceAsString().then((postPrice2WithAS) => {
                        cy.get(cart.selectors.cart_summary_assembly_price).invoke('text').then((ASPrice2) => {
                            expect(extractPriceValue(ASPrice2)).to.eql(2 * extractPriceValue(ASPrice))
                            //Remove AS
                            cart.switchAssemblyServiceCheckbox()
                            cy.get(cart.selectors.cart_summary_assembly).should('be.visible')
                            cy.wait(2000)
                            cart.getCartTotalPriceAsString().then((postPriceWithoutAS) => {
                                expect(extractPriceValue(postPriceWithoutAS)).to.eql(extractPriceValue(postPrice2WithAS) - extractPriceValue(ASPrice2))
                            })
                        })
                    })
                })
            })
        })
    })

    const regionsWithoutAS = [
        'en-ro',
        'en-ie',
        'en-pt',
    ]
    regionsWithoutAS.forEach((region) => {
        it(`Assembly service should not be available in region: ${region}`, () => {
            const testUrl = `${baseUrl}/${region}`
            cy.viewport(desktop)
            cy.visit(testUrl)
            cy.agreeCookies()
            addToCart(['OriginalBookcase', 'ModernBookcase', 'OriginalSample'])
            hp.goToCart()
            cy.get('[data-testid="cart-item"]', { timeout:10000 }).should('have.length', 3)
            cy.get(cart.selectors.cart_summary_assembly).should('not.exist')
        })
    })
})
