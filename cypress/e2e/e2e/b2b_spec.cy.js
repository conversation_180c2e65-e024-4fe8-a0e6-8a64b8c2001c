import { isProdEnv } from "/test_modules/common_functions"

const mobileDevices = ['iphone-se2']
const desktopDevices = ['macbook-15']
const devices = [...mobileDevices, ...desktopDevices]
const regions = [
    'de-de',
    'fr-fr',
]
const getLanguage = (str) => str.split('-')[0]

devices.forEach((device) => {
    regions.forEach((region) => {
        it(`Check sending b2b form on ${region}, from ${device}`, () => {
            const email = "<EMAIL>"
            const name = "Cy"
            const secName = "Test"
            const company = "firma"
            const country = "poland"
            const vat = "1234567891"
            const title = "Real Estate Developer"
            const website = "<EMAIL>"

            cy.viewport(device)
            cy.visit(`${region}/tylko-pro`)
            cy.agreeCookies()
            cy.intercept('POST', '/api/v1/b2b-form/').as('formSend')
            cy.get('input[data-testid="b2b-form-email"]').type(email)
            cy.get('input[data-testid="b2b-form-first-name"]').type(name)
            cy.get('input[data-testid="b2b-form-last-name"]').type(secName)
            cy.get('input[data-testid="b2b-form-company-name"]').type(company)
            cy.get('select[data-testid="b2b-form-country"]').select(country)
            cy.get('input[data-testid="b2b-form-company-vat"]').type(vat)
            cy.get('select[data-testid="b2b-form-company-department"]').select(title)
            cy.get('input[data-testid="b2b-form-company-website"]').type(website)
            cy.get('[data-testid="b2b-form-newsletter-consent"]').should('be.visible')
            cy.get('[data-testid="b2b-form-terms-link"]')
                .should('be.visible').and('have.attr', 'href').and('include', '/terms')
            
            // TODO: BUG - we are sending form even when checkbox is not checked
            // terms not checked
            // cy.get('[data-testid="b2b-form-submit-cta"]').should('be.visible').click()
            // cy.get('[data-testid="b2b-form-marketing-permission-checkbox"]')
            //     .should('not.have.attr', 'checked')
            // cy.get('div[data-family="box"] ul.formkit-messages li.formkit-message[data-message-type="validation"]')
            //     .should('be.visible')

            // terms checked
            cy.get('[data-testid="b2b-form-marketing-permission-checkbox"]').check()
            if (!isProdEnv()) {
                cy.get('[data-testid="b2b-form-submit-cta"]').should('be.visible').click()
                cy.wait('@formSend').then(({response}) => {
                    const body = response.body
                    expect(response.statusCode).to.eq(201)
                    expect(body.email).to.equal(email)
                    expect(body.firstName).to.equal(name)
                    expect(body.lastName).to.equal(secName)
                    expect(body.country).to.equal(country)
                    expect(body.departament).to.equal(title)
                    expect(body.companyName).to.equal(company)
                    expect(body.website).to.equal(website)
                    expect(body.vatNumber).to.equal(vat)
                    expect(body.language).to.equal(getLanguage(region))
                    expect(body.freeSampleCode).to.not.be.empty
                    expect(body.promoCode).to.not.be.empty
                })
                cy.get('[data-testid="b2b-form-icon-check"]').should('be.visible')
                cy.get('[data-testid="b2b-form-success-title"]').should('be.visible')
                cy.get('[data-testid="b2b-form-success-message"]').should('be.visible')
                cy.get('[data-testid="b2b-form-success-link"]')
                    .should('be.visible').and('have.attr', 'href')
            }
        })
    })
})

devices.forEach((device) => {
    regions.forEach((region) => {
        it(`Check tylko-pro sections (region: ${region}, device: ${device})`, () => {
            cy.viewport(device)
            cy.visit(`${region}/tylko-pro`)
            cy.agreeCookies()
            // hero
            cy.get('[data-section="hero"]')
                .should('be.visible').and('have.length', 2).first().as('hero').find('picture')
            cy.get('@hero').find('button:visible').should('have.length', 1)
            // features
            cy.get('[data-testid="b2b-features"]').as('features')
            cy.get('@features').find('[data-testid="feature"]')
                .should('have.length', 6).and('be.visible')
            cy.get('@features').find('[data-testid="feature-icon"]')
                .should('have.length', 6).and('be.visible')
            cy.get('@features').find('[data-testid="feature-heading"]')
                .should('have.length', 6).and('be.visible')
            cy.get('@features').find('[data-testid="feature-caption"]')
                .should('have.length', 6).and('be.visible')
            // minigrid-half
            cy.get('[data-section="minigrid-browse-by-room-half"]').as('minigridHalf')
            cy.get('@minigridHalf').find('[data-testid="toggle-button"]')
                .should('have.length', 4).and('be.visible')
            cy.get('@minigridHalf').find('[data-testid="go-to-form-cta"]')
                .should('be.visible')
            cy.get('@minigridHalf').find('[data-testid="half-carousel-image"] img')
                .should('have.length.at.least', 2).first().as('image')
            cy.wait(1000)
            cy.get('@image').scrollIntoView().should('be.visible')
            cy.get('@minigridHalf').find('[data-testid="half-carousel-logo"]')
                .should('have.length.at.least', 2).first().as('logo')
            cy.wait(1000)
            cy.get('@logo').scrollIntoView().should('be.visible')
            // testimonials
            cy.get('[data-section="testimonials"]').as('testimonials')
            cy.get('@testimonials').find('.swiper-wrapper').should('be.visible')
            cy.get('@testimonials').find('.swiper-slide').should('have.length', '6')
            // carousel
            cy.get('[data-section="categories-carousel"]').as('carousel')
            cy.get('@carousel').find('[data-testid="carousel-headline"]')
                .should('be.visible')
            cy.get('@carousel').find('[data-testid="carousel-subheadline"]')
                .should('be.visible')
            cy.get('@carousel').find('[data-testid="swiper-slide-picture"]')
                .should('be.visible').and('have.length.at.least', 4)
            cy.get('@carousel').find('[data-testid="swiper-slide-link"]')
                .should('be.visible').and('have.length.at.least', 4)
            cy.get('@carousel').find('.text-center button')
                .should('be.visible').and('have.length', 1)
            // form
            cy.get('#b2b-form').should('be.visible')
        })
    })
})
