import { 
    testEmptyCartIfNotSignIn,
    testNotEmptyCartOnDesktop,
    testNotEmptyCartOnMobile,
    testQuantity
 } from "../../test_modules/cart_tests"

const mobileDevices = ['iphone-xr']
const desktopDevices = ['macbook-15']
const devices = [...mobileDevices, ...desktopDevices]

before(() => {
    cy.setCookie('checkout2025', 'ok')
})

describe ('Test cart', () => {
    devices.forEach(device => {
        testEmptyCartIfNotSignIn(device)
        testQuantity(device)
    })

    mobileDevices.forEach(device => {
        testNotEmptyCartOnMobile(device)
    })

    desktopDevices.forEach(device => {
        testNotEmptyCartOnDesktop(device)
    })
})