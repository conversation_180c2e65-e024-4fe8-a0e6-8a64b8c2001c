import { 
    prepareTest,
    testChangingRegionAndLanguageOnMobile,
    testChangingRegionAndLanguageOnDesktop
} from "../../test_modules/change_reg_and_lang_tests"

const mobileDevices = ['iphone-se2']
const desktopDevices = ['macbook-15']

const params = [
    {region: 'france', exp_lang: 'fr', change_lang: 'en'},
    {region: 'germany', exp_lang: 'de'},
    {region: 'netherlands', exp_lang: 'nl'},
    {region: 'norway'},
    // {region: 'switzerland', exp_lang: 'de', change_lang: 'fr'}, // very often there is EN on ch
    {region: 'italy'},
    {region: 'spain', exp_lang: 'es'}
]

describe ('Test region and language change', () => {
    mobileDevices.forEach(device => 
        context(`${device} resolution`, () => {
            beforeEach(() => {
                prepareTest(device)
            })
            params.forEach(param => testChangingRegionAndLanguageOnMobile(param.region, param.exp_lang, param.change_lang))
        })
    )

    desktopDevices.forEach(device => 
        context(`${device} resolution`, () => {
            beforeEach(() => {
                prepareTest(device)  
            })
            params.forEach(param => testChangingRegionAndLanguageOnDesktop(param.region, param.exp_lang, param.change_lang))
        })
    )
})
