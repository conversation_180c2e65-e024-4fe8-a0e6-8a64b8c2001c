import { isProdEnv, generateRandomEmail, addToCart } from "../../test_modules/common_functions"
import login_page from "../../pages/login_page"
import pdp from "../../pages/product_page"
import sample_page from "../../pages/sample_page"
import checkout from "../../pages/checkout_page"
import cart from "../../pages/cart_page"

const url = Cypress.config('baseUrl')
const mobileDevices = ['iphone-se2']
const desktopDevices = ['macbook-15']
const deliveryAddress = {
    first_name: 'A<PERSON>',
    surname: 'B<PERSON>',
    street: 'Cc',
    postal_code: '12-345',
    city: 'Dd',
    phone_number: '123456789',
}

describe('User data on mobile', () => {
    mobileDevices.forEach(device => 
        context(`${device} resolution`, () => {
            beforeEach(() => {             
                cy.viewport(device)
            })
            testElementsExistanceOnMobile(3)
            testUserDataInCheckoutOnMobile('ch', 'fr')
        })
    )
})

describe('User data on desktop', () => {
    desktopDevices.forEach(device => 
        context(`${device} resolution`, () => {
            beforeEach(() => {
                cy.viewport(device)  
            })
            testElementsExistanceOnDesktop()
            testUserDataInCheckout('it', 'it')
        }
    ))
})

function testUserDataInCheckout(region, language) {
    if (!isProdEnv()) {
        it('User data should be filled in checkout on desktop', () => {
            deliveryAddress.email = generateRandomEmail()
            cy.visit(`${url}/${language}-${region}${login_page.url}`)
            cy.agreeCookies()
            login_page.loginUser(deliveryAddress.email, 'TestGold7&')
            cy.wait(5000)
            cy.visit(`${url}/${language}-${region}/change-address/delivery`)
            changeDeliveryAddress(deliveryAddress)
            cy.get('button[type="submit"').first().click()
            cy.url().should('include', '/account')
            cy.wait(4000)
            pdp.openMegaMenu()
            pdp.navigateToSamples()
            cy.wait(3000)
            sample_page.addToCartSampleWithIndex(17)
            pdp.acceptModalAndGoToCheckout()
            checkout.checkFilledForm(deliveryAddress)
        })
    }
}

function testUserDataInCheckoutOnMobile(region, language) {
    if (!isProdEnv()) {
        it('User data should be filled in checkout on mobile', () => {
            deliveryAddress.email = generateRandomEmail()
            cy.visit(`${url}/${language}-${region}${login_page.url}`)
            cy.agreeCookies()
            login_page.loginUser(deliveryAddress.email, 'TestGold7&')
            cy.wait(5000)
            cy.visit(`${url}/${language}-${region}/change-address/delivery`)
            changeDeliveryAddress(deliveryAddress)
            cy.get('button[type="submit"').first().click()
            cy.url().should('include', '/account')
            cy.wait(4000)
            pdp.openMegaMenuOnMobile()
            pdp.navigateToSamplesOnMobile()
            cy.wait(5000)
            sample_page.addToCartSampleWithIndex(17)
            cy.wait(3000)
            pdp.acceptModalAndGoToCheckout()
            checkout.checkFilledForm(deliveryAddress)
        })
    }
}

function changeDeliveryAddress(data) {
    cy.get('[data-testid="input-first-name"]').type(data.first_name)
    cy.get('[data-testid="input-last-name"]').type(data.surname)
    cy.get('[data-testid="input-street-address-1"]').type(data.street)
    cy.get('[data-testid="input-postal-code"]').type(data.postal_code)
    cy.get('[data-testid="input-city"]').type(data.city)
    cy.get('[data-testid="input-phone"]').type(data.phone_number)
}

function testElementsExistanceOnDesktop() {
    it('check checkout sections', () => {
        cy.visit(url + '/en-uk' + cart.url)
        cy.agreeCookies()
        addToCart(['OriginalBookcase', 'ToneWardrobe', 'EdgeSample'])
        cy.get(cart.selectors.cart_summary_cta).click()
        cy.get(login_page.selectors.continue_as_guest).click()
        cy.wait(2000)
        checkout.acceptDeliveryModalIfVisible()
        cy.get(checkout.selectors.breadcrumb).should('have.length', 3).each((breadcrumb) => {
            expect(breadcrumb).to.be.visible
        })
        cy.get('[data-testid="delivery-address-section"]').scrollIntoView().should('be.visible')
        cy.get('[data-testid="contact-section"]').scrollIntoView().should('be.visible')
        cy.get('[data-testid="assembly-section"]').scrollIntoView().should('be.visible')
        cy.get('[data-testid="payment-section"]').scrollIntoView().should('be.visible')
            .find('li.adyen-checkout__payment-method').should('have.length.gte', 1).and('be.visible')
        cy.get('[data-testid="summary-title"]').should('be.visible')
        cy.get('[data-testid="summary-details"]').should('be.visible')
        cy.get('[data-testid="summary-promo"]').should('be.visible')
        // add subtotal price and desktop newsletter, terms
        cy.get('[data-testid="total-price"]').should('be.visible')
        cy.get('[data-testid="payment-button"]').should('be.visible')
        cy.get('[data-testid="checkout-usps"]:visible').should('be.visible')
            .find('[data-testid]').should('have.length', 4)
    })
}

function testElementsExistanceOnMobile(numOfItems) {
    it('check checkout sections', () => {
        cy.visit(url + '/en-uk' + cart.url)
        cy.agreeCookies()
        addToCart(['OriginalBookcase', 'ToneWardrobe', 'EdgeSample'])
        cy.get(cart.selectors.cart_summary_cta).click()
        cy.get(login_page.selectors.continue_as_guest).click()
        cy.wait(2000)
        checkout.acceptDeliveryModalIfVisible()
        cy.get(checkout.selectors.breadcrumb).should('have.length', 3).each((breadcrumb) => {
            expect(breadcrumb).to.be.visible
        })
        cy.get('[data-testid="delivery-address-section"]').scrollIntoView().should('be.visible')
        cy.get('[data-testid="contact-section"]').scrollIntoView().should('be.visible')
        cy.get('[data-testid="assembly-section"]').scrollIntoView().should('be.visible')
        cy.get('[data-testid="payment-section"]').scrollIntoView().should('be.visible')
            .find('li.adyen-checkout__payment-method').should('have.length.gte', 1).and('be.visible')
        cy.get('[data-testid="mobile-newsletter-terms-section"]').scrollIntoView().should('be.visible')
        cy.get('[data-testid="mobile-promo"]').scrollIntoView().should('be.visible')
        cy.get('[data-testid="checkout-mobile-summary"]').scrollIntoView().should('be.visible')
        cy.get('[data-testid="checkout-mobile-sms-newsletter"]').scrollIntoView().should('be.visible')
        cy.get('[data-testid="checkout-mobile-terms"]').scrollIntoView().should('be.visible')
        cy.get('[data-testid="total-price"]').scrollIntoView().should('be.visible')
        cy.get('[id="checkout-teleports"]').should('be.visible')

        cy.get('[data-testid="mobile-summary-bar"]').should('be.visible')
        cy.get('[data-testid="mobile-summary-caret-icon"]').should('be.visible').click()
        cy.get('[data-headlessui-state="open"]').as('items').should('be.visible')
        cy.get('@items').find('[data-testid="item-price"]').should('have.length', numOfItems)
            .and('be.visible')
        cy.get('@items').find('[data-testid="total-price"]').scrollIntoView().should('be.visible')
        cy.get('[data-testid="mobile-summary-caret-icon"]').click({force:true})
    })
}
