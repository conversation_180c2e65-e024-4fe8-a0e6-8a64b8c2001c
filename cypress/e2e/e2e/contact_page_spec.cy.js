import { isProdEnv } from '../../test_modules/common_functions'

const isProd = isProdEnv()

beforeEach(() => {
    cy.intercept('POST', '/api/v1/contact/').as('contact')
})

testSections('/de-de/contact')
sendStandardMessage()
sendChangeOrder()
reportDamage()
verifyFormulateExistance()

// Do note change message and email address
// These are agreed with CS team
const message = 'Stand msg. 123!@'
const testMail = '<EMAIL>'

function sendStandardMessage() {
    const topics = [
        'product_info',
        'feedback',
        'assembly_service',
        'other'
    ]
    topics.forEach((topic) => {
        it(`User is able to send message on topic: "${topic}"`, () => {
            const name = 'Name'
            const email = testMail
            const msg = message

            cy.visit('/contact')
            cy.agreeCookies()
            cy.get('[data-testid="contact-topic"]').select(topic)
            cy.get('[data-testid="contact-message-input"]').type(msg)
            cy.get('[data-testid="contact-name-input"]').type(name)
            cy.get('[data-testid="contact-email-input"]').type(email)
            cy.get('[data-testid="contact-newsletter"]').click().click()
            cy.get('[data-testid="contact-button"]').click()
            if (!isProd) {
                cy.wait('@contact').then(({response}) => {
                    const body = response.body
                    expect(response.statusCode).to.eq(201)
                    expect(body.email).to.equal(email)
                    expect(body.first_name).to.equal(name)
                    expect(body.message).to.equal(msg)
                    expect(body.topic).to.equal(topic)
                })
                verifyContactThankYouPage(name, email)
            }
        }) 
    })
}

function sendChangeOrder() {
    it(`User is able to send message with order change request`, () => {
        const name = 'Name'
        const email = testMail
        const msg = message
        const topic = 'change_order'
        const orderNr = 'test 123!@'

        cy.visit('/contact')
        cy.agreeCookies()
        cy.get('[data-testid="contact-topic"]').select(topic)
        cy.get('[data-testid="contact-message-input"]').type(msg)
        cy.get('[data-testid="contact-order-input"]').type(orderNr)
        cy.get('[data-testid="contact-name-input"]').type(name)
        cy.get('[data-testid="contact-email-input"]').type(email)
        cy.get('[data-testid="contact-newsletter"]').click().click()
        cy.get('[data-testid="contact-button"]').click()
        if (!isProd) {
            cy.wait('@contact').then(({response}) => {
                const body = response.body
                expect(response.statusCode).to.eq(201)
                expect(body.email).to.equal(email)
                expect(body.first_name).to.equal(name)
                expect(body.message).to.equal(msg)
                expect(body.topic).to.equal(topic)
                expect(body.order).to.equal(orderNr)
            })
            verifyContactThankYouPage(name, email)
        }
    })
}

function reportDamage() {
    it(`User is able to report damage or missing`, () => {
        const name = 'Name'
        const email = testMail
        const topic = 'report_an_issue_or_complaint'
        const subtopic = 'issue_damages'
        const orderNr = 'test 123!@'

        cy.visit('/contact')
        cy.agreeCookies()
        cy.get('[data-testid="contact-topic"]').select(topic)
        cy.get('[data-testid="contact-form-issue-subtopic"]').select(subtopic)
        cy.get('[data-testid="contact-order-input"]').type(orderNr)

        cy.get('[data-testid="contact-form-pack-damaged-choice"]').find('input[value="yes"]').click()
        cy.get('[data-testid="contact-form-pack-damaged-report-choice"]').find('input[value="yes"]').click()
        cy.get('[data-type="image"]').should('be.visible').and('have.length', 1)

        cy.get('[data-testid="contact-form-elem-damaged-choice"]').find('input[value="yes"]').click()
        cy.get('[data-testid="contact-form-elem-damaged-which"]').should('be.visible')
        cy.get('[data-testid="contact-form-elem-damaged-p-1"]').should('be.visible')
        cy.get('[data-testid="contact-form-elem-damaged-p-2"]').should('be.visible')
        cy.get('[data-testid="contact-form-elem-damaged-p-3"]').should('be.visible')
        cy.get('[data-testid="contact-form-elem-damaged-img-upload-text"]').should('be.visible')
        cy.get('[data-type="image"]').should('be.visible').and('have.length', 2)

        cy.get('[data-testid="contact-form-elem-damaged-description-text"]').should('be.visible')
        cy.get('[data-testid="contact-form-elem-damaged-description-input"]').should('be.visible').type(message)
        cy.get('[data-testid="contact-form-elem-missing-choice"]').find('input[value="yes"]').click()

        cy.get('[data-testid="contact-form-elem-missing-which-text"]').should('be.visible')
        cy.get('[data-testid="contact-form-elem-damaged-p-3"]').should('be.visible')
        cy.get('[data-testid="contact-form-elem-missing-input"]').should('be.visible').type(message)
        cy.get('[data-testid="contact-form-damaged-or-lacks-additional-input"]').should('be.visible').type(message)

        cy.get('[data-testid="contact-name-input"]').type(name)
        cy.get('[data-testid="contact-email-input"]').type(email)
        cy.get('[data-testid="contact-newsletter"]').click().click()
        cy.get('[data-testid="contact-button"]').click()
        if (!isProd) {
            cy.wait('@contact').then((interception) => {
                const resp = interception.response
                const respBody = resp.body
                expect(resp.statusCode).to.eq(201)
                expect(respBody.email).to.equal(email)
                expect(respBody.first_name).to.equal(name)
                expect(respBody.topic).to.equal(subtopic)
                expect(respBody.order).to.equal(orderNr)
            })
            verifyContactThankYouPage(name, email)
        }  
    })
}

function verifyFormulateExistance() {
    it(`User is informed about empty inputs`, () => {
        cy.visit('/en-uk/contact')
        cy.agreeCookies()
        cy.get('.formulate-input-errors').should('not.exist')
        cy.get('[data-testid="contact-button"]').click()
        cy.get('.formkit-message:visible').as('error').should('have.length', 4)
        cy.get('@error').eq(0).should('have.text', 'This value is required')
        cy.get('@error').eq(1).should('have.text', 'This value is required')
        cy.get('@error').eq(2).should('have.text', 'This value is required')
        cy.get('@error').eq(3).should('have.text', 'Sorry, not all fields are filled out correctly.')
    })
}

function testSections(url) {
    it('Check if page elements are presented', () => {
        cy.visit(url)
        cy.agreeCookies()
        verifyCommonElements()
        cy.get('[data-testid="contact-footer-poland-map"]').should('not.exist')
    })
}

function testSectionsForPoland(url) {
    it('Check if page elements are presented in Poland', () => {
        cy.visit(url)
        cy.agreeCookies()
        verifyCommonElements()
        cy.get('[data-testid="contact-footer-poland-map"]').should('be.visible')
    })
}

function verifyContactThankYouPage(name, email) {
    cy.get('[data-testid="contact-thank-you"]').should('be.visible')
    cy.get('[data-testid="contact-thank-you-personal-data-name"]').should('be.visible').and('contain.text', name)
    cy.get('[data-testid="contact-thank-you-personal-data-email"]').should('be.visible').and('contain.text', email)
    verifyContactData()
    verifyB2B()
    verifyJobs()
    verifyPress()
    verifyLegalNotice()
}

function verifyCommonElements() {
    verifyHeader()
    verifyContactForm()
    verifyForm()
    verifyContactData()
    verifyB2B()
    verifyJobs()
    verifyPress()
    verifyLegalNotice()
}

function verifyHeader() {
    cy.get('[data-testid="contact-heading"]').should('be.visible')
    cy.get('[data-testid="contact-header-picture"]').should('be.visible')
}

function verifyContactForm() {
    cy.get('#contact-form').as('contactForm')
    cy.get('@contactForm').find('[data-testid="contact-faq"]').as('faq').should('be.visible')
    cy.get('@faq').find('[data-testid="contact-faq-title"]').should('be.visible')
    cy.get('@faq').find('[data-testid="contact-faq-subtitle"]').should('be.visible')
    cy.get('@faq').find('[data-testid="contact-faq-order-status"]').as('orderStatus').should('be.visible').click()
    cy.get('@orderStatus').find('[data-testid="contact-faq-question"]').should('be.visible')
    cy.get('@orderStatus').find('[data-testid="contact-faq-answer"]').should('be.visible')

    cy.get('@faq').find('[data-testid="contact-faq-types"]').as('types').should('be.visible').click()
    cy.get('@types').find('[data-testid="contact-faq-question"]').should('be.visible')
    cy.get('@types').find('[data-testid="contact-faq-answer"]').should('be.visible')

    cy.get('@faq').find('[data-testid="contact-faq-delivery"]').as('delivery').should('be.visible').click()
    cy.get('@delivery').find('[data-testid="contact-faq-question"]').should('be.visible')
    cy.get('@delivery').find('[data-testid="contact-faq-answer"]').should('be.visible')

    cy.get('@faq').find('[data-testid="contact-faq-showroom"]').as('showroom').should('be.visible').click()
    cy.get('@showroom').find('[data-testid="contact-faq-question"]').should('be.visible')
    cy.get('@showroom').find('[data-testid="contact-faq-answer"]').should('be.visible')

    cy.get('@faq').find('[data-testid="contact-faq-link-to-faq"]').should('be.visible')
}

function verifyForm() {
    cy.get('[data-testid="contact-topic-title"]').should('be.visible')
    cy.get('[data-testid="contact-topic"]').should('be.visible')
    cy.get('[data-testid="contact-message-input"]').should('be.visible')
    cy.get('[data-testid="contact-name-input"]').should('be.visible')
    cy.get('[data-testid="contact-email-input"]').should('be.visible')
    cy.get('[data-type="checkbox"]').filter(':visible').as('newsletter')
    cy.get('@newsletter').click()
    cy.get('@newsletter').find('label').should('be.visible')
    cy.get('@newsletter').find('[data-testid="contact-newsletter"]').should('be.visible').and('be.checked')
    cy.get('[data-testid="contact-button"]').should('be.visible')
}

function verifyContactData() {
    cy.get('[data-testid="contact-footer-personal-contact"]').as('personalContact').should('be.visible')
    cy.get('@personalContact').find('[data-testid="contact-footer-personal-contact-hours1"]').should('be.visible')
    cy.get('@personalContact').find('[data-testid="contact-footer-personal-contact-hours2"]').should('be.visible')
    cy.get('@personalContact').find('[data-testid="contact-footer-personal-contact-phone-link"]').should('be.visible')
    cy.get('@personalContact').find('[data-testid="contact-footer-personal-contact-livechat-link"]').should('be.visible')
    cy.get('@personalContact').find('[data-testid="contact-footer-personal-contact-whatsapp-link"]').should('be.visible')
    cy.get('@personalContact').find('[data-testid="contact-footer-personal-contact-mail-link"]').should('be.visible')
}

function verifyB2B() {
    cy.get('[data-testid="contact-footer-b2b"]').as('b2b').should('be.visible')
    cy.get('@b2b').find('[data-testid="contact-footer-b2b-link"]').should('be.visible')
}

function verifyJobs() {
    cy.get('[data-testid="contact-footer-jobs"]').as('jobs').should('be.visible')
    cy.get('@jobs').find('[data-testid="contact-footer-jobs-link"]').should('be.visible')
}

function verifyPress() {
    cy.get('[data-testid="contact-footer-press"]').as('press').should('be.visible')
    cy.get('@press').find('[data-testid="contact-footer-press-link"]').should('be.visible')
}

function verifyLegalNotice() {
    cy.get('[data-testid="contact-footer-legal-notice"]').should('be.visible')
    cy.get('[data-testid="contact-footer-legal-notice-address-title"]').should('be.visible')
    cy.get('[data-testid="contact-footer-legal-notice-address"]').should('be.visible')
    cy.get('[data-testid="contact-footer-legal-notice-reg-details-title"]').should('be.visible')
    cy.get('[data-testid="contact-footer-legal-notice-reg-details"]').should('be.visible')
    cy.get('[data-testid="contact-footer-legal-notice-tax-strategy-link"]').should('be.visible')
    .and(($element) => {
        expect($element.attr('href').endsWith('.pdf')).to.be.true
    })
}
