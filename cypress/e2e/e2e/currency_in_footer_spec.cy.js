describe ('Check if amount and currency are proper for region', () => {
    const base_url = Cypress.config('baseUrl')
    const prefix = '/en-de'
    const pages = [
        `${base_url}${prefix}`,
        `${base_url}${prefix}/product-lines`,
        `${base_url}${prefix}/furniture-c/sideboard`,
        `${base_url}${prefix}/furniture/sideboard/3363975,j,deep-cotton-beige-sideboard-with-doors-drawers-backpanels-and-legs-140x73x40cm`,
        `${base_url}${prefix}/material-samples`,
        ]

    const expected_price = '300'
    const expected_currency = '€'

    pages.forEach(page => {
        it(`check if there is "${expected_price} ${expected_currency}" in the newsletter on site: ` + page, () => {
            cy.visit(page)
            cy.get('button[data-testid="footer-newsletter-button"]', { timeout: 5000 }).should(($btn) => {
                expect($btn).to.contain(expected_price)
                expect($btn).to.contain(expected_currency)
            })
        })
    })
})
