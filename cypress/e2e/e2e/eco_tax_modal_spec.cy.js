import { extractPriceValue} from '../../test_modules/common_functions'
import pdp from "../../pages/product_page"
import cart from "../../pages/cart_page"

const devices = ['macbook-15']

describe('Checkout flow', () => {
    devices.forEach((device) => {
        context(`device: ${device}`, () => {
            it('cart - eco part is visible in France', () => {
                cy.viewport(device)
                cy.visit('/fr-fr/meuble/etagere-murale/3727514,j')
                cy.agreeCookies()
                pdp.waitForConfiguratorToBeReadyAndAddToCart()
                pdp.acceptModalAndGoToCart()
                cart.getCartTotalPriceAsString().then((totalPrice) => {
                    cy.wait(2000)
                    cy.get('[data-testid="cart-ecopart-show"]').click()
                    cy.get('[data-testid="ecotax-modal"]:visible', { timeout:20000 }).as('ecotaxModal')
                    cy.get('@ecotaxModal').find('[data-testid="ecotax-total-price"]').invoke('text').then((totalPriceModal) => {
                        cy.get('@ecotaxModal').find('[data-testid="ecotax-total-price-without-tax"]').invoke('text').then((totalPriceWithoutTax) => {
                            cy.get('@ecotaxModal').find('[data-testid="ecotax-tax-price"]').invoke('text').then((tax) => {
                                expect(extractPriceValue(totalPriceModal)).to.equal(extractPriceValue(totalPriceWithoutTax) + extractPriceValue(tax))
                                expect(totalPriceModal.trim()).to.equal(totalPrice.trim())
                            })
                        })   
                    })
                })
            })
        })
    })
})
