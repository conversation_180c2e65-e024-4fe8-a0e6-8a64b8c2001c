import commonPage from '../../pages/common_page'
import { generateRandomEmail, isProdEnv } from '../../test_modules/common_functions'

const devices = ['macbook-15', 'iphone-se2']
const page = new commonPage
const urls = ['de-de', 'en-uk/newsletter']

describe ('Newsletter tests', () => {
    devices.forEach(device => {
        urls.forEach((url) => {
            context(`${device} resolution`, () => {
                beforeEach(() => {
                    cy.viewport(device)
                    cy.intercept('POST', '/api/v1/newsletter/*').as('newsletterFooterRequest')
                })
                testNewsletter(url)
            })
        })
    })
})

function testNewsletter(url) {
    it(`Check newsletter signup from footer`, () => {
        const testEmail = generateRandomEmail()
        cy.visit(url)
        cy.agreeCookies()
        cy.get('[data-testid=footer-newsletter-input]', { timeout:20000 })
        .clear().type(testEmail, { timeout:15000 }).invoke('val').then((typedText) => {
                expect(typedText).to.equal(testEmail)
        })
        cy.get('[data-testid=footer-newsletter-button]').as('submit')
        if (!isProdEnv()) {
            cy.get('@submit').click({timeout:10000})
            cy.wait('@newsletterFooterRequest').then(({request, response}) => {
                expect(response.statusCode).to.eq(200)
                expect(request.body.email).to.equal(testEmail)
            })
            page.getSuccessNotificationNuxt3().should('be.visible')
        }
    })
}
