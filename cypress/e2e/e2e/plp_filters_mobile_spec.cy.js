import plp, { FILTERS, Filter } from "../../pages/product_listing_page"

describe('Test filters on PLP', () => {
    const devices = ['iphone-se2']
    const url = plp.url

    devices.forEach(device => 
        context(`${device} resolution`, () => {
            beforeEach(() => {
                cy.intercept('GET', '/nuxt-api/catalog*').as('catalogue')
                cy.viewport(device)
                cy.visit(url)
                cy.agreeCookies()
            })
            // test single filters
            const filtersToBeTested = [FILTERS.premiumMatte]
            filtersToBeTested.forEach(filter => testSingleFilter(filter))
            // test filters together
            testFiltersFromSameTab(FILTERS.red, FILTERS.blue)
            testFiltersFromDifferentTabs(FILTERS.red, FILTERS.originalModern)
        })
    )
})

describe('remove all filters', () => {
    const devices = ['iphone-se2']
    const url = '/pl-pl/furniture-c/?colors=black,blue&materials=plywood&additional=sale&productLines=0,2'

    devices.forEach(device => 
        context(`${device} resolution`, () => {
            beforeEach(() => {
                cy.intercept('GET', '/nuxt-api/catalog*').as('catalogue')
                cy.viewport(device)
                cy.visit(url)
                cy.agreeCookies()
            })

            it(`Clearing filters`, () => {
                cy.wait(2000)
                plp.clearFiltersOnMobile()
                cy.wait('@catalogue', { timeout: 10000 }).its('response.body.results')
                .should('have.length', 32)
                .then(() => {
                    cy.url().should('eq', Cypress.config('baseUrl') + '/pl-pl/furniture-c')
                })
            })
        })
    )
})

function testSingleFilter(filter) {
    it(`test single filter: ${filter.name}`, () => {
        if (!(filter instanceof Filter)) {
            throw new Error('Invalid filter object');
        }
        else {
            plp.openFilterDrawer()
            plp.selectFilter(filter)
            cy.wait('@catalogue').then((interception) => {
                const items = getItemsFromInterception(interception)
                expect(items).to.have.length(32)
                items.forEach(item => {
                    expect(item[filter.getComparableAttr()]).to.be.oneOf(filter.getExpectedResults())
                })
            })
            plp.applyFilters()
        }
    })
}

function testFiltersFromDifferentTabs(filter1, filter2) {
    it(`test filters from different tabs: ${filter1.name}, ${filter2.name}`, () => {
        if (!(filter1 instanceof Filter && filter2 instanceof Filter)) {
            throw new Error('Invalid filter object');
        }
        else {
            // 1 filter
            plp.openFilterDrawer()
            plp.selectFilter(filter1)
            cy.wait('@catalogue').then((interception) => {
                const items = getItemsFromInterception(interception)
                expect(items).to.have.length(32)
                items.forEach(item => {
                    expect(item[filter1.getComparableAttr()]).to.be.oneOf(filter1.getExpectedResults())
                })
            })
            plp.applyFilters()

            // add 2 filter from another tab
            plp.openFilterDrawer()
            plp.selectFilter(filter2)
            cy.wait('@catalogue').then((interception) => {
                const items = getItemsFromInterception(interception)
                expect(items).to.have.length(32)
                items.forEach(item => {
                    expect(item[filter1.getComparableAttr()]).to.be.oneOf(filter1.getExpectedResults())
                    expect(item[filter2.getComparableAttr()]).to.be.oneOf(filter2.getExpectedResults())
                })
            })
            plp.applyFilters()

            // remove 1 filter
            plp.openFilterDrawer()
            plp.selectFilter(filter1)
            cy.wait('@catalogue').then((interception) => {
                const items = getItemsFromInterception(interception)
                expect(items).to.have.length(32)
                items.forEach(item => {
                    expect(item[filter2.getComparableAttr()]).to.be.oneOf(filter2.getExpectedResults())
                })
            })
            plp.applyFilters()
        }
    })
}

function testFiltersFromSameTab(filter1, filter2) {
    it(`test filters from the same tab: ${filter1.name}, ${filter2.name}`, () => {
        if (!(filter1 instanceof Filter && filter2 instanceof Filter)) {
            throw new Error('Invalid filter object');
        }
        else {
            // 1 filter
            plp.openFilterDrawer()
            plp.selectFilter(filter1)
            cy.wait('@catalogue').then((interception) => {
                const items = getItemsFromInterception(interception)
                expect(items).to.have.length(32)
                items.forEach(item => {
                    expect(item[filter1.getComparableAttr()]).to.be.oneOf(filter1.getExpectedResults())
                })
            })
            plp.applyFilters()
           
            // add 2 filter from the same tab
            plp.openFilterDrawer()
            plp.selectFilter(filter2)
            cy.wait('@catalogue').then((interception) => {
                const items = getItemsFromInterception(interception)
                expect(items).to.have.length(32)
                items.forEach(item => {
                    expect(item[filter1.getComparableAttr()]).to.be.oneOf([...filter1.getExpectedResults(), ...filter2.getExpectedResults()])
                    
                })
            })
            plp.applyFilters()

            // remove 1 filter
            plp.openFilterDrawer()
            plp.selectFilter(filter1)
            cy.wait('@catalogue').then((interception) => {
                const items = getItemsFromInterception(interception)
                expect(items).to.have.length(32)
                items.forEach(item => {
                    expect(item[filter2.getComparableAttr()]).to.be.oneOf(filter2.getExpectedResults())
                })
            })
            plp.applyFilters()
        }
    })
}

function getItemsFromInterception(interception) {
    return interception.response.body.results
}
