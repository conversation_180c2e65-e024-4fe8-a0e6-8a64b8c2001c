import cart from "../../pages/cart_page"
import hp from "../../pages/home_page"
import { 
    extractCurrency,
    extractPriceValue,
    addToCart
} from "../../test_modules/common_functions"

let region = 'de-de'
const url = Cypress.config('baseUrl') + `/${region}`

describe ('Test price rounding', () => {
    it('sum of item prices should equal total price', () => {
        cy.visit(url)
        cy.agreeCookies()
        const items_count = 10
        const items = new Array(items_count).fill('OriginalBookcase')
        addToCart(items)

        hp.goToCart()
        cy.get(cart.selectors.cart_item_price).should('have.length', items_count)
        
        cart.isPromoApplied().then((isApplied) =>  {
            if (isApplied) {
                cart.getTopCartItemPriceAsString().then((itemPrice) => {
                    cart.getCartTotalPriceAsString().then((totalPrice) => {
                        expect(Math.abs(extractPriceValue(totalPrice) - items_count * extractPriceValue(itemPrice))).to.be.lessThan(items_count)
                        expect(extractCurrency(totalPrice)).to.eql(extractCurrency(itemPrice))
                    })
                })
                cart.removePromoIfApplied()
                cy.get(cart.selectors.cart_item_price).should('have.length', items_count)
            }

            else {
                cart.getTopCartItemPriceAsString().then((itemPrice) => {
                    cart.getCartTotalPriceAsString().then((totalPrice) => {
                        expect(extractPriceValue(totalPrice)).to.eql(items_count * extractPriceValue(itemPrice))
                        expect(extractCurrency(totalPrice)).to.eql(extractCurrency(itemPrice))
                    })
                })
            }
        })
    })
})
