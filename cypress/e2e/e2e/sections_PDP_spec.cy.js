import {
    testPdpSectionsOnDesktop,
    testPdpSectionsOnMobile,
    testExpressionsPdpSectionsOnDesktop,
    testExpressionsPdpSectionsOnMobile,
    testOriginalsPdpSectionsOnDesktop,
    testOriginalsPdpSectionsOnMobile
 } from '../../test_modules/sections_PDP_tests'

const desktopDevices = ['macbook-15']
const mobileDevices = ['iphone-se2']

const urls = [
    '/de-de/mobel/kleiderschrank/523148,w,hoher-schlanker-helles-holzimitat-2-tur-kleiderschrank-mit-kleiderstange-141x259x60cm',
    '/de-de/mobel/kleiderschrank/173575,w,hoher-schlanker-grau-dunkelgrau-2-tur-kleiderschrank-mit-innenschubladen-und-kleiderstange-172x237x42cm',
    '/da-dk/mobel/bogreol/521592,w,grande-bibliotheque-en-beige-201x249x27cm'
]

const urlsExpressions = [
    '/fr-fr/meuble/etagerebasse/658351,w,etagere-basse-en-common-configurator-color-powder-pink-141x112x46cm',
]

const urlsOriginals = [
    '/en-uk/furniture/bookcase/3236047,j,large-white-plywood-bookcase-with-drawers-plywood-180x273x40cm',
    '/fr-fr/meuble/bureau/3747197,j,large-bureau-en-blanc-avec-portes-tiroirs-panneaux-arriere-et-passe-cables-150x73x50cm',
    '/nl-nl/meubel/nachtkastje/3527628,j,diepe-eikenfineer-nachtkastje-met-deuren-en-achterwanden-fineer-44x53x40cm',
    '/en-uk/furniture/sideboard/3083786,j,large-sky-blue-sideboard-with-doors-and-drawers-222x83x40cm',
]

describe('PDP sections test', () => {
    desktopDevices.forEach(device => 
        urls.forEach(url => 
            it(`Checking PDP sections: [${url}] on ${device} resolution`, () => {
                cy.viewport(device)
                cy.visit(url)
                cy.agreeCookies()
                testPdpSectionsOnDesktop()
            })
        )
    )

    mobileDevices.forEach(device => 
        urls.forEach(url => 
            it(`Checking PDP sections: [${url}] on ${device} resolution`, () => {
                cy.viewport(device)
                cy.visit(url)
                cy.agreeCookies()
                testPdpSectionsOnMobile()
            })
        )
    )

    desktopDevices.forEach(device => 
        urlsExpressions.forEach(url => 
            it(`Checking PDP sections for expressions: [${url}] on ${device} resolution`, () => {
                cy.viewport(device)
                cy.visit(url)
                cy.agreeCookies()
                testExpressionsPdpSectionsOnDesktop()
            })
        )
    )

    mobileDevices.forEach(device => 
        urlsExpressions.forEach(url => 
            it(`Checking PDP sections for expressions: [${url}] on ${device} resolution`, () => {
                cy.viewport(device)
                cy.visit(url)
                cy.agreeCookies()
                testExpressionsPdpSectionsOnMobile()
            })
        )
    )

    desktopDevices.forEach(device => 
        urlsOriginals.forEach(url => 
            it(`Checking PDP sections for expressions: [${url}] on ${device} resolution`, () => {
                cy.viewport(device)
                cy.visit(url)
                cy.agreeCookies()
                testOriginalsPdpSectionsOnDesktop()
            })
        )
    )

    mobileDevices.forEach(device => 
        urlsOriginals.forEach(url => 
            it(`Checking PDP sections for expressions: [${url}] on ${device} resolution`, () => {
                cy.viewport(device)
                cy.visit(url)
                cy.agreeCookies()
                testOriginalsPdpSectionsOnMobile()
            })
        )
    )
})
