import { testPlpSections, deviceTypes } from '../../test_modules/sections_PLP_tests'

const desktopDevices = ['macbook-15']
const mobileDevices = ['iphone-se2']
const urls = [
    '/pl-pl/furniture-c',
    '/en-uk/furniture-c/wardrobe',
    '/es-es/muebles-c/libreria',
    '/fr-fr/meubles-c/etageres-murales',
    '/nl-nl/meubels-c/kledingkasten',
    '/fr-fr/meubles-c/tables-de-chevet',
    '/es-es/muebles-c/muebles-de-tv',
    '/nl-nl/meubels-c/ladekasten',
    '/pl-pl/furniture-c/vinyl_storage/',
    '/de-de/mobel-c/schreibtische',
]

describe('PLP sections test', () => {
    desktopDevices.forEach(device => 
        urls.forEach(url => 
            it(`Checking PLP sections: [${url}] on ${device} resolution`, () => {
                cy.viewport(device)
                cy.visit(url)
                cy.agreeCookies()
                testPlpSections(deviceTypes.desktop)
            })
        )
    )

    mobileDevices.forEach(device => 
        urls.forEach(url => 
            it(`Checking PLP sections: [${url}] on ${device} resolution`, () => {
                cy.viewport(device) 
                cy.visit(url)
                cy.agreeCookies()
                testPlpSections(deviceTypes.mobile)
            })
        )
    )
})
