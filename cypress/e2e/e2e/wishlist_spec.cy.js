import libraryPage from '../../pages/library_page'
import productPage from '../../pages/product_page'
import {
    generateRandomEmail,
    checkIfCartItemsCountEquals,
    checkIfWishlistItemsCountEquals,
    isProdEnv
} from "../../test_modules/common_functions"

const mobileDevices = ['iphone-se2']
const desktopDevices = ['macbook-15']
const url = Cypress.config('baseUrl')

describe('Wishlist test', () => {
    beforeEach(() => {      
        cy.intercept('POST', '/api/v1/gallery/*/add_to_wishlist_popup/').as('itemAddedToWishlist')
        cy.intercept('POST', '/api/v1/gallery/*/*/add_to_cart_by_id/').as('itemAddedToCart')
        cy.intercept('DELETE', '/api/v1/gallery/*/*').as('itemDeletedFromWishlist')
    })
    
    const desktopParams = [ 
        {region: 'en-be', furniture_link:"/furniture/sideboard/3516424,j"},
        {region: 'en-de', furniture_link:"/furniture/desk/3746954,j"},
        {region: 'en-es', furniture_link:"/furniture/desk/3746954,j"},
        {region: 'en-fr', furniture_link:"/furniture/bookcase/3516467,j"},
        {region: 'en-gr', furniture_link:"/furniture/wardrobe/165805,w"},
    ]
    
    desktopDevices.forEach(device => 
        context(`${device} resolution`, () => {
            desktopParams.forEach(param => testWishlistFlow(device, param.region, param.furniture_link))
        })
    )

    const mobileParams = [ 
        {region: 'en-bg', furniture_link:"/furniture/tv-stand/3762379,j"},
        {region: 'pl-pl', furniture_link:"/furniture/sideboard/3516424,j"}, 
        {region: 'en-no', furniture_link:"/furniture/sideboard/3516424,j"}, 
        {region: 'en-uk', furniture_link:"/furniture/vinyl-storage/3763292,j"},
        {region: 'en-ch', furniture_link:"/furniture/bedside-table/3766689,j"}
    ]
    
    mobileDevices.forEach(device => 
        context(`${device} resolution`, () => {
            mobileParams.forEach(param => testWishlistFlow(device, param.region, param.furniture_link))
        })
    )
})

function testWishlistFlow(device, region, furniture_link) {
    if (!isProdEnv()) {
        it(`Wishlist flow for NEW user: region: ${region}; url: ${furniture_link}`, () => {
            cy.viewport(device)
            cy.visit(buildDirectUrl(region, furniture_link))
            cy.agreeCookies()
            productPage.saveForLaterGuest(generateRandomEmail())
        })
    }
}

function buildDirectUrl(region, furniture_link) {
    return `${url}/${region}${furniture_link}`
}
