import { 
    prepareTest,
    testChangingRegionAndLanguageOnMobile,
    testChangingRegionAndLanguageOnDesktop
} from "../../test_modules/change_reg_and_lang_tests"

const mobileDevices = ['iphone-se2']
const desktopDevices = ['macbook-15']

const paramsMobile = [{region: 'france', exp_lang: 'fr', change_lang: 'en'}]
const paramsDesktop = [{region: 'netherlands', exp_lang: 'nl', change_lang: 'en'}]

describe ('Test region and language change', () => {
    mobileDevices.forEach(device => 
        context(`${device} resolution`, () => {
            beforeEach(() => {
                prepareTest(device)
            })
            paramsMobile.forEach(param => testChangingRegionAndLanguageOnMobile(param.region, param.exp_lang, param.change_lang))
        })
    )

    desktopDevices.forEach(device => 
        context(`${device} resolution`, () => {
            beforeEach(() => {
                prepareTest(device)  
            })
            paramsDesktop.forEach(param => testChangingRegionAndLanguageOnDesktop(param.region, param.exp_lang, param.change_lang))
        })
    )
})
