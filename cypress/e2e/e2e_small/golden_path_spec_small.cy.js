import { 
    testNotLoggedOnUserBuyingFurnitureAndSample, 
    testNotLoggedOnUserBuyingFurnitureOnMobile,
    testNotLoggedOnUserBuyingFurnitureAndSampleOnMobile, 
} from "../../test_modules/goldenpath_tests"

const mobileDevices = ['iphone-se2']
const desktopDevices = ['macbook-15']

describe('Goldenpath on mobile', () => {
    mobileDevices.forEach(device => 
        context(`${device} resolution`, () => {
            beforeEach(() => {             
                cy.viewport(device)       
            })
            
            const test1Params = [
                {region: 'de', language: 'de', category: 'wardrobes', payment_method: 'card'}
            ]
            test1Params.forEach(param => testNotLoggedOnUserBuyingFurnitureOnMobile(param.region, param.category, param.language, param.payment_method))

            const test2Params = [
                {region: 'uk', language: 'en', category: 'bedsidetables', payment_method: 'card'},
            ]
            test2Params.forEach(param => testNotLoggedOnUserBuyingFurnitureAndSampleOnMobile(param.region, param.category, param.language, param.payment_method))
        })
    )
})

describe('Goldenpath on desktop', () => {
    desktopDevices.forEach(device => 
        context(`${device} resolution`, () => {
            beforeEach(() => {             
                cy.viewport(device)       
            })

            const test3Params = [
                {region: 'uk', language: 'en', category: 'wardrobes'}
            ]
            test3Params.forEach(param => testNotLoggedOnUserBuyingFurnitureAndSample(param.region, param.category, param.language))
        }
    ))
})
