import { testPriceForFurnitureAndSample } from '../../test_modules/price_const_tests'

describe('Price should be equal on each step of buying process', () => {
    const devices = [
        'macbook-15',
    ]

    const urls = [
        '/nl-nl/meubel/bureau/3716779,j,grote-oudroze-multiplex-bureau-met-deuren-laden-achterwanden-en-kabelbeheer-multiplex-240x73x40cm',
    ]

    devices.forEach(device => 
        context(`${device} resolution`, () => {
            beforeEach(() => {
                cy.viewport(device)
                cy.setCookie('popup-popup-product_save_nouser1-done', 'ok')
            })
            urls.forEach(url => testPriceForFurnitureAndSample(url))
        })
    )
})
