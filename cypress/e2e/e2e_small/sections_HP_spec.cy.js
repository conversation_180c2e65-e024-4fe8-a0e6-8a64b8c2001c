import { testHpSectionsOnDesktop, testHpSectionsOnMobile } from "../../test_modules/sections_HP_tests"

const desktopDevices = ['macbook-15']
const mobileDevices = ['iphone-se2']
const regions = [
    'de-de',
]

describe('HP sections test', () => {
    desktopDevices.forEach(device => 
        regions.forEach(region => {
            it(`Checking HP sections on ${device} resolution, region: ${region}`, () => {
                cy.viewport(device) 
                cy.visit(`/${region}`)
                cy.agreeCookies()
                testHpSectionsOnDesktop()
            })
        })
    )

    mobileDevices.forEach(device =>
        regions.forEach(region => {
            it(`Checking HP sections on ${device} resolution, region: ${region}`, () => {
                cy.viewport(device) 
                cy.visit(`/${region}`)
                cy.agreeCookies()
                testHpSectionsOnMobile()
            })
        })
    )
})
