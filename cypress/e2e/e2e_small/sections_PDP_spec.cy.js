import { 
    testOriginalsPdpSectionsOnDesktop,
    testOriginalsPdpSectionsOnMobile 
} from '../../test_modules/sections_PDP_tests'

const desktopDevices = ['macbook-15']
const mobileDevices = ['iphone-se2']
const urlsOriginals = [
    '/en-uk/furniture/bookcase/3236047,j,large-white-plywood-bookcase-with-drawers-plywood-180x273x40cm',
]

describe('PDP sections test', () => {
    desktopDevices.forEach(device => 
        urlsOriginals.forEach(url => 
            it(`Checking PDP sections: [${url}] on ${device} resolution`, () => {
                cy.viewport(device) 
                cy.visit(url)
                cy.agreeCookies()
                testOriginalsPdpSectionsOnDesktop()
            })
        )
    )

    mobileDevices.forEach(device => 
        urlsOriginals.forEach(url => 
            it(`Checking PDP sections: [${url}] on ${device} resolution`, () => {
                cy.viewport(device)
                cy.visit(url)
                cy.agreeCookies()
                testOriginalsPdpSectionsOnMobile()
            })
        )
    )
})
