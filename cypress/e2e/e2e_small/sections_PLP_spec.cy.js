import { testPlpSections, deviceTypes } from '../../test_modules/sections_PLP_tests'

const desktopDevices = ['macbook-15']
const mobileDevices = ['iphone-se2']
const urls = [
    '/de-de/mobel-c/sideboard',   
]

describe('PLP sections test', () => {
    desktopDevices.forEach(device => 
        urls.forEach(url => 
            it(`Checking PLP sections: [${url}] on ${device} resolution`, () => {
                cy.viewport(device)
                cy.visit(url)
                cy.agreeCookies()
                testPlpSections(deviceTypes.desktop)
            })
        )
    )

    mobileDevices.forEach(device => 
        urls.forEach(url => 
            it(`Checking PLP sections: [${url}] on ${device} resolution`, () => {
                cy.viewport(device) 
                cy.visit(url)
                cy.agreeCookies()
                testPlpSections(deviceTypes.mobile)
            })
        )
    )
})
