import hp from "../../pages/home_page"
import order_status_page from "../../pages/order_status_page"

var ordersToBeTested = [
    // {order_id: '*********', postcode: '*********', status: 'Order not found'},
    // {order_id: '*********', postcode: '*********', status: 'Bringing your product to life'},
    {order_id: '*********', postcode: '*********', status: 'Delivered'},

]

beforeEach(() => {
    cy.viewport('macbook-15')
    cy.visit('/')
    cy.agreeCookies()                
})

function checkOrderStatusIfExpected(order_id, postcode, status) {
    it(`Checking if order ${order_id} has status "${status}"`, () => {
        cy.get(hp.megaMenu.selectors.top_menu.account).first().click()
        hp.openOrderStatus()
        hp.fillOrderData(order_id, postcode)
        hp.submitOrderData()
        
        cy.url().should('contain', `${order_status_page.url}&order_id=${order_id}&postal_code=${postcode}`)
        cy.get(order_status_page.selectors.status).should('contain', status) 
    })
}

describe('As customer I have possibility to track my orders', () => {
    ordersToBeTested.forEach(order => {
        checkOrderStatusIfExpected(order.order_id, order.postcode, order.status)
    })
})