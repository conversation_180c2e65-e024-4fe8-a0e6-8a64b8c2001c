import checkout from "../../pages/checkout_page"
import { addToCart } from "../../test_modules/common_functions"

const desktop_devices = ['macbook-15']
const regions = [
    'france',
    'germany',
    'netherlands',
    'norway',
    'switzerland',
]

describe ('Test payments with <PERSON>', () => {
    desktop_devices.forEach(device => 
        context(`${device} resolution`, () => {
            beforeEach(() => {
                cy.viewport(device)     
            })

            regions.forEach(region => testCheckPayments(region))
        })
    )
})

function testCheckPayments(region) {
    it(`check payment methods in region ${region}`, () => {
        cy.visit(`/?forced_region=${region}`)
        cy.agreeCookies()
        addToCart(['OriginalSample'])
        cy.wait(1000)
        cy.visit('/checkout')
        checkout.fillForm(Cypress.config('orderData'))
        checkout.continueToPayment()
        cy.wait(5000)
        cy.percySnapshot()
    })
}
