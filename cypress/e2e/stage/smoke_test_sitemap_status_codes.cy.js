describe('Sitemap', () => {
	let urls = []
	let errorStatuses = []
	const baseUrl = Cypress.config('baseUrl')
	const prodUrl = Cypress.config('prodUrl')

	before(() => {
		cy.request('sitemap.xml').as('sitemap').then((response) => {
			const xml = Cypress.$(response.body)
			urls = xml.find('loc')
				.toArray()
				.map((el) => el.innerText.replace(prodUrl, baseUrl))
				.filter((url) => !url.endsWith('a2c') && !url.endsWith('investor-relations'))
			urls.push(`${baseUrl}/cs`, `${baseUrl}/admin`)
		})
	})

	it('should successfully load each URL in the sitemap', () => {
		cy.wrap(urls).each((url) => {
			cy.request({
				url: url,
				failOnStatusCode: false
			}).then((resp) => {
				cy.log(url)
				if (resp.status !== 200) {
					errorStatuses.push({ url: url, status: resp.status })
				}
				expect(resp.status).to.be.lessThan(599)
			})
		}).then(() => {
			if (errorStatuses.length > 0) {
				const errorsList = errorStatuses.map((error) => `${error.url} - Status: ${error.status}`).join('\n')
				throw new Error(`Errors found:\n${errorsList}`)
			} 
			else {
				cy.log('No errors found.')
			}
		})
	})
})
