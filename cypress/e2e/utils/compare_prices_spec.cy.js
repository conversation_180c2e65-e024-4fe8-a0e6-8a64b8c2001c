
import plp, { FILTERS } from "../../pages/product_listing_page"

describe('Price comparison', () => {
    // if there is url of PROD -> should be passed as the 'envWithHigherPrices' due to bigger DB
    // Otherwise the same furniture could not be found on the second environment

    const envWithLowerPrices = Cypress.config('baseUrl')
    const envWithHigherPrices = 'https://jolan.oklyt.pl'

    beforeEach(() => {
        cy.viewport('iphone-se2')
        cy.intercept('GET', '/api/v1/*configurator/*/').as('configData')
    })

    const params = [
        {region: 'sweden', plpCategoryUrl: '/furniture-c', percentageOfRise: 3},
        {region: 'germany', plpCategoryUrl: '/furniture-c/sideboard', percentageOfRise: 3},
        {region: 'netherlands', plpCategoryUrl: '/furniture-c/bookcase', percentageOfRise: 3},
        {region: 'france', plpCategoryUrl: '/furniture-c/wallstorage', percentageOfRise: 3},
        // {region: 'united_kingdom', plpCategoryUrl: '/furniture-c/wardrobe', percentageOfRise: 3}, // UK will not pass due to rounding with 99 endings
        {region: 'poland', plpCategoryUrl: '/furniture-c/wardrobe', percentageOfRise: 3},
        {region: 'switzerland', plpCategoryUrl: '/furniture-c/tv-stand', percentageOfRise: 3},
        {region: 'danmark', plpCategoryUrl: '/furniture-c/chest-of-drawers', percentageOfRise: 3},
        {region: 'belgium', plpCategoryUrl: '/furniture-c/shoerack', percentageOfRise: 3},
        {region: 'spain', plpCategoryUrl: '/furniture-c/desk', percentageOfRise: 3},
        {region: 'italy', plpCategoryUrl: '/furniture-c/vinyl_storage', percentageOfRise: 3}
    ]
    const itemsOnPlpToBeChecked = 15
    params.forEach(param => comparePrices(itemsOnPlpToBeChecked, param.region, param.plpCategoryUrl, param.filter, param.percentageOfRise))

    function comparePrices(itemsOnPlpToBeChecked, region, plpCategoryUrl, filter, percentageOfRaise) {
        for(let i=0; i<itemsOnPlpToBeChecked; i++) {
            it(`${region} - test item nr ${i+1} from PLP: ${plpCategoryUrl}`, () => {
                cy.visit(`${envWithLowerPrices}${plpCategoryUrl}/?forced_region=${region}`).then(() => {
                    cy.intercept('GET', `/api/v1/jetty_configurator/*`).as('configData')
                    cy.agreeCookies()
                    if (filter) {
                        plp.openFilterDrawer()
                        plp.selectFilter(filter)
                        plp.applyFilters()
                    }
                    plp.selectProductFromGridOnMobile(i)
                    cy.wait('@configData').then((interception) => {
                        const response = interception.response
                        expect(response.statusCode).to.eq(200)
                        const regionPrice1 = parseInt(response.body.region_price)
                        cy.log(`API REGION PRICE 1: ${regionPrice1}`)
                        cy.url().then((url) => {
                            let furnitureUrl = url.replace(envWithLowerPrices, '')
                            cy.visit(`${envWithHigherPrices}${furnitureUrl}/?forced_region=${region}`)
                            cy.agreeCookies()
                            cy.wait('@configData').then((interception) => {
                                const response = interception.response
                                expect(response.statusCode).to.eq(200)
                                const regionPrice2 = parseInt(response.body.region_price)
                                cy.log(`API REGION PRICE 2: ${regionPrice2}`)
                                let expectedPrice = regionPrice1 * (1 + percentageOfRaise / 100)
                                expect(regionPrice2).to.be.within(Math.round(0.999 * expectedPrice), Math.round(1.001 * expectedPrice))
                            })
                        })
                    })
                })
            })
        }
    }
})
