describe('Check nuxt PDP', () => {
    function testIfPageExists(page) {
        it(`check page ${page}`, () => {
            cy.visit(`/${page}`, {});
            cy.get('[data-testid=cookies-agree]').first().click();
            cy.get('[data-testid=price').should('have.text', '9027');
            cy.get('[data-section=header]').should('exist');
            cy.wait(500);
        });
    }

    const mockPages = Array(120).fill('furniture/bookcase/317186,j,test?forced_region=poland');
    mockPages.forEach(page => testIfPageExists(page));
});
