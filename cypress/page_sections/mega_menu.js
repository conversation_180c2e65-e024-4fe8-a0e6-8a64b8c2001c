export class MegaMenu {
    selectors = {
        categories: {
            allshelves : '[data-testid=redirect-category-all]',
            sideboards: '[data-testid=redirect-category-sideboard]',
            bookcases: '[data-testid=redirect-category-bookcase]',
            wallstorage: '[data-testid=redirect-category-wallstorage]',
            wardrobes: '[data-testid=redirect-category-wardrobe]',
            desks: '[data-testid=redirect-category-desk]',
            chestofdrawers: '[data-testid=redirect-category-chest]',
            shoeracks: '[data-testid=redirect-category-shoerack]',
            bedsidetables: '[data-testid=redirect-category-bedside-table]', 
            vinylstorage: '[data-testid=redirect-category-vinyl]',
            tvstands: '[data-testid=redirect-category-tvstand]',
        },

        top_menu : {
            shop : '[data-testid=toggle-products-tab]',
            inspiration : '[data-testid=toggle-inspirations-tab]',
            material_samples :'[data-testid=redirect-samples]',
            tylko_for_business : '[data-testid=redirect-tylko-for-business]',
            reviews : '[data-testid=redirect-reviews]',
            signin : '[data-testid=redirect-sign-up]',     
            changeregion : '[data-testid=change-region]',
            orderstatus : '[data-testid=redirect-delivery-status]',
            opencart : '[data-testid=open-cart]',
            account : '[data-testid=redirect-account]',
            wishlist : '[data-testid=redirect-wishlist]',
            navigation: '.navigation-drawer'
        },

        mobile_menu: {
            mobile_menu_burger: '[data-testid=mobile-menu-burger]',
            mobile_category: '[data-testid=mobile-menu-shop-0]',
            languages_list: 'li a[data-testid]',
        },

        rooms : {
           allrooms : '[data-testid=redirect-spaces-all]',
           bedroom :'[data-testid=redirect-spaces-1]',
           livingroom : '[data-testid=redirect-spaces-2]',
           hallway : '[data-testid=redirect-spaces-3]',
           studiooffice : '[data-testid=redirect-spaces-4]',
           kidsroom : '[data-testid=redirect-spaces-5]'
        },

        regions : {
           austria : '[data-testid=region-austria]',
           belgium : '[data-testid=region-belgium]',
           bulgaria : '[data-testid=region-bulgaria]',
           czech : '[data-testid=region-czech]',
           denmark : '[data-testid=region-denmark]',
           estonia : '[data-testid=region-estonia]',
           finland : '[data-testid=region-finland]',
           france : '[data-testid=region-france]',
           germany : '[data-testid=region-germany]',
           greece : '[data-testid=region-greece]',
           hungary : '[data-testid=region-hungary]',
           ireland : '[data-testid=region-ireland]',
           italy : '[data-testid=region-italy]',
           latvia : '[data-testid=region-latvia]',
           lithuania : '[data-testid=region-lithuania]',
           luxembourg : '[data-testid=region-luxembourg]',
           netherlands : '[data-testid=region-netherlands]',
           norway : '[data-testid=region-norway]',
           poland : '[data-testid=region-poland]',
           portugal : '[data-testid=region-portugal]',
           romania : '[data-testid=region-romania]',
           slovakia : '[data-testid=region-slovakia]',
           slovenia : '[data-testid=region-slovenia]',
           spain : '[data-testid=region-spain]',
           sweden : '[data-testid=region-sweden]',
           switzerland : '[data-testid=region-switzerland]',
           united_kingdom : '[data-testid=region-united_kingdom]',
           other_region : '[data-testid=region-other]',
        },

        order_status_checker: {
            order_number : 'input[name=orderNumber]',
            order_postcode : 'input[name=postalCode]',
            check_status :  '[data-testid=contact-button]'
        }
    }
}
