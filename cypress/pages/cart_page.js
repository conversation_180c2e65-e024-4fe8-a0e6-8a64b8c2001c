import CommonPage from './common_page'

class CartPage extends CommonPage {

    url = '/cart'
    
    selectors = {
        continue_shopping_btn: '[data-testid="cart-empty-shop"]',
        login_btn: '[data-testid="cart-empty-login"]',
        empty_cart_main_text: '[data-testid="empty-cart-text"]',
        empty_cart_img: '[data-testid="empty-cart-img"]',
        empty_cart_text_if_not_sign_in: '[data-testid="empty-cart-text-not-signed-in"]',

        cart_header: '[data-testid="cart-header"]',
        cart_counter: '[data-testid="cart-counter"]',

        cart_item: '[data-testid="cart-item"]', //applies to samples and furniture
        cart_item_price: '[data-testid="item-price"]',
        cart_price_per_item: '[data-testid="price-per-item"]',
        cart_item_img: '[data-testid="cart-item-image"]',
        cart_furniture_quantity: '[data-testid="quantity"]',
        cart_furniture_quantity_change: '[data-type="stepNumber"] button',
        cart_item_remove_btn: '[data-testid="cart-item-remove"]',
        cart_furniture_s4l: '[data-testid="cart-item-save"]',
        cart_furniture_edit: '[data-testid="cart-item-edit"]',
        cart_item_link: '[data-testid="cart-item-image-link"]',
        cart_item_delivery_time: '[data-testid="cart-item-image-link"] p',
        promo_code_applied: '[data-testid="promo-code-applied"]',
        
        cart_summary_subtotal_price: '[data-testid="cart-summary-subtotal"]',
        cart_summary_delivery: '[data-testid="cart-summary-delivery"]',
        cart_summary_assembly: '[data-testid="cart-summary-assembly"]',
        cart_summary_assembly_checkbox: '[data-testid="cart-summary-assembly"] input[type="checkbox"]',
        cart_summary_assembly_price: '[data-testid="assembly-price"]',
        cart_summary_cta: '[data-testid="cart-cta-button"]',
        cart_total_price: '[data-testid="total-price"]',
        cart_summary_usps: '[data-testid="checkout-usps"]',
        cart_promocode_input: '[data-testid="cart-promocode-input"]',
        cart_promocode_remove: '[data-testid="cart-promocode-remove"]',
        cart_promocode_submit: '[data-testid="cart-submit-promocode"]',
    }

    increaseItemQuantityOfNumber(indexOfItem, num) {
        cy.get(this.selectors.cart_item).eq(indexOfItem).as('chosenItem')
        cy.get('@chosenItem')
            .find(this.selectors.cart_furniture_quantity_change).eq(1).as('increaseButton')
        for(let i = 0; i < num; i++) {
            cy.get('@increaseButton').click()
        }        
    }

    decreaseItemQuantityOfNumber(indexOfItem, num) {
        cy.get(this.selectors.cart_item).eq(indexOfItem).as('chosenItem')
        cy.get('@chosenItem')
            .find(this.selectors.cart_furniture_quantity_change).eq(0).as('decreaseButton')
        for(let i = 0; i < num; i++) {
            cy.get('@decreaseButton').click()
        }
    }

    getQuantityOfItem(indexOfItem) {
        cy.get(this.selectors.cart_item).eq(indexOfItem).as('chosenItem')
        cy.get('@chosenItem')
            .find(this.selectors.cart_furniture_quantity).invoke('text').then(($quantityText) => {
            const quantity = parseInt($quantityText)
            return quantity
        })
    }

    goToCheckoutFromCart() {
        cy.get(this.selectors.cart_summary_cta).click()
    }

    getTopCartItemPriceAsString() {
        return cy.get(this.selectors.cart_item_price, {timeout: 15000}).first().invoke('text')
        .should('not.contain', 'NaN', { timeout:20000 })
    }

    getCartTotalPriceAsString() {
        return cy.get(this.selectors.cart_total_price, {timeout: 15000}).invoke('text')
        .should('not.contain', 'NaN', { timeout:20000 })
    }

    removePromoIfApplied() {
        cy.get('body').then(($body) => {
            const $promo = $body.find(this.selectors.promo_code_applied)
            if ($promo.length && $promo.is(':visible')) {
                cy.get(this.selectors.cart_promocode_remove).click()
            }
            cy.get($promo).should('not.exist')
        })
    }

    isPromoApplied() {
        return cy.get('body').then(($body) => {
            const $promo = $body.find(this.selectors.promo_code_applied)
            return ($promo.length && $promo.is(':visible'))
        })
    }

    addPromoCodeWithEnter(code='') {
        cy.get(this.selectors.cart_promocode_input).clear().type(code + '{enter}', { force: true })
    }

    addPromoCodeWithButton(code='') {
        cy.get(this.selectors.cart_promocode_input).clear().type(code, { force: true })
        cy.get(this.selectors.cart_promocode_submit).click()
    }

    switchAssemblyServiceCheckbox() {
        cy.get(this.selectors.cart_summary_assembly).find('input[type="checkbox"]').click()
    }
}

export default new CartPage()
