class Checkout {

    selectors = {
        delivery_address_section : '[data-testid="delivery-address-section"]',
        first_name : '[id="input_0"]',
        surname : '[id="input_1"]',
        street : '[id="input_3"]',
        postal_code : '[id="input_6"]',
        city : '[id="input_7"]',
        contact_section : '[data-testid="contact-section"]',
        email : '[id="input_20"]',
        phone_number : '[id="input_21"]',
        total_price: '[data-testid=total-price]',
        delivery_modal: '[data-testid="ModalDeliveryNotice"]',
        breadcrumbs: '.breadcrumb-wrapper',
        breadcrumb: '.breadcrumb',
        dropin_container: '[id="dropin-container"]',
        payment_methods: 'li.adyen-checkout__payment-method',
        payment_method_card: 'li.adyen-checkout__payment-method--card',
        payment_method_paypal: 'li.adyen-checkout__payment-method--paypal',
        payment_method_ideal: 'li.adyen-checkout__payment-method--ideal',
        payment_method_klarna_direct: 'li.adyen-checkout__payment-method--directEbanking',
        payment_method_twint: 'li.adyen-checkout__payment-method--twint',
        payment_method_bancontact: 'li.adyen-checkout__payment-method--bcmc',
        submit_card_payment_btn_dropin: ".adyen-checkout__button__content",
        iframe_card_number_dropin: "iframe[title='Iframe for secured card number']", 
        iframe_card_expiry_date_dropin: "iframe[title='Iframe for secured card expiry date']",
        iframe_card_security_code_dropin: "iframe[title='Iframe for secured card security code']",
        card_number_dropin: 'input[data-fieldtype=encryptedCardNumber]',
        card_expiry_date_dropin: 'input[data-fieldtype=encryptedExpiryDate]',
        card_security_code_dropin: 'input[data-fieldtype=encryptedSecurityCode]',
        payment_cta: '[data-testid="payment-button"]',
    }

    fillForm(order_data) {
        cy.get(this.selectors.first_name, { timeout:10000 }).first().type(order_data.first_name)
        cy.get(this.selectors.surname).first().type(order_data.surname)
        cy.get(this.selectors.street).first().type(order_data.street)
        cy.get(this.selectors.postal_code).first().type(order_data.postal_code)
        cy.get(this.selectors.city).first().type(order_data.city)
        cy.get(this.selectors.email).first().type(order_data.email)
        cy.get(this.selectors.phone_number).first().type(order_data.phone_number)
    }

    checkFilledForm(order_data) {
        cy.get(this.selectors.first_name, { timeout: 6000 }).first().should('have.value', order_data.first_name)
        cy.get(this.selectors.surname).first().should('have.value', order_data.surname)
        cy.get(this.selectors.street).first().should('have.value', order_data.street)
        cy.get(this.selectors.postal_code).first().should('have.value', order_data.postal_code)
        cy.get(this.selectors.city).first().should('have.value', order_data.city)
        cy.get(this.selectors.email).first().should('have.value', order_data.email)
        cy.get(this.selectors.phone_number).first().should('have.value', order_data.phone_number)
    }

    getTotalPrice() {
        cy.wait(1000)
        return cy.get(this.selectors.total_price).invoke('text', {timeout:15000})
    }

    acceptDeliveryModal() {
        cy.get(this.selectors.delivery_modal).find('p button').should('be.visible').click()
    }

    acceptDeliveryModalIfVisible() {
        cy.get('body').then(($body) => {
            if ($body.find(this.selectors.delivery_modal).length > 0) {
              this.acceptDeliveryModal()
            }
        })
    }

    checkVisibilityOfPaymentMethods() {
        cy.get(this.selectors.dropin_container, { timeout:5000 })
        .find(this.selectors.payment_methods)
        .should('have.length.at.least', 2)
        .and('be.visible')
    }

    fillCardFormDropIn(card) {
        cy.get("div.adyen-checkout__payment-method__details__content:visible").as('paymentDetails')
        .find("iframe", { timeout:8000 }).eq(0).should('be.visible').then(($iframe) => {
            const $doc = $iframe.contents().find(this.selectors.card_number_dropin, { timeout:10000 })
            cy.wrap($doc).scrollIntoView().type(card.card_number, { force:true })
        })
        cy.get('@paymentDetails')
        .find("iframe", { timeout:8000 }).eq(1).should('be.visible').then(($iframe) => {
            const $doc = $iframe.contents().find(this.selectors.card_expiry_date_dropin, { timeout:10000 })
            cy.wrap($doc).scrollIntoView().type(card.expiry_date, { force:true })
        })
        cy.get('@paymentDetails')
        .find("iframe", { timeout:8000 }).eq(2).should('be.visible').then(($iframe) => {
            const $doc = $iframe.contents().find(this.selectors.card_security_code_dropin, { timeout:10000 })
            cy.wrap($doc).scrollIntoView().type(card.security_code, { force:true })
        })
    }

    payDropin(payment_method='card', card=Cypress.config('testCardValid')) {
        cy.url().then(($url) => {
            cy.wrap($url).as('currentUrl')
        })
        cy.get('@currentUrl').then((url) => {
            const urlObject = new URL(url)
            const baseUrl = urlObject.origin
            cy.wait(2000)

            switch(payment_method){
                case 'card':
                    cy.get(this.selectors.payment_method_card, { timeout:20000 }).click()
                    this.fillCardFormDropIn(card)
                    cy.wait(2000)
                    cy.get(`${this.selectors.payment_cta}:visible`, { timeout:20000 }).click()
                    break
                case 'paypal':
                    cy.get(this.selectors.payment_method_paypal, { timeout:10000 }).click()
                    cy.get('iframe[title=PayPal]:visible').then(($iframe) => {
                        const $button = $iframe.contents().find('div[role="link"]')
                        cy.wrap($button).click()
                    })
                    cy.get('iframe.paypal-checkout-sandbox-iframe', { timeout:20000 }).should('be.visible')
                    this.visitFakeConfirmationPage(baseUrl)
                    break
                case 'ideal':
                    cy.get(this.selectors.payment_method_ideal, { timeout:20000 }).click()
                    cy.get('ul.adyen-checkout__dropdown__list').find('li').eq(1).click()
                    cy.get('div.adyen-checkout__issuer-list').find('button.adyen-checkout__button').click()
                    cy.get('.btnLink', { timeout:10000 }).click()
                    break
                case 'sofort':
                    cy.intercept('**/checkoutPaymentRedirect**').as('checkoutRedirectRequest')
                    cy.get(this.selectors.payment_method_klarna_direct, { timeout:20000 }).click()
                    cy.get(this.selectors.payment_method_klarna_direct).find('button.adyen-checkout__button').should('be.visible')
                    this.visitFakeConfirmationPage(baseUrl)
                    break
                case 'twint':
                    cy.get(this.selectors.payment_method_twint, { timeout:20000 }).click()
                    cy.get(this.selectors.payment_method_twint).find('button.adyen-checkout__button').trigger('click')
                    cy.get('button[value=authorised]', { timeout:20000 }).click()
                    break
                default:
                    cy.log(`"${payment_method}" is not supported by payment function "payDropin()"`)
            }
        })
    }

    visitFakeConfirmationPage(url) {
        cy.visit(url + '/confirmation_fake')
    }
}

export default new Checkout()
