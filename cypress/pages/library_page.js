import CommonPage from "./common_page"

class LibraryPage extends CommonPage {

    selectors = {
        library_header: '[data-testid=library-header]',
        library_header_with_item_count: '[data-testid=library-header-with-items-count]',
        wishlist_item: '[data-testid=wishlist-item]',
        wishlist_item_a2c: '[data-testid=wishlist-add-to-cart]',
        wishlist_item_price: '[data-testid=wishlist-price]',
        wishlist_item_bin_button: '[data-testid=wishlist-bin-button]',
    }

    addToCartFromWishlist() {
        cy.get(`${this.selectors.wishlist_item_a2c}:visible`, { timeout: 10000 }).trigger('mouseover', { timeout: 20000 }).click();
        cy.wait(2000)
    }

    removeLastItemfromWishlist() {
        cy.get(this.selectors.wishlist_item_bin_button).first().click()
        cy.get('button.ty-btn-outlined:visible').eq(1).click() // TODO add testid
    }
}

export default new LibraryPage()
