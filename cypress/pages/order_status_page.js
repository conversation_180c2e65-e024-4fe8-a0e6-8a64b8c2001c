import CommonPage from './common_page'

class OrderStatusPage extends CommonPage {

    url = '/contact/?topic=order_status'
    
    selectors = {
        status: 'span.bold-20.breathing-opacity',
    }

    selectProductFromGrid(item_index=0) {
            cy.get(this.selectors.product_list_item, { timeout:10000 }).eq(item_index).find('img')
                .first().click()
        }
}

export default new OrderStatusPage()
