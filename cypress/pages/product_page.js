import CommonPage from './common_page'
import { extractPriceValue } from "../test_modules/common_functions"

class ProductPage extends CommonPage {

    url = '/furniture-c/'
    selectors = {
        cta_add_to_cart: '[data-testid="a2c-button"]',
        configurator_price: '[data-testid="configurator-price"]',
        configurator_loader: '[data-testid="configurator-loader"]',
        configurator: '[data-testid="configurator"]',
        s4l_cta: '[data-testid="s4l-button"]',
        s4l_header: '[data-testid="s4l-header"]',
        s4l_subtext: '[data-testid="s4l-subtext"]',
        s4l_email_input: '[data-testid="s4l-email-input"]',
        s4l_permission_checkbox: '[data-testid="s4l-permission-checkbox"]',
        s4l_submit_button: '[data-testid="s4l-submit-button"]',
        s4l_clipboard_input: '[data-testid="s4l-clipboard-input"]',
        s4l_clipboard_button: '[data-testid="s4l-clipboard-button"]',
        modal_view_cart_cta: '[data-testid="view-cart"]',
        modal_go_to_checkout_cta: '[data-testid="checkout"]',
    }

    waitForConfiguratorToBeReady() {
        cy.get(this.selectors.configurator_loader, { timeout:20000 }).should('not.exist')
    } // TODO: not working now - configurator loader is permanently existing in DOM

    waitForConfiguratorToBeReadyAndAddToCart() {
        let retries = 5
        let itemsInCart = 0
        const retryUntilVisible = () => {
            return cy.get(this.selectors.cta_add_to_cart, { timeout: 10000 })
                .trigger('mouseover', { timeout: 20000 })
                .then(($element) => {
                    if ($element.is(':visible')) {
                        return $element
                    } else {
                        throw new Error('Element not visible')
                    }
                })
        }
        const retryClick = (expected_item_count, region) => {
            return new Cypress.Promise((resolve, reject) => {
                if (retries > 0) {
                    retries--
                    cy.get(`${this.selectors.cta_add_to_cart}:visible`, { timeout: 10000 }).trigger('mouseover', { timeout: 20000 }).click()
                    this.checkUserStatus(expected_item_count, region)
                        .then(resolve)
                        .catch(reject)
                } else {
                    reject(new Error('Max retries for adding item to cart exceeded'))
                }
            })
        }
        const addItemToCart = () => {
            cy.get(`${this.selectors.cta_add_to_cart}:visible`, { timeout: 10000 }).trigger('mouseover', { timeout: 20000 }).click()
            cy.wait(2000)
            itemsInCart++
        }
        const checkCartContents = () => {
            cy.wait(500)
            return cy.request({
                method: 'GET',
                url: '/api/v1/user-global/',
                log: true
            }).then((response) => {
                if (response && response.status === 200 && response.body && response.body.shelfItemsCount === 0) {
                    return retryClick(1, 'desired_region')
                } else {
                    return response
                }
            }, (err) => {
                return retryClick(1, 'desired_region')
            })
        }
        retryUntilVisible().then(($element) => {
            if ($element) {
                cy.wait(500)
                addItemToCart()
                itemsInCart++
                checkCartContents()
            }
        })
    }

    addToCart() {
        cy.intercept('POST', `/api/v1/gallery/*/add_to_cart/`).as('addedToCart')
        cy.get(`${this.selectors.cta_add_to_cart}:visible`, { timeout:10000 })
            .trigger('mouseover', { force:true }).click({ timeout:10000 })
        cy.wait('@addedToCart', { timeout:10000 }).its('response.statusCode').should('eq', 201)
    }

    addToCartOnMobile() {
        this.addToCart()
    }

    saveForLater() {
        cy.intercept('POST', 'api/v1/gallery/*/add_to_wishlist/?mm=true').as('addedToWishlist')
        cy.get(this.selectors.s4l_cta, { timeout: 10000 })
            .click({ timeout: 10000 })
        cy.wait('@addedToWishlist', { timeout:10000 }).its('response.statusCode').should('eq', 201)
    }

    saveForLaterGuest(mail) {
        cy.get(this.selectors.s4l_cta, { timeout:20000 }).click({ timeout:20000 })
        cy.get(this.selectors.s4l_email_input, { timeout:20000 })
            .clear().type(mail, { timeout:15000 }).invoke('val').then((typedText) => {
                expect(typedText).to.equal(mail)
            })
        cy.get(this.selectors.s4l_permission_checkbox, { timeout: 20000 }).check()
        cy.get(this.selectors.s4l_permission_checkbox).uncheck()
        cy.get(this.selectors.s4l_submit_button, { timeout: 20000 }).click()
    }
    
    getPricingv3AsString() {
        cy.get(this.selectors.configurator_price).invoke('text').as('price')
        cy.waitUntil(() => 
            cy.get('@price').then((priceText) => {
                const price = extractPriceValue(priceText)
                return price > 0
            }),
            {
                timeout: 10000,
                interval: 500,
            }
        )
        return cy.get('@price') 
    }

    closeModalAfterA2C() {
        cy.get('svg.transition-rotate:visible').click()
    }

    acceptModalAndGoToCheckout() {
        cy.get(this.selectors.modal_go_to_checkout_cta).click()
    }

    acceptModalAndGoToCart() {
        cy.get(this.selectors.modal_view_cart_cta, { timeout: 10000 }).click()
    }
}

export default new ProductPage()
