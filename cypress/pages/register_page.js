import CommonPage from "./common_page"

class RegisterPage extends CommonPage {

  url = '/register'

  selectors = {
    email_input: '[data-testid="input-login-email"]',
    password_input: '[data-testid="input-login-password"]',
    terms_checkbox: '[type=checkbox]',
    create_account_button: '[data-testid=button-login]',
  }

  fillRegistrationData(email, password) {
    this.fillLogin(email)
    this.fillPassword(password)
  }

  fillLogin(email) {
    cy.get(this.selectors.email_input).type(email)
  }

  fillPassword(password) {
    cy.get(this.selectors.password_input).type(password)
  }

  submitRegistration() {
    cy.get(this.selectors.create_account_button).click({timeout:20000})
  }

  acceptNewsletter() {
    cy.get(this.selectors.terms_checkbox).filter(':visible').click()
  }
}

export default new RegisterPage()
