import 'cypress-wait-until';

Cypress.Commands.add('agreeCookies', () => {
  cy.wait(1000);
  cy.get('[data-testid=cookies-agree]', { timeout:10000 }).as('cookies').trigger('mouseover')
  cy.wait(1000);
  cy.get('@cookies').click({force:true});
});

Cypress.Commands.add('destroyWistiaRequests', () => {
  cy.intercept({
    method: 'GET',
    url: /^https:\/\/.*wistia.*/,
  }, req => {
    req.destroy();
  }).as('wistiaRequestRemoval');
});

Cypress.Commands.add('typeIntoIframe', (iframe, selector, text) => {
  cy.get(iframe).then($iframe => {
    const $doc = $iframe.contents().find(selector, { timeout: 10000 });
    cy.wrap($doc).scrollIntoView().type(text, { force: true });
  });
});

Cypress.Commands.add('loginWithApi', (user, password) => {
  cy.request('POST', '/api/v2/auth/login/', {
    username: user,
    password: password
  })
  .then((response) => {
    expect(response.status).to.equal(200);
  })
  cy.reload();
});

Cypress.Commands.add('registerWithApi', (email, password) => {
  cy.request('POST', '/api/v2/auth/register/', {
    username: email,
    password: password,
    email: email,
    terms: true,
  })
  .then((response) => {
    expect(response.status).to.equal(201);
  })
  cy.reload();
});
