
// Import commands.js using ES2015 syntax:
import './commands';
import '@percy/cypress';
import 'cypress-file-upload';

beforeEach(() => {
  cy.destroyWistiaRequests()
  Cypress.Cookies.debug(true)
  cy.setCookie('original_col_configurator_v2', 'nok')
  cy.setCookie('original_row_configurator', 'nok')
  cy.setCookie('new_checkout2025', 'ok')
  cy.setCookie('test_main_nav_2025', 'nok')
  cy.setCookie('new_plp', 'ok')
  cy.setCookie('cart_modal_cta', 'nok')
  cy.reload()
})

Cypress.on('uncaught:exception', (err, runnable) => {
  // returning false here prevents <PERSON><PERSON> from failing the test
  return false
});

// Hide fetch/XHR requests (source: https://stackoverflow.com/questions/71357705/hide-xhr-calls-on-cypress-test-runner)
const app = window.top;
if (!app.document.head.querySelector('[data-hide-command-log-request]')) {
  const style = app.document.createElement('style');
  style.innerHTML =
    '.command-name-request, .command-name-xhr { display: none }';
  style.setAttribute('data-hide-command-log-request', '');

  app.document.head.appendChild(style);
}
