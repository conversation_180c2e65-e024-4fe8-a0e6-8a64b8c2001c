import cart from "../pages/cart_page"
import login_page from "../pages/login_page"
import plp from "../pages/product_listing_page"
import { addToCart, extractPriceValue } from "./common_functions"

const url = Cypress.config('baseUrl') + '/en-de' + cart.url

function testEmptyCartIfNotSignIn(device) {
    it('check elements of an empty cart', () => {
        cy.viewport(device)
        cy.visit(url)
        cy.agreeCookies()
        checkIfEmptyCartElementsVisible()
        cy.get(cart.selectors.empty_cart_text_if_not_sign_in).should('be.visible')
    })
}

function testNotEmptyCartOnDesktop(device) {
    it('check elements of NOT empty cart', () => {
        cy.viewport(device)
        cy.visit(url)
        cy.agreeCookies()
        addToCart(['OriginalBookcase', 'ToneWardrobe', 'EdgeSample'])
        checkCartItems(3)
        checkIfFurnitureVisibleInCart(2)
        checkIfPricesAreSummedCorrectly()
        cy.get(cart.selectors.cart_summary_subtotal_price).should('be.visible')
        cy.get(cart.selectors.cart_summary_delivery).should('be.visible')
        cy.get(cart.selectors.cart_summary_assembly).should('be.visible')
        cy.get(cart.selectors.cart_summary_assembly_checkbox).should('have.value', 'false').and('be.visible')
        cy.get(cart.selectors.cart_summary_usps).filter(':visible').find('p[data-testid]').should('have.length', 4)
        cy.get(cart.selectors.cart_summary_cta).should('be.visible').click()
        cy.url().should('contain', login_page.url)
    })
}

function testNotEmptyCartOnMobile(device) {
    it('check elements of NOT empty cart', () => {
        cy.viewport(device)
        cy.visit(url)
        cy.agreeCookies()
        addToCart(['OriginalBookcase', 'ToneWardrobe', 'EdgeSample'])
        checkCartItems(3)
        checkIfFurnitureVisibleInCart(2)
        checkIfPricesAreSummedCorrectly()
        cy.get(cart.selectors.cart_item_remove_btn).first().scrollIntoView() // just to ensure right view for further checks on mobile
        cy.get(cart.selectors.cart_summary_subtotal_price).should('be.visible')
        cy.get(cart.selectors.cart_summary_delivery).should('be.visible')
        cy.get(cart.selectors.cart_summary_assembly).should('be.visible')
        cy.get(cart.selectors.cart_summary_assembly_checkbox).should('have.value', 'false').and('be.visible')
        cy.get(cart.selectors.cart_summary_usps).filter(':visible').find('p[data-testid]').should('have.length', 4)
        cy.get(cart.selectors.cart_summary_cta).should('be.visible').click()
        cy.url().should('contain', login_page.url)
    })
}

function testQuantity(device) {
    it('test increasing and decreasing quantity', () => {
        cy.viewport(device)
        let quantity = 0
        cy.visit(url)
        cy.agreeCookies()
        addToCart(['OriginalBookcase', 'ToneWardrobe'])
        checkIfPricesAreSummedCorrectly()
        // NOTE: order of items in the cart is from the bottom to the top. So index=0 points the lowest item (last one added to the cart)
        cy.get(cart.selectors.cart_item).eq(0)
        .find(cart.selectors.cart_furniture_quantity).invoke('text').then(($quantityText) => {
            quantity = parseInt($quantityText)
            expect(quantity).to.eql(1)
            cart.increaseItemQuantityOfNumber(0, 4)
            cart.decreaseItemQuantityOfNumber(0, 2)
            cy.wait(2000)
            cy.get(cart.selectors.cart_item).eq(0)
            .find(cart.selectors.cart_furniture_quantity).invoke('text').then(($quantityText) => {
                quantity = parseInt($quantityText)
                expect(quantity).to.eql(3)
            })
        })
        checkIfPricesAreSummedCorrectly()
    })
}

function checkIfEmptyCartElementsVisible() {
    cy.get(cart.selectors.empty_cart_img).should('be.visible')
    cy.get(cart.selectors.empty_cart_main_text).should('have.text', 'Your cart is empty')
        .and('be.visible')
    cy.get(cart.selectors.login_btn).should('be.visible')
        .and('have.attr', 'href').and('include', login_page.url)
    cy.get(cart.selectors.continue_shopping_btn).should('be.visible')
        .and('have.attr', 'href').and('include', plp.url)
}

function checkCartItems(itemsCount) {
    cy.get(cart.selectors.cart_counter).should('contain', itemsCount)
    cy.get(cart.selectors.cart_item).should('have.length', itemsCount)
    cy.get(cart.selectors.cart_item_img).should('be.visible').should('have.length', itemsCount)
    cy.get(cart.selectors.cart_item_remove_btn).should('be.visible').should('have.length', itemsCount)
    cy.get(cart.selectors.cart_item_delivery_time).should('be.visible').should('have.length', itemsCount).as('delivery_times')
    cy.get('@delivery_times').then(($elements) => {
        const texts = $elements.toArray().map(el => el.innerText)
        const uniqueText = new Set(texts)
        expect(uniqueText.size).to.equal(2)
    })
}

function checkIfFurnitureVisibleInCart(itemsCount) {
    cy.get(cart.selectors.cart_furniture_quantity).should('be.visible').should('have.length', itemsCount)
    cy.get(cart.selectors.cart_furniture_s4l).should('be.visible').should('have.length', itemsCount)
    cy.get(cart.selectors.cart_furniture_edit).should('be.visible').should('have.length', itemsCount)
}

function checkIfPricesAreSummedCorrectly() {
    let totalCalculated = 0
    let price = 0
    cy.get(cart.selectors.cart_item).each(($item) => {
        let quantity = 1
        let pricePerItem
        const quantityElement = $item.find(cart.selectors.cart_furniture_quantity)
        if (quantityElement.length > 0) {
            cy.wrap($item).find(cart.selectors.cart_furniture_quantity).invoke('text').then(($quantityText) => {
                quantity = parseInt($quantityText, 10)
                cy.wrap($item).find(cart.selectors.cart_item_price).invoke('text').then(($priceText) => {
                    price = extractPriceValue($priceText)
                    pricePerItem = price
                    if (quantity > 1) {
                        cy.wrap($item).find(cart.selectors.cart_price_per_item).invoke('text').then(($pricePerItem) => {
                            pricePerItem = extractPriceValue($pricePerItem)
                            let calcPrice = quantity * pricePerItem
                            expect(price).to.be.within(calcPrice - 1, calcPrice + 1)
                        })
                    }
                    totalCalculated += price
                })
            })
        } else {
            cy.wrap($item).find(cart.selectors.cart_item_price).invoke('text').then(($priceText) => {
                price = extractPriceValue($priceText)
                totalCalculated += price
            })
        }
    })
    .then(() => {
        cy.get(cart.selectors.cart_total_price).invoke('text').then((totalPriceText) => {
            const totalPrice = extractPriceValue(totalPriceText)
            expect(totalPrice).to.be.within(totalCalculated - 1, totalCalculated + 1)
        })
    })
}

module.exports = {
    testEmptyCartIfNotSignIn,
    testNotEmptyCartOnDesktop,
    testNotEmptyCartOnMobile,
    testQuantity
}
