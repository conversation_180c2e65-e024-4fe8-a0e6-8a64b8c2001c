import hp from "../pages/home_page"

const url = Cypress.config('baseUrl')

function prepareTest(device) {
    cy.intercept('POST', '/api/v1/regions/change_region/').as('changeRegion')
    cy.viewport(device)
    cy.visit(url)
    cy.agreeCookies()
}

function testChangingRegionAndLanguageOnMobile(region, exp_lang, change_lang) {
    it(`change region on ${region}${change_lang ? ` and language on ${change_lang}` : ''}`, () => {
        hp.changeRegionOnMobile(region)
        cy.wait('@changeRegion').then((interception) => {
            expect(interception.request.body.region_name).to.equal(region)
            expect(interception.response.statusCode).to.eq(200)
            if (exp_lang && exp_lang !== 'en') {
                cy.url({ timeout:5000 }).should(($url) => {
                    expect($url).to.contain(`${exp_lang}-`)
                })
            }
        })
        if (change_lang) {
            cy.get('[data-testid="mega-menu"]').should('not.be.visible')
            cy.wait(3000)
            hp.changeLanguageOnMobile(change_lang)
            cy.url({ timeout:5000 }).should(($url) => {
                expect($url).to.contain(`/${change_lang}`)
            })
        }
    })
}

function testChangingRegionAndLanguageOnDesktop(region, exp_lang, change_lang) {
    it(`change region on ${region}${change_lang ? ` and language on ${change_lang}` : ''}`, () => {
        hp.changeRegion(region)
        cy.wait('@changeRegion').then((interception) => {
            expect(interception.request.body.region_name).to.equal(region)
            expect(interception.response.statusCode).to.eq(200)
            if (exp_lang) {
                cy.url({ timeout:5000 }).should(($url) => {
                    expect($url).to.contain(`/${exp_lang}`)
                })
            }
        })
        if (change_lang) {
            cy.get('[data-testid="lang-menu"]').should('not.be.visible')
            cy.wait(3000)
            hp.changeLanguage(change_lang)
            cy.url({ timeout:5000 }).should(($url) => {
                expect($url).to.contain(`/${change_lang}`)
            })
        }
    })
}

module.exports = {
    prepareTest,
    testChangingRegionAndLanguageOnMobile,
    testChangingRegionAndLanguageOnDesktop,
}
