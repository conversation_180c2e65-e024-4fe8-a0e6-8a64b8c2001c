import pdp from "../pages/product_page"
import checkout from "../pages/checkout_page"
import sample_page from "../pages/sample_page"
import cart from "../pages/cart_page"
import login_page from "../pages/login_page"
import { 
    checkIfPricesAreEqual,
    extractCurrency,
    extractPriceValue
} from "../test_modules/common_functions"

const sampleIndex = 17

function testPriceForFurnitureAndSample(url) {
    it(`Price check on "${url}"`, () => {
        let globalPriceValue = 0
        cy.visit(url)
        cy.agreeCookies()
        cy.wait(500)
        pdp.getPricingv3AsString().then((configuratorItemPrice) => {
            const configuratorItemPriceValue = extractPriceValue(configuratorItemPrice)
            const configuratorItemPriceCurrency = extractCurrency(configuratorItemPrice)
            pdp.addToCart()
            pdp.acceptModalAndGoToCart()
            cart.getTopCartItemPriceAsString().then((cartItemPrice) => {
                checkIfPricesAreEqual(configuratorItemPrice, cartItemPrice)
            })
            globalPriceValue += configuratorItemPriceValue
            cy.wait(2000)
            cart.openMegaMenu()
            cart.navigateToSamples()
            sample_page.getSampleSetPriceAsString(sampleIndex).then((sampleSetPrice) => {
                const sampleSetPriceValue = extractPriceValue(sampleSetPrice)
                cy.wait(3000)
                sample_page.addToCartSampleWithIndex(17)
                sample_page.acceptModalAndGoToCart()
                cart.getTopCartItemPriceAsString().then((cartItemPrice) => {
                    checkIfPricesAreEqual(cartItemPrice, sampleSetPrice)
                })
                globalPriceValue += sampleSetPriceValue
            })
            cart.getCartTotalPriceAsString().then((cartTotalPrice) => {
                expect(extractPriceValue(cartTotalPrice)).to.eql(globalPriceValue)
                expect(extractCurrency(cartTotalPrice)).to.eql(configuratorItemPriceCurrency)
            })
            cart.goToCheckoutFromCart()
            login_page.continueAsGuest()
            cy.get(checkout.selectors.total_price).invoke('text').then((totalPrice) => {
                expect(extractPriceValue(totalPrice)).to.eql(globalPriceValue)
                expect(extractCurrency(totalPrice)).to.eql(configuratorItemPriceCurrency)
            })
        })
    })
}

module.exports = {
    testPriceForFurnitureAndSample
}
