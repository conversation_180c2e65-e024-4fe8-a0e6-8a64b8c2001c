import {
    verifyFooterDesktop,
    verifyFooterMobile,
    verifyNewsletter
 } from './common_functions'

function verifyHeader() {
    cy.scrollTo('top')
    cy.get('[data-section="header"]').should('be.visible')
}

function verifyHero() {
    cy.get('[data-testid="hero-video"]').as('heroVideo').should('be.visible')
    cy.get('@heroVideo').find('h1').should('be.visible')
    cy.get('@heroVideo').find('h2').should('be.visible')
    cy.get('@heroVideo').find('a').should('be.visible')
}

function verifyButtonCategories() {
    cy.get('[data-testid="button-categories"]').as('buttonCategories').should('be.visible')
    cy.get('@buttonCategories').find('[data-testid="button-categories-headline"]').should('be.visible')
    cy.get('@buttonCategories').find('[data-testid="button-categories-title"]').should('be.visible')
    cy.get('@buttonCategories').find('[data-testid="button-categories-button"]').should('be.visible').and('have.length.gte', 8)
}

function verifyBrandVideo() {
    cy.get('[data-testid="brand-video"]').as('brandVideo').should('be.visible')
}

function verifyChooseLine() {
    cy.get('[data-testid="choose-line"]').as('chooseLine').should('be.visible')
    cy.get('@chooseLine').find('[data-testid="carousel-tagline"]').should('be.visible')
    cy.get('@chooseLine').find('[data-testid="carousel-headline"]').should('be.visible')
    cy.get('@chooseLine').find('a[href]').should('be.visible')
    cy.get('@chooseLine').find('[data-testid="swiper-slide"]')
    cy.get('@chooseLine').find('[data-testid="swiper-slide-picture"]').should('be.visible').and('have.length', 4)
    cy.get('@chooseLine').find('[data-testid="swiper-slide-text"]').should('be.visible').and('have.length', 4)
    cy.get('@chooseLine').find('fieldset').should('be.visible').and('have.length', 4) 
}

function verifyBrandImage() {
    cy.get('[data-testid="brand-image"]').as('brandImage').should('be.visible')
    cy.get('@brandImage').find('picture').should('be.visible')
    cy.get('@brandImage').find('h1').should('be.visible')
    cy.get('@brandImage').find('p').should('be.visible')
    cy.get('@brandImage').find('a')
}

function verifyCategoriesCarouselOnDesktop() {
    cy.get('[data-section="categories-carousel"]').as('carousel').scrollIntoView().should('be.visible')
    cy.get('@carousel').find('[data-testid="carousel-headline"]').should('be.visible')
    cy.get('@carousel').find('[data-testid="carousel-subheadline"]').should('be.visible')
    cy.get('@carousel').find('.swiper-button-prev', { timeout: 6000 }).should('exist').and('be.disabled')
    cy.get('@carousel').find('.swiper-button-next', { timeout: 6000 }).should('exist').and('not.be.disabled')
    cy.get('@carousel').find('.swiper-scrollbar').should('be.visible')
    cy.get('@carousel').find('.swiper').as('swiper').should('be.visible')
    cy.get('@swiper').find('[data-testid="swiper-slide"]').as('slide').should('have.length', 6)
    cy.get('@slide').find('[data-testid="swiper-slide-link"] [data-testid="swiper-slide-text"]')
    .should('have.length', 6).and('be.visible')
    cy.get('@slide').find('[data-testid="right-arrow-icon"]').should('be.visible') 
}

function verifyCategoriesCarouselOnMobile() {
    cy.get('[data-section="categories-carousel"]').as('carousel').scrollIntoView().should('be.visible')
    cy.get('@carousel').find('[data-testid="carousel-headline"]').should('be.visible')
    cy.get('@carousel').find('[data-testid="carousel-subheadline"]').should('be.visible')
    cy.get('[data-testid="categories-grid"]').as('categoriesGrid')
    cy.get('@categoriesGrid').find('[data-testid="swiper-slide-link"]').should('have.length', 6)
    cy.get('@categoriesGrid').find('[data-testid="swiper-slide-picture"]').should('have.length', 6)
    cy.get('@categoriesGrid').find('[data-testid="swiper-slide-text"]').should('have.length', 6)
}

function verifyPromoBanners() {
    cy.get('[data-section="promo-banners"]').as('promoBanners').scrollIntoView().should('be.visible')
    cy.get('@promoBanners').find('[data-testid="heading"]').should('be.visible')
    cy.get('@promoBanners').find('[data-testid="subheading"]').should('be.visible')
    cy.get('@promoBanners').find('[data-testid="promo-banner-link"]').as('bannerLink').should('have.length.gte', 2)
    cy.get('@bannerLink').find('[data-testid="promo-banner-picture"]').should('be.visible').and('have.length.gte', 2)
    cy.get('@promoBanners').find('[data-testid="promo-banner-h1"]').should('be.visible').and('have.length.gte', 2)
    cy.get('@promoBanners').find('[data-testid="promo-banner-h2"]').should('be.visible').and('have.length.gte', 2)
}

function verifyConfigurabilityOnDesktop() {
    cy.get('[data-section="section-configurability"]').as('configurability').scrollIntoView().should('be.visible')
    cy.get('@configurability').find('[data-testid="heading"]').should('be.visible')
    cy.get('@configurability').find('[data-testid="subheading"]').should('be.visible')
    cy.get('@configurability').find('[data-testid="configurability-link"]')
    cy.get('@configurability').find('[data-testid="configurability-video"]').should('be.visible')
}

function verifyConfigurabilityOnMobile() {
    cy.get('[data-section="section-configurability"]').as('configurability').scrollIntoView().should('be.visible')
    cy.get('@configurability').find('[data-testid="heading"]').should('be.visible')
    cy.get('@configurability').find('[data-testid="subheading"]').should('be.visible')
    cy.get('@configurability').find('[data-testid="configurability-link-mobile"]')
    cy.get('@configurability').find('[data-testid="configurability-video"]').should('be.visible')
}

function verifyMinigridOnDesktop() {
    cy.get('[data-testid="hp-minigrid"]').as('minigrid').scrollIntoView().should('be.visible')
    cy.get('@minigrid').find('.swiper-button-prev', { timeout: 6000 }).should('exist').and('be.disabled')
    cy.get('@minigrid').find('.swiper-button-next', { timeout: 6000 }).should('exist').and('not.be.disabled')
    cy.get('@minigrid').find('[data-testid="product-card-link"]').as('card').should('have.length.gte', 4)
    cy.get('@card').eq(0).should('be.visible')
    cy.get('@card').eq(3).should('be.visible')
    cy.get('@minigrid').find('.swiper-scrollbar').should('be.visible')
}

function verifyMinigridOnMobile() {
    cy.get('[data-testid="hp-minigrid"]').as('minigrid')
    .scrollIntoView().should('be.visible')
    cy.get('@minigrid').find('[data-testid="product-card-link"]').as('card').should('have.length.gte', 4)
    cy.get('@card').eq(0).should('be.visible')
    cy.get('@minigrid').find('.swiper-scrollbar').should('be.visible')
}

function verifyBrandHighlights() {
    cy.get('[data-testid="hp-brand-highlights"]').as('brandHighlights').scrollIntoView().should('be.visible')
    cy.get('@brandHighlights').find('[data-testid="hp-brand-highlights-subtitle"]').should('be.visible')
    cy.get('@brandHighlights').find('[data-testid="hp-brand-highlights-title"]').should('be.visible')
    cy.get('@brandHighlights').find('[data-testid="hp-brand-highlights-tiles"]').as('tiles').should('be.visible')
    cy.get('@tiles').find('[data-testid="hp-brand-highlights-tile1"]').should('be.visible')
    cy.get('@tiles').find('[data-testid="hp-brand-highlights-tile2"]').should('be.visible')
    cy.get('@tiles').find('[data-testid="hp-brand-highlights-tile3"]')
    cy.get('@tiles').find('[data-testid="hp-brand-highlights-tile4"]')
}

function verifyCreators() {
    cy.get('[data-testid="hp-creators"]').as('creators').scrollIntoView().should('be.visible')
    cy.get('@creators').find('[data-testid="carousel-tagline"]').should('be.visible')
    cy.get('@creators').find('[data-testid="carousel-headline"]').should('be.visible')
    cy.get('@creators').find('a[href]').first().then($el => {
        expect($el.attr('href')).to.include('/creators')
    })
    cy.get('@creators').find('[data-testid="swiper-slide"]').as('slide').should('have.length.gte', 6).and('be.visible')
    cy.get('@slide').find('a[href]').should('have.length.gte', 6).and('be.visible')
    cy.get('@slide').find('[data-testid="swiper-slide-picture"]').should('have.length.gte', 6).and('be.visible')
    cy.get('@slide').find('[data-testid="swiper-slide-text"]').should('have.length.gte', 6).and('be.visible')
}

function verifySustainability() {
    cy.get('[data-testid="hp-sustainability"]').as('sustainability').scrollIntoView().should('be.visible')
    cy.get('@sustainability').find('h1').should('be.visible')
    cy.get('@sustainability').find('p').should('be.visible')
    cy.get('@sustainability').find('a[href="/Tylko_ESG_report_digital_EN_19.pdf"]').should('be.visible')
    cy.get('@sustainability').find('[data-testid="half-carousel-swiper-slide"]')
        .should('be.visible').and('have.length', 4)
    cy.get('@sustainability').find('[data-testid="half-carousel-swiper-slide"]').first().should('be.visible')
}

function verifyLifeOfTylko() {
    cy.get('[data-testid="hp-life-of-tylko"]').as('lifeOfTylko').scrollIntoView().should('be.visible')
    cy.get('@lifeOfTylko').find('[data-testid="carousel-tagline"]').should('be.visible')
    cy.get('@lifeOfTylko').find('[data-testid="carousel-headline"]').should('be.visible')
    cy.get('@lifeOfTylko').find('[data-testid="swiper-slide"]').should('have.length', 4)
    cy.get('@lifeOfTylko').find('[data-testid="swiper-slide"]').first().should('be.visible')
}

function verifyAboutTylkoOnDesktop() {
    cy.get('[data-testid="instagrid"]').as('instagrid').scrollIntoView()
    cy.get('@instagrid').find('[data-testid="instagrid-reviews"]').should('be.visible')
    cy.get('@instagrid').find('[data-testid="instagrid-headline"]').should('be.visible')
    cy.get('@instagrid').find('[data-testid="instagrid-link"]').should('be.visible')
    cy.get('@instagrid').find('[data-testid="instagrid-grid"]').as('grid').should('be.visible')
    cy.get('@grid').find('[data-testid="instagrid-tile"]').as('tiles').should('have.length', 9)
    cy.get('@tiles').first().should('be.visible')
    cy.get('@tiles').last().should('be.visible')
}

function verifyAboutTylkoOnMobile() {
    cy.get('[data-testid="instagrid"]').as('instagrid').scrollIntoView()
    cy.get('@instagrid').find('[data-testid="instagrid-reviews"]').should('be.visible')
    cy.get('@instagrid').find('[data-testid="instagrid-headline"]').should('be.visible')
    cy.get('@instagrid').find('[data-testid="instagrid-link-mobile"]').should('be.visible')
    cy.get('@instagrid').find('[data-testid="instagrid-grid"]').as('grid').should('be.visible')
    cy.get('@grid').find('[data-testid="instagrid-tile"]').as('tiles').should('have.length', 9)
    cy.get('@tiles').first().should('be.visible')
    cy.get('@tiles').last().should('be.visible')
}

function verifyAdditionalLinkCarousel() {
    cy.get('[data-section="additional-links-carousel"]').as('aboutTylko').scrollIntoView().should('be.visible')
    cy.get('@aboutTylko').find('[data-testid="swiper-slide"]').as('swiperSlide').should('have.length.gte', 3).and('be.visible')
    cy.get('@swiperSlide').find('[data-testid="swiper-slide-link"]').should('have.length.gte', 3).and('be.visible')
    cy.get('@swiperSlide').find('[data-testid="swiper-slide-picture"]').should('have.length.gte', 3).and('be.visible')
    cy.get('@swiperSlide').find('[data-testid="swiper-slide-text"]').should('have.length.gte', 3).and('be.visible')
    cy.get('@swiperSlide').find('svg').should('have.length.gte', 3).and('be.visible')
}

function verifySEO() {
    cy.get('.seo-container').scrollIntoView().should('be.visible')
}

function testHpSectionsOnDesktop() {
    verifyHeader()
    verifyHero()
    verifyButtonCategories()
    verifyBrandVideo()
    verifyChooseLine()
    verifyBrandImage()
    verifyMinigridOnDesktop()
    verifyBrandHighlights()
    verifyCreators()
    verifySustainability()
    verifyLifeOfTylko()
    verifyNewsletter()
    verifyFooterDesktop()
}

function testHpSectionsOnMobile() {
    verifyHeader()
    verifyHero()
    verifyButtonCategories()
    verifyBrandVideo()
    verifyChooseLine()
    verifyBrandImage()
    verifyMinigridOnMobile()
    verifyBrandHighlights()
    verifyCreators()
    verifySustainability()
    verifyLifeOfTylko()
    verifyNewsletter()
    verifyFooterMobile()
}

module.exports = {
    testHpSectionsOnDesktop,
    testHpSectionsOnMobile
}
