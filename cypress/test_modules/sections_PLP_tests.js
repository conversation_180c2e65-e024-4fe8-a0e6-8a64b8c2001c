import { 
    verifyFooterDesktop,
    verifyFooterMobile
} from './common_functions'

const itemsOnGrid = 32
const deviceTypes = {
    desktop: 'desktop',
    mobile: 'mobile'
}

function verifyHeader() {
    cy.get('[data-section="header"]').should('be.visible')
}

function verifyNavigation() {
    cy.get('[data-testid="plp-category-carousel"]').as('categoryCarousel')
        .find('[data-testid="plp-category-carousel-slide"]').as('carouselItem')
        .should('have.length.within', 12, 13)
    cy.get('@carouselItem').should('be.visible')
}

function verifyCategoryName() {
    cy.get('[data-testid=plp-category-title]').scrollIntoView().should('be.visible')
    // cy.get('[data-testid=plp-category-description]').should('be.visible') // will be added in new navigation
}

function verifyFiltersOn(deviceType) {
    if (deviceType in deviceTypes) {
        cy.get('[data-section="plp-filters-bar"]').as('filtersBar')
        .should('be.visible')
        cy.get('@filtersBar').find('[data-testid^="grid-filter-"]').as('filterPills')
        .should('have.length.gte', 3)
        cy.get('@filterPills').eq(0).should('be.visible')
        cy.wait(1000)
        if (deviceType === 'desktop') {
            cy.get(`[data-testid=grid-filters-all-desktop]`).should('be.visible')
            .click()
        }
        if ((deviceType === 'mobile')) {
            cy.get(`[data-testid=grid-filters-all-mobile]`)
            .dblclick()
        }
        cy.get('[data-testid=filters-drawer-aside]', {timeout:4000}).as('filtersDrawer')
        cy.get('@filtersDrawer').find('[data-testid=filter-tab-colors]')
        .should('be.visible').and('have.length', 1)
        cy.get('@filtersDrawer').find('[data-testid=filters-drawer-show-results-button]')
        .should('be.visible').and('have.length', 1)
        cy.get('[data-testid=drawer-close-button]')
        .should('be.visible').and('have.length', 1)
        .click()
        cy.get('@filtersDrawer').should('not.be.visible')
    }
}

function verifyGridBoard() {
    cy.get('[data-section=grid-v2-board]').as('board').scrollIntoView()
    .find('[data-testid=product-card]').as('productCard')
    .should('be.visible').and('have.length', itemsOnGrid)
    cy.get('@productCard').find('[data-testid=product-card-link]').should('have.length', 2 * itemsOnGrid)
    cy.get('@productCard').find('[data-testid=product-card-image-instagrid]').should('have.length', itemsOnGrid).and('be.visible')
    cy.get('@productCard').find('[data-testid=product-card-image]').should('have.length.gte', itemsOnGrid - 5) // most cards should have second image (on hover)
    cy.get('@productCard').find('[data-testid=product-card-furniture-type]').should('have.length', itemsOnGrid)
    cy.get('@productCard').find('[data-testid=product-card-furniture-description]').should('have.length', itemsOnGrid)
    cy.get('@productCard').find('[data-testid=product-card-furniture-size]').should('have.length', itemsOnGrid)
    cy.get('@productCard').find('[data-testid=product-card-swatches]').should('have.length', itemsOnGrid)
}

function verifyShowMoreItems() {
    cy.get('[data-testid=plp-show-more-items-button]').scrollIntoView().should('be.visible')
    cy.get('[data-testid=plp-pagination-counter-bottom]').should('be.visible').and('contain.text', itemsOnGrid)
}

function verifyUspConfigureYours() {
    cy.get('[data-section=customise-section]').as('customiseSection').scrollIntoView()
    cy.get('@customiseSection').find('h2').should('be.visible')
    cy.get('@customiseSection').find('a[href]')
}

function verifyFooterOn(deviceType) {
    if (deviceType === 'desktop') {
        verifyFooterDesktop()
    }
    if ((deviceType === 'mobile')) {
        verifyFooterMobile()
    }
}

function testPlpSections(deviceType) {
    verifyHeader()
    verifyNavigation()
    verifyCategoryName()
    verifyFiltersOn(deviceType)
    verifyGridBoard()
    verifyShowMoreItems()
    verifyUspConfigureYours()
    verifyFooterOn(deviceType)
}

module.exports = {
    testPlpSections,
    deviceTypes
}
