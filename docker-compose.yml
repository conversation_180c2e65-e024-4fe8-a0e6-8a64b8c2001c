---
version: '3.8'
services:
  db:
    image: postgres:14-bullseye
    env_file:
      - .env
    volumes:
      - ./shared/postgresql:/var/lib/postgresql/data
    environment:
      # set `${PGDATA}` to a subfolder because postgresql needs this folder to
      # be empty and we have a `.gitkeep` file there
      - PGDATA=/var/lib/postgresql/data/pgdata

  redis:
    image: redis:alpine
    volumes:
      - /var/lib/redis/data

  chrome-headless:
    init: true
    image: justinribeiro/chrome-headless:stable
    command:
      - "--headless"
      - "--disable-gpu"
      - "--disable-translate"
      - "--mute-audio"
      - "--hide-scrollbars"
      - "--no-sandbox"
      - "--remote-debugging-address=0.0.0.0"
      - "--remote-debugging-port=9222"
    ports:
      - "9222:9222"
    volumes:
      - ./shared/headless_files:/tmp
      - ./shared/static_files:/opt/project/src/r_static/

  mailhog:
    image: mailhog/mailhog:latest
    volumes:
      - ./shared/mailhog:/maildir/
    environment:
      - MH_HOSTNAME=mailhog
      - MH_CORS_ORIGIN=*
      - MH_STORAGE=maildir
      - MH_MAILDIR_PATH=/maildir/mails

  app: &app
    init: true
    build:
      context: .
      dockerfile: ./.devops/docker/backend/Dockerfile
      target: production_build
      args:
        DJANGO_ENV: production
    depends_on:
      - db
      - redis
    volumes:
      - ./shared/static_files:/app/r_static
      - ./shared/media:/app/media
      - ./shared/headless_files:/tmp

  celeryworker:
    <<: *app
    command: /start-celeryworker.sh
    ports: [ ]

  celerybeat:
    <<: *app
    command: /start-celerybeat.sh
    ports: [ ]

  flower:
    <<: *app
    expose:
      - "5555"
    command: /start-flower.sh
    ports: [ ]


networks:
  tylko_global:
    external: true
