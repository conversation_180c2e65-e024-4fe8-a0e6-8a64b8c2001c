Automatic merchandising
=

What?
-
**Automatic merchandising** is the process of setting the order of the furniture in our catalogue automatically.

On the first page of all our boards the order of the furniture is currently set manually, a.k.a. **manual
merchandising** (BoardManualOrder model is used for the overrides). The order of the next pages is set by automatic
merchandising.

We want to rewrite the existing algorithm and introduce the following features:

**I. Improving existing merchandising (a.k.a Esthetic)**

1. Setting the order of each category separately.

We want to treat category boards separately from 'All styles' board. (So category boards will no longer be
filtered out 'All styles' board). This will enable us to apply much more design rules and to set the furniture much
better. Without that feature we would never be able to resign from the overrides.\
It will also enable us to set up the order for one category if there was such need, we would not have to reorder
the whole catalogue.

2. Adding new design rules:

The rules are specified here: https://cstm-tasks.atlassian.net/wiki/spaces/DOC/pages/**********/PLP+design+rules

**II. Introducing three new strategies of ordering:**
1. Profit priority
2. Revenue priority
3. Popularity priority

We will gather the statistic data for each CatalogueEntry object and use that data for setting the catalogue's order
by different strategies.

**Profit priority**\
We would take into account the data relating to the original furniture that we see on PLP. We will be using CM2 - so
contribution margin 2, which means: how much money will be left in the company after paying for production and logistic,
seen as percentage of original price. So: (price - production cost - logistic cost) / price.

**Revenue and popularity priorities**\
For both revenue and popularity we will get the information from the final furniture (customized by the user), but we
assign it to the original one. F. ex. if we have a black bookcase on the board and the user makes it as small as
a shoerack, changes its color to white and buys it, still the credit goes to the original black bookcase from the board.
We will have both aggregate data and separate data for five most important regions: Germany, France, Netherlands, UK
and Switzerland.\
In `popularity` field we will have absolute clicks.


**III. Enabling comparing the strategies' performance in ab tests.**\
We want to add a new class for activating and deactivating the strategies, so that could be done from admin panel.

**IV. Providing human-friendly interface in admin.**


How?
--
**Ad. I.  Improving existing merchandising (a.k.a "Esthetic").**

We will add `category_order` field (unique per category), and the category boards will be ordered by it (not by `order`
field). Default ordering CatalogueEntry objects from class Meta should be removed.
Also `test_category_order` field will be added for testing.\
There will be workshops and consultations with the designers to determine the design rules for making the boards
diverse and esthetically pleasing.
The current `Merchandiser` class should be separated into `BaseMerchandiser`, `AllStylesMerchandiser` and
`CategoryMerchandiser`.
The new rules should be implemented in the algorithm.
****


**Ad. II. Introducing three strategies of ordering**

Following fields for keeping the statistic data will be added: `profit`, `revenue`, `popularity`.
And for revenue and popularity the corresponding fields for five main regions will also be added.

We will use the different merchandising strategies and save their order in the appropriate fields,
so it will be clear and explicit and make comparing different strategies in ab tests easier.

So following fields for keeping different ordering will also be introduced:
`profit_order`, `revenue_order`, `popularity_order`, `category_profit_order`, `category_revenue_order`,
`category_popularity_order` plus fields for revenue and popularity for five main regions.

That is a lot of new fields, so new base model will be introduced for keeping that fields (`CatalogueEntryBase`)


New `StrategyAllShelvesMerchandiser` and `StrategyCategoryMerchandiser` will be introduced. The algorithms will order
the entries by a strategy field, but at the same time apply all design rules used in esthetic merchandising.

**Main differences between strategy and esthetic merchandising:**
- **randomness**\
Esthetic merchandising algorithm firstly determines what furniture is wanted (chosen category probability is determined
in weights), while in strategy merchandising the opposite should be done. Firstly we should take the entry with
the highest f. ex. profit and then decide if we take it if it fulfills the design rules or we reject it.

- **different versions**\
For esthetic merchandising different versions of the ordering can be created easily, by using different seeds.
Strategy merchandising algorithm should be as accurate as possible and it should only give different results when we
provide it with updated data or we change the algorithm itself.
- **limits**\
In esthetic merchandising we do not have to set any limits.\
In strategic merchandising we want to set limit for the first stage ordering (with all the rules applied) and decided it
to be the first 100 entries. The reason for that limit is that the more we set up the less accurately the strategy is
implemented and the users use filters that "spoil" our order anyway. We checked how many times users actually do press
"Show more" button and counted in the cache to make more informed decision.\
The rest should be ordered by the strategy data with only some basic rules applied.
****

**Ad. III. Enabling comparing the strategies' performance in ab tests.**

We will add abtests for determining by which strategy the catalogue should be ordered: `profit_strategy`,
`revenue_strategy` and `popularity_strategy`.
New class `Strategy` could be added, with crucial strategy information and foreign key to the abtest, as well
as `StrategyEnum`.
***

**Ad. IV. Provide human-friendly interface in admin.**

The existing form (for action `set up test order data`) will be developed, there will be added choice of:
-  one of the three strategies (or None?)
- 'All styles' board (order for the whole catalogue), all categories or a chosen category
- region (for Germany, France, Netherlands, UK and Switzerland)


Some pseudocode
--

Separating Merchandiser class:
```
class BaseMerchandiser:
    """Class containing the basic design rules that should be applied to every board"""


class AllProductsMerchandiser(BaseMerchandiser):
    """Class for setting up the order for 'All products' board with the design rules specific design rules."""


class CategoryMerchandiser(BaseMerchandiser):
    """Class for setting up a category board with category specific design rules."""
```
---
Add Strategy merchandising classes:
```
class StrategyAllProductsMerchandiser:
    """Class for setting up 'All products' board in a chosen strategy and optionally region"""


class StrategyCategoryMerchandiser:
     """Class for setting up a category board in a chosen strategy and optionally region"""
```

---

Add new enum for strategies
```
class StrategyEnum(ChoicesMixin, enum.Enum):
    PROFIT = 'profit'
    REVENUE = 'revenue'
    POPULARITY = 'popularity'

    @property
    def separately_ordered_regions(self):
        """Information to which regions the strategy is applied separately.

        For result regions, the strategy will be ordered by regionalised strategy order,
        not the 'global' strategy order, f.ex. by 'revenue_order_de', not just 'revenue_order'.
        """
        regions = Region.objects.filter(
            name__in={'germany', 'france', 'netherlands', 'united_kingdom', 'switzerland'}
            )
        separately_ordered_regions_map = {
             self.PROFIT: Region.objects.none(),
             self.REVENUE: regions,
             self.POPULARITY: regions,
        }
        return separately_ordered_regions_map.get(self, Region.objects.none())
```
---
Add Strategy class, that will be handled in the admin panel:
```
class Strategy(models.Model):
    name = models.CharField(choices=StrategyEnum.choices(), max_length=32)
    # Field for activating strategies (at first only one strategy will be activated)
    active = models.BooleanField(default=False)
    # Abtest has to be created first
    abtest = models.ForeignKey('abtests.ABTest', on_delete=models.CASCADE)
```
---
Adding new base abstract class for keeping orders and new statistic fields:
```
class CatalogueEntryOrders(models.Model):
 # Margin strategy
 profit_order: int
 category_profit_order: int

 # Revenue strategy
 revenue_order: int
 category_revenue_order: int
 revenue_order_de: int
 category_revenue_order_de: int
 revenue_order_fr: int
 category_revenue_order_fr: int
 revenue_order_nl: int
 category_revenue_order_nl: int
 revenue_order_uk: int
 category_revenue_order_uk: int
 revenue_order_ch: int
 category_revenue_order_ch: int

 # Popularity strategy
 popularity_order: int
 category_popularity_order: int
 popularity_order_de: int
 category_popularity_order_de: int
 popularity_order_fr: int
 category_popularity_order_fr: int
 popularity_order_nl: int
 category_popularity_order_nl: int
 popularity_order_uk: int
 category_popularity_order_uk: int
 popularity_order_ch: int
 category_popularity_order_ch: int


class CatalogueEntry(CatalogueEntryOrders):
    ...
    category order: int

    ...
    profit: int

    revenue: int
    revenue_de: int
    revenue_fr: int
    revenue_nl: int
    revenue_uk: int
    revenue_ch: int

    popularity: int
    popularity_de: int
    popularity_fr: int
    popularity_nl: int
    popularity_uk: int
    popularity_ch: int
    ...
```

---
Determining the result order in the view:
```

    def get_queryset(self):
        ...
        strategy = self._get_strategy(request, region)
        if not strategy:
            return self._order_queryset_esthetically(queryset, category)
        return self._order_queryset_by_strategy(queryset, category, strategy, region)

    def _get_strategy(self, request, region):
        active_strategies = Strategy.objects.filter(active=True)
        for strategy in active_strategies:
            ab_test_regions = strategy.ab_test.regions
            if is_ab_test(request, strategy.ab_test.codename) and region in ab_test_regions:
                # todo: do something to avoid mistakes here
                return strategy

    def _order_queryset_esthetically(self, queryset, category):
        if category:
            return queryset.order_by('category_order')
        return queryset.order_by('order')

    def _order_queryset_by_strategy(self, queryset, category, strategy, region):
        separate_regions = StrategyEnum(strategy.name).separately_ordered_regions
        region_set_separately = region in separate_regions
        base_ordering_field = f'{strategy.name}_order'

        if region_set_separately:
            code = Country.objects.get(region=region).code.lower()
            if category:
                return queryset.order_by(f'category_{base_ordering_field}_{code}')
            return queryset.order_by(f'{base_ordering_field}_{code}')
        else:
            if category:
                return queryset.order_by(f'category_{base_ordering_field}')
            return queryset.order_by(base_ordering_field)
```
