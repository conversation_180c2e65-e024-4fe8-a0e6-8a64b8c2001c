# RFC: Separation of Order and Cart Models

## Summary

This RFC proposes the separation of the existing `Order` model into distinct `Order` and `Cart` models to optimize database performance and enhance code organization in CSTM module. The separation aims to address performance issues, code organization concerns, and simplify the cleanup of empty carts.

## Motivation

The current Order model, covering various order states, results in database performance
issues. The significant number of empty or abandoned orders makes it difficult to
clean up the database due to long query time and often timeouts.
Moreover, Order model is getting huge and in order to keep our codebase
maintainable and understandable we need to optimize code structure.


## Base Design Principles

* Establish a OneToOne relationship between the Order and Cart models.
* Order is created after submitting the checkout form. This way we don't interfere with payment sessions since the already operate on Order model.
* After each checkout form submission, Order items are overwritten with Cart items in order to keep Order and <PERSON>t in sync.
* Once the order is paid, the cart changes its status and new cart is created for user.
* Cart is soft deleted after Order is paid in order to maintain order history and make debug easier.
* Cart is automatically created for each authenticated user if they don't have one.
* Inactive carts are automatically deleted in batches by periodic task. (Inactive carts are carts that are not modified for some time).
* Cart and Order models have the same price calculations interface.

## Proposal

### Model Design
In an effort to minimize code redundancy, we will establish an abstract model to keep shared price related fields for both the `Cart` and `Order` models. The same approach will be taken for the `CartItem` and `OrderItem` models.
Also within this task we will introduce a new abstract model to keep address related fields for `Order` and `UserProfile`.

#### Abstract models
```python
# orders/models/abstract.py

class CartPriceMixin(models.Model):
    """Abstract model that keeps price related fields for Order and Cart."""

    # prices fields
    total_price = models.DecimalField(...)
    total_price_net = models.DecimalField(...)
    ...

    # fields that are necessary to calculate price
    vat_type = models.PositiveSmallIntegerField(...)
    assembly = models.BooleanField(...)
    is_fast_track = models.BooleanField(...)
    ...

    class Meta:
        abstract = True


class CartItemPriceMixin(models.Model):
    """Abstract model that keeps price related fields for OrderItem and CartItem."""

    price = models.DecimalField(max_digits=12, decimal_places=2)
    price_net = models.DecimalField(max_digits=12, decimal_places=2)
    vat_amount = models.DecimalField(max_digits=12, decimal_places=2, default=0)
    ...

    class Meta:
        abstract = True

```
#### Models
```python
# orders/models/order.py

class Order(CartPriceMixin):
    ...


class OrderItem(SafeDeleteModel, CartItemPriceMixin):
    order = models.ForeignKey(
        Order,
        related_name='items',
        on_delete=models.CASCADE,
    )
    ...
```
```python
# carts/models.py

class Cart(CartPriceMixin):
    owner = models.ForeignKey(
        settings.AUTHUSERMODEL,
        on_delete=models.CASCADE,
    )
    order = models.OneToOneField(
        Order,
        on_delete=models.PROTECT, # or models.CASCADE?
        null=True,
        blank=True,
    )
    status = models.IntegerField(
        choices=CartStatus.choices, # draft/paid
        default=CartStatus.DRAFT,
        db_index=True,
    )
    ...


class CartItem(SafeDeleteModel, CartItemPriceMixin):
    cart = models.ForeignKey(
        Cart,
        related_name='items',
        on_delete=models.CASCADE,
    )
    ...

```
### Service Design
Use the existing `OrderPriceCalculator` as a base class for calculating prices for both the Order and Cart models. A similar approach will be applied to the `OrderItemPriceCalculator`.

```python
class PriceCalculator:
    """Main service responsible for calculating all price related fields from Order"""

    def __init__(self) -> None:
        self.instance = None
        self.total = Decimal(0)
        self.base_total = Decimal(0)
        ...

    ...

class OrderPriceCalculator(PriceCalculator):
    def __init__(self, order: Order) -> None:
        self.instance = order

        # used when switching statuses, as defined in OrderSwitchStatusTransitionsMixin
        self.completed_target_items = self.order.completed_target_order_items.all()

        super().__init__()

    ...


class CartPriceCalculator(PriceCalculator):
    def __init__(self, cart: Cart) -> None:
        self.instance = cart
        super().__init__()

    ...

```
Use the existing `CartService` and make it operate on new `Cart` model.
This service should handle all necessary operations on the cart, such as adding items, removing items, adding assembly, applying promo codes, recalculating, and other related functionalities.
```python
# carts/services/cart_service.py

class CartService:
    """Service responsible for handling all operations on Cart model."""

    def __init__(self, cart: Cart) -> None:
        self.cart = cart
        self.items = cart.items.all()
        self.price_calculator = CartPriceCalculator(cart)
        ...

    ...

    @staticmethod
    def get_cart(user: User) -> Optional[Cart]:
        ...

    @staticmethod
    def create_cart(user: User) -> Cart:
        ...

    def create_cart_item(self, item: FurnitureAbstract) -> CartItem:
        ...

    def add_item(self, cart: Cart, item: FurnitureAbstract) -> CartItem:
        ...

    def delete_item(self, cart: Cart, item: FurnitureAbstract) -> None:
        ...

    @staticmethod
    def create_cart_from_order(order: Order) -> Cart:
        ...

    def create_order_from_cart(self) -> Order:
        ...

    ...

```

### Views
Introduce new view for Cart model and move here all cart related operations from `FurnitureViewSet` and `UserCartRest`.
From now on `FurnitureViewSet` should handle CRUD operations on furniture, and `CartViewSet` should handle all cart related operations.

```python
# carts/views.py

class CartViewSet(ModelViewSet):
    queryset = Cart.objects.filter(status=CartStatus.DRAFT)
    ...

    @action(detail=True, methods=['POST'])
    def add_to_cart(self, request) -> Response:
        cart = self.get_object()
        cart_service = CartService(cart)

        sellable_item = self.get_sellable_item()
        cart_item = cart_service.create_cart_item(sellable_item)
        cart_service.add_item(cart, cart_item)

        return Response(status=status.HTTP_201_CREATED)

    ...

```

## Migration Plan
In order to maintain order status history, we need to distinguish between cart orders that have history (i.e. were proceeded to payment) and those that don't.

#### Empty cart orders with history
* Create new Cart.
* Change Order status to `DRAFT`.
* Assign Order to newly created Cart.

#### Empty cart orders without history
* Create new Cart.
* Delete Order.

#### Non emtpy cart orders with history
* Create new Cart.
* Move items from Order to Cart.
* Change Order status to `DRAFT`.
* Assign Order to newly created Cart.

#### Non empty cart orders without history
* Create new Cart.
* Move items from Order to Cart.
* Delete Order.

## Testing
Apart from unit and manual tests, we should also make sure that all price calculations remains the same.
In order to do this, we will create a .csv file with prices calculated on orders and compare them to new prices calculated on carts.

Migration process should be tested on dev envs before we deploy it to production.

## Alternatives Considered
The main alternative is to use Redis to store cart orders.

Pros:
* It may be faster than database.
* We can use cache expiration to easily remove empty carts.

Cons:
* We would need to create a new ORM for Redis and it would be hard to maintain.
* Cache invalidation is hard.

## Open Questions
Q: If database querying for orders is very slow, how can we massively delete cart orders and create carts for them?
A: We should try first to delete orders on staging env via python script. If it is too slow, we can try to do it via SQL query.

Q: Should we create new app for carts?
A: Yes, it is going to have quite a lot of code in time. We can put price calculators and abstract models with price fields in pricing app.

Q: What about order modification by CS team? How do they add/remove/change items in already paid orders? Is their workflow going to change? They should not modify the Cart instance anymore, so they should be able to do all order manipulation on Order model itself e.g. recalculating prices, adding/removing items. Should we log these changes in order to know why Cart is different from Order?
A: Order and Cart should have similar interface for price calculations. Ideally all operations after Order is paid should remain the same.

Q: How can we make sure that this new separation od Cart and Order doesn't introduce new performance bottleneck?
A: We have no such concerns, it is made with the intention to improve performance and get rid of current bottleneck.

Q: Do we soft-delete inactive Carts or hard delete them?
A: We should soft-delete them, so we can easily restore them if needed.

Q: What do we do if user did the checkout and an Order is created from Cart. If new tab he added new items to Cart. After that he switched to checkout tab and paid for the Order. In this case we would create a new Cart for the user but what about new items that he added before paying for the Order? When creating new cart, should we transfer all unpaid items from the old cart?
A: We should transfer all unpaid items from the old cart to the new one.

Q: How will these changes impact the user experience, particularly in scenarios where a user wants to modify an order after it's been placed but before it's paid?
A: User will only be able to modify items from the cart level. After that he needs to get back to check out and order will be updated with cart items.

Q: What to we do with the abandoned carts that already have an Order but abandoned before payment? Do we keep such carts forever? Or should we delete them together with assigned Order?
A: For now we shouldn't delete such carts and orders. But we should create a query and run it from time to time to keep track of such abandoned orders.

Q: Are there any additional privacy concerns that arise from keeping carts and orders separate?
A: I guess no :)

Q: How will this change affect integrations with external systems such as payment gateways, inventory management, and shipping services?
A: This should have no impact, since all these services work with Order model and its behaviour should stay unchanged.

Q: In the event of a critical failure post-implementation, what are your plans for rollback or recovery?
A: It is better to have a scrypt that can transfer data from Cart to Order and vice versa. This way we will be able rollback the changes.



