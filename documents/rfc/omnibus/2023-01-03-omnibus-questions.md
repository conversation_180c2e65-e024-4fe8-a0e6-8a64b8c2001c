Questions regarding Omnibus in TYLKO
==
**Q:** **What about furniture that introduces new element(for example new type of doors)?**
It's price still be calculated by previous pricing algorithms even though such furniture didn't exist before. Could we add
a flag to jetty that indicates if omnibus applies or not?

**A:** ??
***
**Q:** Furthermore, you can have a situation when there is a new feature not present in the some of the oldest pricing versions within 30 days range, but already present in another pricing version introduced later on – then omnibus 'applies' but only partially.

**A:** ??
***
**Q:** **What about the same furniture but in different category?** If user makes a shelf in let's say shoerack category, should we display it's lowest price if the exact same shelf could be made under let's say tv stand category(or whatever other combination).

**A:** Not a problem. Different category means different furniture, hence no need to check all combinations of geometry and category.
***
**Q:** **Is daily gradation enough?** What about situation some promocodes apply until midday?

**A:** It should be enough since we need to apply all promos at a given day anyway.
***
**Q:** **What about timezones and DST?** Is UTC time enough to track when the day ended?

**A:** Shouldn't be a problem...
***
**Q:** **How we will be removing old (30+ days) files? How should take care of those?**

**A:** Probably shouldn't be a problem. I guess we should store all pricing versions since the beginning of the universe. This will help recalculate order swtiches using given pricing algorithm.
***
**Q:** **What if we will change exchange rate? Should we also store exchange rates?**

**A:** Yes we should store ConversionRate and RegionRate.
***
**Q:** **What are pros/cons for microservice vs python module here?**

**A:** My concern when using python module is that we need to phsically store different pricing algorithm versions(as in files in repo).
It would be easier to keep that as separate project/deployment. Down side to this is we would need to provide API for getting `Promotions` `Vouchers` `CurrencyRates` and `RegionRates`.
So we'll probably stick with separate django app for pricing with all version inside.
***
**Q:** **What strategy should we use in configurator and in mobile app? Refresh on move, on mouse down, or denounced?**

**A:** ??
***
**Q:**  **Should we somehow put it into item schema.org on pdp?**

**A:** ??
