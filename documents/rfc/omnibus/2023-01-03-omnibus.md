Omnibus in TYLKO
==

What?
--
**Omnibus** is [EU directive](https://eur-lex.europa.eu/legal-content/PL/TXT/HTML/?uri=CELEX:32019L2161&from=EN) that enforces that every e-commerce site should provide, appart from current price,
information what was the lowes price in last 30 days. This is to make sure price is not artificially inflated and then
lowered using promocodes.

How?
--
We will create pricing microservice(or python module within CSTM), that will be able to provide lowest price in last 30
days. It will take into consideraion pricing algorithm changes, coefficiens changes, publically available promocodes and promotions.
We need to store:
* pricing algorithm change in a registry(probably separate module, indexed by date)
* pricing coefficients(already stored as PricingVersion)
* promotions and promocodes available each day
* exchange rates

All of the above need to be fetched once a day and cached for optimization.

Questions?
--
Moved to [2023-01-03-omnibus-questions.md](2023-01-03-omnibus-questions.md)

More technical details?
--
See `pricing_v3/omnibus.py` file as well.

Some python pseudocode:
```python
class OmnibusCalculator:
    def __init__(self, jetty_watty):
        self.geometry = jetty_watty
        self.now = datetime.now().date
        self.since = self.now - datetime.timedelta(days=30)
        self.pricing_algorithms = self.get_pricing_algorithms()
        self.promocodes = self.get_promocodes()

    @cache
    def get_pricing_algoriths(self):
        # look into pricing registry and get pricing calculator for given date
        calculators = pricing_registry.get_calculator(since=self.since, until=self.now)
        # look into pricing registry and get pricing coefficients (we have this in PricingVersion models)
        coefficients = pricing_registry.registry.get_coefficients(since=self.since, until=self.now)
        # group by date, so that each date consists of single calculator and coefficients
        groupped_pricing_data = {
            ...
        }
        return groupped_pricing_data

    @cache
    def get_promocodes(self):
        # get all promostuff for date range
        codes = promo_tool.get_promostuff(since=self.since, until=self.now)
        # group codes by date
        groupped_codes = {
            ...
        }
        return groupped_codes

    def get_lowest_price_naive(self):
        # check performance; maybe its enough?
        prices = []
        for date in daterange(self.since, self.now):
            prices.append(self._get_price_for_date(date))
        return min(prices)

    def get_lowest_price_less_naive(self):
        #get only those dates that actually have a change
        viable_dates = []
        for date in daterange(self.since, self.now):
            if date in self.pricing_algorithms or date in self.promocodes:
                viable_dates.append(date)
        #calculate prices only for some dates
        prices = []
        for date in viable_dates:
            prices.append(self._get_price_for_date(date))
        return min(prices)


    def _get_price_for_date(self, date):
        pricing, coefficients = self.pricing_algorithms[date]
        promocodes = self.promocodes[date]
        price = pricing.get_price(self.geometry, coefficients)
        return self._apply_promocodes(price, promocodes)

```

Registry itself could be something like module:

```python
calculators/
├─ __init__.py
├─ version_1/
│  ├─ __init__.py  <- this should contain UniversalPricingInterface implemented
│  ├─ (...) etc. What we now have inside `pricing_v3` module
├─ version_2/
│  ├─ __init__.py
│  ├─ (...)
```

Where each version follows UniversalPricingInterface meaning -
should be able to calculate price based on geometry, region and coefficients.

Preferably we need to pair code with date in some kind of DB.
We can't hardcode dates when which Calculator is effective since we don't know when are the deploys.
So flow should be probably as follows:
1. Add new Calculator as new module inside `calculators` module
2. Deploy
3. Enable calculator in DjangoAdmin providing path to calculator + starting date for calculator

Getting and using calculator from registry POC:
```python
import importlib


#Assume we have something like
class CalculatorRegistryEntry(models.Model):
    start_date = models.DateTimeField()
    end_date = models.DateTimeField()
    calculator_version = models.CHarField()  #path to calculator module



def get_calculators_registry(since:date, until:date):
    """since and until are 30 days apart for omnibus reasons, but could be whatever"""
    entries = CalculatorRegistryEntry.objects.filter(
        Q(start_date__lte=until, end_date__gte=since) |
        Q(start_date__lte=until, end_date__isnull=True),
    ).order_by('start_date')
    registry = {}
    for date in daterange(since, until):
        cre = entries.get(start_date__gte=date, end_date_lte=date)
        Calculator = getattr(
            importlib.import_module(cre.calculator_version),
            'PricingCalculator'
        )
        registry[date] = Calculator

    return registry

#cache this daily
registry = get_calculators_registry('2022-01-01', '2022-02-01')

omnibus = OmnibusCalculator(since='2022-01-01', pricing_registry=registry)
omnibus.calculate_lowest_price(geometry, region)
```
