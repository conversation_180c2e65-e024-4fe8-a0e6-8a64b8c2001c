{"writeKey": "a7012506425589988397-74e7f3862d26b7a8c5cb6c9238368e71ecd6fdbf659a5b5abf7cb2ab2f6ff819", "readKey": "a7012506425589988397-d4e91362670e690cb7a451b74e4881b13c6237a3bd69e5ab8b63bf29c2f8e227", "upload": {"files": [{"pattern": "./frontend_src/src_vue/vue-locale/cplus/en.json", "type": "json", "lang": "en", "file": "cplus"}, {"pattern": "./frontend_src/src_vue/vue-locale/cplus/de.json", "type": "json", "lang": "de", "file": "cplus"}, {"pattern": "./frontend_src/src_vue/vue-locale/cplus/fr.json", "type": "json", "lang": "fr", "file": "cplus"}, {"pattern": "./frontend_src/src_vue/vue-locale/cplus/es.json", "type": "json", "lang": "es", "file": "cplus"}, {"pattern": "./frontend_src/src_vue/vue-locale/cplus/it.json", "type": "json", "lang": "it", "file": "cplus"}, {"pattern": "./frontend_src/src_vue/vue-locale/cplus/nl.json", "type": "json", "lang": "nl", "file": "cplus"}, {"pattern": "./frontend_src/src_vue/vue-locale/cplus/pl.json", "type": "json", "lang": "pl", "file": "cplus"}, {"pattern": "./frontend_src/src_vue/vue-locale/cplus/sv.json", "type": "json", "lang": "sv", "file": "cplus"}, {"pattern": "./frontend_src/src_vue/vue-locale/cplus/da.json", "type": "json", "lang": "da", "file": "cplus"}, {"pattern": "./frontend_src/src_vue/vue-locale/cplus/nb.json", "type": "json", "lang": "nb", "file": "cplus"}, {"pattern": "./frontend_src/src_vue/vue-locale/crow/en.json", "type": "json", "lang": "en", "file": "crow"}, {"pattern": "./frontend_src/src_vue/vue-locale/crow/de.json", "type": "json", "lang": "de", "file": "crow"}, {"pattern": "./frontend_src/src_vue/vue-locale/crow/fr.json", "type": "json", "lang": "fr", "file": "crow"}, {"pattern": "./frontend_src/src_vue/vue-locale/crow/es.json", "type": "json", "lang": "es", "file": "crow"}, {"pattern": "./frontend_src/src_vue/vue-locale/crow/it.json", "type": "json", "lang": "it", "file": "crow"}, {"pattern": "./frontend_src/src_vue/vue-locale/crow/nl.json", "type": "json", "lang": "nl", "file": "crow"}, {"pattern": "./frontend_src/src_vue/vue-locale/crow/pl.json", "type": "json", "lang": "pl", "file": "crow"}, {"pattern": "./frontend_src/src_vue/vue-locale/crow/sv.json", "type": "json", "lang": "sv", "file": "crow"}, {"pattern": "./frontend_src/src_vue/vue-locale/crow/da.json", "type": "json", "lang": "da", "file": "crow"}, {"pattern": "./frontend_src/src_vue/vue-locale/crow/nb.json", "type": "json", "lang": "nb", "file": "crow"}, {"pattern": "./frontend_src/src_vue/vue-locale/cwatcol/en.json", "type": "json", "lang": "en", "file": "cwatcol"}, {"pattern": "./frontend_src/src_vue/vue-locale/cwatcol/de.json", "type": "json", "lang": "de", "file": "cwatcol"}, {"pattern": "./frontend_src/src_vue/vue-locale/cwatcol/fr.json", "type": "json", "lang": "fr", "file": "cwatcol"}, {"pattern": "./frontend_src/src_vue/vue-locale/cwatcol/es.json", "type": "json", "lang": "es", "file": "cwatcol"}, {"pattern": "./frontend_src/src_vue/vue-locale/cwatcol/it.json", "type": "json", "lang": "it", "file": "cwatcol"}, {"pattern": "./frontend_src/src_vue/vue-locale/cwatcol/nl.json", "type": "json", "lang": "nl", "file": "cwatcol"}, {"pattern": "./frontend_src/src_vue/vue-locale/cwatcol/pl.json", "type": "json", "lang": "pl", "file": "cwatcol"}, {"pattern": "./frontend_src/src_vue/vue-locale/cwatcol/sv.json", "type": "json", "lang": "sv", "file": "cwatcol"}, {"pattern": "./frontend_src/src_vue/vue-locale/cwatcol/da.json", "type": "json", "lang": "da", "file": "cwatcol"}, {"pattern": "./frontend_src/src_vue/vue-locale/cwatcol/nb.json", "type": "json", "lang": "nb", "file": "cwatcol"}, {"pattern": "./frontend_src/src_vue/vue-locale/cwatwar/en.json", "type": "json", "lang": "en", "file": "cwatwar"}, {"pattern": "./frontend_src/src_vue/vue-locale/cwatwar/de.json", "type": "json", "lang": "de", "file": "cwatwar"}, {"pattern": "./frontend_src/src_vue/vue-locale/cwatwar/fr.json", "type": "json", "lang": "fr", "file": "cwatwar"}, {"pattern": "./frontend_src/src_vue/vue-locale/cwatwar/es.json", "type": "json", "lang": "es", "file": "cwatwar"}, {"pattern": "./frontend_src/src_vue/vue-locale/cwatwar/it.json", "type": "json", "lang": "it", "file": "cwatwar"}, {"pattern": "./frontend_src/src_vue/vue-locale/cwatwar/nl.json", "type": "json", "lang": "nl", "file": "cwatwar"}, {"pattern": "./frontend_src/src_vue/vue-locale/cwatwar/pl.json", "type": "json", "lang": "pl", "file": "cwatwar"}, {"pattern": "./frontend_src/src_vue/vue-locale/cwatwar/sv.json", "type": "json", "lang": "sv", "file": "cwatwar"}, {"pattern": "./frontend_src/src_vue/vue-locale/cwatwar/da.json", "type": "json", "lang": "da", "file": "cwatwar"}, {"pattern": "./frontend_src/src_vue/vue-locale/cwatwar/nb.json", "type": "json", "lang": "nb", "file": "cwatwar"}, {"pattern": "./frontend_src/src_vue/vue-locale/cvert/en.json", "type": "json", "lang": "en", "file": "cvert"}, {"pattern": "./frontend_src/src_vue/vue-locale/cvert/de.json", "type": "json", "lang": "de", "file": "cvert"}, {"pattern": "./frontend_src/src_vue/vue-locale/cvert/fr.json", "type": "json", "lang": "fr", "file": "cvert"}, {"pattern": "./frontend_src/src_vue/vue-locale/cvert/es.json", "type": "json", "lang": "es", "file": "cvert"}, {"pattern": "./frontend_src/src_vue/vue-locale/cvert/it.json", "type": "json", "lang": "it", "file": "cvert"}, {"pattern": "./frontend_src/src_vue/vue-locale/cvert/nl.json", "type": "json", "lang": "nl", "file": "cvert"}, {"pattern": "./frontend_src/src_vue/vue-locale/cvert/pl.json", "type": "json", "lang": "pl", "file": "cvert"}, {"pattern": "./frontend_src/src_vue/vue-locale/cvert/sv.json", "type": "json", "lang": "sv", "file": "cvert"}, {"pattern": "./frontend_src/src_vue/vue-locale/cvert/da.json", "type": "json", "lang": "da", "file": "cvert"}, {"pattern": "./frontend_src/src_vue/vue-locale/cvert/nb.json", "type": "json", "lang": "nb", "file": "cvert"}, {"pattern": "./frontend_src/src_vue/vue-locale/delivery_timeslots/en.json", "type": "json", "lang": "en", "file": "delivery_timeslots"}, {"pattern": "./frontend_src/src_vue/vue-locale/delivery_timeslots/de.json", "type": "json", "lang": "de", "file": "delivery_timeslots"}, {"pattern": "./frontend_src/src_vue/vue-locale/delivery_timeslots/fr.json", "type": "json", "lang": "fr", "file": "delivery_timeslots"}, {"pattern": "./frontend_src/src_vue/vue-locale/delivery_timeslots/es.json", "type": "json", "lang": "es", "file": "delivery_timeslots"}, {"pattern": "./frontend_src/src_vue/vue-locale/delivery_timeslots/it.json", "type": "json", "lang": "it", "file": "delivery_timeslots"}, {"pattern": "./frontend_src/src_vue/vue-locale/delivery_timeslots/nl.json", "type": "json", "lang": "nl", "file": "delivery_timeslots"}, {"pattern": "./frontend_src/src_vue/vue-locale/delivery_timeslots/pl.json", "type": "json", "lang": "pl", "file": "delivery_timeslots"}, {"pattern": "./frontend_src/src_vue/vue-locale/delivery_timeslots/sv.json", "type": "json", "lang": "sv", "file": "delivery_timeslots"}, {"pattern": "./frontend_src/src_vue/vue-locale/delivery_timeslots/da.json", "type": "json", "lang": "da", "file": "delivery_timeslots"}, {"pattern": "./frontend_src/src_vue/vue-locale/delivery_timeslots/nb.json", "type": "json", "lang": "nb", "file": "delivery_timeslots"}, {"pattern": "./frontend_src/src_vue/vue-locale/mail24/en.json", "type": "json", "lang": "en", "file": "mail24"}, {"pattern": "./frontend_src/src_vue/vue-locale/mail24/de.json", "type": "json", "lang": "de", "file": "mail24"}, {"pattern": "./frontend_src/src_vue/vue-locale/mail24/fr.json", "type": "json", "lang": "fr", "file": "mail24"}, {"pattern": "./frontend_src/src_vue/vue-locale/mail24/es.json", "type": "json", "lang": "es", "file": "mail24"}, {"pattern": "./frontend_src/src_vue/vue-locale/mail24/it.json", "type": "json", "lang": "it", "file": "mail24"}, {"pattern": "./frontend_src/src_vue/vue-locale/mail24/nl.json", "type": "json", "lang": "nl", "file": "mail24"}, {"pattern": "./frontend_src/src_vue/vue-locale/mail24/pl.json", "type": "json", "lang": "pl", "file": "mail24"}, {"pattern": "./frontend_src/src_vue/vue-locale/mail24/sv.json", "type": "json", "lang": "sv", "file": "mail24"}, {"pattern": "./frontend_src/src_vue/vue-locale/mail24/da.json", "type": "json", "lang": "da", "file": "mail24"}, {"pattern": "./frontend_src/src_vue/vue-locale/mail24/nb.json", "type": "json", "lang": "nb", "file": "mail24"}, {"pattern": "./frontend_src/src_vue/vue-locale/row_configurator/en.json", "type": "json", "lang": "en", "file": "row_configurator"}, {"pattern": "./frontend_src/src_vue/vue-locale/row_configurator/de.json", "type": "json", "lang": "de", "file": "row_configurator"}, {"pattern": "./frontend_src/src_vue/vue-locale/row_configurator/fr.json", "type": "json", "lang": "fr", "file": "row_configurator"}, {"pattern": "./frontend_src/src_vue/vue-locale/row_configurator/es.json", "type": "json", "lang": "es", "file": "row_configurator"}, {"pattern": "./frontend_src/src_vue/vue-locale/row_configurator/it.json", "type": "json", "lang": "it", "file": "row_configurator"}, {"pattern": "./frontend_src/src_vue/vue-locale/row_configurator/nl.json", "type": "json", "lang": "nl", "file": "row_configurator"}, {"pattern": "./frontend_src/src_vue/vue-locale/row_configurator/pl.json", "type": "json", "lang": "pl", "file": "row_configurator"}, {"pattern": "./frontend_src/src_vue/vue-locale/row_configurator/sv.json", "type": "json", "lang": "sv", "file": "row_configurator"}, {"pattern": "./frontend_src/src_vue/vue-locale/row_configurator/da.json", "type": "json", "lang": "da", "file": "row_configurator"}, {"pattern": "./frontend_src/src_vue/vue-locale/row_configurator/nb.json", "type": "json", "lang": "nb", "file": "row_configurator"}, {"pattern": "./frontend_src/src_vue/vue-locale/cookiebar/en.json", "type": "json", "lang": "en", "file": "cookiebar"}, {"pattern": "./frontend_src/src_vue/vue-locale/cookiebar/de.json", "type": "json", "lang": "de", "file": "cookiebar"}, {"pattern": "./frontend_src/src_vue/vue-locale/cookiebar/fr.json", "type": "json", "lang": "fr", "file": "cookiebar"}, {"pattern": "./frontend_src/src_vue/vue-locale/cookiebar/es.json", "type": "json", "lang": "es", "file": "cookiebar"}, {"pattern": "./frontend_src/src_vue/vue-locale/cookiebar/it.json", "type": "json", "lang": "it", "file": "cookiebar"}, {"pattern": "./frontend_src/src_vue/vue-locale/cookiebar/nl.json", "type": "json", "lang": "nl", "file": "cookiebar"}, {"pattern": "./frontend_src/src_vue/vue-locale/cookiebar/pl.json", "type": "json", "lang": "pl", "file": "cookiebar"}, {"pattern": "./frontend_src/src_vue/vue-locale/cookiebar/sv.json", "type": "json", "lang": "sv", "file": "cookiebar"}, {"pattern": "./frontend_src/src_vue/vue-locale/cookiebar/da.json", "type": "json", "lang": "da", "file": "cookiebar"}, {"pattern": "./frontend_src/src_vue/vue-locale/cookiebar/nb.json", "type": "json", "lang": "nb", "file": "cookiebar"}, {"pattern": "./src/locale/en/LC_MESSAGES/django.po", "type": "po", "lang": "en", "file": "django"}, {"pattern": "./src/locale/de/LC_MESSAGES/django.po", "type": "po", "lang": "de", "file": "django"}, {"pattern": "./src/locale/fr/LC_MESSAGES/django.po", "type": "po", "lang": "fr", "file": "django"}, {"pattern": "./src/locale/es/LC_MESSAGES/django.po", "type": "po", "lang": "es", "file": "django"}, {"pattern": "./src/locale/it/LC_MESSAGES/django.po", "type": "po", "lang": "it", "file": "django"}, {"pattern": "./src/locale/nl/LC_MESSAGES/django.po", "type": "po", "lang": "nl", "file": "django"}, {"pattern": "./src/locale/pl/LC_MESSAGES/django.po", "type": "po", "lang": "pl", "file": "django"}, {"pattern": "./src/locale/sv/LC_MESSAGES/django.po", "type": "po", "lang": "sv", "file": "django"}, {"pattern": "./src/locale/da/LC_MESSAGES/django.po", "type": "po", "lang": "da", "file": "django"}, {"pattern": "./src/locale/no/LC_MESSAGES/django.po", "type": "po", "lang": "nb", "file": "django"}]}, "transformations": [{"name": "adjust_lang_code", "source": "${lang}", "operations": ["replace:nb,no"]}], "download": {"includeSourceLang": true, "files": [{"output": "frontend_src/src_vue/vue-locale/${file}/${lang}.json", "conditions": "!~equals: ${file}, django"}, {"output": "src/locale/${adjust_lang_code}/LC_MESSAGES/django.po", "conditions": "equals: ${file}, django"}]}, "conversion": {"actions": {"output": "src/locale/${adjust_lang_code}/LC_MESSAGES/django.mo", "conditions": "equals: ${file}, django", "type": "mo"}}}