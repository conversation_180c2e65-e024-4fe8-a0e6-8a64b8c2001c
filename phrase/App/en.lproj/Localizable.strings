"ar_view_alert_1" = "Try moving your iPhone more slowly";

"ar_view_alert_2" = "Try turning on more lights";

"ar_view_alert_3" = "Try moving around";

"ar_view_alert_4" = "Keep moving";

"button_colour_unavailable" = "Colour unavailable";

"camera_access_denied_1" = "Camera access denied";

"camera_access_denied_2" = "Unable to show the shelf in your space without access to camera. Please go to Settings to grant access.";

"camera_access_denied_3" = "Settings";

"camera_access_denied_4" = "Cancel";

"checkout_failed_payment_alert_buton_1" = "Close";

"checkout_failed_payment_alert_header_1" = "Payment Failed";

"checkout_failed_payment_alert_header_2" = "There was a problem processing your payment. Please try again.";

"common_3D_buton_1" = "3D";

"common_adjust" = "Adjust";

"common_AR_buton_1" = "AR";

"common_ARview_alert_1" = "AR Mode is not supported on this device";

"common_ARview_alert_2" = "AR requires camera permission";

"common_bookcase_singular" = "Bookcase";

"common_chest_drawers_singular" = "Chest of Drawers";

"common_configurator_backpanels" = "Back panels";

"common_configurator_backpanels_disclaimer" = "Back panels are behind drawers and doors by default.";

"common_configurator_backpanels_off" = "Off";

"common_configurator_backpanels_on" = "On";

"common_configurator_backpanels_vinyl_disclaimer" = "Vinyl storage is only available with back panels.";

"common_configurator_color_burgundy_red" = "Burgundy Red";

"common_configurator_color_cotton_beige" = "Cotton Beige";

"common_configurator_color_grey" = "Grey";

"common_configurator_color_grey_dark_grey" = "Grey + Dark Grey";

"common_configurator_color_matte_black" = "Matte Black";

"common_configurator_color_midnight_blue" = "Midnight Blue";

"common_configurator_color_mint" = "Mint + Forest Green";

"common_configurator_color_sand" = "Sand + Midnight Blue";

"common_configurator_color_sand_yellow" = "Sand + Mustard Yellow";

"common_configurator_color_sky_blue" = "Sky Blue";

"common_configurator_color_snow_white" = "White";

"common_configurator_color_terracotta" = "Terracotta";

"common_configurator_depth_vinyls_alert_1" = "Vinyl storage shelves are a standard 40cm deep - perfectly-proportioned for your records.";

"common_configurator_drawers_app_24" = "Drawers removed as they\n do not fit a 24cm depth";

"common_configurator_drawers_app_24_2" = "Drawers re-added as they\n fit a 32/40cm depth";

"common_configurator_drawers_depth_limit" = "Drawers are only available for 32cm and 40cm depth shelves";

"common_configurator_feet" = "Feet";

"common_configurator_off" = "Off";

"common_configurator_on" = "On";

"common_design_removed" = "Design Removed";

"common_design_saved" = "Design Saved";

"common_menu_buton_1" = "Explore";

"common_menu_buton_2" = "Wishlist";

"common_menu_buton_contact" = "Contact";

"common_menu_cart" = "Cart";

"common_region_country_austria" = "Austria";

"common_region_country_belgium" = "Belgium";

"common_region_country_bulgaria" = "Bulgaria";

"common_region_country_croatia" = "Croatia";

"common_region_country_czech" = "Czech";

"common_region_country_denmark" = "Denmark";

"common_region_country_estonia" = "Estonia";

"common_region_country_finland" = "Finland";

"common_region_country_france" = "France";

"common_region_country_germany" = "Germany";

"common_region_country_greece" = "Greece";

"common_region_country_hungary" = "Hungary";

"common_region_country_ireland" = "Ireland";

"common_region_country_italy" = "Italy";

"common_region_country_latvia" = "Latvia";

"common_region_country_lithuania" = "Lithuania";

"common_region_country_luxembourg" = "Luxembourg";

"common_region_country_netherlands" = "Netherlands";

"common_region_country_norway" = "Norway";

"common_region_country_other_regions" = "Other Regions";

"common_region_country_poland" = "Poland";

"common_region_country_portugal" = "Portugal";

"common_region_country_romania" = "Romania";

"common_region_country_slovakia" = "Slovakia";

"common_region_country_slovenia" = "Slovenia";

"common_region_country_spain" = "Spain";

"common_region_country_sweden" = "Sweden";

"common_region_country_switzerland" = "Switzerland";

"common_region_country_united_kingdom" = "United Kingdom";

"common_shoe_rack_singular" = "Shoe Rack";

"common_sideboard_singular" = "Sideboard";

"common_tv_stand_singular" = "TV Stand";

"common_vinyl_storage_singular" = "Vinyl Storage";

"common_wall_storage_singular" = "Wall Storage";

"common_wishlist_alert_1" = "Wishlist is full!";

"configurator_arview_alert_1_buton_1" = "Back";

"configurator_arview_alert_1_header_1" = "Uh-oh!";

"configurator_arview_alert_1_header_2" = "Sorry, it seems the ARKit is not supported on your device.";

"configurator_arview_alert_2_buton_1" = "Back";

"configurator_arview_alert_2_header_1" = "Uh-oh!";

"configurator_arview_alert_2_header_2" = "AR requires camera permission.";

"configurator_common_alert_extended_width" = "Shelves wider than 240cm\ncome in two connectable sections.";

"configurator_common_alert_legs24" = "Legs are not available for\n24cm depth Sideboards.";

"configurator_common_alert_plinth24" = "Plinth not available for\n24cm depth sideboards.";

"configurator_common_bottom_storage" = "Bottom storage";

"configurator_common_bottom_storage_info" = "This furniture is too tall for additional Bottom Storage.";

"configurator_common_col_regular" = "Standard";

"configurator_common_col_slim" = "Slim";

"configurator_common_col_wide" = "Wide";

"configurator_common_color" = "Color";

"configurator_common_color_aubergine" = "Aubergine";

"configurator_common_color_black" = "Black";

"configurator_common_color_classic_red" = "Classic Red";

"configurator_common_color_dusty_pink" = "Dusty Pink";

"configurator_common_color_grey" = "Grey";

"configurator_common_color_natural" = "Natural";

"configurator_common_color_veneer_ash" = "Ash";

"configurator_common_color_veneer_oak" = "Oak";

"configurator_common_color_white" = "White";

"configurator_common_color_yellow" = "Yellow";

"configurator_common_colour_aubergine" = "Aubergine Plywood";

"configurator_common_colour_black" = "Black Plywood";

"configurator_common_colour_grey" = "Grey Plywood";

"configurator_common_colour_natural" = "Natural Plywood";

"configurator_common_colour_white" = "White Plywood";

"configurator_common_columns" = "Columns";

"configurator_common_density" = "Density";

"configurator_common_depth" = "Depth";

"configurator_common_details" = "Details";

"configurator_common_doors" = "Doors";

"configurator_common_drawer_external" = "External";

"configurator_common_drawer_internal" = "Internal";

"configurator_common_drawer_standard" = "Standard";

"configurator_common_drawers" = "Drawers";

"configurator_common_feet_legs" = "Legs";

"configurator_common_feet_plinth" = "Plinth";

"configurator_common_feet_standard" = "Standard";

"configurator_common_height" = "Height";

"configurator_common_height_plus" = "Height+";

"configurator_common_interior" = "Interior";

"configurator_common_interior_drawers" = "Tap the interior tab and select a segment to customise with drawers.";

"configurator_common_legroom_area" = "Legroom area";

"configurator_common_menu_drawers_limit" = "Drawers are only available for rows below 160cm.";

"configurator_common_menu_height" = "Height";

"configurator_common_menu_material" = "Material";

"configurator_common_menu_particle_board" = "Particle board";

"configurator_common_menu_plywood" = "Plywood";

"configurator_common_menu_width" = "Width";

"configurator_common_row_height" = "Row Height";

"configurator_common_sections" = "Sections";

"configurator_common_standard" = "Standard";

"configurator_common_style" = "Style";

"configurator_common_style_dense_grid" = "Dense Grid";

"configurator_common_style_frame" = "Frame";

"configurator_common_style_gradient" = "Gradient";

"configurator_common_style_grid" = "Grid";

"configurator_common_style_pattern" = "Pattern";

"configurator_common_style_slant" = "Slant";

"configurator_common_style_standard_grid" = "Standard Grid";

"configurator_common_title" = "EDIT";

"configurator_common_upper_storage" = "Upper storage";

"configurator_common_upper_storage_info" = "This furniture is too tall for additional Upper Storage";

"configurator_common_width" = "Width";

"configurator_common_zoom_menu_doors_size_limit" = "Row must be at least 28cm tall to fit doors";

"configurator_cplus_cable" = "Cable Opening";

"configurator_cplus_cable_off" = "Off";

"configurator_cplus_cable_on" = "On";

"configurator_cplus_cable_opening_description" = "Pre-installed, colour-matched steel cable opening with magnetic cap keeps cables tidy.";

"configurator_cplus_cable_system_description" = "Preinstalled and colour matched steel cable opening with magnetic cap keeps cables tidy at all times. For comfort's sake, desk includes two openings in the legroom area: one on the desk top and one on the back panel.";

"configurator_cplus_cable_two_line" = "Cable\nOpening";

"configurator_cplus_dimensions" = "Show Dimensions (cm)";

"configurator_cplus_doors" = "Door Opening";

"configurator_cplus_doors_description" = "Doors with ultra-modern flush handles and half-cylinder aluminium pulls. Tactile and understated.";

"configurator_cplus_doors_left" = "​​​Left";

"configurator_cplus_doors_right" = "Right";

"configurator_cplus_drawers_description" = "Extruded aluminium flush handles, self-closing and with extendable runners for deep access.";

"configurator_cplus_drawers_handles" = "Drawers’ handles";

"configurator_cwar_col_width" = "Column width";

"configurator_cwar_door" = "Door Opening";

"configurator_cwar_layout" = "Layout";

"configurator_cwatwar_col_width" = "Column width";

"configurator_cwatwar_col_width_war_width" = "Wardrobe width";

"configurator_density_stepper_info" = "Click on any column to adjust width with sliders.";

"configurator_menu_buton_1" = "Browse";

"configurator_menu_buton_2" = "Save";

"configurator_menu_buton_2_1" = "Saved";

"configurator_menu_buton_3" = "AR";

"configurator_menu_buton_4" = "Reset Position";

"configurator_menu_buton_5" = "Explore";

"configurator_menu_buton_6" = "Wishlist";

"configurator_save_popup_alert_buton_1" = "Retry";

"configurator_save_popup_alert_header_1" = "Oops - the email seems incorrect";

"configurator_save_popup_alert_header_2" = "Please try again";

"configurator_save_popup_page_1_buton_1" = "Save My Design";

"configurator_save_popup_page_1_checkbox_1" = "By submitting your email you agree to receive emails regarding your saved design as well as occasional carefully-curated content from Tylko.";

"configurator_save_popup_page_1_checkbox_2" = "The data administrator is Custom Sp. z o.o.";

"configurator_save_popup_page_1_checkbox_3" = "Yes, I agree to receive valuable content from Tylko in the form of occasional emails.";

"configurator_save_popup_page_1_checkbox_4" = "How is my data used?";

"configurator_save_popup_page_1_checkbox_5" = "Learn more";

"configurator_save_popup_page_1_checkbox_6" = "The controller of your data is Custom Sp.z o.o. We use your personal data to allow you to save your design and, if you agree (by ticking the checkbox), to send you occasional Tylko marketing emails which may be of interest.";

"configurator_save_popup_page_1_checkbox_7" = "You can read more in our";

"configurator_save_popup_page_1_checkbox_8" = "Privacy Policy.";

"configurator_save_popup_page_1_header_1" = "Smart thinking!";

"configurator_save_popup_page_1_header_2" = "We'll keep this design safe until you come back to finish it up.";

"configurator_save_popup_page_1_header_3" = "We’ll hang onto your design and send you occasional reminders until you’re ready to finish it up. Please enter your email below to agree:";

"configurator_save_popup_page_2_header_1" = "Your shelf design will be in your inbox.";

"configurator_section_info" = "Tap a column to customise the section.";

"configurator_section_info_watty" = "Tap a column to start customising.";

"configurator_unsaved_menu_buton_1" = "Save to Wishlist";

"configurator_unsaved_menu_buton_2" = "Don't Save";

"configurator_unsaved_menu_header_1" = "Unsaved Design";

"configurator_unsaved_menu_header_2" = "You're leaving your design...do you want to save it to your wishlist first?";

"configurator_watty_color_cashmere" = "Cashmere Beige";

"configurator_watty_color_cashmere_pink" = "Cashmere Beige + Antique Pink";

"configurator_watty_color_graphite" = "Graphite Grey";

"configurator_watty_color_graphite_pink" = "Graphite Grey + Antique Pink";

"configurator_watty_color_white" = "White";

"configurator_watty_color_white_pink" = "White + Antique Pink";

"configurator_wishlist_empty_1" = "Your wishlist is empty. \nTap";

"configurator_wishlist_empty_2" = "here";

"configurator_wishlist_empty_3" = "to add this shelf.";

"contact_menu_buton_1" = "Call +44 113 868 0195";

"contact_menu_buton_2" = "Email";

"contact_menu_buton_3" = "Cancel";

"contact_menu_buton_4_1" = "Live Chat";

"contact_menu_buton_4_2" = "(%@ new)";

"contact_menu_message_alert_1" = "New message from\nTylko Customer Service";

"contact_menu_message_alert_2" = "New messages from\nTylko Customer Service";

"cplus_snackbar_wall_fastening" = "Safety first! Remember to fasten your shelf to the wall after assembly.";

"cwatwar_segments_headline_1" = "Extra Long Hang";

"cwatwar_segments_headline_10" = "Stack + 2 Drawer";

"cwatwar_segments_headline_11" = "Stack + 4 Drawer";

"cwatwar_segments_headline_12" = "Stack + 6 Drawer";

"cwatwar_segments_headline_13" = "Long Hang + External drawers";

"cwatwar_segments_headline_14" = "Hang + External Drawers";

"cwatwar_segments_headline_15" = "Stack + External Drawers";

"cwatwar_segments_headline_16" = "Internal + External Drawers";

"cwatwar_segments_headline_2" = "Double Hang";

"cwatwar_segments_headline_3" = "Long Hang";

"cwatwar_segments_headline_4" = "Long Hang + 2 Drawer";

"cwatwar_segments_headline_5" = "Hang";

"cwatwar_segments_headline_6" = "Hang + 2 Drawer";

"cwatwar_segments_headline_7" = "Hang + 4 Drawer";

"cwatwar_segments_headline_8" = "Stack";

"cwatwar_segments_headline_9" = "Dense Stack";

"cwatwar_soldout_popup_na_body" = "The Type03 Wardrobe will soon be launching in %@. Leave us your email and we'll let you know as soon as it is available to order.";

"cwatwar_soldout_popup_na_confirmation_body" = "Great, thanks for signing up. You will now get notified when the wardrobe is available to order from your country.";

"cwatwar_soldout_popup_na_confirmation_header" = "You're on the list";

"cwatwar_soldout_popup_na_CTA" = "Get notified";

"cwatwar_soldout_popup_na_headline" = "Join the waitlist";

"cwatwar_waitlist_description" = "The Tylko Wardrobe will be available to shop from your location soon. Join our waiting list and we'll keep you updated.";

"deeplink_alert_button" = "Show me";

"deeplink_alert_description" = "Pick up where you left off. Use augmented reality to preview your shelf live in your space.";

"deeplink_alert_header" = "Your custom design is here!";

"fetch_failed_popup" = "Looks like something isn't working.";

"filter_menu_section_1_label_1" = "White";

"filter_menu_section_1_label_2" = "Gray";

"filter_menu_section_1_label_3" = "Black";

"filter_menu_section_1_label_4" = "Natural";

"filter_menu_section_1_label_5" = "Aubergine";

"login_failed_popup_header_1_1" = "Oops! Login failed";

"ola_dimension_view_snackbar" = "All dimensions are\n in centimetres (cm)";

"ola_qrcode_description_1" = "With a QR code, you can quickly transfer your design from our desktop configurator to our app.";

"ola_qrcode_description_2" = "Click on the AR badge in the bottom left corner of the desktop configurator, then select the \"Transfer directly\" option.";

"ola_qrcode_description_3" = "The Tylko app didn't recognise this QR code. Make sure to generate the QR code on tylko.com.";

"ola_qrcode_header_1" = "Scan QR code";

"ola_qrcode_header_2" = "How do I generate a QR code?";

"ola_qrcode_header_3" = "Unknown shelf parameter";

"ola_qrcode_help_cta" = "Where is the QR code?";

"onboarding_popup_B_buton_1" = "Close";

"onboarding_welcome_screen_buton_1" = "LET'S GO";

"onboarding_welcome_screen_header_1_1" = "Tylko";

"onboarding_welcome_screen_header_1_2" = "No internet connection detected. Please try again.";

"onboarding_welcome_screen_header_1_3" = "Please give Tylko permission to access your camera.";

"onboarding_welcome_screen_video_part_1" = "See in AR";

"onboarding_welcome_screen_video_part_2" = "Simple configuration";

"onboarding_welcome_screen_video_part_4" = "Easy assembly";

"onboarding_welcome_screen_video_part_5" = "Perfect-fit";

"pdp_common_promo_code_add_buton_1" = "ADD PROMOCODE";

"pdp_common_promo_code_add_popup1_buton_1" = "ADD PROMOCODE";

"pdp_common_promo_code_add_popup1_buton_2" = "CANCEL";

"pdp_common_size_depth" = "D:";

"pdp_common_size_height" = "H:";

"pdp_common_size_width" = "W:";

"pdp_common_top_menu_buton_2" = "Contact Us";

"recycling_tax_close_button" = "Close";

"recycling_tax_configurator_label" = "Eco-fee included";

"recycling_tax_description" = "On May 1, 2013, an eco-participation law was set up in France for the management and finance of sorting, recycling and recovery of used furniture (furniture, bedding and box springs).\n\nFrom October 1, 2018, eco-participation also began being applied to duvets, pillows, bolsters and sleeping bags. The scope of the eco-participation was last extended to many other products on January 1, 2020, including: baskets, boxes, bathroom and kitchen accessories, planters, cable covers, phone / computer mounts, bins, mirrors with storage, hangers and rods.\n\nThese amounts are entirely donated to Eco-Meubles, an organization approved by the State, and devoted to the development of solutions for the collection, recycling and recovery of these used products. Eco-Meubles works in partnership with local authorities, social and solidarity economy associations (Réseau des Ressourceries and Emmaüs) and furniture entities such as Tylko.\n\nFramed by law, eco-contribution applies in the same way to all furniture, new bedding, lighting, textiles and accessories throughout France. The amount is defined according to a scale common to all manufacturers and distributors, and corresponds to the cost of waste management for these used products.";

"recycling_tax_description_part_1" = "On May 1, 2013, an eco-participation law was set up in France for the management and finance of sorting, recycling and recovery of used furniture (furniture, bedding and box springs).";

"recycling_tax_description_part_2" = "From October 1, 2018, eco-participation also began being applied to duvets, pillows, bolsters and sleeping bags. The scope of the eco-participation was last extended to many other products on January 1, 2020, including: baskets, boxes, bathroom and kitchen accessories, planters, cable covers, phone / computer mounts, bins, mirrors with storage, hangers and rods.";

"recycling_tax_description_part_3" = "These amounts are entirely donated to Eco-Meubles, an organization approved by the State, and devoted to the development of solutions for the collection, recycling and recovery of these used products. Eco-Meubles works in partnership with local authorities, social and solidarity economy associations (Réseau des Ressourceries and Emmaüs) and furniture entities such as Tylko.";

"recycling_tax_description_part_4" = "Framed by law, eco-contribution applies in the same way to all furniture, new bedding, lighting, textiles and accessories throughout France. The amount is defined according to a scale common to all manufacturers and distributors, and corresponds to the cost of waste management for these used products.";

"recycling_tax_eco_label" = "Eco-fee";

"recycling_tax_eco_part_1" = "eco-fee";

"recycling_tax_eco_part_2" = "included";

"recycling_tax_header" = "What is eco-fee?";

"recycling_tax_price_label" = "Price";

"recycling_tax_total_label" = "Total";

"section_2_buton_1" = "Explore";

"section_2_description_1" = "Your wishlist is empty. Time to add some favorites!";

"shipping_common_alert_2" = "%@ is required";

"shipping_common_input_mail" = "E-mail";

"top_menu_section_1" = "Explore";

"top_menu_section_2" = "Wishlist";

"top_menu_section_3" = "Cart";

"tylko_shelf" = "Tylko Shelf";

