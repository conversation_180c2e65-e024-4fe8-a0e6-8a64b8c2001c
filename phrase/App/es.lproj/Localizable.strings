"ar_view_alert_1" = "";

"ar_view_alert_2" = "";

"ar_view_alert_3" = "";

"ar_view_alert_4" = "";

"button_colour_unavailable" = "";

"camera_access_denied_1" = "";

"camera_access_denied_2" = "";

"camera_access_denied_3" = "";

"camera_access_denied_4" = "";

"checkout_failed_payment_alert_buton_1" = "";

"checkout_failed_payment_alert_header_1" = "";

"checkout_failed_payment_alert_header_2" = "";

"common_3D_buton_1" = "";

"common_adjust" = "";

"common_AR_buton_1" = "";

"common_ARview_alert_1" = "";

"common_ARview_alert_2" = "";

"common_bookcase_singular" = "";

"common_chest_drawers_singular" = "";

"common_configurator_backpanels" = "";

"common_configurator_backpanels_disclaimer" = "";

"common_configurator_backpanels_off" = "";

"common_configurator_backpanels_on" = "";

"common_configurator_backpanels_vinyl_disclaimer" = "";

"common_configurator_color_burgundy_red" = "";

"common_configurator_color_cotton_beige" = "";

"common_configurator_color_grey" = "";

"common_configurator_color_grey_dark_grey" = "";

"common_configurator_color_matte_black" = "";

"common_configurator_color_midnight_blue" = "";

"common_configurator_color_mint" = "";

"common_configurator_color_sand" = "";

"common_configurator_color_sand_yellow" = "";

"common_configurator_color_sky_blue" = "";

"common_configurator_color_snow_white" = "";

"common_configurator_color_terracotta" = "";

"common_configurator_depth_vinyls_alert_1" = "";

"common_configurator_drawers_app_24" = "";

"common_configurator_drawers_app_24_2" = "";

"common_configurator_drawers_depth_limit" = "";

"common_configurator_feet" = "";

"common_configurator_off" = "";

"common_configurator_on" = "";

"common_design_removed" = "";

"common_design_saved" = "";

"common_menu_buton_1" = "";

"common_menu_buton_2" = "";

"common_menu_buton_contact" = "";

"common_menu_cart" = "";

"common_region_country_austria" = "";

"common_region_country_belgium" = "";

"common_region_country_bulgaria" = "";

"common_region_country_croatia" = "";

"common_region_country_czech" = "";

"common_region_country_denmark" = "";

"common_region_country_estonia" = "";

"common_region_country_finland" = "";

"common_region_country_france" = "";

"common_region_country_germany" = "";

"common_region_country_greece" = "";

"common_region_country_hungary" = "";

"common_region_country_ireland" = "";

"common_region_country_italy" = "";

"common_region_country_latvia" = "";

"common_region_country_lithuania" = "";

"common_region_country_luxembourg" = "";

"common_region_country_netherlands" = "";

"common_region_country_norway" = "";

"common_region_country_other_regions" = "";

"common_region_country_poland" = "";

"common_region_country_portugal" = "";

"common_region_country_romania" = "";

"common_region_country_slovakia" = "";

"common_region_country_slovenia" = "";

"common_region_country_spain" = "";

"common_region_country_sweden" = "";

"common_region_country_switzerland" = "";

"common_region_country_united_kingdom" = "";

"common_shoe_rack_singular" = "";

"common_sideboard_singular" = "";

"common_tv_stand_singular" = "";

"common_vinyl_storage_singular" = "";

"common_wall_storage_singular" = "";

"common_wishlist_alert_1" = "";

"configurator_arview_alert_1_buton_1" = "";

"configurator_arview_alert_1_header_1" = "";

"configurator_arview_alert_1_header_2" = "";

"configurator_arview_alert_2_buton_1" = "";

"configurator_arview_alert_2_header_1" = "";

"configurator_arview_alert_2_header_2" = "";

"configurator_common_alert_extended_width" = "";

"configurator_common_alert_legs24" = "";

"configurator_common_alert_plinth24" = "";

"configurator_common_bottom_storage" = "";

"configurator_common_bottom_storage_info" = "";

"configurator_common_col_regular" = "";

"configurator_common_col_slim" = "";

"configurator_common_col_wide" = "";

"configurator_common_color" = "";

"configurator_common_color_aubergine" = "";

"configurator_common_color_black" = "";

"configurator_common_color_classic_red" = "";

"configurator_common_color_dusty_pink" = "";

"configurator_common_color_grey" = "";

"configurator_common_color_natural" = "";

"configurator_common_color_veneer_ash" = "";

"configurator_common_color_veneer_oak" = "";

"configurator_common_color_white" = "";

"configurator_common_color_yellow" = "";

"configurator_common_colour_aubergine" = "";

"configurator_common_colour_black" = "";

"configurator_common_colour_grey" = "";

"configurator_common_colour_natural" = "";

"configurator_common_colour_white" = "";

"configurator_common_columns" = "";

"configurator_common_density" = "";

"configurator_common_depth" = "";

"configurator_common_details" = "";

"configurator_common_doors" = "";

"configurator_common_drawer_external" = "";

"configurator_common_drawer_internal" = "";

"configurator_common_drawer_standard" = "";

"configurator_common_drawers" = "";

"configurator_common_feet_legs" = "";

"configurator_common_feet_plinth" = "";

"configurator_common_feet_standard" = "";

"configurator_common_height" = "";

"configurator_common_height_plus" = "";

"configurator_common_interior" = "";

"configurator_common_interior_drawers" = "";

"configurator_common_legroom_area" = "";

"configurator_common_menu_drawers_limit" = "";

"configurator_common_menu_height" = "";

"configurator_common_menu_material" = "";

"configurator_common_menu_particle_board" = "";

"configurator_common_menu_plywood" = "";

"configurator_common_menu_width" = "";

"configurator_common_row_height" = "";

"configurator_common_sections" = "";

"configurator_common_standard" = "";

"configurator_common_style" = "";

"configurator_common_style_dense_grid" = "";

"configurator_common_style_frame" = "";

"configurator_common_style_gradient" = "";

"configurator_common_style_grid" = "";

"configurator_common_style_pattern" = "";

"configurator_common_style_slant" = "";

"configurator_common_style_standard_grid" = "";

"configurator_common_title" = "";

"configurator_common_upper_storage" = "";

"configurator_common_upper_storage_info" = "";

"configurator_common_width" = "";

"configurator_common_zoom_menu_doors_size_limit" = "";

"configurator_cplus_cable" = "";

"configurator_cplus_cable_off" = "";

"configurator_cplus_cable_on" = "";

"configurator_cplus_cable_opening_description" = "";

"configurator_cplus_cable_system_description" = "";

"configurator_cplus_cable_two_line" = "";

"configurator_cplus_dimensions" = "";

"configurator_cplus_doors" = "";

"configurator_cplus_doors_description" = "";

"configurator_cplus_doors_left" = "";

"configurator_cplus_doors_right" = "";

"configurator_cplus_drawers_description" = "";

"configurator_cplus_drawers_handles" = "";

"configurator_cwar_col_width" = "";

"configurator_cwar_door" = "";

"configurator_cwar_layout" = "";

"configurator_cwatwar_col_width" = "";

"configurator_cwatwar_col_width_war_width" = "";

"configurator_density_stepper_info" = "";

"configurator_menu_buton_1" = "";

"configurator_menu_buton_2" = "";

"configurator_menu_buton_2_1" = "";

"configurator_menu_buton_3" = "";

"configurator_menu_buton_4" = "";

"configurator_menu_buton_5" = "";

"configurator_menu_buton_6" = "";

"configurator_save_popup_alert_buton_1" = "";

"configurator_save_popup_alert_header_1" = "";

"configurator_save_popup_alert_header_2" = "";

"configurator_save_popup_page_1_buton_1" = "";

"configurator_save_popup_page_1_checkbox_1" = "";

"configurator_save_popup_page_1_checkbox_2" = "";

"configurator_save_popup_page_1_checkbox_3" = "";

"configurator_save_popup_page_1_checkbox_4" = "";

"configurator_save_popup_page_1_checkbox_5" = "";

"configurator_save_popup_page_1_checkbox_6" = "";

"configurator_save_popup_page_1_checkbox_7" = "";

"configurator_save_popup_page_1_checkbox_8" = "";

"configurator_save_popup_page_1_header_1" = "";

"configurator_save_popup_page_1_header_2" = "";

"configurator_save_popup_page_1_header_3" = "";

"configurator_save_popup_page_2_header_1" = "";

"configurator_section_info" = "";

"configurator_section_info_watty" = "";

"configurator_unsaved_menu_buton_1" = "";

"configurator_unsaved_menu_buton_2" = "";

"configurator_unsaved_menu_header_1" = "";

"configurator_unsaved_menu_header_2" = "";

"configurator_watty_color_cashmere" = "";

"configurator_watty_color_cashmere_pink" = "";

"configurator_watty_color_graphite" = "";

"configurator_watty_color_graphite_pink" = "";

"configurator_watty_color_white" = "";

"configurator_watty_color_white_pink" = "";

"configurator_wishlist_empty_1" = "";

"configurator_wishlist_empty_2" = "";

"configurator_wishlist_empty_3" = "";

"contact_menu_buton_1" = "";

"contact_menu_buton_2" = "";

"contact_menu_buton_3" = "";

"contact_menu_buton_4_1" = "";

"contact_menu_buton_4_2" = "";

"contact_menu_message_alert_1" = "";

"contact_menu_message_alert_2" = "";

"cplus_snackbar_wall_fastening" = "";

"cwatwar_segments_headline_1" = "";

"cwatwar_segments_headline_10" = "";

"cwatwar_segments_headline_11" = "";

"cwatwar_segments_headline_12" = "";

"cwatwar_segments_headline_13" = "";

"cwatwar_segments_headline_14" = "";

"cwatwar_segments_headline_15" = "";

"cwatwar_segments_headline_16" = "";

"cwatwar_segments_headline_2" = "";

"cwatwar_segments_headline_3" = "";

"cwatwar_segments_headline_4" = "";

"cwatwar_segments_headline_5" = "";

"cwatwar_segments_headline_6" = "";

"cwatwar_segments_headline_7" = "";

"cwatwar_segments_headline_8" = "";

"cwatwar_segments_headline_9" = "";

"cwatwar_soldout_popup_na_body" = "";

"cwatwar_soldout_popup_na_confirmation_body" = "";

"cwatwar_soldout_popup_na_confirmation_header" = "";

"cwatwar_soldout_popup_na_CTA" = "";

"cwatwar_soldout_popup_na_headline" = "";

"cwatwar_waitlist_description" = "";

"deeplink_alert_button" = "";

"deeplink_alert_description" = "";

"deeplink_alert_header" = "";

"fetch_failed_popup" = "";

"filter_menu_section_1_label_1" = "";

"filter_menu_section_1_label_2" = "";

"filter_menu_section_1_label_3" = "";

"filter_menu_section_1_label_4" = "";

"filter_menu_section_1_label_5" = "";

"login_failed_popup_header_1_1" = "";

"ola_dimension_view_snackbar" = "";

"ola_qrcode_description_1" = "";

"ola_qrcode_description_2" = "";

"ola_qrcode_description_3" = "";

"ola_qrcode_header_1" = "";

"ola_qrcode_header_2" = "";

"ola_qrcode_header_3" = "";

"ola_qrcode_help_cta" = "";

"onboarding_popup_B_buton_1" = "";

"onboarding_welcome_screen_buton_1" = "";

"onboarding_welcome_screen_header_1_1" = "";

"onboarding_welcome_screen_header_1_2" = "";

"onboarding_welcome_screen_header_1_3" = "";

"onboarding_welcome_screen_video_part_1" = "";

"onboarding_welcome_screen_video_part_2" = "";

"onboarding_welcome_screen_video_part_4" = "";

"onboarding_welcome_screen_video_part_5" = "";

"pdp_common_promo_code_add_buton_1" = "";

"pdp_common_promo_code_add_popup1_buton_1" = "";

"pdp_common_promo_code_add_popup1_buton_2" = "";

"pdp_common_size_depth" = "";

"pdp_common_size_height" = "";

"pdp_common_size_width" = "";

"pdp_common_top_menu_buton_2" = "";

"recycling_tax_close_button" = "";

"recycling_tax_configurator_label" = "";

"recycling_tax_description" = "";

"recycling_tax_description_part_1" = "";

"recycling_tax_description_part_2" = "";

"recycling_tax_description_part_3" = "";

"recycling_tax_description_part_4" = "";

"recycling_tax_eco_label" = "";

"recycling_tax_eco_part_1" = "";

"recycling_tax_eco_part_2" = "";

"recycling_tax_header" = "";

"recycling_tax_price_label" = "";

"recycling_tax_total_label" = "";

"section_2_buton_1" = "";

"section_2_description_1" = "";

"shipping_common_alert_2" = "";

"shipping_common_input_mail" = "";

"top_menu_section_1" = "Explorar";

"top_menu_section_2" = "Lista de deseos";

"top_menu_section_3" = "Carro";

"tylko_shelf" = "";

