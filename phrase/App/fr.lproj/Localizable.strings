"ar_view_alert_1" = "Bougez votre iPhone plus lentement";

"ar_view_alert_2" = "Allumez plus de lumières";

"ar_view_alert_3" = "Déplacez-vous";

"ar_view_alert_4" = "Continuez à vous déplacer";

"button_colour_unavailable" = "Couleur indisponible";

"camera_access_denied_1" = "Accès à la caméra refusé";

"camera_access_denied_2" = "L'accès à votre caméra est nécessaire pour visualiser l'étagère. Merci d'aller dans vos paramètres et d'autoriser l'accès.";

"camera_access_denied_3" = "Paramètres";

"camera_access_denied_4" = "Annuler";

"checkout_failed_payment_alert_buton_1" = "Fermer";

"checkout_failed_payment_alert_header_1" = "Echec du paiement";

"checkout_failed_payment_alert_header_2" = "La transaction n'a pas pu aboutir. Merci de réessayer.";

"common_3D_buton_1" = "3D";

"common_adjust" = "Ajuster";

"common_AR_buton_1" = "RA";

"common_ARview_alert_1" = "Mode RA non pris en charge sur cet appareil";

"common_ARview_alert_2" = "RA requiert l'accès à l'appareil photo";

"common_bookcase_singular" = "Bibliothèque";

"common_chest_drawers_singular" = "Commode";

"common_configurator_backpanels" = "Fond";

"common_configurator_backpanels_disclaimer" = "Les panneaux de fond se trouvent derrière les tiroirs et les portes.";

"common_configurator_backpanels_off" = "Off";

"common_configurator_backpanels_on" = "On";

"common_configurator_backpanels_vinyl_disclaimer" = "Les Meubles Platine sont disponibles uniquement avec des panneaux de fond.";

"common_configurator_color_burgundy_red" = "Rouge Bordeaux";

"common_configurator_color_cotton_beige" = "Beige Crème";

"common_configurator_color_grey" = "Gris";

"common_configurator_color_grey_dark_grey" = "Gris + Gris Foncé";

"common_configurator_color_matte_black" = "Noir Mat";

"common_configurator_color_midnight_blue" = "Bleu nuit";

"common_configurator_color_mint" = "Menthe + Vert forêt";

"common_configurator_color_sand" = "Sable + Bleu nuit";

"common_configurator_color_sand_yellow" = "Sable + Jaune Moutarde";

"common_configurator_color_sky_blue" = "Bleu Ciel";

"common_configurator_color_snow_white" = "Blanc";

"common_configurator_color_terracotta" = "Terracotta";

"common_configurator_depth_vinyls_alert_1" = "Des étagères d’une profondeur standard de 40 cm = parfait pour y ranger vos disques.";

"common_configurator_drawers_app_24" = "Tiroirs supprimés car indisponibles\n avec une profondeur de 24 cm";

"common_configurator_drawers_app_24_2" = "Tiroirs ré-ajoutés car disponibles\n avec une profondeur de 32 ou 40 cm";

"common_configurator_drawers_depth_limit" = "Les tiroirs sont disponibles uniquement dans une profondeur de 32 cm et 40 cm";

"common_configurator_feet" = "Socle";

"common_configurator_off" = "Off";

"common_configurator_on" = "On";

"common_design_removed" = "Design supprimé";

"common_design_saved" = "Design enregistré";

"common_menu_buton_1" = "Découvrir";

"common_menu_buton_2" = "Wishlist";

"common_menu_buton_contact" = "Contact";

"common_menu_cart" = "Panier";

"common_region_country_austria" = "Autriche";

"common_region_country_belgium" = "Belgique";

"common_region_country_bulgaria" = "Bulgarie";

"common_region_country_croatia" = "Croatie";

"common_region_country_czech" = "Tchèque";

"common_region_country_denmark" = "Danemark";

"common_region_country_estonia" = "Estonie";

"common_region_country_finland" = "Finlande";

"common_region_country_france" = "France";

"common_region_country_germany" = "Allemagne";

"common_region_country_greece" = "Grèce";

"common_region_country_hungary" = "Hongrie";

"common_region_country_ireland" = "Irlande";

"common_region_country_italy" = "Italie";

"common_region_country_latvia" = "Lettonie";

"common_region_country_lithuania" = "Lituanie";

"common_region_country_luxembourg" = "Luxembourg";

"common_region_country_netherlands" = "Pays-Bas";

"common_region_country_norway" = "Norvège";

"common_region_country_other_regions" = "Autres régions";

"common_region_country_poland" = "Pologne";

"common_region_country_portugal" = "Portugal";

"common_region_country_romania" = "Roumanie";

"common_region_country_slovakia" = "Slovaquie";

"common_region_country_slovenia" = "Slovénie";

"common_region_country_spain" = "Espagne";

"common_region_country_sweden" = "Suède";

"common_region_country_switzerland" = "Suisse";

"common_region_country_united_kingdom" = "Royaume-Uni";

"common_shoe_rack_singular" = "Meuble à chaussures";

"common_sideboard_singular" = "Étagère basse";

"common_tv_stand_singular" = "Meuble TV";

"common_vinyl_storage_singular" = "Meuble Platine";

"common_wall_storage_singular" = "Étagère murale";

"common_wishlist_alert_1" = "Plus de place dans la wishlist !";

"configurator_arview_alert_1_buton_1" = "Retour";

"configurator_arview_alert_1_header_1" = "Oups";

"configurator_arview_alert_1_header_2" = "ARKit n'est pas disponible sur votre appareil";

"configurator_arview_alert_2_buton_1" = "Retour";

"configurator_arview_alert_2_header_1" = "Oups";

"configurator_arview_alert_2_header_2" = "RA requiert l'accès à l'appareil photo";

"configurator_common_alert_extended_width" = "Étagères + de 240 cm de largeur\nen 2 parties connectables.";

"configurator_common_alert_legs24" = "Pieds non disponibles pour\nla profondeur de 24 cm.";

"configurator_common_alert_plinth24" = "Plinthes non disponibles pour\nla profondeur de 24 cm.";

"configurator_common_bottom_storage" = "Rangement inférieur";

"configurator_common_bottom_storage_info" = "Ce meuble est trop haut pour un rangement inférieur supplémentaire.";

"configurator_common_col_regular" = "Standard";

"configurator_common_col_slim" = "Fine";

"configurator_common_col_wide" = "Large";

"configurator_common_color" = "Couleur";

"configurator_common_color_aubergine" = "Aubergine";

"configurator_common_color_black" = "Noir";

"configurator_common_color_classic_red" = "Vrai Rouge";

"configurator_common_color_dusty_pink" = "Rose Poudré";

"configurator_common_color_grey" = "Gris";

"configurator_common_color_natural" = "Naturel";

"configurator_common_color_veneer_ash" = "Ash";

"configurator_common_color_veneer_oak" = "Oak";

"configurator_common_color_white" = "Blanc";

"configurator_common_color_yellow" = "Jaune";

"configurator_common_colour_aubergine" = "Panneau multiplex aubergine";

"configurator_common_colour_black" = "Panneau multiplex noir";

"configurator_common_colour_grey" = "Panneau multiplex gris";

"configurator_common_colour_natural" = "Panneau multiplex naturel";

"configurator_common_colour_white" = "Panneau multiplex blanc";

"configurator_common_columns" = "Colonnes";

"configurator_common_density" = "Densité";

"configurator_common_depth" = "Profondeur";

"configurator_common_details" = "Détails";

"configurator_common_doors" = "Portes";

"configurator_common_drawer_external" = "Extérieur";

"configurator_common_drawer_internal" = "Intérieur";

"configurator_common_drawer_standard" = "Standard";

"configurator_common_drawers" = "Tiroirs";

"configurator_common_feet_legs" = "Pieds";

"configurator_common_feet_plinth" = "Plinthes";

"configurator_common_feet_standard" = "Patins";

"configurator_common_height" = "Hauteur";

"configurator_common_height_plus" = "Hauteur+";

"configurator_common_interior" = "Intérieur";

"configurator_common_interior_drawers" = "Allez dans l'onglet « Intérieur » et choisissez le module à personnaliser avec des tiroirs.";

"configurator_common_legroom_area" = "Espace pour les jambes";

"configurator_common_menu_drawers_limit" = "Les tiroirs ne sont disponibles que pour des rangées de moins de 160 cm";

"configurator_common_menu_height" = "Hauteur";

"configurator_common_menu_material" = "Matériau";

"configurator_common_menu_particle_board" = "Panneaux en aggloméré";

"configurator_common_menu_plywood" = "Panneau multiplex";

"configurator_common_menu_width" = "Largeur";

"configurator_common_row_height" = "Hauteur de la rangée";

"configurator_common_sections" = "Sections";

"configurator_common_standard" = "Standard";

"configurator_common_style" = "Style";

"configurator_common_style_dense_grid" = "Dense Grid";

"configurator_common_style_frame" = "Frame";

"configurator_common_style_gradient" = "Gradient";

"configurator_common_style_grid" = "Grid";

"configurator_common_style_pattern" = "Pattern";

"configurator_common_style_slant" = "Slant";

"configurator_common_style_standard_grid" = "Standard Grid";

"configurator_common_title" = "MODIFIER";

"configurator_common_upper_storage" = "Rangement supérieur";

"configurator_common_upper_storage_info" = "Ce meuble est trop haut pour une étagère supérieure supplémentaire.";

"configurator_common_width" = "Largeur";

"configurator_common_zoom_menu_doors_size_limit" = "La hauteur de la rangée doit être de 28 cm min. pour ajouter des portes.";

"configurator_cplus_cable" = "Passe-câbles";

"configurator_cplus_cable_off" = "NON";

"configurator_cplus_cable_on" = "OUI";

"configurator_cplus_cable_opening_description" = "Passage pour câbles en acier assorti, pré-installé et accompagné d'un capuchon magnétique pour des câbles toujours bien rangés.";

"configurator_cplus_cable_system_description" = "Le passage de câbles en acier préinstallé, de couleur assortie, avec capuchon magnétique, évite les enchevêtrements de câbles. Pour plus de confort, le bureau dispose de deux ouvertures au niveau des pieds : une sur le plateau du bureau et une dans le panneau arrière.";

"configurator_cplus_cable_two_line" = "Passe-\ncâbles";

"configurator_cplus_dimensions" = "Voir les dimensions (en cm)";

"configurator_cplus_doors" = "Ouverture des portes";

"configurator_cplus_doors_description" = "Des portes dotées de poignées barre ultramodernes pour un design discret et un toucher agréable.";

"configurator_cplus_doors_left" = "Gauche";

"configurator_cplus_doors_right" = "Droite";

"configurator_cplus_drawers_description" = "Des poignées en aluminium extrudé, un système de fermeture automatique et des coulisses à sortie totale pour un accès facilité. Élégant et silencieux.";

"configurator_cplus_drawers_handles" = "Poignées de tiroirs";

"configurator_cwar_col_width" = "Largeur colonne";

"configurator_cwar_door" = "Ouverture des portes";

"configurator_cwar_layout" = "Disposition";

"configurator_cwatwar_col_width" = "Largeur colonne";

"configurator_cwatwar_col_width_war_width" = "Largeur du dressing";

"configurator_density_stepper_info" = "Cliquez sur la colonne et utilisez les curseurs pour en ajuster la largeur.";

"configurator_menu_buton_1" = "Rechercher";

"configurator_menu_buton_2" = "Enregistrer";

"configurator_menu_buton_2_1" = "Enregistré";

"configurator_menu_buton_3" = "RA";

"configurator_menu_buton_4" = "Repositionner";

"configurator_menu_buton_5" = "Découvrir";

"configurator_menu_buton_6" = "Wishlist";

"configurator_save_popup_alert_buton_1" = "Réessayer";

"configurator_save_popup_alert_header_1" = "Oups - l'adresse email est erronée.";

"configurator_save_popup_alert_header_2" = "Merci de réessayer.";

"configurator_save_popup_page_1_buton_1" = "Enregistrer";

"configurator_save_popup_page_1_checkbox_1" = "En fournissant votre e-mail, vous acceptez de recevoir des e-mails concernant votre design enregistré ainsi que des contenus occasionnels et triés sur le volet de la part de Tylko.";

"configurator_save_popup_page_1_checkbox_2" = "L'administrateur des données est Custom Sp. z o.o.";

"configurator_save_popup_page_1_checkbox_3" = "Oui, j'accepte de recevoir du contenu intéressant de la part de Tylko sous forme d'e-mails occasionnels.";

"configurator_save_popup_page_1_checkbox_4" = "Comment mes données sont-elles utilisées?";

"configurator_save_popup_page_1_checkbox_5" = "En savoir plus";

"configurator_save_popup_page_1_checkbox_6" = "Le gestionnaire de vos données est Custom Sp.z o.o. Nous utilisons vos données personnelles pour vous permettre de sauvegarder votre design et, si vous l'acceptez (en cochant la case), pour vous envoyer de temps à autre des emails à caractère marketing de la part de Tylko qui pourraient vous intéresser.";

"configurator_save_popup_page_1_checkbox_7" = "Vous pouvez trouver plus d’informations en consultant notre";

"configurator_save_popup_page_1_checkbox_8" = "Politique de confidentialité.";

"configurator_save_popup_page_1_header_1" = "Malin !";

"configurator_save_popup_page_1_header_2" = "Votre design est en lieu sûr jusqu'à ce que vous reveniez le récupérer.";

"configurator_save_popup_page_1_header_3" = "Nous gardons votre design au chaud et vous enverrons des rappels de temps à autre jusqu'à ce que vous soyez prêt(e) à le terminer. Merci d’indiquer ci-dessous votre adresse e-mail pour donner votre accord:";

"configurator_save_popup_page_2_header_1" = "Le design de votre étagère vous attendra dans votre boîte mail.";

"configurator_section_info" = "Tapotez pour personnaliser le design des colonnes.";

"configurator_section_info_watty" = "Pour débuter la personnalisation, appuyez sur une colonne";

"configurator_unsaved_menu_buton_1" = "Ajouter";

"configurator_unsaved_menu_buton_2" = "Ne pas ajouter";

"configurator_unsaved_menu_header_1" = "Design non enregistré";

"configurator_unsaved_menu_header_2" = "Avant de partir... Vous ne voulez pas ajouter votre design à votre wishlist ?";

"configurator_watty_color_cashmere" = "Beige Cachemire";

"configurator_watty_color_cashmere_pink" = "Beige Cachemire + Vieux Rose";

"configurator_watty_color_graphite" = "Gris Graphite";

"configurator_watty_color_graphite_pink" = "Gris Graphite + Vieux Rose";

"configurator_watty_color_white" = "Blanc";

"configurator_watty_color_white_pink" = "Blanc + Vieux Rose";

"configurator_wishlist_empty_1" = "Votre wishlist est vide. \nAppuyez";

"configurator_wishlist_empty_2" = "ici";

"configurator_wishlist_empty_3" = "pour y enregistrer cette étagère.";

"contact_menu_buton_1" = "Appeler +33 1 76 42 01 08";

"contact_menu_buton_2" = "E-mail";

"contact_menu_buton_3" = "Annuler";

"contact_menu_buton_4_1" = "Live Chat";

"contact_menu_buton_4_2" = "(%@ nouveau)";

"contact_menu_message_alert_1" = "Nouveau message du Service Client Tylko";

"contact_menu_message_alert_2" = "Nouveaux messages du Service Client Tylko";

"cplus_snackbar_wall_fastening" = "La sécurité, avant tout ! Fixez votre étagère au mur, une fois assemblée.";

"cwatwar_segments_headline_1" = "Barre Extra Longue";

"cwatwar_segments_headline_10" = "Caisson + 2 Tiroirs";

"cwatwar_segments_headline_11" = "Caisson + 4 Tiroirs";

"cwatwar_segments_headline_12" = "Caisson + 6 Tiroirs";

"cwatwar_segments_headline_13" = "Longue Barre + Tiroirs Extérieurs";

"cwatwar_segments_headline_14" = "Barre + Tiroirs Extérieurs";

"cwatwar_segments_headline_15" = "Caisson + Tiroirs Extérieurs";

"cwatwar_segments_headline_16" = "Tiroirs Intérieurs + Extérieurs";

"cwatwar_segments_headline_2" = "Double Barre";

"cwatwar_segments_headline_3" = "Longue Barre";

"cwatwar_segments_headline_4" = "Longue Barre + 2 Tiroirs";

"cwatwar_segments_headline_5" = "Barre";

"cwatwar_segments_headline_6" = "Barre + 2 Tiroirs";

"cwatwar_segments_headline_7" = "Barre + 4 Tiroirs";

"cwatwar_segments_headline_8" = "Caisson";

"cwatwar_segments_headline_9" = "Caisson Compact";

"cwatwar_soldout_popup_na_body" = "Le Dressing Type 03 sera bientôt disponible dans ce pays : %@. Donnez-nous votre email et nous vous informerons dès qu'il sera possible de commander ce meuble.";

"cwatwar_soldout_popup_na_confirmation_body" = "Super ! Merci pour votre inscription. Vous serez informé lorsqu'il sera possible de commander le dressing dans votre pays.";

"cwatwar_soldout_popup_na_confirmation_header" = "Vous êtes bien sur la liste";

"cwatwar_soldout_popup_na_CTA" = "Recevoir une notification";

"cwatwar_soldout_popup_na_headline" = "Inscrivez-vous sur la liste d'attente";

"cwatwar_waitlist_description" = "Le Dressing Tylko sera bientôt disponible dans votre pays. Inscrivez-vous sur notre liste d'attente pour être informé(e) de sa disponibilité.";

"deeplink_alert_button" = "Voir mon design";

"deeplink_alert_description" = "Reprenez votre design là où vous l'avez arrêté. Et grâce la réalité augmentée, vous pouvez voir en direct ce que donne votre étagère dans votre espace.";

"deeplink_alert_header" = "Votre design personnalisé est là !";

"fetch_failed_popup" = "Une erreur est survenue.";

"filter_menu_section_1_label_1" = "Blanc";

"filter_menu_section_1_label_2" = "Gris";

"filter_menu_section_1_label_3" = "Noir";

"filter_menu_section_1_label_4" = "Naturel";

"filter_menu_section_1_label_5" = "Aubergine";

"login_failed_popup_header_1_1" = "Oups ! Échec de connexion";

"ola_dimension_view_snackbar" = "Toutes les dimensions sont\n en centimètres (cm)";

"ola_qrcode_description_1" = "Grâce au code QR, transférez rapidement vers l'App le design que vous avez créé avec notre configurateur pour PC.";

"ola_qrcode_description_2" = "Cliquez sur le symbole AR situé dans le coin en bas, à gauche, du configurateur pour PC. Sélectionnez ensuite l'option \"Transférer immédiatement\"";

"ola_qrcode_description_3" = "Ce code QR n'est pas reconnu par l'App Tylko. Assurez-vous de bien avoir généré le code QR sur tylko.com";

"ola_qrcode_header_1" = "Scanner le code QR";

"ola_qrcode_header_2" = "Comment puis-je générer un code QR ?";

"ola_qrcode_header_3" = "Paramètre d'étagère inconnu";

"ola_qrcode_help_cta" = "Où puis-je trouver le code QR ?";

"onboarding_popup_B_buton_1" = "Fermer";

"onboarding_welcome_screen_buton_1" = "C’EST PARTI";

"onboarding_welcome_screen_header_1_1" = "Tylko";

"onboarding_welcome_screen_header_1_2" = "Aucune connexion Internet détectée. Veuillez réessayer.";

"onboarding_welcome_screen_header_1_3" = "Autorisez Tylko à accéder à votre appareil photo.";

"onboarding_welcome_screen_video_part_1" = "Voir en RA";

"onboarding_welcome_screen_video_part_2" = "Configuration simple";

"onboarding_welcome_screen_video_part_4" = "Assemblage fluide";

"onboarding_welcome_screen_video_part_5" = "Adaptation parfaite";

"pdp_common_promo_code_add_buton_1" = "AJOUTER LE CODE";

"pdp_common_promo_code_add_popup1_buton_1" = "AJOUTER LE CODE";

"pdp_common_promo_code_add_popup1_buton_2" = "ANNULER";

"pdp_common_size_depth" = "P :";

"pdp_common_size_height" = "H :";

"pdp_common_size_width" = "L :";

"pdp_common_top_menu_buton_2" = "Nous contacter";

"recycling_tax_close_button" = "Fermer";

"recycling_tax_configurator_label" = "Éco-participation incluses.";

"recycling_tax_description" = "Le 1er mai 2013, une loi sur l'éco-participation a été mise en place en France pour la gestion et le financement du tri, du recyclage et de la valorisation des meubles usagés (mobilier, literie et sommiers).\n\nDepuis le 1er octobre 2018, l'éco-participation a également commencé à être appliquée aux couettes, oreillers, traversins et sacs de couchage. Le champ d'application de l'éco-participation a enfin été étendu à de nombreux autres produits le 1er janvier 2020, notamment : paniers, boîtes, accessoires de salle de bain et de cuisine, jardinières, cache-câbles, supports de téléphone / ordinateur, bacs, miroirs avec rangement, cintres et tringles.\n\nCes montants sont entièrement reversés à Eco-Meubles, organisme agréé par l'État, et dédié au développement de solutions de collecte, de recyclage et de valorisation de ces produits usagés. Eco-Meubles travaille en partenariat avec des collectivités locales, des associations de l'économie sociale et solidaire (Réseau des Ressourceries et Emmaüs) et des entités du secteur de l'ameublement comme Tylko.\n\nEncadrée par la loi, l'éco-contribution s'applique de la même manière à tous les meubles, la literie neuve, les luminaires, les textiles et les accessoires sur l'ensemble du territoire français. Son montant est défini selon un barème commun à tous les fabricants et distributeurs, et correspond au coût de la gestion des déchets de ces produits usagés.";

"recycling_tax_description_part_1" = "Le 1er mai 2013, une loi sur l'éco-participation a été mise en place en France pour la gestion et le financement du tri, du recyclage et de la valorisation des meubles usagés (mobilier, literie et sommiers).";

"recycling_tax_description_part_2" = "Depuis le 1er octobre 2018, l'éco-participation a également commencé à être appliquée aux couettes, oreillers, traversins et sacs de couchage. Le champ d'application de l'éco-participation a enfin été étendu à de nombreux autres produits le 1er janvier 2020, notamment : paniers, boîtes, accessoires de salle de bain et de cuisine, jardinières, cache-câbles, supports de téléphone / ordinateur, bacs, miroirs avec rangement, cintres et tringles.";

"recycling_tax_description_part_3" = "Ces montants sont entièrement reversés à Eco-Meubles, organisme agréé par l'État, et dédié au développement de solutions de collecte, de recyclage et de valorisation de ces produits usagés. Eco-Meubles travaille en partenariat avec des collectivités locales, des associations de l'économie sociale et solidaire (Réseau des Ressourceries et Emmaüs) et des entités du secteur de l'ameublement comme Tylko.";

"recycling_tax_description_part_4" = "Encadrée par la loi, l'éco-contribution s'applique de la même manière à tous les meubles, la literie neuve, les luminaires, les textiles et les accessoires sur l'ensemble du territoire français. Son montant est défini selon un barème commun à tous les fabricants et distributeurs, et correspond au coût de la gestion des déchets de ces produits usagés.";

"recycling_tax_eco_label" = "Éco-participation";

"recycling_tax_eco_part_1" = "d'éco-participation";

"recycling_tax_eco_part_2" = "inclus";

"recycling_tax_header" = "Qu'est-ce que l'éco-participation ?";

"recycling_tax_price_label" = "Prix";

"recycling_tax_total_label" = "Total";

"section_2_buton_1" = "Découvrir";

"section_2_description_1" = "Votre wishlist est vide. Il est temps d'y ajouter vos étagères favorites !";

"shipping_common_alert_2" = "%@ est requis";

"shipping_common_input_mail" = "E-mail";

"top_menu_section_1" = "Découvrir";

"top_menu_section_2" = "Wishlist";

"top_menu_section_3" = "Panier";

"tylko_shelf" = "Étagère Tylko";

