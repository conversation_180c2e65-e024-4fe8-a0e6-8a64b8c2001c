#!/usr/bin/env bash

set -o errexit
set -o pipefail
set -o nounset
set -o xtrace

ENV_NAME="${1:-${ENV_NAME}}"
ENV_HOSTNAME="${2:-${ENV_URL}}"
PORT_INCREMENTER="${3:-${ENV_PORT_PREFIX}}"

MAILHOG_PLAIN_AUTH_FILE_PATH="/home/<USER>/mailhog/plain_auth.txt"
MAILHOG_AUTH_FILE_PATH="/home/<USER>/mailhog/auth.txt"
MAILHOG_WEB_UI_PORT=$((8025 + PORT_INCREMENTER))
MAILHOG_SMTP_SERVER_PORT=$((1025 + PORT_INCREMENTER))


###############################################################################
# Create MailH<PERSON>'s auth file for env's main user.
#
# Globals:
#   None
# Arguments:
#   $1 - username of main env's user
#   $2 - password
#   $3 - path to plain auth file
# Returns:
#   None
###############################################################################
create_plain_auth_file() {
  local name="${1}"
  local password="${2}"
  local plain_auth_file_path="${3}"

  echo "${name}:${password}" > "${plain_auth_file_path}"
}


###############################################################################
# Crypt passwords in auth file.
#
# Globals:
#   None
# Arguments:
#   $1 - path to auth file
#   $2 - path to auth file with passwords encrypted
# Returns:
#   None
###############################################################################
crypt_passwords_in_auth_file() {
  local auth_file_path="${1}"
  local encrypted_auth_file_path="${2:-${auth_file_path}}"

  tmp_auth_file_path="$(mktemp)"
  echo "MailHog usernames and passwords:"
  cat "${auth_file_path}"

  while IFS= read -r line
  do
    username="$(echo "${line}" | cut -d ":" -f 1)"
    password="$(echo "${line}" | cut -d ":" -f 2)"
    encrypted_password=$(docker run --rm --tty mailhog/mailhog bcrypt "${password}")
    echo "${username}:${encrypted_password}" >> "${tmp_auth_file_path}"
  done < "${auth_file_path}"
  mv "${tmp_auth_file_path}" "${encrypted_auth_file_path}"
  chmod a+r "${encrypted_auth_file_path}"
}


###############################################################################
# Rerun mailhog container for ``${1}`` env.
#
# Globals:
#   None
# Arguments:
#   $1 - env name
#   $2 - MailHog's hostname
#   $3 - MailHog's WEB UI port
#   $4 - MailHog's SMTP server port
#   $5 - MailHog's `maildir` directory - default `${HOME}/mailhog/mails`
#   $6 - MailHog's auth file - default `${HOME}/mailhog/auth.txt``
# Returns:
#   None
###############################################################################
run_mailhog_container() {
  local name="${1}"
  local container_name="mailhog_${name}"
  local hostname="${2}"
  local web_ui_port="${3}"
  local smtp_server_port="${4}"
  local maildir_path="${5:-/home/<USER>/mailhog/mails}"
  local auth_file_path="${6:-/home/<USER>/mailhog/auth.txt}"

  docker rm --force "${container_name}" || true
  docker run \
    --detach \
    --init \
    --name "${container_name}" \
    --restart "always" \
    --env "MH_HOSTNAME=${hostname}" \
    --env "MH_UI_WEB_PATH=dev/mailhog" \
    --env "MH_STORAGE=maildir" \
    --env "MH_MAILDIR_PATH=/maildir" \
    --env "MH_AUTH_FILE=/auth.txt" \
    --mount "type=bind,source=${maildir_path},target=/maildir" \
    --mount "type=bind,source=${auth_file_path},target=/auth.txt" \
    --publish "${smtp_server_port}:1025" \
    --publish "${web_ui_port}:8025" \
  mailhog/mailhog:latest
}


create_plain_auth_file \
  "${ENV_NAME}" \
  "${MAILHOG_PASSWORD}" \
  "${MAILHOG_PLAIN_AUTH_FILE_PATH}"
crypt_passwords_in_auth_file \
  "${MAILHOG_PLAIN_AUTH_FILE_PATH}" \
  "${MAILHOG_AUTH_FILE_PATH}"
run_mailhog_container \
  "${ENV_NAME}" \
  "${ENV_HOSTNAME}" \
  "${MAILHOG_WEB_UI_PORT}" \
  "${MAILHOG_SMTP_SERVER_PORT}"
