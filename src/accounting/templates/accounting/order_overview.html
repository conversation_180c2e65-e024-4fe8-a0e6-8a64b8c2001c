{% extends "admin/change_form.html" %}
{% load i18n %}
{% load admin_urls %}
{% load activity_tags %}
{% load static %}
{% load humanize %}

{% block extrahead %}

    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">

    <link href="{% static 'css/bootcards-desktop.css' %}" rel="stylesheet">

    <script src="{% static 'js/bootcards.min.js' %}"></script>

    <!-- MetisMenu CSS -->
    <link href="{% static 'css/plugins/metisMenu/metisMenu.min.css' %}" rel="stylesheet">

    <!-- Timeline CSS -->
    <link href="{% static 'css/plugins/timeline.css' %}" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'css/sb-admin-2.css' %}" rel="stylesheet">

    <!-- <PERSON> Charts CSS -->
    <link href="{% static 'css/plugins/morris.css' %}" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="{% static 'font-awesome-4.1.0/css/font-awesome.min.css' %}" rel="stylesheet" type="text/css">
    <script src="{% static 'js/plugins/morris/raphael.min.js' %}"></script>
    <script src="{% static 'js/plugins/morris/morris.js' %}"></script>
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
        <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
        <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
    .bg-danger-important {
        background-color: #f2dede !important;
    }
    </style>
{% endblock %}

{% block breadcrumbs %}
    <div class="breadcrumbs">
        <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a> &rsaquo;
        <a href="../">{{ app_label|capfirst|escape }}</a> &rsaquo;
        {{ opts.verbose_name|capfirst }}
    </div>
{% endblock %}

{% block content %}
    <div class="container bootcards-container push-right">


        <!-- This is where you come in... -->
        <!-- I've added some sample data below so you can get a feel for the required markup -->

    <div class="row">
    <div class="col-sm-12" style="padding: 0px;">
        <a href='{% url 'admin:order-overview' prev %}' class="pull-left btn btn-default btn-lg">Previous - Order {{ prev }} </a>
        <a href='{% url 'admin:order-overview' next %}'  class="pull-right btn btn-default btn-lg">Next - Order {{ next }} </a>
    </div>
    <div class="col-sm-12" style="padding: 0px;">
    <nav class="navbar navbar-default">
  <div class="container-fluid">
    <div class="">
      <ul class="nav navbar-nav navbar-left">
            <li><p class="navbar-brand">{{ this }} / {{ list_length }} in {{ list_type }}</p></li>
    </div><!-- /.navbar-collapse -->
  </div><!-- /.container-fluid -->
</nav>
    </div>

        <div class="row">

            <div class="col-sm-6 bootcards-cards hidden-xs">

                <!--contact details -->
                <div id="contactCard">

                    <div class="panel panel-default">
                        <div class="panel-heading clearfix">
                            <h3 class="panel-title pull-left">Order summary for order : {{ original.id }}</h3>
                        </div>
                        <div class="list-group">
                            <div class="list-group-item">
                                <label>Name</label>
                                <h4 class="list-group-item-heading"><a href="/admin/user_profile/userprofile/{{ original.owner.profile.id }}/">{{ original.get_customer_as_string }}</a></h4>
                            </div>
                            <div class="list-group-item">
                                <label>Country</label>
                                <h4 class="list-group-item-heading">{{ original.get_country }}</h4>
                            </div>
                            <div class="list-group-item">
                                <label>Email</label>
                                <h4 class="list-group-item-heading">{{ original.email }}</h4>
                            </div>
                            <div class="list-group-item">
                                <label>Order type</label>
                                <h4 class="list-group-item-heading">{{ original.get_order_type_display }}</h4>
                            </div>

                            <div class="list-group-item">
                                <label>Total value</label>
                                <h4 class="list-group-item-heading">{{ original.get_total_price }}</h4>
                            </div>

                            <div class="list-group-item">
                                <label>Discount amount</label>
                                {% if original.region_promo_amount > 0 %}
                                <h4 class="list-group-item-heading">{{ original.promo_text|default_if_none:"" }} {{ original.region_promo_amount }} €</h4>
                                {% else %}
                                    <h4 class="list-group-item-heading">0 €</h4>
                                {% endif %}
                            </div>

                            <div class="list-group-item">
                                <label>Created at</label>
                                <h4 class="list-group-item-heading">{{ original.created_at }}</h4>
                            </div>

                            <div class="list-group-item">
                                <label>Paid at</label>
                                <h4 class="list-group-item-heading">{{ original.paid_at }}</h4>
                            </div>

                            <div class="list-group-item">
                                <label>Chosen payment method</label>
                                <h4 class="list-group-item-heading">{{ original.chosen_payment_method }}</h4>
                            </div>

                            <div class="list-group-item">
                                <label>All payment actions summary</label>
                                <table style="width: 100%;">
                                    <tbody>
                                    <tr>
                                        <th rowspan="1" colspan="1"><p><strong>Date</strong></p></th>
                                        <th rowspan="1" colspan="1"><p><strong>Payment type</strong></p></th>
                                        <th rowspan="1" colspan="1"><p><strong>Refusal reason or SUCCESS</strong></p></th>
                                    </tr>
                                    {% for transaction_description in original.transaction_description %}
                                        <tr>
                                            <td rowspan="1" colspan="1"><p>{{ transaction_description.date }}</p></td>
                                            <td rowspan="1" colspan="1">
                                                <p>{{ transaction_description.payment_method }}</p></td>
                                            <td rowspan="1" colspan="1">
                                                {% if transaction_description.success %}
                                                    <p class="status_color_3">PAID</p>
                                                {% else %}
                                                    <p>{{ transaction_description.refusal_reason }}</p>
                                                {% endif %}
                                            </td>

                                    {% endfor %}
                                    </tbody>
                                </table>
                            </div>

                            <div class="list-group-item">
                                <label>Ab tests</label>
                                <h4 class="list-group-item-heading"><pre>{{ original.ab_tests }}</pre></h4>
                            </div>

                            <div class="list-group-item">
                                <label>Order Log history</label>
                                {% for log in original.match_log %}
                                    <h4 class="list-group-item-heading"><a href="/admin/logger/log/{{ log.id }}">{{ log.action }} {{ log.user }}</a></h4>
                                {% endfor %}
                            </div>

                            <div class="list-group-item">
                                <label>Link to filtered lists (for actions)</label>
                                <h4 class="list-group-item-heading">
                                    <a href="{% url "admin:orders_order_changelist" %}?id={{ original.id }}">Orders</a>
                                </h4>
                                <h4 class="list-group-item-heading">
                                    <a href="{% url "admin:invoice_invoice_changelist" %}?order_id={{ original.id }}">Invoices</a>
                                </h4>
                                {% if original.earliest_invoice_domestic_version_supported %}
                                    <h4 class="list-group-item-heading">
                                        <a href="{% url "admin:invoice_invoicedomestic_changelist" %}?order_id={{ original.id }}">Domestic Invoices</a>
                                    </h4>
                                {% endif %}
                                <h4 class="list-group-item-heading">
                                    <a href="{% url "admin:producers_product_changelist" %}?order_id={{ original.id }}">Products</a>
                                </h4>
                            </div>
                        </div>

                        <div class="panel-footer">
                            <a class="btn btn-link btn-xs pull-right" href="/admin/orders/order/{{ original.id }}/">
                                <small class="pull-left">Go to order edit/profile view</small>
                                </a>
                        </div>
                    </div>

                </div><!--contact card-->
            </div><!--list-details-->

            <div class="col-sm-6 bootcards-cards hidden-xs">

                <!--contact details -->
                <div id="contactHistoryCard">

                    <div class="panel panel-default">
                        <div class="panel-heading clearfix">
                            <h3 class="panel-title pull-left">Connected items</h3>
                        </div>
                        {% for product in original.product_set.all %}
                        <div class="list-group">
                            <div class="list-group-item">
                                <label><a href="/admin/producers/product/{{ product.id }}/">Product: {{ product.id }} @ {{ product.manufactor }} - added {{ product.created_at}}</a></label>
                                <p>{{ product.get_status_display }}</p>
                                <p>Priority (empty=normal): {{ product.get_priority_display|default:"NORMAL" }}</p>

                                <table class="table table-condensed">
                                    <thead>
                                    <tr><td>Log action history:</td><td>Author</td></tr>
                                    </thead>
                                    <tbody>

                                    {% for log in product.match_log %}
                                           <tr><td><a href="/admin/logger/log/{{ log.id }}">{{ log.action }}</td><td>{{ log.user }}</td></tr>
                                    {% endfor %}
                                    </tbody>
                                    </table>
                            </div>
                        </div>
                        {% empty %}
                        <div class="list-group">
                            <div class="list-group-item">
                                <label>Missing products in production</label>
                            </div>
                        </div>
                        {% endfor %}
                        {% for invoice in original.invoice_set.all %}
                        <div class="list-group">
                            <div class="list-group-item">
                                {% with invoice_dict=invoice.to_dict %}
                                <label> <a href="/admin/invoice/invoice/{{ invoice.id }}/">Invoice {{ invoice.pretty_id }}<br/> issued at: {{ invoice_dict.issued_at }}<br/> Sell at: {{ invoice_dict.sell_at }}</a>
                                <br/>{% if invoice.pdf %}<a href="{{ invoice.pdf.url }}">PDF</a>{% else %} missing pdf{% endif %}</label>
                                    <br/>
                                    <table class="table table-condensed">
                                    <thead>
                                    <tr><td>$$$</td><td>Netto</td><td>VAT</td><td>Brutto</td></tr>
                                    </thead>
                                    <tbody>
                                    <tr><td>{{ invoice.currency_symbol }}</td><td>{{ invoice_dict.net_value }}</td><td>{{ invoice_dict.vat_value }}</td><td>{{ invoice_dict.total_value }}</td></tr>
                                    <tr><td>pln</td><td>{{ invoice_dict.net_value_in_pln }}</td><td>{{ invoice_dict.vat_in_pln }}</td><td>{{ invoice_dict.total_value_in_pln }}</td></tr>
                                    <tr><td>Correction diffs</td></tr>
                                    {% if invoice.status == 4 %}
                                        {% with correction_dict=invoice.to_diff_dict %}
                                            <tr><td>euro</td><td>{{ correction_dict.net_value }}</td><td>{{ correction_dict.vat_value }}</td><td>{{ correction_dict.total_value }}</td></tr>
                                            <tr><td>pln</td><td>{{ correction_dict.net_value_in_pln }}</td><td>{{ correction_dict.vat_in_pln }}</td><td>{{ correction_dict.total_value_in_pln }}</td></tr>
                                        {% endwith %}
                                    {% endif %}
                                    </tbody>
                                    </table>
                                    <table class="table table-condensed">
                                    <thead>
                                    <tr><td>Log action history:</td><td>Author</td></tr>
                                    </thead>
                                    <tbody>

                                    {% for log in invoice.match_log %}
                                           <tr><td><a href="/admin/logger/log/{{ log.id }}">{{ log.action }}</td><td>{{ log.user }}</td></tr>
                                    {% endfor %}
                                    </tbody>
                                    </table>
                                {% endwith %}
                            </div>
                        </div>
                        {% empty %}
                        <div class="list-group">
                            <div class="list-group-item">
                                <label>Missing invoices</label>
                            </div>
                        </div>
                        {% endfor %}
                        {% for mt in original.moneytransfer_set.all %}
                        <div class="list-group">
                            <div class="list-group-item">
                                <label> <a href="/admin/accounting/moneytransfer/{{ mt.id }}/">Money transfer: {{ mt.get_payment_source_display }} {{ mt.get_payment_type_display }} <br/> Issued at: {{ mt.issue_date }}</a></label>
                                <p>Amount: {{ mt.amount }} {{ mt.get_currency_display }}</p>
                            </div>
                        </div>
                        {% empty %}
                        <div class="list-group">
                            <div class="list-group-item">
                                <label>Missing money tranfers</label>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div><!--contact card-->

            <div id="contactHistoryCard">

                    <div class="panel panel-default">
                        <div class="panel-heading clearfix">
                            <h3 class="panel-title pull-left">Checks</h3>
                        </div>
                        <div class="list-group {% if check_totals.is_ok == False %}bg-danger-important{% endif %}">
                            <div class="list-group-item {% if check_totals.is_ok == False %}bg-danger-important{% endif %}">
                                <label>Invoices vs money transfers - total</label>
                                <p>{{ check_totals.reason|safe }}</p>
                            </div>
                        </div>
                        <div class="list-group {% if check_dates.is_ok == False %}bg-danger-important{% endif %}">
                            <div class="list-group-item {% if check_dates.is_ok == False %}bg-danger-important{% endif %}">
                                <label>Money transfer vs invoice(with correction)sale data check</label>
                                <p>{{ check_dates.reason|safe }}</p>
                                {% for pair in check_dates.matched  %}
                                    <p >Matched: {{ pair.0.pretty_id }} with {{ pair.1.issue_date }} for {{ pair.1.amount }} {{ pair.1.get_currency_display }}</p>
                                {% endfor %}
                            </div>
                        </div>

                    </div>
                </div><!--contact card-->

            </div><!--list-details-->

        </div><!--row-->
        <div class="row">

            <div class="col-sm-12 bootcards-cards hidden-xs">

                <!--contact details -->
                <div id="CartList">

                    <div class="panel panel-default">
                        <div class="panel-heading clearfix">
                            <h3 class="panel-title pull-left">Items in order</h3>
                        </div>
                        <div class="list-group">
                            <a class="list-group-item" href="/admin/orders/order/{{ original.id }}/">
                                <div class="row">
                                    <div class="col-sm-3">
                                        <p class="list-group-item-text"> {{ original.created_at|naturalday }}</p>
                                        <p class="list-group-item-text">Order: <span class="btn btn-success">{{ original.get_status_display }}</span</p>
                                        <p class="list-group-item-text">Production: {{ original.get_status_display }}</p>
                                    </div>
                                    <div class="col-sm-3">
                                        <p class="list-group-item-text"> Total: {{ original.get_base_total_value }} €</p>
                                        <p class="list-group-item-text"> Items: {{ original.get_items_as_string|safe }}</p>
                                    </div>
                                    <div class="col-sm-6">
                                        {% for item in original.items.all %}
                                            <img style="width: 135px; height:100px" src="{{ item.order_item.preview | file_url }}">
                                        {% endfor %}
                                    </div>
                                </div>
                            </a>
                        </div>

                        <div class="panel-footer">
                            <a class="btn btn-link btn-xs pull-right" href="/admin/orders/order/">
                                <small class="pull-left">Go to orders</small>
                            </a>
                        </div>
                    </div>

                </div><!--contact card-->
            </div><!--list-details-->
        </div>
        {% comment %}
        <div class="row">

            <div class="col-sm-12 bootcards-cards hidden-xs">

                <!--contact details -->
                <div id="CartList">

                    <div class="panel panel-default">
                        <div class="panel-heading clearfix">
                            <h3 class="panel-title pull-left">Items in order</h3>
                        </div>
                        <div class="list-group">
                            <a class="list-group-item" href="/admin/orders/order/{{ order.id }}/">
                                <div class="row">
                                    <div class="col-sm-3">
                                        <p class="list-group-item-text"> {{ original.created_at|naturalday }}</p>
                                        <p class="list-group-item-text">Order: <span class="btn btn-success">{{ original.get_status_display }}</span</p>
                                        <p class="list-group-item-text">Production: {{ original.get_status_display }}</p>
                                    </div>
                                    <div class="col-sm-3">
                                        <p class="list-group-item-text"> Total: {{ original.total_price }} €</p>
                                        <p class="list-group-item-text"> Items: {{ original.get_items_as_string|safe }}</p>
                                    </div>
                                    <div class="col-sm-6">
                                        {% for item in original.items.all %}
                                            <img style="width: 135px; height:100px" src="/uploaded/{{ item.order_item.preview }}">
                                        {% endfor %}
                                    </div>
                                </div>
                            </a>
                        </div>

                        <div class="panel-footer">
                            <a class="btn btn-link btn-xs pull-right" href="/admin/auth/orders/">
                                <small class="pull-left">Go to orders</small>
                            </a>
                        </div>
                    </div>

                </div><!--contact card-->
            </div><!--list-details-->
        </div>
{% endcomment %}
    </div><!--container-->

{% endblock %}
