from typing import ClassVar

from django.contrib import admin
from django.urls import path


class ButtonActionsBaseAdmin(admin.ModelAdmin):

    change_list_template = 'admin_custom/list_view_with_button_actions.html'
    button_actions: ClassVar[tuple[str]] = ()

    def changelist_view(self, request, extra_context=None):
        """Add button actions to context."""
        extra_context = extra_context or {}
        button_actions_context = [
            (button_action, getattr(self, button_action).short_description)
            for button_action in self.button_actions
        ]
        return super().changelist_view(
            request,
            extra_context={'button_actions': button_actions_context} | extra_context,
        )

    def get_urls(self):
        """Add button actions to urls."""
        urls = super().get_urls()
        button_actions_urls = [
            path(f'{button_action}/', getattr(self, button_action))
            for button_action in self.button_actions
        ]
        return button_actions_urls + urls
