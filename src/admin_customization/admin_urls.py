from django.contrib.admin import AdminSite
from django.urls import (
    path,
    re_path,
)

from admin_customization.views import (
    AdminSearchView,
    AnomalyChecksView,
    BatchingToolVueView,
    DashboardWelcomeView,
    DeeplTranslationView,
    ImportJettyWattyRedirectView,
    ImportObjectsView,
    JsonProductionView,
    KPIsView,
    MailingListView,
    MassRandomFurnitureCreator,
    OffscreenRendererView,
    QueueStatusView,
    RevPerDayView,
    ShortUrlAddView,
    TestMailingFlowsView,
    mailing_preview_view,
    test_template,
)


class AdminCustomizationAdminPage(AdminSite):
    def get_admin_customization_admin_urls(self):
        return [
            path(
                'search/',
                self.admin_view(AdminSearchView.as_view()),
                name='admin-search',
            ),
            path(
                'anomaly_checks/',
                self.admin_view(AnomalyChecksView.as_view()),
                name='anomaly_checks',
            ),
            re_path(
                r'^batching_tool_vue/?$',
                self.admin_view(BatchingToolVueView.as_view()),
                name='batching_tool',
            ),
            path(
                '',
                self.admin_view(DashboardWelcomeView.as_view(), cacheable=True),
                name='index',
            ),
            path(
                'create/jetty/',
                ImportJettyWattyRedirectView.as_view(),
                name='create_jetty',
            ),
            path(
                'create/watty/',
                ImportJettyWattyRedirectView.as_view(),
                name='create_watty',
            ),
            path(
                'create_objects/',
                ImportObjectsView.as_view(),
                name='create_objects',
            ),
            path(
                'json_production/<int:shelf_id>/',
                self.admin_view(JsonProductionView.as_view()),
                name='admin_json_production_view',
            ),
            path(
                'json_production/<int:shelf_id>/<str:mode>/',
                self.admin_view(JsonProductionView.as_view()),
                name='admin_json_production_view',
            ),
            re_path(
                r'kpi/(?P<board_name>[A-Za-z0-9_]+)/'
                r'(?P<time_interval>(daily|weekly|monthly))/',
                self.admin_view(KPIsView.as_view()),
                name='kpis',
            ),
            path(
                'mailing_list/',
                self.admin_view(MailingListView.as_view()),
                name='mailing_list',
            ),
            path(
                'queue_status/',
                self.admin_view(QueueStatusView.as_view()),
                name='queue_status',
            ),
            path(
                'report_rev_per_day/',
                self.admin_view(RevPerDayView.as_view()),
                name='report_rev_per_day',
            ),
            path(
                'shorturl/',
                self.admin_view(ShortUrlAddView.as_view()),
                name='shorturl_add',
            ),
            path(
                'mailing_flow_test/',
                self.admin_view(TestMailingFlowsView.as_view()),
                name='mailing_flow_test',
            ),
            re_path(
                r'mailing_preview/(?P<mailing_name>[A-Za-z0-9]+)/'
                r'(?P<language>[a-z]{2})/?$',
                self.admin_view(mailing_preview_view),
                name='mailing_preview',
            ),
            re_path(
                r'test_template/(?P<mailing_name>[A-Za-z0-9]+)/',
                self.admin_view(test_template),
                name='test_template',
            ),
            re_path(
                r'mailing_test/(?P<mailing_name>[A-Za-z0-9]+)/'
                r'(?P<language>[a-z]{2})/?$',
                self.admin_view(test_template),
                name='mailing_test',
            ),
            path(
                'offscreen_renderer/',
                self.admin_view(OffscreenRendererView.as_view()),
                name='offscreen_renderer',
            ),
            path(
                'translate_with_deepl/',
                self.admin_view(DeeplTranslationView.as_view()),
                name='translate_with_deepl',
            ),
            path(
                'generate_furniture/',
                self.admin_view(MassRandomFurnitureCreator.as_view()),
                name='generate_furniture',
            ),
        ]


urlpatterns = AdminCustomizationAdminPage().get_admin_customization_admin_urls()
