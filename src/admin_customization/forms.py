from django import forms
from django.contrib.admin.widgets import AdminSplitDateTime
from django.core.validators import FileExtensionValidator


class ShortUrlAddForm(forms.Form):
    name = forms.CharField(max_length=64, required=True)
    description = forms.CharField(max_length=128)
    url = forms.CharField(max_length=1024)


class UploadFileForm(forms.Form):
    file = forms.FileField()


class ImportGalleryForm(forms.Form):
    file = forms.FileField(
        validators=[
            FileExtensionValidator(
                allowed_extensions=['json'],
                message='You can only upload files with json extensions',
            )
        ]
    )
    process_to_production = forms.BooleanField(required=False)
    is_influencers = forms.BooleanField(required=False)


class RunFlowsWithTimeForm(forms.Form):
    run_flows_with_date = forms.SplitDateTimeField(
        required=True,
        widget=AdminSplitDateTime(),
    )


class DeeplTranslationForm(forms.Form):
    phrase_in_english = forms.CharField(
        max_length=2048,
        required=True,
        widget=forms.Textarea,
    )


class MassRandomFurnitureForm(forms.Form):
    quantity = forms.IntegerField()
