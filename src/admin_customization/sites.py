from functools import update_wrapper

from django.contrib.admin.sites import AdminSite
from django.urls import (
    include,
    path,
)

from admin_customization.admin_urls import urlpatterns as admin_customization_admin_urls


class SitePlus(AdminSite):
    """Mixin for AdminSite to allow custom dashboard views."""

    def get_urls(self):
        """Add our dashboard view to the admin urlconf. Deleted the default index."""

        def wrap(view, cacheable=False):
            def wrapper(*args, **kwargs):
                return self.admin_view(view, cacheable)(*args, **kwargs)

            return update_wrapper(wrapper, view)

        urls = super().get_urls()
        del urls[0]
        custom_url = [
            path(
                'accounting/',
                include('accounting.urls'),
            ),
            path(
                'all_index/',
                wrap(self.index),
                name='admin_all_index',
            ),
            path('', include('b2b.admin_urls')),
            path('', include('custom_audiences.admin_urls')),
            path('', include('producers.admin_urls')),
            path('', include('production_margins.admin_urls')),
            path('', include('reviews.admin_urls')),
            path('', include('warehouse.admin_urls')),
            # TODO Add admin_customization_admin_urls as a string without import it
            # will raise KeyError: 'customer_service.kpis'
            path('', include(admin_customization_admin_urls)),
        ]
        return custom_url + urls
