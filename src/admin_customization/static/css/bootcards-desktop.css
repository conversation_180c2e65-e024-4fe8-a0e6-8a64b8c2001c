@charset "UTF-8";
/* NAVBAR */
/* OFFCANVAS MENU */
.offcanvas,
.offcanvas-list {
  position: fixed;
  top: 0;
  bottom: 0;
  width: 200px;
  overflow-y: auto;
  z-index: 1050;
  height: 100%;
  -webkit-transform: translate3d(0px, 0px, 0px);
  -moz-transform: translate3d(0px, 0px, 0px);
  -o-transform: translate3d(0px, 0px, 0px);
  -ms-transform: translate3d(0px, 0px, 0px);
  transform: translate3d(0px, 0px, 0px);
  -webkit-transition: 0.25s ease;
  -moz-transition: 0.25s ease;
  -o-transition: 0.25s ease;
  transition: 0.25s ease; }

.offcanvas-left {
  left: -200px; }
  .offcanvas-left.active {
    -webkit-transform: translate3d(200px, 0px, 0px);
    -moz-transform: translate3d(200px, 0px, 0px);
    -o-transform: translate3d(200px, 0px, 0px);
    -ms-transform: translate3d(200px, 0px, 0px);
    transform: translate3d(200px, 0px, 0px); }

/* OFFCANVAS LIST FOR TABLET PORTRAIT MODE */
.offcanvas-list {
  width: 350px;
  left: -350px; }
  .offcanvas-list.active {
    -webkit-transform: translate3d(350px, 0px, 0px);
    -moz-transform: translate3d(350px, 0px, 0px);
    -o-transform: translate3d(350px, 0px, 0px);
    -ms-transform: translate3d(350px, 0px, 0px);
    transform: translate3d(350px, 0px, 0px); }

.offcanvas-list-title {
  position: fixed;
  background: rgba(247, 247, 247, 0.98);
  height: 45px;
  font-size: 17px;
  font-weight: 500;
  line-height: 44px;
  border-right: 1px solid rgba(0, 0, 0, 0.2);
  border-bottom: 1px solid rgba(0, 0, 0, 0.2); }

.offcanvaslist-toggle {
  -webkit-transform: translate3d(0px, 0px, 0px);
  -moz-transform: translate3d(0px, 0px, 0px);
  -o-transform: translate3d(0px, 0px, 0px);
  -ms-transform: translate3d(0px, 0px, 0px);
  transform: translate3d(0px, 0px, 0px);
  -webkit-transition: 0.25s ease;
  -moz-transition: 0.25s ease;
  -o-transition: 0.25s ease;
  transition: 0.25s ease; }
  .offcanvaslist-toggle.active {
    -webkit-transform: translate3d(50px, 0px, 0px);
    -moz-transform: translate3d(50px, 0px, 0px);
    -o-transform: translate3d(50px, 0px, 0px);
    -ms-transform: translate3d(50px, 0px, 0px);
    transform: translate3d(50px, 0px, 0px); }

.push-right {
  -webkit-transform: translate3d(0px, 0px, 0px);
  -moz-transform: translate3d(0px, 0px, 0px);
  -o-transform: translate3d(0px, 0px, 0px);
  -ms-transform: translate3d(0px, 0px, 0px);
  transform: translate3d(0px, 0px, 0px);
  -webkit-transition: 0.25s ease;
  -moz-transition: 0.25s ease;
  -o-transition: 0.25s ease;
  transition: 0.25s ease; }
  .push-right.active-left {
    -webkit-transform: translate3d(200px, 0px, 0px);
    -moz-transform: translate3d(200px, 0px, 0px);
    -o-transform: translate3d(200px, 0px, 0px);
    -ms-transform: translate3d(200px, 0px, 0px);
    transform: translate3d(200px, 0px, 0px); }
  .push-right.active-right {
    -webkit-transform: translate3d(-200px, 0px, 0px);
    -moz-transform: translate3d(-200px, 0px, 0px);
    -o-transform: translate3d(-200px, 0px, 0px);
    -ms-transform: translate3d(-200px, 0px, 0px);
    transform: translate3d(-200px, 0px, 0px); }

/* BUTTONS */
.btn i {
  margin-right: 5px; }

.btn.icon-only i,
.btn.icononly i {
  margin-right: 0; }

/* LISTS */
.list-group-item {
  margin-left: 15px;
  padding-left: 0; }
  .list-group-item .list-group-item-text {
    line-height: 18px;
    margin-bottom: 5px;
    overflow: hidden; }
  .list-group-item .list-group-item-heading:last-child,
  .list-group-item .list-group-item-text:last-child {
    margin-bottom: 0; }
  .list-group-item .row div > .list-group-item-heading:last-child,
  .list-group-item .row div > .list-group-item-text:last-child {
    margin-bottom: 5px; }
  .list-group-item .row div:last-child > .list-group-item-heading:last-child,
  .list-group-item .row div:last-child > .list-group-item-text:last-child {
    margin-bottom: 0; }
  .list-group-item img {
    height: 40px;
    width: 40px;
    margin-right: 15px; }
  .list-group-item i {
    opacity: 0.3;
    width: 40px;
    text-align: center;
    margin-right: 15px; }

a.list-group-item {
  cursor: pointer; }
  a.list-group-item:hover, a.list-group-item:active, a.list-group-item.active, a.list-group-item.active:hover, a.list-group-item.active:focus {
    margin-left: 0;
    padding-left: 15px; }

.panel > .list-group .list-group-item:last-child {
  border-bottom: 0; }

/* List Images */
.bootcards-list-group-item-content {
  overflow: hidden; }

/* Disclosure Indicators */
a.list-group-item:before {
  font-family: 'FontAwesome';
  content: '';
  position: absolute;
  right: 15px;
  top: 50%;
  font-size: 14px;
  line-height: 14px;
  margin-top: -7px;
  color: #ccc; }

.list-group.bootcards-no-indicators a.list-group-item:before {
  display: none; }

/* List - Subheadings */
.list-group-item.bootcards-list-subheading {
  margin-left: 0;
  padding-left: 15px;
  font-weight: 500;
  font-size: 14px;
  z-index: 6; }

a.list-group-item.bootcards-list-subheading {
  padding-left: 40px; }
  a.list-group-item.bootcards-list-subheading:before {
    font-family: 'FontAwesome';
    content: '';
    position: absolute;
    left: 15px;
    top: 50%;
    font-size: 14px;
    line-height: 14px;
    margin-top: -7px;
    color: #ccc; }
  a.list-group-item.bootcards-list-subheading.collapsed:before {
    content: ''; }

/* List - AZ Picker */
.bootcards-az-picker {
  position: fixed;
  width: 25px;
  margin: 0 0 0 -26px;
  padding: 0;
  list-style: none;
  z-index: 9;
  top: 0;
  right: 0;
  bottom: 0;
  height: 100%;
  background: #ffffff;
  padding-bottom: 50px;
  padding-top: 5px; }
  .bootcards-az-picker li {
    font-size: 11px;
    font-weight: 500;
    text-align: center;
    padding: 0;
    background: #ffffff;
    height: 3.846%;
    /* 100% / 26 */ }
  .bootcards-az-picker a {
    display: -webkit-box;
    display: -webkit-flex;
    display: -moz-box;
    display: -ms-flexbox;
    display: flex;
    -webkit-box-align: center;
    -webkit-align-items: center;
    -moz-box-align: center;
    -ms-flex-align: center;
    align-items: center;
    -webkit-box-pack: center;
    -webkit-justify-content: center;
    -moz-box-pack: center;
    -ms-flex-pack: center;
    justify-content: center;
    height: 100%; }
    .bootcards-az-picker a:hover {
      text-decoration: none; }
  @media only screen and (min-device-width: 480px) and (max-device-width: 767px) and (orientation: landscape) {
    .bootcards-az-picker li {
      height: 7.692%;
      /* 100% / 13 */ }
      .bootcards-az-picker li:nth-child(even) {
        display: none;
        /* hide every other letter on smartphones/landscape */ } }

/* List - Search Form */
.bootcards-list .form-group {
  position: relative;
  margin-bottom: 0; }
  @media (min-width: 768px) {
    .bootcards-list .form-group {
      margin-bottom: 0; } }
.bootcards-list form input,
.bootcards-list .search-form input {
  padding-left: 32px; }
.bootcards-list form .btn,
.bootcards-list .search-form .btn {
  color: white; }
.bootcards-list form i.fa-search,
.bootcards-list .search-form i.fa-search {
  position: absolute;
  left: 12px;
  top: 10px;
  color: #999;
  font-size: 14px; }

/* FOOTER */
.bootcards-desktop-footer {
  display: none; }

/* CARDS */
.panel-default .panel-heading {
  color: #333;
  background-color: #f5f5f5;
  border-color: #ddd; }

.panel-body > *:last-child {
  margin-bottom: 0; }

.panel-footer {
  overflow: hidden; }

.panel-footer small,
.modal-footer small {
  color: #aaa;
  display: block;
  text-align: center;
  line-height: 22px;
  /* Line up vertically with .btn */ }

.list-group + .panel-footer {
  border-top-width: 1px;
  /* ML: panel-footer has a top border, but that's removed by Bootstrap if there's a list-group before it. We restore it here. */ }

/* FORM CARD */
.btn label {
  margin: 0;
  font-weight: inherit;
  cursor: pointer; }

.bootcards-clearinput {
  position: absolute;
  right: 0;
  top: 11px;
  color: #ccc;
  display: block;
  font-size: 0; }

.bootcards-clearinput i {
  line-height: 1;
  font-size: 18px; }

.form-horizontal .form-group div div {
  padding: 0; }

/* Toggle – based on Ratchet.css */
.bootcards-toggle {
  position: relative;
  display: block;
  width: 74px;
  height: 30px;
  background-color: #fff;
  border: 2px solid #ddd;
  border-radius: 20px;
  -webkit-transition-duration: .2s;
  -moz-transition-duration: .2s;
  transition-duration: .2s;
  -webkit-transition-property: background-color, border;
  -moz-transition-property: background-color, border;
  transition-property: background-color, border; }

.bootcards-toggle .bootcards-toggle-handle {
  position: absolute;
  top: -1px;
  left: -1px;
  z-index: 2;
  width: 28px;
  height: 28px;
  background-color: #fff;
  border: 1px solid #ddd;
  border-radius: 100px;
  -webkit-transition-duration: .2s;
  -moz-transition-duration: .2s;
  transition-duration: .2s;
  -webkit-transition-property: -webkit-transform, border, width;
  -moz-transition-property: -moz-transform, border, width;
  transition-property: transform, border, width; }

.bootcards-toggle:before {
  position: absolute;
  top: 3px;
  right: 11px;
  font-size: 13px;
  color: #999;
  text-transform: uppercase;
  content: "Off"; }

.bootcards-toggle.active {
  background-color: #5cb85c;
  border: 2px solid #5cb85c; }

.bootcards-toggle.active .bootcards-toggle-handle {
  border-color: #5cb85c;
  -webkit-transform: translate3d(44px, 0, 0);
  -ms-transform: translate3d(44px, 0, 0);
  transform: translate3d(44px, 0, 0); }

.bootcards-toggle.active:before {
  right: auto;
  left: 15px;
  color: #fff;
  content: "On"; }

.bootcards-toggle input[type="checkbox"] {
  display: none; }

/* CALENDAR */
.bootcards-calendar {
  margin-top: -15px;
  background: white; }

.bootcards-calendar .fc-header-left,
.bootcards-calendar .fc-header-right {
  padding: 15px; }

.bootcards-calendar .fc-header-title h2 {
  margin: 0; }

.bootcards-calendar .fc-content th:first-child,
.bootcards-calendar .fc-content td:first-child {
  border-left-width: 0; }

.bootcards-calendar .fc-content th:last-child,
.bootcards-calendar .fc-content td:last-child {
  border-right-width: 0; }

.bootcards-calendar .fc-header-title h2 {
  font-size: 14px;
  text-transform: uppercase;
  color: #8f8f94;
  font-weight: 400;
  margin: 0;
  padding: 8px 0;
  line-height: 20px; }

.bootcards-calendar .fc-header-left,
.bootcards-calendar .fc-header-right {
  padding: 0 10px; }

.bootcards-calendar .fc-button .fc-icon {
  margin: 0; }

.bootcards-calendar .fc-header .fc-button {
  background: transparent;
  box-shadow: none;
  border-color: transparent;
  margin: 0;
  height: 44px;
  line-height: 40px;
  font-size: 16px;
  text-shadow: none;
  padding: 0; }

.bootcards-calendar .fc-header-left .fc-button {
  margin-right: 10px; }

.bootcards-calendar .fc-header-right .fc-button {
  margin-left: 10px; }

.bootcards-calendar .fc-icon-left-single-arrow:after,
.bootcards-calendar .fc-icon-right-single-arrow:after {
  font-family: 'FontAwesome';
  font-size: 18px;
  font-weight: normal; }

.bootcards-calendar .fc-icon-left-single-arrow:after {
  content: ''; }

.bootcards-calendar .fc-icon-right-single-arrow:after {
  content: ''; }

.bootcards-calendar .fc-header .fc-button.fc-state-disabled {
  color: #AAA;
  opacity: 1; }

.bootcards-calendar .fc-header .fc-button.fc-state-active {
  color: #AAA; }

.bootcards-calendar .fc-day-header {
  font-weight: 400;
  font-size: 12px;
  border-left-color: transparent;
  padding: 5px 0;
  padding-right: 3px; }

.bootcards-calendar .fc-event {
  background: #007aff;
  border-color: #007aff; }

.bootcards-calendar .fc-today {
  background: #FFF2F2; }

/* TABLE CARD */
.table > thead > tr > th:first-child,
.table > tbody > tr > th:first-child,
.table > tfoot > tr > th:first-child,
.table > thead > tr > td:first-child,
.table > tbody > tr > td:first-child,
.table > tfoot > tr > td:first-child {
  padding-left: 15px; }

.table > thead > tr > th:last-child,
.table > tbody > tr > th:last-child,
.table > tfoot > tr > th:last-child,
.table > thead > tr > td:last-child,
.table > tbody > tr > td:last-child,
.table > tfoot > tr > td:last-child {
  padding-right: 15px; }

/* FILE CARD */
@font-face {
  font-family: 'icomoon';
  src: url("../fonts/icomoon.eot?-n2q9vw");
  src: url("../fonts/icomoon.eot?#iefix-n2q9vw") format("embedded-opentype"), url("../fonts/icomoon.woff?-n2q9vw") format("woff"), url("../fonts/icomoon.ttf?-n2q9vw") format("truetype"), url("../fonts/icomoon.svg?-n2q9vw#icomoon") format("svg");
  font-weight: normal;
  font-style: normal; }
[class^="icon-"], [class*=" icon-"] {
  font-family: 'icomoon';
  speak: none;
  font-style: normal;
  font-weight: normal;
  font-variant: normal;
  text-transform: none;
  line-height: 1;
  /* Better Font Rendering =========== */
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale; }

.icon-file-pdf:before {
  content: "\e4e2"; }

.icon-file-word:before {
  content: "\e4e4"; }

.icon-file-excel:before {
  content: "\e4e5"; }

.icon-file-powerpoint:before {
  content: "\e4e7"; }

.icon-file:before {
  content: "\e08d"; }

.bootcards-file .list-group-item:first-child {
  position: relative;
  padding-left: 74px; }

.bootcards-file .list-group-item:first-child i {
  font-size: 64px;
  width: 64px;
  position: absolute;
  left: 0;
  top: 10px; }

.bootcards-file .list-group-item *:last-child {
  margin-bottom: 0; }

/* CHART CARD */
.bootcards-chart .bootcards-chart-canvas {
  height: 200px; }

/* SUMMARY CARD */
.bootcards-summary .panel-body {
  padding: 7px 25px; }

.bootcards-summary .panel-body > .row > div {
  padding: 8px; }

.bootcards-summary-item {
  background: #f5f5f5;
  display: block;
  border-radius: 4px;
  padding: 25px 10px;
  text-align: center;
  position: relative;
  height: 130px; }

@media (max-width: 400px) {
  .bootcards-summary-item {
    padding: 15px 5px; } }
.bootcards-summary-item:hover {
  text-decoration: none;
  background: #eee; }

.bootcards-summary-item > i {
  color: #bbb;
  display: block;
  text-align: center;
  margin-bottom: 5px; }

.bootcards-summary-item h4 {
  margin: 0 auto; }

.bootcards-summary-item .badge,
.bootcards-summary-item .label {
  position: absolute;
  top: 10px;
  right: 10px; }

/* RICH TEXT CARD */
.bootcards-richtext > .panel-body {
  padding: 25px;
  max-width: 640px;
  margin: 0 auto; }

.bootcards-richtext > .panel-body > *:first-child {
  margin-top: 0; }

/* ALERTS & DIALOGS */
@media (max-width: 767px) {
  .modal-dialog.modal-sm {
    margin: 15px; } }
@media (min-width: 768px) {
  .modal-dialog.modal-sm {
    width: 400px; } }
body {
  /*padding-top: 80px;*/
  background: #f5f5f5; }

/* NAVBAR */
.navbar .navbar-brand {
  font-weight: bold;
  color: #333; }
.navbar .btn i {
  vertical-align: 0; }
.navbar .btn-back,
.navbar .btn-menu,
.navbar button[data-toggle="offcanvas"] {
  display: none !important; }

/* NAVBAR WITH 2 LINES OF LINKS (DESKTOP ONLY) */
@media (min-width: 768px) {
  body.has-bootcards-navbar-double {
    padding-top: 110px; }
    body.has-bootcards-navbar-double .bootcards-navbar-double .navbar-brand {
      line-height: 50px; }
    body.has-bootcards-navbar-double .bootcards-nav-secondary.navbar-nav > li > a {
      padding-top: 5px;
      padding-bottom: 5px; }
    body.has-bootcards-navbar-double .bootcards-nav-primary,
    body.has-bootcards-navbar-double .bootcards-nav-secondary {
      margin-right: -15px;
      clear: right; }
    body.has-bootcards-navbar-double .bootcards-nav-secondary,
    body.has-bootcards-navbar-double .bootcards-nav-secondary .dropdown-menu {
      font-size: 12px; } }
/* LISTS */
.list-group label {
  font-weight: normal; }

a.list-group-item.active:before {
  color: #e1edf7; }

/* List Search */
.bootcards-list .panel-body > form > .row > div:first-child,
.bootcards-list .panel-body > .search-form > .row > div:first-child {
  padding-right: 0; }

/* CARDS */
.panel-title {
  margin: 7px 0; }

.panel-content form {
  padding: 15px; }

/* MODAL */
.modal-header {
  line-height: 32px; }

.modal-title {
  text-align: center; }

/* FORMS */
.bootcards-clearinput {
  right: 23px; }

/* FOOTER */
.navbar-fixed-bottom {
  position: static;
  padding-top: 20px;
  padding-bottom: 20px;
  background: transparent; }
  .navbar-fixed-bottom .btn-group {
    display: none; }
  .navbar-fixed-bottom .bootcards-desktop-footer {
    display: block;
    text-align: center;
    color: #999; }

/* DOCUMENTATION */
.bootcards-documentation-list > .panel {
  position: fixed; }
  @media (min-width: 768px) {
    .bootcards-documentation-list > .panel {
      width: 163px; } }
  @media (min-width: 992px) {
    .bootcards-documentation-list > .panel {
      width: 213px; } }
  @media (min-width: 1200px) {
    .bootcards-documentation-list > .panel {
      width: 263px; } }

/*# sourceMappingURL=bootcards-desktop.css.map */
