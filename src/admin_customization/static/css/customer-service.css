.status_color_item_replacement, .status_color_cost_recalculation, .status_color_waiting_for_additional_payment {
    color: #BC7A00;
    font-weight: bold;
}

.order_item_target {
    color: #C20000;
}

.source-order-item {
    margin-bottom: 20px;
    opacity: 0.5;
}

.col-sm-6 .item__order_item__preview, .item__order_item__preview {
    width: 270px;
    height: 200px;
}

.source-pricing {
    background-color: #EB9486;
}

.target-pricing {
    background-color: #CAE7B9;
}

.btn-cancel {
    float: left;
    margin-right: 10px;
}

.btn-go-back {
    float: left;
}

.btn-next {
    float: right;
}

.search-submit {
    display: flex;
    justify-content: end;
    padding: 10px;
}

.search-field {
    margin: 4px 0;
}

.search-field-by-id {
    margin: 8px 0;
}

.btn-customer-paid {
    float: right;
    margin-left: 10px;
}

.bolder {
    font-weight: bolder;
}

.total-price-diff {
    text-align: center;
}

.errorlist {
    color: red;
}

.list-filter > .active > a {
    padding-left: 18px;
    font-weight: 700;
    color: #563d7c;
    background-color: transparent;
    border-left: 2px solid #563d7c;
}

.sidebar ul.list-filter li {
    border-bottom: none;
}

.nav.list-filter > li > a {
    padding: 2px 15px;
}

.complaints-list-element {
    border-bottom: 1px solid #ddd;

}

.priority-buttons {
    display: flex;
    gap: 5px;
}

.complaint-buttons {
    margin: 10px 0 10px 0;
}

.deactivate-user-account {
    margin: 20px 20px 20px 0;
}

.inline-form {
    display: inline;
}

.d-flex {
    display: flex;
    width: 100%;
    gap: 20px;
}

.d-flex-full {
    flex: 1;
}

.aligned-table th,
.aligned-table td {
    text-align: left;
}

.aligned-table th:nth-child(1),
.aligned-table td:nth-child(1) {
    width: 20%;
}

.aligned-table th:nth-child(2),
.aligned-table td:nth-child(2) {
    width: 30%;
}

.aligned-table th:nth-child(3),
.aligned-table td:nth-child(3) {
    width: 30%;
}

.aligned-table th:nth-child(4),
.aligned-table td:nth-child(4) {
    width: 20%;
}

.mt-1 {
    margin-top: 20px;
}

.btn-block-fit {
    display: block;
    width: fit-content;
    margin-top: 10px;
}
