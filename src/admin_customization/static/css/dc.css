div.dc-chart {
    float: left;
}

.dc-chart rect.bar {
    stroke: none;
    cursor: pointer;
}

.dc-chart rect.bar:hover {
    fill-opacity: .5;
}

.dc-chart rect.stack1 {
    stroke: none;
    fill: red;
}

.dc-chart rect.stack2 {
    stroke: none;
    fill: green;
}

.dc-chart rect.deselected {
    stroke: none;
    fill: #ccc;
}

.dc-chart .empty-chart .pie-slice path {
    fill: #FFEEEE;
    cursor: default;
}

.dc-chart .empty-chart .pie-slice {
    cursor: default;
}

.dc-chart .pie-slice {
    fill: white;
    font-size: 12px;
    cursor: pointer;
}

.dc-chart .pie-slice.external{
    fill: black;
}

.dc-chart .pie-slice :hover {
    fill-opacity: .8;
}

.dc-chart .pie-slice.highlight {
    fill-opacity: .8;
}

.dc-chart .pie-path {
  fill:none;
  stroke-width: 2px;
  stroke: black;
  opacity: 0.4;
}

.dc-chart .selected path {
    stroke-width: 3;
    stroke: #ccc;
    fill-opacity: 1;
}

.dc-chart .deselected path {
    stroke: none;
    fill-opacity: .5;
    fill: #ccc;
}

.dc-chart .axis path,
.dc-chart .axis line {
    fill: none;
    stroke: #000;
    shape-rendering: crispEdges;
}

.dc-chart .axis text {
    font: 10px sans-serif;
}

.dc-chart .grid-line,
.dc-chart .axis .grid-line {
    fill: none;
    stroke: #ccc;
    opacity: .5;
    shape-rendering: crispEdges;
}

.dc-chart .grid-line line,
.dc-chart .axis .grid-line line {
    fill: none;
    stroke: #ccc;
    opacity: .5;
    shape-rendering: crispEdges;
}

.dc-chart .brush rect.background {
    z-index: -999;
}

.dc-chart .brush rect.extent {
    fill: steelblue;
    fill-opacity: .125;
}

.dc-chart .brush .resize path {
    fill: #eee;
    stroke: #666;
}

.dc-chart path.line {
    fill: none;
    stroke-width: 1.5px;
}

.dc-chart circle.dot {
    stroke: none;
}

.dc-chart g.dc-tooltip path {
    fill: none;
    stroke: grey;
    stroke-opacity: .8;
}

.dc-chart path.area {
    fill-opacity: .3;
    stroke: none;
}

.dc-chart .node {
    font-size: 0.7em;
    cursor: pointer;
}

.dc-chart .node :hover {
    fill-opacity: .8;
}

.dc-chart .selected circle {
    stroke-width: 3;
    stroke: #ccc;
    fill-opacity: 1;
}

.dc-chart .deselected circle {
    stroke: none;
    fill-opacity: .5;
    fill: #ccc;
}

.dc-chart .bubble {
    stroke: none;
    fill-opacity: 0.6;
}

.dc-data-count {
    float: right;
    margin-top: 15px;
    margin-right: 15px;
}

.dc-data-count .filter-count {
    color: #3182bd;
    font-weight: bold;
}

.dc-data-count .total-count {
    color: #3182bd;
    font-weight: bold;
}

.dc-data-table {
}

.dc-chart g.state {
    cursor: pointer;
}

.dc-chart g.state :hover {
    fill-opacity: .8;
}

.dc-chart g.state path {
    stroke: white;
}

.dc-chart g.selected path {
}

.dc-chart g.deselected path {
    fill: grey;
}

.dc-chart g.selected text {
}

.dc-chart g.deselected text {
    display: none;
}

.dc-chart g.county path {
    stroke: white;
    fill: none;
}

.dc-chart g.debug rect {
    fill: blue;
    fill-opacity: .2;
}

.dc-chart g.row rect {
    fill-opacity: 0.8;
    cursor: pointer;
}

.dc-chart g.row rect:hover {
    fill-opacity: 0.6;
}

.dc-chart g.row text {
    fill: white;
    font-size: 12px;
    cursor: pointer;
}

.dc-legend {
    font-size: 11px;
}

.dc-legend-item {
    cursor: pointer;
}

.dc-chart g.axis text {
    /* Makes it so the user can't accidentally click and select text that is meant as a label only */
    -webkit-user-select: none; /* Chrome/Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* IE10 */
    -o-user-select: none;
    user-select: none;
    pointer-events: none;
}

.dc-chart path.highlight {
    stroke-width: 3;
    fill-opacity: 1;
    stroke-opacity: 1;
}

.dc-chart .highlight {
    fill-opacity: 1;
    stroke-opacity: 1;
}

.dc-chart .fadeout {
    fill-opacity: 0.2;
    stroke-opacity: 0.2;
}

.dc-chart path.dc-symbol,
g.dc-legend-item.fadeout {
    fill-opacity: 0.5;
    stroke-opacity: 0.5;
}

.dc-hard .number-display {
    float: none;
}

.dc-chart .box text {
    font: 10px sans-serif;
    -webkit-user-select: none; /* Chrome/Safari */
    -moz-user-select: none; /* Firefox */
    -ms-user-select: none; /* IE10 */
    -o-user-select: none;
    user-select: none;
    pointer-events: none;
}

.dc-chart .box line,
.dc-chart .box circle {
    fill: #fff;
    stroke: #000;
    stroke-width: 1.5px;
}

.dc-chart .box rect {
    stroke: #000;
    stroke-width: 1.5px;
}

.dc-chart .box .center {
    stroke-dasharray: 3,3;
}

.dc-chart .box .outlier {
    fill: none;
    stroke: #ccc;
}

.dc-chart .box.deselected .box {
    fill: #ccc;
}

.dc-chart .box.deselected {
    opacity: .5;
}

.dc-chart .symbol{
    stroke: none;
}

.dc-chart .heatmap .box-group.deselected rect {
    stroke: none;
    fill-opacity: .5;
    fill: #ccc;
}

.dc-chart .heatmap g.axis text {
    pointer-events: all;
    cursor: pointer;
}
