/* bootcards 1.1.2 2015-04-17 12:01 */
var bootcards=bootcards||{portraitModeEnabled:!1,_isXS:null,isFullScreen:!1};bootcards.init=function(a){this.isFullScreen="standalone"in navigator&&navigator.standalone,$(document).ready(function(){Bootcards.init(a),Bootcards.OffCanvas.init(),Bootcards.options.enableTabletPortraitMode&&bootcards._initTabletPortraitMode(),Bootcards.options.disableRubberBanding&&bootcards.disableRubberBanding()}),this.isFullScreen&&a.disableBreakoutSelector&&$(document).on("click",a.disableBreakoutSelector,function(a){a.preventDefault(),location.href=$(a.target).prop("href")})},bootcards.isXS=function(){if(null===this._isXS){var a=$("<div class='visible-xs'>").appendTo($("body"));this._isXS=a.is(":visible"),a.remove()}return this._isXS},bootcards.disableRubberBanding=function(){document.body.addEventListener("touchstart",function(){document.body.addEventListener("touchmove",function a(b){document.body.removeEventListener("touchmove",a);var c=b.target;do{var d=parseInt(window.getComputedStyle(c,null).height,10),e=c.scrollHeight;if(e>d)return}while(c!=document.body&&c.parentElement!=document.body&&(c=c.parentElement));b.preventDefault()})})};var Bootcards=function(){return this.$mainContentEl=null,this.options=null,{init:function(a){this.$mainContentEl=$(".bootcards-container"),this.options=$.extend({},Bootcards.DEFAULTS,a)}}}();Bootcards.DEFAULTS={offCanvasHideOnMainClick:!1,offCanvasBackdrop:!1,enableTabletPortraitMode:!1,disableRubberBanding:!1,disableBreakoutSelector:null},function(a){return a.OffCanvas={$backdrop:null,$toggleEl:null,$menuEl:null,$menuTitleEl:null,offCanvasHideOnMainClick:!1,offCanvasBackdrop:!1,init:function(){this.offCanvasHideOnMainClick=a.options.offCanvasHideOnMainClick,this.offCanvasBackdrop=a.options.offCanvasBackdrop,this.$toggleEl=$("[data-toggle=offcanvas]"),this.$menuEl=$(".offcanvas"),this.$menuEl.length>0&&this.$toggleEl.length>0&&this.$toggleEl.on("click",function(){a.OffCanvas.toggle()}),this.offCanvasHideOnMainClick&&a.$mainContentEl&&a.$mainContentEl.on("click",function(){a.OffCanvas.hide()})},toggle:function(){this.$menuEl.hasClass("active")?this.hide():this.show()},show:function(){this.showBackdrop(),this.$toggleEl.css("opacity",""),this.$menuEl.addClass("active"),this.offCanvasHideOnMainClick&&a.$mainContentEl&&a.$mainContentEl.addClass("active-left")},hide:function(){this.hideBackdrop(),this.$toggleEl&&this.$toggleEl.css("opacity","1"),this.$menuEl&&this.$menuEl.removeClass("active"),this.offCanvasHideOnMainClick&&a.$mainContentEl&&a.$mainContentEl.removeClass("active-left"),this.$menuTitleEl&&this.$menuTitleEl.removeClass("active")},showBackdrop:function(){this.offCanvasBackdrop&&(this.$backdrop=$('<div class="modal-backdrop fade in" />').appendTo(a.$mainContentEl))},hideBackdrop:function(){this.offCanvasBackdrop&&(this.$backdrop&&this.$backdrop.remove(),this.$backdrop=null)},showToggleEl:function(){this.$toggleEl&&this.$toggleEl.show()},hideToggleEl:function(){this.$toggleEl&&this.$toggleEl.hide()},insertToggleButton:function(b){this.$menuTitleEl=$("<div class='offcanvas-list offcanvas-list-title'><span>Menu</span></div>"),this.$menuEl.before(this.$menuTitleEl),this.$toggleEl.clone(!1).prependTo(b).on("click",function(){a.OffCanvas.$menuEl.toggleClass("active"),a.OffCanvas.$menuTitleEl.toggleClass("active")}).children("i").removeClass("fa-bars").addClass("fa-angle-left")}},a}(Bootcards),bootcards.enablePortraitMode=function(){return"undefined"==typeof window.orientation||bootcards.isXS()?!1:!0},bootcards._initTabletPortraitMode=function(){"undefined"==typeof window.orientation||bootcards.isXS()||(bootcards.portraitModeEnabled=!0,$(window).on("resize",function(){setTimeout(function(){bootcards._setOrientation(!1)},250)}).on("load",bootcards._setOrientation(!0)))},bootcards._setOrientation=function(a){if(bootcards.portraitModeEnabled){var b=$(window).width()>$(window).height()?!1:!0;if(Bootcards.OffCanvas.hide(),bootcards._initListEl(),bootcards._initCardsEl(),b){if(0===bootcards.listEl.length)return void Bootcards.OffCanvas.showToggleEl();a&&bootcards.listEl.hide(),bootcards.cardsEl.removeClass(bootcards.cardsColClass).addClass("col-xs-12"),$(".bootcards-az-picker").hide(),bootcards.listOffcanvasToggle||(bootcards.listOffcanvasToggle=$('<button type="button" class="btn btn-default pull-left offcanvaslist-toggle"><i class="fa fa-lg fa-angle-left"></i><span>'+bootcards.listTitle+"</span></button>").on("click",function(){bootcards.listTitleEl.hasClass("active")?Bootcards.OffCanvas.hideBackdrop():Bootcards.OffCanvas.showBackdrop(),bootcards.listEl.toggleClass("active"),bootcards.listTitleEl.toggleClass("active")}),bootcards.listTitleEl=$("<div class='offcanvas-list offcanvas-list-title'><span>"+bootcards.listTitle+"</span></div>"),Bootcards.OffCanvas.$toggleEl&&Bootcards.OffCanvas.insertToggleButton(bootcards.listTitleEl),$(".navbar-header").after(bootcards.listTitleEl,bootcards.listOffcanvasToggle),Bootcards.$mainContentEl.on("click",function(){bootcards.listEl.removeClass("active"),bootcards.listTitleEl.removeClass("active"),Bootcards.OffCanvas.$menuTitleEl.removeClass("active"),Bootcards.OffCanvas.hideBackdrop()}),bootcards.listEl.on("click",function(){bootcards.listEl.removeClass("active"),bootcards.listTitleEl.removeClass("active"),Bootcards.OffCanvas.hideBackdrop()}),Bootcards.OffCanvas.$menuEl&&Bootcards.OffCanvas.$menuEl.addClass("offcanvas-list").on("click",function(){var a=$(this);a.removeClass("active"),Bootcards.OffCanvas.hide(),bootcards.listEl&&bootcards.listEl.removeClass("active"),bootcards.listTitleEl&&bootcards.listTitleEl.removeClass("active")})),Bootcards.OffCanvas.hideToggleEl(),bootcards.listOffcanvasToggle.show(),bootcards.listEl.removeClass(bootcards.listColClass).addClass("offcanvas-list").show()}else Bootcards.OffCanvas.showToggleEl(),bootcards.listEl&&bootcards.listEl.hasClass("offcanvas-list")&&(bootcards.listEl.removeClass("offcanvas-list active").addClass(bootcards.listColClass).show(),bootcards.listEl.css("overflow","hidden"),setTimeout(function(){bootcards.listEl.css("overflow-y","auto")},300)),bootcards.listOffcanvasToggle&&(bootcards.listOffcanvasToggle.hide(),bootcards.listTitleEl.removeClass("active")),bootcards.cardsEl&&bootcards.cardsEl.removeClass("col-xs-12").addClass(bootcards.cardsColClass),$(".bootcards-az-picker").show()}},bootcards._initListEl=function(){return null!=bootcards.listEl?bootcards.listEl:(bootcards.listEl=$(".bootcards-list"),bootcards.listColClass="",void(bootcards.listEl.length>0&&(bootcards.listTitle=bootcards.listEl.data("title")||"List",$.each(bootcards.listEl.prop("class").split(" "),function(a,b){0===b.indexOf("col")&&(bootcards.listColClass+=b+" ")}))))},bootcards._initCardsEl=function(){return null!=bootcards.cardsEl?bootcards.cardsEl:(bootcards.cardsEl=$(".bootcards-cards"),bootcards.cardsColClass="",void(bootcards.cardsEl.length>0&&$.each(bootcards.cardsEl.prop("class").split(" "),function(a,b){0===b.indexOf("col")&&(bootcards.cardsColClass+=b+" ")})))},bootcards.initAZPicker=function(a){var b=$(a);if(b.length>0){$("a",b).off().on("click",function(a){var b=$(this);return a.stopPropagation(),bootcards._jumpToLetter(b,a),!1});var c=b.parents(".bootcards-list");if(c.length>0){var d=c.attr("class").split(/\s+/),e="";$.each(d,function(a,b){return 0===b.indexOf("col-")?void(e=b):void 0});var f=e.substring(e.lastIndexOf("-")+1),g=e.substring(0,e.lastIndexOf("-"))+"-push-"+f;b.appendTo($(".bootcards-container")).addClass(g)}}},bootcards._jumpToLetter=function(a){var b=$("#list");b.animate({scrollTop:0},0);var c=a.text().toLowerCase(),d="#list .list-group a";$(".bootcards-list-subheading").length>0&&(d=".bootcards-list-subheading");var e=$(d),f=!1;if(e.each(function(a,d){var e=$(d),g="";g="a"==e.prop("tagName").toLowerCase()?e.find("h4").text():e.text();var h=g.substring(0,1).toLowerCase(),i=null;return h==c?i=e.offset().top-60:h>c&&(i=e.offset().top-120),null!==i?(b.animate({scrollTop:i},0),f=!0,!1):void 0}),!f){var g=$(e[e.length-1]);b.animate({scrollTop:g.offset().top-120},0)}};