/*!
 *  dc 2.0.0-beta.26
 *  http://dc-js.github.io/dc.js/
 *  Copyright 2012-2016 <PERSON> & the dc.js Developers
 *  https://github.com/dc-js/dc.js/blob/master/AUTHORS
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

!function(){function a(a,b){"use strict";var c={version:"2.0.0-beta.26",constants:{CHART_CLASS:"dc-chart",DEBUG_GROUP_CLASS:"debug",STACK_CLASS:"stack",DESELECTED_CLASS:"deselected",SELECTED_CLASS:"selected",NODE_INDEX_NAME:"__index__",GROUP_INDEX_NAME:"__group_index__",DEFAULT_CHART_GROUP:"__default_chart_group__",EVENT_DELAY:40,NEGLIGIBLE_NUMBER:1e-10},_renderlet:null};c.chartRegistry=function(){function a(a){return a||(a=c.constants.DEFAULT_CHART_GROUP),b[a]||(b[a]=[]),a}var b={};return{has:function(a){for(var c in b)if(b[c].indexOf(a)>=0)return!0;return!1},register:function(c,d){d=a(d),b[d].push(c)},deregister:function(c,d){d=a(d);for(var e=0;e<b[d].length;e++)if(b[d][e].anchorName()===c.anchorName()){b[d].splice(e,1);break}},clear:function(a){a?delete b[a]:b={}},list:function(c){return c=a(c),b[c]}}}(),c.registerChart=function(a,b){c.chartRegistry.register(a,b)},c.deregisterChart=function(a,b){c.chartRegistry.deregister(a,b)},c.hasChart=function(a){return c.chartRegistry.has(a)},c.deregisterAllCharts=function(a){c.chartRegistry.clear(a)},c.filterAll=function(a){for(var b=c.chartRegistry.list(a),d=0;d<b.length;++d)b[d].filterAll()},c.refocusAll=function(a){for(var b=c.chartRegistry.list(a),d=0;d<b.length;++d)b[d].focus&&b[d].focus()},c.renderAll=function(a){for(var b=c.chartRegistry.list(a),d=0;d<b.length;++d)b[d].render();null!==c._renderlet&&c._renderlet(a)},c.redrawAll=function(a){for(var b=c.chartRegistry.list(a),d=0;d<b.length;++d)b[d].redraw();null!==c._renderlet&&c._renderlet(a)},c.disableTransitions=!1,c.transition=function(a,b,d,e){if(0>=b||void 0===b||c.disableTransitions)return a;var f=a.transition(e).duration(b);return"function"==typeof d&&d(f),f},c.optionalTransition=function(a,b,d,e){return a?function(a){return c.transition(a,b,d,e)}:function(a){return a}},c.afterTransition=function(a,b){if(a.empty()||!a.duration)b.call(a);else{var c=0;a.each(function(){++c}).each("end",function(){--c||b.call(a)})}},c.units={},c.units.integers=function(a,b){return Math.abs(b-a)},c.units.ordinal=function(a,b,c){return c},c.units.fp={},c.units.fp.precision=function(a){var b=function(a,d){var e=Math.abs((d-a)/b.resolution);return c.utils.isNegligible(e-Math.floor(e))?Math.floor(e):Math.ceil(e)};return b.resolution=a,b},c.round={},c.round.floor=function(a){return Math.floor(a)},c.round.ceil=function(a){return Math.ceil(a)},c.round.round=function(a){return Math.round(a)},c.override=function(a,b,c){var d=a[b];a["_"+b]=d,a[b]=c},c.renderlet=function(a){return arguments.length?(c._renderlet=a,c):c._renderlet},c.instanceOfChart=function(a){return a instanceof Object&&a.__dcFlag__&&!0},c.errors={},c.errors.Exception=function(a){var b=a||"Unexpected internal error";this.message=b,this.toString=function(){return b},this.stack=(new Error).stack},c.errors.Exception.prototype=Object.create(Error.prototype),c.errors.Exception.prototype.constructor=c.errors.Exception,c.errors.InvalidStateException=function(){c.errors.Exception.apply(this,arguments)},c.errors.InvalidStateException.prototype=Object.create(c.errors.Exception.prototype),c.errors.InvalidStateException.prototype.constructor=c.errors.InvalidStateException,c.errors.BadArgumentException=function(){c.errors.Exception.apply(this,arguments)},c.errors.BadArgumentException.prototype=Object.create(c.errors.Exception.prototype),c.errors.BadArgumentException.prototype.constructor=c.errors.BadArgumentException,c.dateFormat=a.time.format("%m/%d/%Y"),c.printers={},c.printers.filters=function(a){for(var b="",d=0;d<a.length;++d)d>0&&(b+=", "),b+=c.printers.filter(a[d]);return b},c.printers.filter=function(a){var b="";return"undefined"!=typeof a&&null!==a&&(a instanceof Array?a.length>=2?b="["+c.utils.printSingleValue(a[0])+" -> "+c.utils.printSingleValue(a[1])+"]":a.length>=1&&(b=c.utils.printSingleValue(a[0])):b=c.utils.printSingleValue(a)),b},c.pluck=function(a,b){return b?function(c,d){return b.call(c,c[a],d)}:function(b){return b[a]}},c.utils={},c.utils.printSingleValue=function(a){var b=""+a;return a instanceof Date?b=c.dateFormat(a):"string"==typeof a?b=a:c.utils.isFloat(a)?b=c.utils.printSingleValue.fformat(a):c.utils.isInteger(a)&&(b=Math.round(a)),b},c.utils.printSingleValue.fformat=a.format(".2f"),c.utils.add=function(a,b){if("string"==typeof b&&(b=b.replace("%","")),a instanceof Date){"string"==typeof b&&(b=+b);var c=new Date;return c.setTime(a.getTime()),c.setDate(a.getDate()+b),c}if("string"==typeof b){var d=+b/100;return a>0?a*(1+d):a*(1-d)}return a+b},c.utils.subtract=function(a,b){if("string"==typeof b&&(b=b.replace("%","")),a instanceof Date){"string"==typeof b&&(b=+b);var c=new Date;return c.setTime(a.getTime()),c.setDate(a.getDate()-b),c}if("string"==typeof b){var d=+b/100;return 0>a?a*(1+d):a*(1-d)}return a-b},c.utils.isNumber=function(a){return a===+a},c.utils.isFloat=function(a){return a===+a&&a!==(0|a)},c.utils.isInteger=function(a){return a===+a&&a===(0|a)},c.utils.isNegligible=function(a){return!c.utils.isNumber(a)||a<c.constants.NEGLIGIBLE_NUMBER&&a>-c.constants.NEGLIGIBLE_NUMBER},c.utils.clamp=function(a,b,c){return b>a?b:a>c?c:a};var d=0;return c.utils.uniqueId=function(){return++d},c.utils.nameToId=function(a){return a.toLowerCase().replace(/[\s]/g,"_").replace(/[\.']/g,"")},c.utils.appendOrSelect=function(a,b,c){c=c||b;var d=a.select(b);return d.empty()&&(d=a.append(c)),d},c.utils.safeNumber=function(a){return c.utils.isNumber(+a)?+a:0},c.logger={},c.logger.enableDebugLog=!1,c.logger.warn=function(a){return console&&(console.warn?console.warn(a):console.log&&console.log(a)),c.logger},c.logger.debug=function(a){return c.logger.enableDebugLog&&console&&(console.debug?console.debug(a):console.log&&console.log(a)),c.logger},c.logger.deprecate=function(a,b){function d(){return e||(c.logger.warn(b),e=!0),a.apply(this,arguments)}var e=!1;return d},c.events={current:null},c.events.trigger=function(a,b){return b?(c.events.current=a,void setTimeout(function(){a===c.events.current&&a()},b)):void a()},c.filters={},c.filters.RangedFilter=function(a,b){var c=new Array(a,b);return c.isFiltered=function(a){return a>=this[0]&&a<this[1]},c.filterType="RangedFilter",c},c.filters.TwoDimensionalFilter=function(a){if(null===a)return null;var b=a;return b.isFiltered=function(a){return a.length&&a.length===b.length&&a[0]===b[0]&&a[1]===b[1]},b.filterType="TwoDimensionalFilter",b},c.filters.RangedTwoDimensionalFilter=function(a){if(null===a)return null;var b,c=a;return b=c[0]instanceof Array?[[Math.min(a[0][0],a[1][0]),Math.min(a[0][1],a[1][1])],[Math.max(a[0][0],a[1][0]),Math.max(a[0][1],a[1][1])]]:[[a[0],-(1/0)],[a[1],1/0]],c.isFiltered=function(a){var c,d;if(a instanceof Array){if(2!==a.length)return!1;c=a[0],d=a[1]}else c=a,d=b[0][1];return c>=b[0][0]&&c<b[1][0]&&d>=b[0][1]&&d<b[1][1]},c.filterType="RangedTwoDimensionalFilter",c},c.baseMixin=function(d){function e(){m&&m.attr("width",d.width()).attr("height",d.height())}function f(){return m=d.root().append("svg"),e(),m}function g(a){if(!d[a]||!d[a]())throw new c.errors.InvalidStateException("Mandatory attribute chart."+a+" is missing on chart[#"+d.anchorName()+"]")}function h(){if(d.dimension()&&d.dimension().filter){var a=N(d.dimension(),M);M=a?a:M}}d.__dcFlag__=c.utils.uniqueId();var i,j,k,l,m,n,o,p,q,r,s,t=200,u=function(a){var b=a&&a.getBoundingClientRect&&a.getBoundingClientRect().width;return b&&b>t?b:t},v=u,w=200,x=function(a){var b=a&&a.getBoundingClientRect&&a.getBoundingClientRect().height;return b&&b>w?b:w},y=x,z=c.pluck("key"),A=c.pluck("value"),B=c.pluck("key"),C=c.pluck("key"),D=!1,E=function(a){return d.keyAccessor()(a)+": "+d.valueAccessor()(a)},F=!0,G=!1,H=750,I=c.printers.filters,J=["dimension","group"],K=c.constants.DEFAULT_CHART_GROUP,L=a.dispatch("preRender","postRender","preRedraw","postRedraw","filtered","zoomed","renderlet","pretransition"),M=[],N=function(a,b){return 0===b.length?a.filter(null):1!==b.length||b[0].isFiltered?1===b.length&&"RangedFilter"===b[0].filterType?a.filterRange(b[0]):a.filterFunction(function(a){for(var c=0;c<b.length;c++){var d=b[c];if(d.isFiltered&&d.isFiltered(a))return!0;if(a>=d&&d>=a)return!0}return!1}):a.filterExact(b[0]),b},O=function(a){return a.all()};d.height=function(b){return arguments.length?(y=a.functor(b||x),p=void 0,d):(c.utils.isNumber(p)||(p=y(l.node())),p)},d.width=function(b){return arguments.length?(v=a.functor(b||u),o=void 0,d):(c.utils.isNumber(o)||(o=v(l.node())),o)},d.minWidth=function(a){return arguments.length?(t=a,d):t},d.minHeight=function(a){return arguments.length?(w=a,d):w},d.dimension=function(a){return arguments.length?(i=a,d.expireCache(),d):i},d.data=function(b){return arguments.length?(O=a.functor(b),d.expireCache(),d):O.call(d,j)},d.group=function(a,b){return arguments.length?(j=a,d._groupName=b,d.expireCache(),d):j},d.ordering=function(a){return arguments.length?(C=a,q=b.quicksort.by(C),d.expireCache(),d):C},d._computeOrderedGroups=function(a){var c=a.slice(0);return c.length<=1?c:(q||(q=b.quicksort.by(C)),q(c,0,c.length))},d.filterAll=function(){return d.filter(null)},d.select=function(a){return l.select(a)},d.selectAll=function(a){return l?l.selectAll(a):null},d.anchor=function(b,e){if(!arguments.length)return k;if(c.instanceOfChart(b))k=b.anchor(),l=b.root(),n=!0;else{if(!b)throw new c.errors.BadArgumentException("parent must be defined");k=b.select&&b.classed?b.node():b,l=a.select(k),l.classed(c.constants.CHART_CLASS,!0),c.registerChart(d,e),n=!1}return K=e,d},d.anchorName=function(){var a=d.anchor();return a&&a.id?a.id:a&&a.replace?a.replace("#",""):"dc-chart"+d.chartID()},d.root=function(a){return arguments.length?(l=a,d):l},d.svg=function(a){return arguments.length?(m=a,d):m},d.resetSvg=function(){return d.select("svg").remove(),f()},d.filterPrinter=function(a){return arguments.length?(I=a,d):I},d.controlsUseVisibility=function(a){return arguments.length?(G=a,d):G},d.turnOnControls=function(){if(l){var a=d.controlsUseVisibility()?"visibility":"display";d.selectAll(".reset").style(a,null),d.selectAll(".filter").text(I(d.filters())).style(a,null)}return d},d.turnOffControls=function(){if(l){var a=d.controlsUseVisibility()?"visibility":"display",b=d.controlsUseVisibility()?"hidden":"none";d.selectAll(".reset").style(a,b),d.selectAll(".filter").style(a,b).text(d.filter())}return d},d.transitionDuration=function(a){return arguments.length?(H=a,d):H},d._mandatoryAttributes=function(a){return arguments.length?(J=a,d):J},d.render=function(){p=o=void 0,L.preRender(d),J&&J.forEach(g);var a=d._doRender();return r&&r.render(),d._activateRenderlets("postRender"),a},d._activateRenderlets=function(a){L.pretransition(d),d.transitionDuration()>0&&m?m.transition().duration(d.transitionDuration()).each("end",function(){L.renderlet(d),a&&L[a](d)}):(L.renderlet(d),a&&L[a](d))},d.redraw=function(){e(),L.preRedraw(d);var a=d._doRedraw();return r&&r.render(),d._activateRenderlets("postRedraw"),a},d.commitHandler=function(a){return arguments.length?(s=a,d):s},d.redrawGroup=function(){return s?s(!1,function(a,b){a?console.log(a):c.redrawAll(d.chartGroup())}):c.redrawAll(d.chartGroup()),d},d.renderGroup=function(){return s?s(!1,function(a,b){a?console.log(a):c.renderAll(d.chartGroup())}):c.renderAll(d.chartGroup()),d},d._invokeFilteredListener=function(a){void 0!==a&&L.filtered(d,a)},d._invokeZoomedListener=function(){L.zoomed(d)};var P=function(a,b){return null===b||"undefined"==typeof b?a.length>0:a.some(function(a){return a>=b&&b>=a})};d.hasFilterHandler=function(a){return arguments.length?(P=a,d):P},d.hasFilter=function(a){return P(M,a)};var Q=function(a,b){for(var c=0;c<a.length;c++)if(a[c]<=b&&a[c]>=b){a.splice(c,1);break}return a};d.removeFilterHandler=function(a){return arguments.length?(Q=a,d):Q};var R=function(a,b){return a.push(b),a};d.addFilterHandler=function(a){return arguments.length?(R=a,d):R};var S=function(a){return[]};return d.resetFilterHandler=function(a){return arguments.length?(S=a,d):S},d.replaceFilter=function(a){M=[],d.filter(a)},d.filter=function(a){return arguments.length?(a instanceof Array&&a[0]instanceof Array&&!a.isFiltered?a[0].forEach(function(a){d.hasFilter(a)?Q(M,a):R(M,a)}):null===a?M=S(M):d.hasFilter(a)?Q(M,a):R(M,a),h(),d._invokeFilteredListener(a),null!==l&&d.hasFilter()?d.turnOnControls():d.turnOffControls(),d):M.length>0?M[0]:null},d.filters=function(){return M},d.highlightSelected=function(b){a.select(b).classed(c.constants.SELECTED_CLASS,!0),a.select(b).classed(c.constants.DESELECTED_CLASS,!1)},d.fadeDeselected=function(b){a.select(b).classed(c.constants.SELECTED_CLASS,!1),a.select(b).classed(c.constants.DESELECTED_CLASS,!0)},d.resetHighlight=function(b){a.select(b).classed(c.constants.SELECTED_CLASS,!1),a.select(b).classed(c.constants.DESELECTED_CLASS,!1)},d.onClick=function(a){var b=d.keyAccessor()(a);c.events.trigger(function(){d.filter(b),d.redrawGroup()})},d.filterHandler=function(a){return arguments.length?(N=a,d):N},d._doRender=function(){return d},d._doRedraw=function(){return d},d.legendables=function(){return[]},d.legendHighlight=function(){},d.legendReset=function(){},d.legendToggle=function(){},d.isLegendableHidden=function(){return!1},d.keyAccessor=function(a){return arguments.length?(z=a,d):z},d.valueAccessor=function(a){return arguments.length?(A=a,d):A},d.label=function(a,b){return arguments.length?(B=a,(void 0===b||b)&&(D=!0),d):B},d.renderLabel=function(a){return arguments.length?(D=a,d):D},d.title=function(a){return arguments.length?(E=a,d):E},d.renderTitle=function(a){return arguments.length?(F=a,d):F},d.renderlet=c.logger.deprecate(function(a){return d.on("renderlet."+c.utils.uniqueId(),a),d},'chart.renderlet has been deprecated.  Please use chart.on("renderlet.<renderletKey>", renderletFunction)'),d.chartGroup=function(a){return arguments.length?(n||c.deregisterChart(d,K),K=a,n||c.registerChart(d,K),d):K},d.expireCache=function(){return d},d.legend=function(a){return arguments.length?(r=a,r.parent(d),d):r},d.chartID=function(){return d.__dcFlag__},d.options=function(a){var b=["anchor","group","xAxisLabel","yAxisLabel","stack","title","point","getColor","overlayGeoJson"];for(var e in a)"function"==typeof d[e]?a[e]instanceof Array&&-1!==b.indexOf(e)?d[e].apply(d,a[e]):d[e].call(d,a[e]):c.logger.debug("Not a valid option setter name: "+e);return d},d.on=function(a,b){return L.on(a,b),d},d},c.marginMixin=function(a){var b={top:10,right:50,bottom:30,left:30};return a.margins=function(c){return arguments.length?(b=c,a):b},a.effectiveWidth=function(){return a.width()-a.margins().left-a.margins().right},a.effectiveHeight=function(){return a.height()-a.margins().top-a.margins().bottom},a},c.colorMixin=function(b){var c=a.scale.category20c(),d=!0,e=function(a){return b.keyAccessor()(a)};return b.colors=function(d){return arguments.length?(c=d instanceof Array?a.scale.quantize().range(d):a.functor(d),b):c},b.ordinalColors=function(c){return b.colors(a.scale.ordinal().range(c))},b.linearColors=function(c){return b.colors(a.scale.linear().range(c).interpolate(a.interpolateHcl))},b.colorAccessor=function(a){return arguments.length?(e=a,d=!1,b):e},b.defaultColorAccessor=function(){return d},b.colorDomain=function(a){return arguments.length?(c.domain(a),b):c.domain()},b.calculateColorDomain=function(){var d=[a.min(b.data(),b.colorAccessor()),a.max(b.data(),b.colorAccessor())];return c.domain(d),b},b.getColor=function(a,b){return c(e.call(this,a,b))},b.colorCalculator=function(a){return arguments.length?(b.getColor=a,b):b.getColor},b},c.coordinateGridMixin=function(b){function d(){V=!0,Y&&(b.x().domain(m(b.x().domain(),z)),G&&b.x().domain(m(b.x().domain(),G.x().domain())));var a=b.x().domain(),d=c.filters.RangedFilter(a[0],a[1]);b.replaceFilter(d),b.rescale(),b.redraw(),G&&!n(b.filter(),G.filter())&&c.events.trigger(function(){G.replaceFilter(d),G.redraw()}),b._invokeZoomedListener(),c.events.trigger(function(){b.redrawGroup()},c.constants.EVENT_DELAY),V=!n(a,z)}function e(a,b){return!a||!b||a.length!==b.length||a.some(function(a,c){return a&&b[c]?a.toString()!==b[c].toString():a===b[c]})}function f(a,c){b.isOrdinal()?(b.elasticX()||0===y.domain().length)&&y.domain(b._ordinalXDomain()):b.elasticX()&&y.domain([b.xAxisMin(),b.xAxisMax()]);var d=y.domain();(c||e(B,d))&&b.rescale(),B=d,b.isOrdinal()?y.rangeBands([0,b.xAxisLength()],da,b._useOuterPadding()?ca:0):y.range([0,b.xAxisLength()]),I=I.scale(b.x()),g(a)}function g(a){var d=a.selectAll("g."+r);if(U){d.empty()&&(d=a.insert("g",":first-child").attr("class",p+" "+r).attr("transform","translate("+b.margins().left+","+b.margins().top+")"));var e=I.tickValues()?I.tickValues():"function"==typeof y.ticks?y.ticks(I.ticks()[0]):y.domain(),f=d.selectAll("line").data(e),g=f.enter().append("line").attr("x1",function(a){return y(a)}).attr("y1",b._xAxisY()-b.margins().top).attr("x2",function(a){return y(a)}).attr("y2",0).attr("opacity",0);c.transition(g,b.transitionDuration()).attr("opacity",1),c.transition(f,b.transitionDuration()).attr("x1",function(a){return y(a)}).attr("y1",b._xAxisY()-b.margins().top).attr("x2",function(a){return y(a)}).attr("y2",0),f.exit().remove()}else d.selectAll("line").remove()}function h(){return b._xAxisY()-b.margins().top}function i(){return b.anchorName().replace(/[ .#=\[\]]/g,"-")+"-clip"}function j(){var a=c.utils.appendOrSelect(v,"defs"),d=i(),e=c.utils.appendOrSelect(a,"#"+d,"clipPath").attr("id",d),f=2*ba;c.utils.appendOrSelect(e,"rect").attr("width",b.xAxisLength()+f).attr("height",b.yAxisHeight()+f).attr("transform","translate(-"+ba+", -"+ba+")")}function k(a){b.isOrdinal()&&(S=!1),f(b.g(),a),b._prepareYAxis(b.g()),b.plotData(),(b.elasticX()||W||a)&&b.renderXAxis(b.g()),(b.elasticY()||W||a)&&b.renderYAxis(b.g()),a?b.renderBrush(b.g(),!1):b.redrawBrush(b.g(),W),b.fadeDeselectedArea(),W=!1}function l(){aa?b._enableMouseZoom():_&&b._disableMouseZoom()}function m(b,c){var d=[];return d[0]=a.max([b[0],c[0]]),d[1]=a.min([b[1],c[1]]),d}function n(a,b){return a||b?a&&b?0===a.length&&0===b.length?!0:a[0].valueOf()===b[0].valueOf()&&a[1].valueOf()===b[1].valueOf()?!0:!1:!1:!0}function o(a){return a instanceof Array&&a.length>1}var p="grid-line",q="horizontal",r="vertical",s="y-axis-label",t="x-axis-label",u=12;b=c.colorMixin(c.marginMixin(c.baseMixin(b))),b.colors(a.scale.category10()),b._mandatoryAttributes().push("x");var v,w,x,y,z,A,B,C,D,E,F,G,H,I=a.svg.axis().orient("bottom"),J=c.units.integers,K=0,L=!1,M=0,N=a.svg.axis().orient("left"),O=0,P=!1,Q=0,R=a.svg.brush(),S=!0,T=!1,U=!1,V=!1,W=!1,X=[1,1/0],Y=!0,Z=a.behavior.zoom().on("zoom",d),$=a.behavior.zoom().on("zoom",null),_=!1,aa=!1,ba=0,ca=.5,da=0,ea=!1;return b.rescale=function(){return F=void 0,W=!0,b},b.resizing=function(){return W},b.rangeChart=function(a){return arguments.length?(G=a,G.focusChart(b),b):G},b.zoomScale=function(a){return arguments.length?(X=a,b):X},b.zoomOutRestrict=function(a){return arguments.length?(X[0]=a?1:0,Y=a,b):Y},b._generateG=function(a){return v=void 0===a?b.svg():a,w=v.append("g"),x=w.append("g").attr("class","chart-body").attr("transform","translate("+b.margins().left+", "+b.margins().top+")").attr("clip-path","url(#"+i()+")"),w},b.g=function(a){return arguments.length?(w=a,b):w},b.mouseZoomable=function(a){return arguments.length?(aa=a,b):aa},b.chartBodyG=function(a){return arguments.length?(x=a,b):x},b.x=function(a){return arguments.length?(y=a,z=y.domain(),b.rescale(),b):y},b.xOriginalDomain=function(){return z},b.xUnits=function(a){return arguments.length?(J=a,b):J},b.xAxis=function(a){return arguments.length?(I=a,b):I},b.elasticX=function(a){return arguments.length?(L=a,b):L},b.xAxisPadding=function(a){return arguments.length?(K=a,b):K},b.xUnitCount=function(){if(void 0===F){var a=b.xUnits()(b.x().domain()[0],b.x().domain()[1],b.x().domain());F=a instanceof Array?a.length:a}return F},b.useRightYAxis=function(a){return arguments.length?(ea=a,b):ea},b.isOrdinal=function(){return b.xUnits()===c.units.ordinal},b._useOuterPadding=function(){return!0},b._ordinalXDomain=function(){var a=b._computeOrderedGroups(b.data());return a.map(b.keyAccessor())},b.renderXAxis=function(a){var d=a.selectAll("g.x");d.empty()&&(d=a.append("g").attr("class","axis x").attr("transform","translate("+b.margins().left+","+b._xAxisY()+")"));var e=a.selectAll("text."+t);e.empty()&&b.xAxisLabel()&&(e=a.append("text").attr("class",t).attr("transform","translate("+(b.margins().left+b.xAxisLength()/2)+","+(b.height()-M)+")").attr("text-anchor","middle")),b.xAxisLabel()&&e.text()!==b.xAxisLabel()&&e.text(b.xAxisLabel()),c.transition(d,b.transitionDuration()).attr("transform","translate("+b.margins().left+","+b._xAxisY()+")").call(I),c.transition(e,b.transitionDuration()).attr("transform","translate("+(b.margins().left+b.xAxisLength()/2)+","+(b.height()-M)+")")},b._xAxisY=function(){return b.height()-b.margins().bottom},b.xAxisLength=function(){return b.effectiveWidth()},b.xAxisLabel=function(a,c){return arguments.length?(A=a,b.margins().bottom-=M,M=void 0===c?u:c,b.margins().bottom+=M,b):A},b._prepareYAxis=function(c){if(void 0===C||b.elasticY()){void 0===C&&(C=a.scale.linear());var d=b.yAxisMin()||0,e=b.yAxisMax()||0;C.domain([d,e]).rangeRound([b.yAxisHeight(),0])}C.range([b.yAxisHeight(),0]),N=N.scale(C),ea&&N.orient("right"),b._renderHorizontalGridLinesForAxis(c,C,N)},b.renderYAxisLabel=function(a,d,e,f){f=f||Q;var g=b.g().selectAll("text."+s+"."+a+"-label"),h=b.margins().top+b.yAxisHeight()/2;g.empty()&&d&&(g=b.g().append("text").attr("transform","translate("+f+","+h+"),rotate("+e+")").attr("class",s+" "+a+"-label").attr("text-anchor","middle").text(d)),d&&g.text()!==d&&g.text(d),c.transition(g,b.transitionDuration()).attr("transform","translate("+f+","+h+"),rotate("+e+")")},b.renderYAxisAt=function(a,d,e){var f=b.g().selectAll("g."+a);f.empty()&&(f=b.g().append("g").attr("class","axis "+a).attr("transform","translate("+e+","+b.margins().top+")")),c.transition(f,b.transitionDuration()).attr("transform","translate("+e+","+b.margins().top+")").call(d)},b.renderYAxis=function(){var a=ea?b.width()-b.margins().right:b._yAxisX();b.renderYAxisAt("y",N,a);var c=ea?b.width()-Q:Q,d=ea?90:-90;b.renderYAxisLabel("y",b.yAxisLabel(),d,c)},b._renderHorizontalGridLinesForAxis=function(a,d,e){var f=a.selectAll("g."+q);if(T){var g=e.tickValues()?e.tickValues():d.ticks(e.ticks()[0]);f.empty()&&(f=a.insert("g",":first-child").attr("class",p+" "+q).attr("transform","translate("+b.margins().left+","+b.margins().top+")"));var h=f.selectAll("line").data(g),i=h.enter().append("line").attr("x1",1).attr("y1",function(a){return d(a)}).attr("x2",b.xAxisLength()).attr("y2",function(a){return d(a)}).attr("opacity",0);c.transition(i,b.transitionDuration()).attr("opacity",1),c.transition(h,b.transitionDuration()).attr("x1",1).attr("y1",function(a){return d(a)}).attr("x2",b.xAxisLength()).attr("y2",function(a){return d(a)}),h.exit().remove()}else f.selectAll("line").remove()},b._yAxisX=function(){return b.useRightYAxis()?b.width()-b.margins().right:b.margins().left},b.yAxisLabel=function(a,c){return arguments.length?(D=a,b.margins().left-=Q,Q=void 0===c?u:c,b.margins().left+=Q,b):D},b.y=function(a){return arguments.length?(C=a,b.rescale(),b):C},b.yAxis=function(a){return arguments.length?(N=a,b):N},b.elasticY=function(a){return arguments.length?(P=a,b):P},b.renderHorizontalGridLines=function(a){return arguments.length?(T=a,b):T},b.renderVerticalGridLines=function(a){return arguments.length?(U=a,b):U},b.xAxisMin=function(){var d=a.min(b.data(),function(a){return b.keyAccessor()(a)});return c.utils.subtract(d,K)},b.xAxisMax=function(){var d=a.max(b.data(),function(a){return b.keyAccessor()(a)});return c.utils.add(d,K)},b.yAxisMin=function(){var d=a.min(b.data(),function(a){return b.valueAccessor()(a)});return c.utils.subtract(d,O)},b.yAxisMax=function(){var d=a.max(b.data(),function(a){return b.valueAccessor()(a)});return c.utils.add(d,O)},b.yAxisPadding=function(a){return arguments.length?(O=a,b):O},b.yAxisHeight=function(){return b.effectiveHeight()},b.round=function(a){return arguments.length?(E=a,b):E},b._rangeBandPadding=function(a){return arguments.length?(da=a,b):da},b._outerRangeBandPadding=function(a){return arguments.length?(ca=a,b):ca},c.override(b,"filter",function(a){return arguments.length?(b._filter(a),a?b.brush().extent(a):b.brush().clear(),b):b._filter()}),b.brush=function(a){return arguments.length?(R=a,b):R},b.renderBrush=function(a){if(S){R.on("brush",b._brushing),R.on("brushstart",b._disableMouseZoom),R.on("brushend",l);var c=a.append("g").attr("class","brush").attr("transform","translate("+b.margins().left+","+b.margins().top+")").call(R.x(b.x()));b.setBrushY(c,!1),b.setHandlePaths(c),b.hasFilter()&&b.redrawBrush(a,!1)}},b.setHandlePaths=function(a){a.selectAll(".resize").append("path").attr("d",b.resizeHandlePath)},b.setBrushY=function(a){a.selectAll(".brush rect").attr("height",h()),a.selectAll(".resize path").attr("d",b.resizeHandlePath)},b.extendBrush=function(){var a=R.extent();return b.round()&&(a[0]=a.map(b.round())[0],a[1]=a.map(b.round())[1],w.select(".brush").call(R.extent(a))),a},b.brushIsEmpty=function(a){return R.empty()||!a||a[1]<=a[0]},b._brushing=function(){var a=b.extendBrush();if(b.redrawBrush(w,!1),b.brushIsEmpty(a))c.events.trigger(function(){b.filter(null),b.redrawGroup()},c.constants.EVENT_DELAY);else{var d=c.filters.RangedFilter(a[0],a[1]);c.events.trigger(function(){b.replaceFilter(d),b.redrawGroup()},c.constants.EVENT_DELAY)}},b.redrawBrush=function(a,d){if(S){b.filter()&&b.brush().empty()&&b.brush().extent(b.filter());var e=c.optionalTransition(d,b.transitionDuration())(a.select("g.brush"));b.setBrushY(e),e.call(b.brush().x(b.x()).extent(b.brush().extent()))}b.fadeDeselectedArea()},b.fadeDeselectedArea=function(){},b.resizeHandlePath=function(a){var b=+("e"===a),c=b?1:-1,d=h()/3;return"M"+.5*c+","+d+"A6,6 0 0 "+b+" "+6.5*c+","+(d+6)+"V"+(2*d-6)+"A6,6 0 0 "+b+" "+.5*c+","+2*d+"ZM"+2.5*c+","+(d+8)+"V"+(2*d-8)+"M"+4.5*c+","+(d+8)+"V"+(2*d-8)},b.clipPadding=function(a){return arguments.length?(ba=a,b):ba},b._preprocessData=function(){},b._doRender=function(){return b.resetSvg(),b._preprocessData(),b._generateG(),j(),k(!0),l(),b},b._doRedraw=function(){return b._preprocessData(),k(!1),j(),b},b._enableMouseZoom=function(){_=!0,Z.x(b.x()).scaleExtent(X).size([b.width(),b.height()]).duration(b.transitionDuration()),b.root().call(Z)},b._disableMouseZoom=function(){b.root().call($)},b.focus=function(a){o(a)?b.x().domain(a):b.x().domain(z),Z.x(b.x()),d()},b.refocused=function(){return V},b.focusChart=function(a){return arguments.length?(H=a,b.on("filtered",function(a){a.filter()?n(a.filter(),H.filter())||c.events.trigger(function(){H.focus(a.filter())}):c.events.trigger(function(){H.x().domain(H.xOriginalDomain())})}),b):H},b.brushOn=function(a){return arguments.length?(S=a,b):S},b},c.stackMixin=function(b){function d(a,c){var d=a.accessor||b.valueAccessor();return a.name=String(a.name||c),a.values=a.group.all().map(function(c,e){return{x:b.keyAccessor()(c,e),y:a.hidden?null:d(c,e),data:c,layer:a.name,hidden:a.hidden}}),a.values=a.values.filter(e()),a.values}function e(){if(!b.x())return a.functor(!0);var c=b.x().domain();return b.isOrdinal()?function(){return!0}:b.elasticX()?function(){return!0}:function(a){return a.x>=c[0]&&a.x<=c[c.length-1]}}function f(a){var b=j.map(c.pluck("name")).indexOf(a);return j[b]}function g(){var a=b.data().map(function(a){return a.values});return Array.prototype.concat.apply([],a)}function h(a){return!a.hidden}var i=a.layout.stack().values(d),j=[],k={},l=!1;return b.stack=function(a,c,d){if(!arguments.length)return j;arguments.length<=2&&(d=c);var e={group:a};return"string"==typeof c&&(e.name=c),"function"==typeof d&&(e.accessor=d),j.push(e),b},c.override(b,"group",function(a,c,d){return arguments.length?(j=[],k={},b.stack(a,c),d&&b.valueAccessor(d),b._group(a,c)):b._group()}),b.hidableStacks=function(a){return arguments.length?(l=a,b):l},b.hideStack=function(a){var c=f(a);return c&&(c.hidden=!0),b},b.showStack=function(a){var c=f(a);return c&&(c.hidden=!1),b},b.getValueAccessorByIndex=function(a){return j[a].accessor||b.valueAccessor()},b.yAxisMin=function(){var d=a.min(g(),function(a){return a.y+a.y0<a.y0?a.y+a.y0:a.y0});return c.utils.subtract(d,b.yAxisPadding())},b.yAxisMax=function(){var d=a.max(g(),function(a){return a.y+a.y0});return c.utils.add(d,b.yAxisPadding())},b.xAxisMin=function(){var d=a.min(g(),c.pluck("x"));return c.utils.subtract(d,b.xAxisPadding())},b.xAxisMax=function(){var d=a.max(g(),c.pluck("x"));return c.utils.add(d,b.xAxisPadding())},c.override(b,"title",function(a,c){return a?"function"==typeof a?b._title(a):a===b._groupName&&"function"==typeof c?b._title(c):"function"!=typeof c?k[a]||b._title():(k[a]=c,b):b._title()}),b.stackLayout=function(a){return arguments.length?(i=a,b):i},b.data(function(){var a=j.filter(h);return a.length?b.stackLayout()(a):[]}),b._ordinalXDomain=function(){var a=g().map(c.pluck("data")),d=b._computeOrderedGroups(a);return d.map(b.keyAccessor())},b.colorAccessor(function(a){var b=this.layer||this.name||a.name||a.layer;return b}),b.legendables=function(){return j.map(function(a,c){return{chart:b,name:a.name,hidden:a.hidden||!1,color:b.getColor.call(a,a.values,c)}})},b.isLegendableHidden=function(a){var b=f(a.name);return b?b.hidden:!1},b.legendToggle=function(a){l&&(b.isLegendableHidden(a)?b.showStack(a.name):b.hideStack(a.name),b.renderGroup())},b},c.capMixin=function(b){var d=1/0,e="Others",f=function(c){var d=a.sum(c,b.valueAccessor()),f=b.group().all(),g=a.sum(f,b.valueAccessor()),h=c.map(b.keyAccessor()),i=f.map(b.keyAccessor()),j=a.set(h),k=i.filter(function(a){return!j.has(a)});return g>d?c.concat([{others:k,key:e,value:g-d}]):c};return b.cappedKeyAccessor=function(a,c){return a.others?a.key:b.keyAccessor()(a,c)},b.cappedValueAccessor=function(a,c){return a.others?a.value:b.valueAccessor()(a,c)},b.data(function(a){if(d===1/0)return b._computeOrderedGroups(a.all());var c=a.top(d);return c=b._computeOrderedGroups(c),f?f(c):c}),b.cap=function(a){return arguments.length?(d=a,b):d},b.othersLabel=function(a){return arguments.length?(e=a,b):e},b.othersGrouper=function(a){return arguments.length?(f=a,b):f},c.override(b,"onClick",function(a){a.others&&b.filter([a.others]),b._onClick(a)}),b},c.bubbleMixin=function(b){var d=.3,e=10;b.BUBBLE_NODE_CLASS="node",b.BUBBLE_CLASS="bubble",b.MIN_RADIUS=10,b=c.colorMixin(b),b.renderLabel(!0),b.data(function(a){return a.top(1/0)});var f=a.scale.linear().domain([0,100]),g=function(a){return a.r};b.r=function(a){return arguments.length?(f=a,b):f},b.radiusValueAccessor=function(a){return arguments.length?(g=a,b):g},b.rMin=function(){var c=a.min(b.data(),function(a){return b.radiusValueAccessor()(a)});return c},b.rMax=function(){var c=a.max(b.data(),function(a){return b.radiusValueAccessor()(a)});return c},b.bubbleR=function(a){var c=b.radiusValueAccessor()(a),d=b.r()(c);return(isNaN(d)||0>=c)&&(d=0),d};var h=function(a){return b.label()(a)},i=function(a){return b.bubbleR(a)>e},j=function(a){return i(a)?1:0},k=function(a){return i(a)?"all":"none"};b._doRenderLabel=function(a){if(b.renderLabel()){var d=a.select("text");d.empty()&&(d=a.append("text").attr("text-anchor","middle").attr("dy",".3em").on("click",b.onClick)),d.attr("opacity",0).attr("pointer-events",k).text(h),c.transition(d,b.transitionDuration()).attr("opacity",j)}},b.doUpdateLabels=function(a){if(b.renderLabel()){var d=a.selectAll("text").attr("pointer-events",k).text(h);c.transition(d,b.transitionDuration()).attr("opacity",j)}};var l=function(a){return b.title()(a)};return b._doRenderTitles=function(a){if(b.renderTitle()){var c=a.select("title");c.empty()&&a.append("title").text(l)}},b.doUpdateTitles=function(a){b.renderTitle()&&a.selectAll("title").text(l)},b.minRadius=function(a){return arguments.length?(b.MIN_RADIUS=a,b):b.MIN_RADIUS},b.minRadiusWithLabel=function(a){return arguments.length?(e=a,b):e},b.maxBubbleRelativeSize=function(a){return arguments.length?(d=a,b):d},b.fadeDeselectedArea=function(){b.hasFilter()?b.selectAll("g."+b.BUBBLE_NODE_CLASS).each(function(a){
b.isSelectedNode(a)?b.highlightSelected(this):b.fadeDeselected(this)}):b.selectAll("g."+b.BUBBLE_NODE_CLASS).each(function(){b.resetHighlight(this)})},b.isSelectedNode=function(a){return b.hasFilter(a.key)},b.onClick=function(a){var d=a.key;c.events.trigger(function(){b.filter(d),b.redrawGroup()})},b},c.pieChart=function(b,d){function e(){E=F?F:a.min([S.width(),S.height()])/2;var b,d=s(),e=u();if(a.sum(S.data(),S.valueAccessor())?(b=e(S.data()),G.classed(M,!1)):(b=e([{key:N,value:1,others:[N]}]),G.classed(M,!0)),G){var g=G.selectAll("g."+L).data(b);f(g,d,b),m(b,d),q(g),r(),c.transition(G,S.transitionDuration()).attr("transform","translate("+S.cx()+","+S.cy()+")")}}function f(a,b,c){var d=g(a);h(d,b),i(d),k(c,b)}function g(a){var b=a.enter().append("g").attr("class",function(a,b){return L+" _"+b});return b}function h(a,b){var d=a.append("path").attr("fill",z).on("click",A).attr("d",function(a,c){return B(a,c,b)});c.transition(d,S.transitionDuration(),function(a){a.attrTween("d",x)})}function i(a){S.renderTitle()&&a.append("title").text(function(a){return S.title()(a.data)})}function j(a,b){S._applyLabelText(a),c.transition(a,S.transitionDuration()).attr("transform",function(a){return C(a,b)}).attr("text-anchor","middle")}function k(a,b){if(S.renderLabel()){var c=G.selectAll("text."+L).data(a);c.exit().remove();var d=c.enter().append("text").attr("class",function(a,b){var c=L+" _"+b;return J&&(c+=" external"),c}).on("click",A);j(d,b),J&&R&&l(a,b)}}function l(b,d){var e=G.selectAll("polyline."+L).data(b);e.enter().append("polyline").attr("class",function(a,b){return"pie-path _"+b+" "+L}),e.exit().remove(),c.transition(e,S.transitionDuration()).attrTween("points",function(b){this._current=this._current||b;var c=a.interpolate(this._current,b);return this._current=c(0),function(b){var e=a.svg.arc().outerRadius(E-P+J).innerRadius(E-P),f=c(b);return[d.centroid(f),e.centroid(f)]}}).style("visibility",function(a){return a.endAngle-a.startAngle<1e-4?"hidden":"visible"})}function m(a,b){n(a,b),o(a,b),p(a)}function n(a,b){var d=G.selectAll("g."+L).data(a).select("path").attr("d",function(a,c){return B(a,c,b)});c.transition(d,S.transitionDuration(),function(a){a.attrTween("d",x)}).attr("fill",z)}function o(a,b){if(S.renderLabel()){var c=G.selectAll("text."+L).data(a);j(c,b),J&&R&&l(a,b)}}function p(a){S.renderTitle()&&G.selectAll("g."+L).data(a).select("title").text(function(a){return S.title()(a.data)})}function q(a){a.exit().remove()}function r(){S.hasFilter()?S.selectAll("g."+L).each(function(a){t(a)?S.highlightSelected(this):S.fadeDeselected(this)}):S.selectAll("g."+L).each(function(){S.resetHighlight(this)})}function s(){return a.svg.arc().outerRadius(E-P).innerRadius(O)}function t(a){return S.hasFilter(S.cappedKeyAccessor(a.data))}function u(){return a.layout.pie().sort(null).value(S.cappedValueAccessor)}function v(a){var b=a.endAngle-a.startAngle;return isNaN(b)||Q>b}function w(a){return 0===S.cappedValueAccessor(a)}function x(b){b.innerRadius=O;var c=this._current;y(c)&&(c={startAngle:0,endAngle:0});var d=a.interpolate(c,b);return this._current=d(0),function(a){return B(d(a),0,s())}}function y(a){return!a||isNaN(a.startAngle)||isNaN(a.endAngle)}function z(a,b){return S.getColor(a.data,b)}function A(a,b){G.attr("class")!==M&&S.onClick(a.data,b)}function B(a,b,c){var d=c(a,b);return d.indexOf("NaN")>=0&&(d="M0,0"),d}function C(b,c){var d;return d=J?a.svg.arc().outerRadius(E-P+J).innerRadius(E-P+J).centroid(b):c.centroid(b),isNaN(d[0])||isNaN(d[1])?"translate(0,0)":"translate("+d+")"}function D(b,c){S.selectAll("g.pie-slice").each(function(d){b.name===d.data.key&&a.select(this).classed("highlight",c)})}var E,F,G,H,I,J,K=.5,L="pie-slice",M="empty-chart",N="empty",O=0,P=0,Q=K,R=!1,S=c.capMixin(c.colorMixin(c.baseMixin({})));return S.colorAccessor(S.cappedKeyAccessor),S.title(function(a){return S.cappedKeyAccessor(a)+": "+S.cappedValueAccessor(a)}),S.slicesCap=S.cap,S.label(S.cappedKeyAccessor),S.renderLabel(!0),S.transitionDuration(350),S._doRender=function(){return S.resetSvg(),G=S.svg().append("g").attr("transform","translate("+S.cx()+","+S.cy()+")"),e(),S},S._applyLabelText=function(a){a.text(function(a){var b=a.data;return!w(b)&&!v(a)||t(a)?S.label()(a.data):""})},S.externalRadiusPadding=function(a){return arguments.length?(P=a,S):P},S.innerRadius=function(a){return arguments.length?(O=a,S):O},S.radius=function(a){return arguments.length?(F=a,S):F},S.cx=function(a){return arguments.length?(H=a,S):H||S.width()/2},S.cy=function(a){return arguments.length?(I=a,S):I||S.height()/2},S._doRedraw=function(){return e(),S},S.minAngleForLabel=function(a){return arguments.length?(Q=a,S):Q},S.emptyTitle=function(a){return 0===arguments.length?N:(N=a,S)},S.externalLabels=function(a){return 0===arguments.length?J:(J=a?a:void 0,S)},S.drawPaths=function(a){return 0===arguments.length?R:(R=a,S)},S.legendables=function(){return S.data().map(function(a,b){var c={name:a.key,data:a.value,others:a.others,chart:S};return c.color=S.getColor(a,b),c})},S.legendHighlight=function(a){D(a,!0)},S.legendReset=function(a){D(a,!1)},S.legendToggle=function(a){S.onClick({key:a.name,others:a.others})},S.anchor(b,d)},c.barChart=function(b,d){function e(a){return c.utils.safeNumber(Math.abs(n.y()(a.y+a.y0)-n.y()(a.y0)))}function f(a,b,d){var f=a.selectAll("text.barLabel").data(d.values,c.pluck("x"));f.enter().append("text").attr("class","barLabel").attr("text-anchor","middle"),n.isOrdinal()&&(f.on("click",n.onClick),f.attr("cursor","pointer")),c.transition(f,n.transitionDuration()).attr("x",function(a){var b=n.x()(a.x);return p||(b+=j/2),c.utils.safeNumber(b)}).attr("y",function(a){var b=n.y()(a.y+a.y0);return a.y<0&&(b-=e(a)),c.utils.safeNumber(b-m)}).text(function(a){return n.label()(a)}),c.transition(f.exit(),n.transitionDuration()).attr("height",0).remove()}function g(a,b,d){var f=a.selectAll("rect.bar").data(d.values,c.pluck("x")),g=f.enter().append("rect").attr("class","bar").attr("fill",c.pluck("data",n.getColor)).attr("y",n.yAxisHeight()).attr("height",0);n.renderTitle()&&g.append("title").text(c.pluck("data",n.title(d.name))),n.isOrdinal()&&f.on("click",n.onClick),c.transition(f,n.transitionDuration()).attr("x",function(a){var b=n.x()(a.x);return p&&(b-=j/2),n.isOrdinal()&&void 0!==o&&(b+=o/2),c.utils.safeNumber(b)}).attr("y",function(a){var b=n.y()(a.y+a.y0);return a.y<0&&(b-=e(a)),c.utils.safeNumber(b)}).attr("width",j).attr("height",function(a){return e(a)}).attr("fill",c.pluck("data",n.getColor)).select("title").text(c.pluck("data",n.title(d.name))),c.transition(f.exit(),n.transitionDuration()).attr("height",0).remove()}function h(){if(void 0===j){var a=n.xUnitCount();j=n.isOrdinal()&&void 0===o?Math.floor(n.x().rangeBand()):o?Math.floor((n.xAxisLength()-(a-1)*o)/a):Math.floor(n.xAxisLength()/(1+n.barPadding())/a),(j===1/0||isNaN(j)||k>j)&&(j=k)}}function i(b,c){return function(){var d=a.select(this),e=d.attr("fill")===b;return c?!e:e}}var j,k=1,l=2,m=3,n=c.stackMixin(c.coordinateGridMixin({})),o=l,p=!1,q=!1;return c.override(n,"rescale",function(){return n._rescale(),j=void 0,n}),c.override(n,"render",function(){return n.round()&&p&&!q&&c.logger.warn("By default, brush rounding is disabled if bars are centered. See dc.js bar chart API documentation for details."),n._render()}),n.label(function(a){return c.utils.printSingleValue(a.y0+a.y)},!1),n.plotData=function(){var b=n.chartBodyG().selectAll("g.stack").data(n.data());h(),b.enter().append("g").attr("class",function(a,b){return"stack _"+b});var c=b.size()-1;b.each(function(b,d){var e=a.select(this);g(e,d,b),n.renderLabel()&&c===d&&f(e,d,b)})},n.fadeDeselectedArea=function(){var a=n.chartBodyG().selectAll("rect.bar"),b=n.brush().extent();if(n.isOrdinal())n.hasFilter()?(a.classed(c.constants.SELECTED_CLASS,function(a){return n.hasFilter(a.x)}),a.classed(c.constants.DESELECTED_CLASS,function(a){return!n.hasFilter(a.x)})):(a.classed(c.constants.SELECTED_CLASS,!1),a.classed(c.constants.DESELECTED_CLASS,!1));else if(n.brushIsEmpty(b))a.classed(c.constants.DESELECTED_CLASS,!1);else{var d=b[0],e=b[1];a.classed(c.constants.DESELECTED_CLASS,function(a){return a.x<d||a.x>=e})}},n.centerBar=function(a){return arguments.length?(p=a,n):p},c.override(n,"onClick",function(a){n._onClick(a.data)}),n.barPadding=function(a){return arguments.length?(n._rangeBandPadding(a),o=void 0,n):n._rangeBandPadding()},n._useOuterPadding=function(){return void 0===o},n.outerPadding=n._outerRangeBandPadding,n.gap=function(a){return arguments.length?(o=a,n):o},n.extendBrush=function(){var a=n.brush().extent();return!n.round()||p&&!q||(a[0]=a.map(n.round())[0],a[1]=a.map(n.round())[1],n.chartBodyG().select(".brush").call(n.brush().extent(a))),a},n.alwaysUseRounding=function(a){return arguments.length?(q=a,n):q},n.legendHighlight=function(a){n.isLegendableHidden(a)||n.g().selectAll("rect.bar").classed("highlight",i(a.color)).classed("fadeout",i(a.color,!0))},n.legendReset=function(){n.g().selectAll("rect.bar").classed("highlight",!1).classed("fadeout",!1)},c.override(n,"xAxisMax",function(){var a=this._xAxisMax();if("resolution"in n.xUnits()){var b=n.xUnits().resolution;a+=b}return a}),n.anchor(b,d)},c.lineChart=function(b,d){function e(a,b){return z.getColor.call(a,a.values,b)}function f(b,d){var f=a.svg.line().x(function(a){return z.x()(a.x)}).y(function(a){return z.y()(a.y+a.y0)}).interpolate(F).tension(G);r&&f.defined(r);var g=b.append("path").attr("class","line").attr("stroke",e);s&&g.attr("stroke-dasharray",s),c.transition(d.select("path.line"),z.transitionDuration()).attr("stroke",e).attr("d",function(a){return h(f(a.values))})}function g(b,d){if(A){var f=a.svg.area().x(function(a){return z.x()(a.x)}).y(function(a){return z.y()(a.y+a.y0)}).y0(function(a){return z.y()(a.y0)}).interpolate(F).tension(G);r&&f.defined(r),b.append("path").attr("class","area").attr("fill",e).attr("d",function(a){return h(f(a.values))}),c.transition(d.select("path.area"),z.transitionDuration()).attr("fill",e).attr("d",function(a){return h(f(a.values))})}}function h(a){return!a||a.indexOf("NaN")>=0?"M0,0":a}function i(b,d){if(!z.brushOn()&&z.xyTipsOn()){var e=u+"-list",f=b.select("g."+e);f.empty()&&(f=b.append("g").attr("class",e)),d.each(function(b,d){var e=b.values;r&&(e=e.filter(r));var g=f.select("g."+u+"._"+d);g.empty()&&(g=f.append("g").attr("class",u+" _"+d)),j(g);var h=g.selectAll("circle."+v).data(e,c.pluck("x"));h.enter().append("circle").attr("class",v).attr("r",m()).style("fill-opacity",D).style("stroke-opacity",E).on("mousemove",function(){var b=a.select(this);k(b),l(b,g)}).on("mouseout",function(){var b=a.select(this);n(b),o(g)}),h.attr("cx",function(a){return c.utils.safeNumber(z.x()(a.x))}).attr("cy",function(a){return c.utils.safeNumber(z.y()(a.y+a.y0))}).attr("fill",z.getColor).call(p,b),h.exit().remove()})}}function j(a){var b=a.select("path."+w).empty()?a.append("path").attr("class",w):a.select("path."+w);b.style("display","none").attr("stroke-dasharray","5,5");var c=a.select("path."+x).empty()?a.append("path").attr("class",x):a.select("path."+x);c.style("display","none").attr("stroke-dasharray","5,5")}function k(a){return a.style("fill-opacity",.8),a.style("stroke-opacity",.8),a.attr("r",B),a}function l(a,b){var c=a.attr("cx"),d=a.attr("cy"),e=z._yAxisX()-z.margins().left,f="M"+e+" "+d+"L"+c+" "+d,g="M"+c+" "+z.yAxisHeight()+"L"+c+" "+d;b.select("path."+w).style("display","").attr("d",f),b.select("path."+x).style("display","").attr("d",g)}function m(){return C||B}function n(a){a.style("fill-opacity",D).style("stroke-opacity",E).attr("r",m())}function o(a){a.select("path."+w).style("display","none"),a.select("path."+x).style("display","none")}function p(a,b){z.renderTitle()&&(a.selectAll("title").remove(),a.append("title").text(c.pluck("data",z.title(b.name))))}function q(b,c,d){return function(){var e=a.select(this),f=e.attr("stroke")===b&&e.attr("stroke-dasharray")===(c instanceof Array?c.join(","):null)||e.attr("fill")===b;return d?!f:f}}var r,s,t=5,u="dc-tooltip",v="dot",w="yRef",x="xRef",y=1e-6,z=c.stackMixin(c.coordinateGridMixin({})),A=!1,B=t,C=null,D=y,E=y,F="linear",G=.7,H=!0;return z.transitionDuration(500),z._rangeBandPadding(1),z.plotData=function(){var a=z.chartBodyG(),b=a.selectAll("g.stack-list");b.empty()&&(b=a.append("g").attr("class","stack-list"));var c=b.selectAll("g.stack").data(z.data()),d=c.enter().append("g").attr("class",function(a,b){return"stack _"+b});f(d,c),g(d,c),i(a,c)},z.interpolate=function(a){return arguments.length?(F=a,z):F},z.tension=function(a){return arguments.length?(G=a,z):G},z.defined=function(a){return arguments.length?(r=a,z):r},z.dashStyle=function(a){return arguments.length?(s=a,z):s},z.renderArea=function(a){return arguments.length?(A=a,z):A},z.xyTipsOn=function(a){return arguments.length?(H=a,z):H},z.dotRadius=function(a){return arguments.length?(B=a,z):B},z.renderDataPoints=function(a){return arguments.length?(a?(D=a.fillOpacity||.8,E=a.strokeOpacity||.8,C=a.radius||2):(D=y,E=y,C=null),z):{fillOpacity:D,strokeOpacity:E,radius:C}},z.legendHighlight=function(a){z.isLegendableHidden(a)||z.g().selectAll("path.line, path.area").classed("highlight",q(a.color,a.dashstyle)).classed("fadeout",q(a.color,a.dashstyle,!0))},z.legendReset=function(){z.g().selectAll("path.line, path.area").classed("highlight",!1).classed("fadeout",!1)},c.override(z,"legendables",function(){var a=z._legendables();return s?a.map(function(a){return a.dashstyle=s,a}):a}),z.anchor(b,d)},c.dataCount=function(b,d){var e=a.format(",d"),f=c.baseMixin({}),g={some:"",all:""};return f.html=function(a){return arguments.length?(a.all&&(g.all=a.all),a.some&&(g.some=a.some),f):g},f.formatNumber=function(a){return arguments.length?(e=a,f):e},f._doRender=function(){var a=f.dimension().size(),b=f.group().value(),c=e(a),d=e(b);return a===b&&""!==g.all?f.root().html(g.all.replace("%total-count",c).replace("%filter-count",d)):""!==g.some?f.root().html(g.some.replace("%total-count",c).replace("%filter-count",d)):(f.selectAll(".total-count").text(c),f.selectAll(".filter-count").text(d)),f},f._doRedraw=function(){return f._doRender()},f.anchor(b,d)},c.dataTable=function(b,d){function e(){var a=!0;if(p.forEach(function(b){a&="function"==typeof b}),!a){var b=n.selectAll("thead").data([0]);b.enter().append("thead"),b.exit().remove();var c=b.selectAll("tr").data([0]);c.enter().append("tr"),c.exit().remove();var d=c.selectAll("th").data(p);d.enter().append("th"),d.exit().remove(),d.attr("class",m).html(function(a){return n._doColumnHeaderFormat(a)})}var e=n.root().selectAll("tbody").data(f(),function(a){return n.keyAccessor()(a)}),g=e.enter().append("tbody");return t===!0&&g.append("tr").attr("class",l).append("td").attr("class",i).attr("colspan",p.length).html(function(a){return n.keyAccessor()(a)}),e.exit().remove(),g}function f(){var b;return b=r===a.ascending?n.dimension().bottom(o):n.dimension().top(o),a.nest().key(n.group()).sortKeys(r).entries(b.sort(function(a,b){return r(q(a),q(b))}).slice(s,h))}function g(a){var b=a.order().selectAll("tr."+j).data(function(a){return a.values}),c=b.enter().append("tr").attr("class",j);return p.forEach(function(a,b){c.append("td").attr("class",k+" _"+b).html(function(b){return n._doColumnValueFormat(a,b)})}),b.exit().remove(),b}var h,i="dc-table-label",j="dc-table-row",k="dc-table-column",l="dc-table-group",m="dc-table-head",n=c.baseMixin({}),o=25,p=[],q=function(a){return a},r=a.ascending,s=0,t=!0;return n._doRender=function(){return n.selectAll("tbody").remove(),g(e()),n},n._doColumnValueFormat=function(a,b){return"function"==typeof a?a(b):"string"==typeof a?b[a]:a.format(b)},n._doColumnHeaderFormat=function(a){return"function"==typeof a?n._doColumnHeaderFnToString(a):"string"==typeof a?n._doColumnHeaderCapitalize(a):String(a.label)},n._doColumnHeaderCapitalize=function(a){return a.charAt(0).toUpperCase()+a.slice(1)},n._doColumnHeaderFnToString=function(a){var b=String(a),c=b.indexOf("return ");if(c>=0){var d=b.lastIndexOf(";");if(d>=0){b=b.substring(c+7,d);var e=b.indexOf("numberFormat");e>=0&&(b=b.replace("numberFormat",""))}}return b},n._doRedraw=function(){return n._doRender()},n.size=function(a){return arguments.length?(o=a,n):o},n.beginSlice=function(a){return arguments.length?(s=a,n):s},n.endSlice=function(a){return arguments.length?(h=a,n):h},n.columns=function(a){return arguments.length?(p=a,n):p},n.sortBy=function(a){return arguments.length?(q=a,n):q},n.order=function(a){return arguments.length?(r=a,n):r},n.showGroups=function(a){return arguments.length?(t=a,n):t},n.anchor(b,d)},c.dataGrid=function(b,d){function e(){var a=m.root().selectAll("div."+l).data(f(),function(a){return m.keyAccessor()(a)}),b=a.enter().append("div").attr("class",l);return s&&b.html(function(a){return s(a)}),a.exit().remove(),b}function f(){var b=m.dimension().top(n);return a.nest().key(m.group()).sortKeys(q).entries(b.sort(function(a,b){return q(p(a),p(b))}).slice(r,h))}function g(a){var b=a.order().selectAll("div."+j).data(function(a){return a.values});return b.enter().append("div").attr("class",j).html(function(a){return o(a)}),b.exit().remove(),b}var h,i="dc-grid-label",j="dc-grid-item",k="dc-grid-group",l="dc-grid-top",m=c.baseMixin({}),n=999,o=function(a){return"you need to provide an html() handling param:  "+JSON.stringify(a)},p=function(a){return a},q=a.ascending,r=0,s=function(a){return"<div class='"+k+"'><h1 class='"+i+"'>"+m.keyAccessor()(a)+"</h1></div>"};return m._doRender=function(){return m.selectAll("div."+l).remove(),g(e()),m},m._doRedraw=function(){return m._doRender()},m.beginSlice=function(a){return arguments.length?(r=a,m):r},m.endSlice=function(a){return arguments.length?(h=a,m):h},m.size=function(a){return arguments.length?(n=a,m):n},m.html=function(a){return arguments.length?(o=a,m):o},m.htmlGroup=function(a){return arguments.length?(s=a,m):s},m.sortBy=function(a){return arguments.length?(p=a,m):p},m.order=function(a){return arguments.length?(q=a,m):q},m.anchor(b,d)},c.bubbleChart=function(b,d){function e(a){var b=a.enter().append("g");b.attr("class",j.BUBBLE_NODE_CLASS).attr("transform",m).append("circle").attr("class",function(a,b){return j.BUBBLE_CLASS+" _"+b}).on("click",j.onClick).attr("fill",j.getColor).attr("r",0),c.transition(a,j.transitionDuration()).selectAll("circle."+j.BUBBLE_CLASS).attr("r",function(a){return j.bubbleR(a)}).attr("opacity",function(a){return j.bubbleR(a)>0?1:0}),j._doRenderLabel(b),j._doRenderTitles(b)}function f(a){c.transition(a,j.transitionDuration()).attr("transform",m).selectAll("circle."+j.BUBBLE_CLASS).attr("fill",j.getColor).attr("r",function(a){return j.bubbleR(a)}).attr("opacity",function(a){return j.bubbleR(a)>0?1:0}),j.doUpdateLabels(a),j.doUpdateTitles(a)}function g(a){a.exit().remove()}function h(a){var b=j.x()(j.keyAccessor()(a));return isNaN(b)&&(b=0),b}function i(a){var b=j.y()(j.valueAccessor()(a));return isNaN(b)&&(b=0),b}var j=c.bubbleMixin(c.coordinateGridMixin({})),k=!1,l=!1;j.transitionDuration(750);var m=function(a){return"translate("+h(a)+","+i(a)+")"};return j.elasticRadius=function(a){return arguments.length?(k=a,j):k},j.sortBubbleSize=function(a){return arguments.length?(l=a,j):l},j.plotData=function(){k&&j.r().domain([j.rMin(),j.rMax()]),j.r().range([j.MIN_RADIUS,j.xAxisLength()*j.maxBubbleRelativeSize()]);var b=j.data();if(l){var c=j.radiusValueAccessor();b.sort(function(b,d){return a.descending(c(b),c(d))})}var d=j.chartBodyG().selectAll("g."+j.BUBBLE_NODE_CLASS).data(b,function(a){return a.key});l&&d.order(),e(d),f(d),g(d),j.fadeDeselectedArea()},j.renderBrush=function(){},j.redrawBrush=function(){j.fadeDeselectedArea()},j.anchor(b,d)},c.compositeChart=function(b,d){function e(a,b){var c,d,e,f;if(a&&(c=l(),d=o()),b&&(e=m(),f=p()),v.alignYAxes()&&a&&b&&(0>c||0>e)){var g,h;0>c&&(g=d/c),0>e&&(h=f/e),0>c&&0>e?h>g?f=e*g:d=c*h:0>c?e=f/g:c=d/(f/e)}return{lyAxisMin:c,lyAxisMax:d,ryAxisMin:e,ryAxisMax:f}}function f(b){var c=void 0===v.rightY()||v.elasticY(),d=c||v.resizing();void 0===v.rightY()&&v.rightY(a.scale.linear()),c&&v.rightY().domain([b.ryAxisMin,b.ryAxisMax]),d&&v.rightY().rangeRound([v.yAxisHeight(),0]),v.rightY().range([v.yAxisHeight(),0]),v.rightYAxis(v.rightYAxis().scale(v.rightY())),v.rightYAxis().orient("right")}function g(b){var c=void 0===v.y()||v.elasticY(),d=c||v.resizing();void 0===v.y()&&v.y(a.scale.linear()),c&&v.y().domain([b.lyAxisMin,b.lyAxisMax]),d&&v.y().rangeRound([v.yAxisHeight(),0]),v.y().range([v.yAxisHeight(),0]),v.yAxis(v.yAxis().scale(v.y())),v.yAxis().orient("left")}function h(a,b){a._generateG(v.g()),a.g().attr("class",t+" _"+b)}function i(){return w.filter(function(a){return!a.useRightYAxis()})}function j(){return w.filter(function(a){return a.useRightYAxis()})}function k(a){return a.map(function(a){return a.yAxisMin()})}function l(){return a.min(k(i()))}function m(){return a.min(k(j()))}function n(a){return a.map(function(a){return a.yAxisMax()})}function o(){return c.utils.add(a.max(n(i())),v.yAxisPadding())}function p(){return c.utils.add(a.max(n(j())),v.yAxisPadding())}function q(){return w.map(function(a){return a.xAxisMin()})}function r(){return w.map(function(a){return a.xAxisMax()})}var s,t="sub",u=12,v=c.coordinateGridMixin({}),w=[],x={},y=!1,z=!0,A=!1,B=a.svg.axis(),C=0,D=u,E=!1;return v._mandatoryAttributes([]),v.transitionDuration(500),c.override(v,"_generateG",function(){for(var a=this.__generateG(),b=0;b<w.length;++b){var c=w[b];h(c,b),c.dimension()||c.dimension(v.dimension()),c.group()||c.group(v.group()),c.chartGroup(v.chartGroup()),c.svg(v.svg()),c.xUnits(v.xUnits()),c.transitionDuration(v.transitionDuration()),c.brushOn(v.brushOn()),c.renderTitle(v.renderTitle()),c.elasticX(v.elasticX())}return a}),v._brushing=function(){for(var a=v.extendBrush(),b=v.brushIsEmpty(a),c=0;c<w.length;++c)w[c].filter(null),b||w[c].filter(a)},v._prepareYAxis=function(){var a=0!==i().length,b=0!==j().length,c=e(a,b);a&&g(c),b&&f(c),i().length>0&&!E?v._renderHorizontalGridLinesForAxis(v.g(),v.y(),v.yAxis()):j().length>0&&v._renderHorizontalGridLinesForAxis(v.g(),s,B)},v.renderYAxis=function(){0!==i().length&&(v.renderYAxisAt("y",v.yAxis(),v.margins().left),v.renderYAxisLabel("y",v.yAxisLabel(),-90)),0!==j().length&&(v.renderYAxisAt("yr",v.rightYAxis(),v.width()-v.margins().right),v.renderYAxisLabel("yr",v.rightYAxisLabel(),90,v.width()-D))},v.plotData=function(){for(var a=0;a<w.length;++a){var b=w[a];b.g()||h(b,a),y&&b.colors(v.colors()),b.x(v.x()),b.xAxis(v.xAxis()),b.useRightYAxis()?(b.y(v.rightY()),b.yAxis(v.rightYAxis())):(b.y(v.y()),b.yAxis(v.yAxis())),b.plotData(),b._activateRenderlets()}},v.useRightAxisGridLines=function(a){return arguments?(E=a,v):E},v.childOptions=function(a){return arguments.length?(x=a,w.forEach(function(a){a.options(x)}),v):x},v.fadeDeselectedArea=function(){for(var a=0;a<w.length;++a){var b=w[a];b.brush(v.brush()),b.fadeDeselectedArea()}},v.rightYAxisLabel=function(a,b){return arguments.length?(C=a,v.margins().right-=D,D=void 0===b?u:b,v.margins().right+=D,v):C},v.compose=function(a){return w=a,w.forEach(function(a){a.height(v.height()),a.width(v.width()),a.margins(v.margins()),z&&a.title(v.title()),a.options(x)}),v},v.children=function(){return w},v.shareColors=function(a){return arguments.length?(y=a,v):y},v.shareTitle=function(a){return arguments.length?(z=a,v):z},v.rightY=function(a){return arguments.length?(s=a,v.rescale(),v):s},v.alignYAxes=function(a){return arguments.length?(A=a,v.rescale(),v):A},delete v.yAxisMin,delete v.yAxisMax,c.override(v,"xAxisMin",function(){return c.utils.subtract(a.min(q()),v.xAxisPadding())}),c.override(v,"xAxisMax",function(){return c.utils.add(a.max(r()),v.xAxisPadding())}),v.legendables=function(){return w.reduce(function(a,b){return y&&b.colors(v.colors()),a.push.apply(a,b.legendables()),a},[])},v.legendHighlight=function(a){for(var b=0;b<w.length;++b){var c=w[b];c.legendHighlight(a)}},v.legendReset=function(a){for(var b=0;b<w.length;++b){var c=w[b];c.legendReset(a)}},v.legendToggle=function(){console.log("composite should not be getting legendToggle itself")},v.rightYAxis=function(a){return arguments.length?(B=a,v):B},v.anchor(b,d)},c.seriesChart=function(b,d){function e(b,c){return a.ascending(i.keyAccessor()(b),i.keyAccessor()(c))}function f(a){j[a].g()&&j[a].g().remove(),delete j[a]}function g(){Object.keys(j).map(f),j={}}var h,i=c.compositeChart(b,d),j={},k=c.lineChart,l=a.ascending,m=e;return i._mandatoryAttributes().push("seriesAccessor","chart"),i.shareColors(!0),i._preprocessData=function(){var b,c=[],e=a.nest().key(h);l&&e.sortKeys(l),m&&e.sortValues(m);var g=e.entries(i.data()),n=g.map(function(e,f){var g=j[e.key]||k.call(i,i,d,e.key,f);return j[e.key]||(b=!0),j[e.key]=g,c.push(e.key),g.dimension(i.dimension()).group({all:a.functor(e.values)},e.key).keyAccessor(i.keyAccessor()).valueAccessor(i.valueAccessor()).brushOn(i.brushOn())});Object.keys(j).filter(function(a){return-1===c.indexOf(a)}).forEach(function(a){f(a),b=!0}),i._compose(n),b&&i.legend()&&i.legend().render()},i.chart=function(a){return arguments.length?(k=a,g(),i):k},i.seriesAccessor=function(a){return arguments.length?(h=a,g(),i):h},i.seriesSort=function(a){return arguments.length?(l=a,g(),i):l},i.valueSort=function(a){return arguments.length?(m=a,g(),i):m},i._compose=i.compose,delete i.compose,i},c.geoChoroplethChart=function(b,d){function e(a){var b=f();if(g(a)){var c=h(a);n(c,a,b),o(c,a,b)}}function f(){for(var a={},b=p.data(),c=0;c<b.length;++c)a[p.keyAccessor()(b[c])]=p.valueAccessor()(b[c]);return a}function g(a){return m(a).keyAccessor}function h(a){var b=p.svg().selectAll(i(a)).classed("selected",function(b){return j(a,b)}).classed("deselected",function(b){return k(a,b)}).attr("class",function(b){var d=m(a).name,e=c.utils.nameToId(m(a).keyAccessor(b)),f=d+" "+e;return j(a,b)&&(f+=" selected"),k(a,b)&&(f+=" deselected"),f});return b}function i(a){return"g.layer"+a+" g."+m(a).name}function j(a,b){return p.hasFilter()&&p.hasFilter(l(a,b))}function k(a,b){return p.hasFilter()&&!p.hasFilter(l(a,b))}function l(a,b){return m(a).keyAccessor(b)}function m(a){return s[a]}function n(b,d,e){var f=b.select("path").attr("fill",function(){var b=a.select(this).attr("fill");return b?b:"none"}).on("click",function(a){return p.onClick(a,d)});c.transition(f,p.transitionDuration()).attr("fill",function(a,b){return p.getColor(e[m(d).keyAccessor(a)],b)})}function o(a,b,c){p.renderTitle()&&a.selectAll("title").text(function(a){var d=l(b,a),e=c[d];return p.title()({key:d,value:e})})}var p=c.colorMixin(c.baseMixin({}));p.colorAccessor(function(a){return a||0});var q,r=a.geo.path(),s=[];return p._doRender=function(){p.resetSvg();for(var a=0;a<s.length;++a){var b=p.svg().append("g").attr("class","layer"+a),c=b.selectAll("g."+m(a).name).data(m(a).data).enter().append("g").attr("class",m(a).name);c.append("path").attr("fill","white").attr("d",r),c.append("title"),e(a)}q=!1},p.onClick=function(a,b){var d=m(b).keyAccessor(a);c.events.trigger(function(){p.filter(d),p.redrawGroup()})},p._doRedraw=function(){for(var a=0;a<s.length;++a)e(a),q&&p.svg().selectAll("g."+m(a).name+" path").attr("d",r);q=!1},p.overlayGeoJson=function(a,b,c){for(var d=0;d<s.length;++d)if(s[d].name===b)return s[d].data=a,s[d].keyAccessor=c,p;return s.push({name:b,data:a,keyAccessor:c}),p},p.projection=function(a){return r.projection(a),q=!0,p},p.geoJsons=function(){return s},p.geoPath=function(){return r},p.removeGeoJson=function(a){for(var b=[],c=0;c<s.length;++c){var d=s[c];d.name!==a&&b.push(d)}return s=b,p},p.anchor(b,d)},c.bubbleOverlay=function(b,d){function e(){return j=n.select("g."+k),j.empty()&&(j=n.svg().append("g").attr("class",k)),j}function f(){var a=g();o.forEach(function(b){var d=h(b,a),e=d.select("circle."+m);e.empty()&&(e=d.append("circle").attr("class",m).attr("r",0).attr("fill",n.getColor).on("click",n.onClick)),c.transition(e,n.transitionDuration()).attr("r",function(a){return n.bubbleR(a)}),n._doRenderLabel(d),n._doRenderTitles(d)})}function g(){var a={};return n.data().forEach(function(b){a[n.keyAccessor()(b)]=b}),a}function h(a,b){var d=l+" "+c.utils.nameToId(a.name),e=j.select("g."+c.utils.nameToId(a.name));return e.empty()&&(e=j.append("g").attr("class",d).attr("transform","translate("+a.x+","+a.y+")")),e.datum(b[a.name]),e}function i(){var a=g();o.forEach(function(b){var d=h(b,a),e=d.select("circle."+m);c.transition(e,n.transitionDuration()).attr("r",function(a){return n.bubbleR(a)}).attr("fill",n.getColor),n.doUpdateLabels(d),n.doUpdateTitles(d)})}var j,k="bubble-overlay",l="node",m="bubble",n=c.bubbleMixin(c.baseMixin({})),o=[];return n.transitionDuration(750),n.radiusValueAccessor(function(a){return a.value}),n.point=function(a,b,c){return o.push({name:a,x:b,y:c}),n},n._doRender=function(){return j=e(),n.r().range([n.MIN_RADIUS,n.width()*n.maxBubbleRelativeSize()]),f(),n.fadeDeselectedArea(),n},n._doRedraw=function(){return i(),n.fadeDeselectedArea(),n},n.debug=function(b){if(b){var d=n.select("g."+c.constants.DEBUG_GROUP_CLASS);d.empty()&&(d=n.svg().append("g").attr("class",c.constants.DEBUG_GROUP_CLASS));var e=d.append("text").attr("x",10).attr("y",20);d.append("rect").attr("width",n.width()).attr("height",n.height()).on("mousemove",function(){var b=a.mouse(d.node()),c=b[0]+", "+b[1];e.text(c)})}else n.selectAll(".debug").remove();return n},n.anchor(b,d),n},c.rowChart=function(b,d){function e(){if(!t||u){var b=a.extent(v,G.cappedValueAccessor);b[0]>0&&(b[0]=0),t=a.scale.linear().domain(b).range([0,G.effectiveWidth()])}H.scale(t)}function f(){var a=s.select("g.axis");e(),a.empty()&&(a=s.append("g").attr("class","axis")),a.attr("transform","translate(0, "+G.effectiveHeight()+")"),c.transition(a,G.transitionDuration()).call(H)}function g(){s.selectAll("g.tick").select("line.grid-line").remove(),s.selectAll("g.tick").append("line").attr("class","grid-line").attr("x1",0).attr("y1",0).attr("x2",0).attr("y2",function(){return-G.effectiveHeight()})}function h(){v=G.data(),f(),g();var a=s.selectAll("g."+D).data(v);i(a),j(a),l(a)}function i(a){var b=a.enter().append("g").attr("class",function(a,b){return D+" _"+b});b.append("rect").attr("width",0),n(b),o(a)}function j(a){a.exit().remove()}function k(){var a=t(0);return a===-(1/0)||a!==a?t(1):a}function l(a){var b,d=v.length;b=C?C:(G.effectiveHeight()-(d+1)*B)/d,y||(x=b/2);var e=a.attr("transform",function(a,c){return"translate(0,"+((c+1)*B+c*b)+")"}).select("rect").attr("height",b).attr("fill",G.getColor).on("click",p).classed("deselected",function(a){return G.hasFilter()?!r(a):!1}).classed("selected",function(a){return G.hasFilter()?r(a):!1});c.transition(e,G.transitionDuration()).attr("width",function(a){return Math.abs(k()-t(G.valueAccessor()(a)))}).attr("transform",q),m(a),o(a)}function m(a){G.renderTitle()&&(a.selectAll("title").remove(),a.append("title").text(G.title()))}function n(a){G.renderLabel()&&a.append("text").on("click",p),G.renderTitleLabel()&&a.append("text").attr("class",E).on("click",p)}function o(a){if(G.renderLabel()){var b=a.select("text").attr("x",w).attr("y",x).attr("dy",z).on("click",p).attr("class",function(a,b){return D+" _"+b}).text(function(a){return G.label()(a)});c.transition(b,G.transitionDuration()).attr("transform",q)}if(G.renderTitleLabel()){var d=a.select("."+E).attr("x",G.effectiveWidth()-A).attr("y",x).attr("text-anchor","end").on("click",p).attr("class",function(a,b){return E+" _"+b}).text(function(a){return G.title()(a)});c.transition(d,G.transitionDuration()).attr("transform",q)}}function p(a){G.onClick(a)}function q(a){var b=t(G.cappedValueAccessor(a)),c=k(),d=b>c?c:b;return"translate("+d+",0)"}function r(a){return G.hasFilter(G.cappedKeyAccessor(a))}var s,t,u,v,w=10,x=15,y=!1,z="0.35em",A=2,B=5,C=!1,D="row",E="titlerow",F=!1,G=c.capMixin(c.marginMixin(c.colorMixin(c.baseMixin({})))),H=a.svg.axis().orient("bottom");return G.rowsCap=G.cap,G._doRender=function(){return G.resetSvg(),s=G.svg().append("g").attr("transform","translate("+G.margins().left+","+G.margins().top+")"),h(),G},G.title(function(a){return G.cappedKeyAccessor(a)+": "+G.cappedValueAccessor(a)}),G.label(G.cappedKeyAccessor),
G.x=function(a){return arguments.length?(t=a,G):t},G.renderTitleLabel=function(a){return arguments.length?(F=a,G):F},G._doRedraw=function(){return h(),G},G.xAxis=function(){return H},G.fixedBarHeight=function(a){return arguments.length?(C=a,G):C},G.gap=function(a){return arguments.length?(B=a,G):B},G.elasticX=function(a){return arguments.length?(u=a,G):u},G.labelOffsetX=function(a){return arguments.length?(w=a,G):w},G.labelOffsetY=function(a){return arguments.length?(x=a,y=!0,G):x},G.titleLabelOffsetX=function(a){return arguments.length?(A=a,G):A},G.anchor(b,d)},c.legend=function(){function a(){return j+i}var b,d,e=2,f={},g=0,h=0,i=12,j=5,k=!1,l=560,m=70,n=!1,o=c.pluck("name");return f.parent=function(a){return arguments.length?(b=a,f):b},f.render=function(){b.svg().select("g.dc-legend").remove(),d=b.svg().append("g").attr("class","dc-legend").attr("transform","translate("+g+","+h+")");var f=b.legendables(),p=d.selectAll("g.dc-legend-item").data(f).enter().append("g").attr("class","dc-legend-item").on("mouseover",function(a){b.legendHighlight(a)}).on("mouseout",function(a){b.legendReset(a)}).on("click",function(a){a.chart.legendToggle(a)});d.selectAll("g.dc-legend-item").classed("fadeout",function(a){return a.chart.isLegendableHidden(a)}),f.some(c.pluck("dashstyle"))?p.append("line").attr("x1",0).attr("y1",i/2).attr("x2",i).attr("y2",i/2).attr("stroke-width",2).attr("stroke-dasharray",c.pluck("dashstyle")).attr("stroke",c.pluck("color")):p.append("rect").attr("width",i).attr("height",i).attr("fill",function(a){return a?a.color:"blue"}),p.append("text").text(o).attr("x",i+e).attr("y",function(){return i/2+(this.clientHeight?this.clientHeight:13)/2-2});var q=0,r=0;p.attr("transform",function(b,c){if(k){var d="translate("+q+","+r*a()+")",e=n===!0?this.getBBox().width+j:m;return q+e>=l?(++r,q=0):q+=e,d}return"translate(0,"+c*a()+")"})},f.x=function(a){return arguments.length?(g=a,f):g},f.y=function(a){return arguments.length?(h=a,f):h},f.gap=function(a){return arguments.length?(j=a,f):j},f.itemHeight=function(a){return arguments.length?(i=a,f):i},f.horizontal=function(a){return arguments.length?(k=a,f):k},f.legendWidth=function(a){return arguments.length?(l=a,f):l},f.itemWidth=function(a){return arguments.length?(m=a,f):m},f.autoItemWidth=function(a){return arguments.length?(n=a,f):n},f.legendText=function(a){return arguments.length?(o=a,f):o},f},c.scatterPlot=function(b,d){function e(b,d){var e=g.selectAll(".chart-body path.symbol").filter(function(){return b(a.select(this))}),f=h.size();h.size(Math.pow(d,2)),c.transition(e,g.transitionDuration()).attr("d",h),h.size(f)}function f(a){var b=g.selectAll(".chart-body path.symbol").each(function(b){this.filtered=a&&a.isFiltered(b.key)});c.transition(b,g.transitionDuration()).attr("d",h)}var g=c.coordinateGridMixin({}),h=a.svg.symbol(),i=function(a){return a.value},j=g.keyAccessor();g.keyAccessor(function(a){return j(a)[0]}),g.valueAccessor(function(a){return j(a)[1]}),g.colorAccessor(function(){return g._groupName});var k=function(a){return"translate("+g.x()(g.keyAccessor()(a))+","+g.y()(g.valueAccessor()(a))+")"},l=3,m=5,n=0;return h.size(function(a){return i(a)?this.filtered?Math.pow(m,2):Math.pow(l,2):n}),c.override(g,"_filter",function(a){return arguments.length?g.__filter(c.filters.RangedTwoDimensionalFilter(a)):g.__filter()}),g.plotData=function(){var a=g.chartBodyG().selectAll("path.symbol").data(g.data());a.enter().append("path").attr("class","symbol").attr("opacity",0).attr("fill",g.getColor).attr("transform",k),c.transition(a,g.transitionDuration()).attr("opacity",function(a){return i(a)?1:0}).attr("fill",g.getColor).attr("transform",k).attr("d",h),c.transition(a.exit(),g.transitionDuration()).attr("opacity",0).remove()},g.existenceAccessor=function(a){return arguments.length?(i=a,this):i},g.symbol=function(a){return arguments.length?(h.type(a),g):h.type()},g.symbolSize=function(a){return arguments.length?(l=a,g):l},g.highlightedSize=function(a){return arguments.length?(m=a,g):m},g.hiddenSize=function(a){return arguments.length?(n=a,g):n},g.legendables=function(){return[{chart:g,name:g._groupName,color:g.getColor()}]},g.legendHighlight=function(b){e(function(a){return a.attr("fill")===b.color},m),g.selectAll(".chart-body path.symbol").filter(function(){return a.select(this).attr("fill")!==b.color}).classed("fadeout",!0)},g.legendReset=function(b){e(function(a){return a.attr("fill")===b.color},l),g.selectAll(".chart-body path.symbol").filter(function(){return a.select(this).attr("fill")!==b.color}).classed("fadeout",!1)},g.setHandlePaths=function(){},g.extendBrush=function(){var a=g.brush().extent();return g.round()&&(a[0]=a[0].map(g.round()),a[1]=a[1].map(g.round()),g.g().select(".brush").call(g.brush().extent(a))),a},g.brushIsEmpty=function(a){return g.brush().empty()||!a||a[0][0]>=a[1][0]||a[0][1]>=a[1][1]},g._brushing=function(){var a=g.extendBrush();if(g.redrawBrush(g.g()),g.brushIsEmpty(a))c.events.trigger(function(){g.filter(null),g.redrawGroup()}),f(!1);else{var b=c.filters.RangedTwoDimensionalFilter(a);c.events.trigger(function(){g.filter(null),g.filter(b),g.redrawGroup()},c.constants.EVENT_DELAY),f(b)}},g.setBrushY=function(a){a.call(g.brush().y(g.y()))},g.anchor(b,d)},c.numberDisplay=function(b,d){var e="number-display",f=a.format(".2s"),g=c.baseMixin({}),h={one:"",some:"",none:""};return g._mandatoryAttributes(["group"]),g.html=function(a){return arguments.length?(a.none?h.none=a.none:a.one?h.none=a.one:a.some&&(h.none=a.some),a.one?h.one=a.one:a.some&&(h.one=a.some),a.some?h.some=a.some:a.one&&(h.some=a.one),g):h},g.value=function(){return g.data()},g.data(function(a){var b=a.value?a.value():a.top(1)[0];return g.valueAccessor()(b)}),g.transitionDuration(250),g._doRender=function(){var b=g.value(),c=g.selectAll("."+e);c.empty()&&(c=c.data([0]).enter().append("span").attr("class",e)),c.transition().duration(g.transitionDuration()).ease("quad-out-in").tween("text",function(){var c=a.interpolateNumber(this.lastValue||0,b);return this.lastValue=b,function(a){var d=null,e=g.formatNumber()(c(a));0===b&&""!==h.none?d=h.none:1===b&&""!==h.one?d=h.one:""!==h.some&&(d=h.some),this.innerHTML=d?d.replace("%number",e):e}})},g._doRedraw=function(){return g._doRender()},g.formatNumber=function(a){return arguments.length?(f=a,g):f},g.anchor(b,d)},c.heatMap=function(b,d){function e(a,b){var d=m.selectAll(".box-group").filter(function(c){return c.key[a]===b}),e=d.filter(function(a){return!m.hasFilter(a.key)});c.events.trigger(function(){e.empty()?d.each(function(a){m.filter(a.key)}):e.each(function(a){m.filter(a.key)}),m.redrawGroup()})}function f(a,b,c){return!b||c[b-1]!==a}var g,h,i,j=6.75,k=j,l=j,m=c.colorMixin(c.marginMixin(c.baseMixin({})));m._mandatoryAttributes(["group"]),m.title(m.colorAccessor());var n=function(a){return a},o=function(a){return a};m.colsLabel=function(a){return arguments.length?(n=a,m):n},m.rowsLabel=function(a){return arguments.length?(o=a,m):o};var p=function(a){e(0,a)},q=function(a){e(1,a)},r=function(a){var b=a.key;c.events.trigger(function(){m.filter(b),m.redrawGroup()})};return c.override(m,"filter",function(a){return arguments.length?m._filter(c.filters.TwoDimensionalFilter(a)):m._filter()}),m.rows=function(b){if(arguments.length)return i=b,m;if(i)return i;var c=m.data().map(m.valueAccessor());return c.sort(a.ascending),a.scale.ordinal().domain(c.filter(f))},m.cols=function(b){if(arguments.length)return h=b,m;if(h)return h;var c=m.data().map(m.keyAccessor());return c.sort(a.ascending),a.scale.ordinal().domain(c.filter(f))},m._doRender=function(){return m.resetSvg(),g=m.svg().append("g").attr("class","heatmap").attr("transform","translate("+m.margins().left+","+m.margins().top+")"),m._doRedraw()},m._doRedraw=function(){var a=m.rows(),b=m.cols(),d=a.domain().length,e=b.domain().length,f=Math.floor(m.effectiveWidth()/e),h=Math.floor(m.effectiveHeight()/d);b.rangeRoundBands([0,m.effectiveWidth()]),a.rangeRoundBands([m.effectiveHeight(),0]);var i=g.selectAll("g.box-group").data(m.data(),function(a,b){return m.keyAccessor()(a,b)+"\x00"+m.valueAccessor()(a,b)}),j=i.enter().append("g").attr("class","box-group");j.append("rect").attr("class","heat-box").attr("fill","white").on("click",m.boxOnClick()),m.renderTitle()&&(j.append("title"),i.selectAll("title").text(m.title())),c.transition(i.selectAll("rect"),m.transitionDuration()).attr("x",function(a,c){return b(m.keyAccessor()(a,c))}).attr("y",function(b,c){return a(m.valueAccessor()(b,c))}).attr("rx",k).attr("ry",l).attr("fill",m.getColor).attr("width",f).attr("height",h),i.exit().remove();var n=g.selectAll("g.cols");n.empty()&&(n=g.append("g").attr("class","cols axis"));var o=n.selectAll("text").data(b.domain());o.enter().append("text").attr("x",function(a){return b(a)+f/2}).style("text-anchor","middle").attr("y",m.effectiveHeight()).attr("dy",12).on("click",m.xAxisOnClick()).text(m.colsLabel()),c.transition(o,m.transitionDuration()).text(m.colsLabel()).attr("x",function(a){return b(a)+f/2}).attr("y",m.effectiveHeight()),o.exit().remove();var p=g.selectAll("g.rows");p.empty()&&(p=g.append("g").attr("class","rows axis"));var q=p.selectAll("text").data(a.domain());return q.enter().append("text").attr("dy",6).style("text-anchor","end").attr("x",0).attr("dx",-2).on("click",m.yAxisOnClick()).text(m.rowsLabel()),c.transition(q,m.transitionDuration()).text(m.rowsLabel()).attr("y",function(b){return a(b)+h/2}),q.exit().remove(),m.hasFilter()?m.selectAll("g.box-group").each(function(a){m.isSelectedNode(a)?m.highlightSelected(this):m.fadeDeselected(this)}):m.selectAll("g.box-group").each(function(){m.resetHighlight(this)}),m},m.boxOnClick=function(a){return arguments.length?(r=a,m):r},m.xAxisOnClick=function(a){return arguments.length?(p=a,m):p},m.yAxisOnClick=function(a){return arguments.length?(q=a,m):q},m.xBorderRadius=function(a){return arguments.length?(k=a,m):k},m.yBorderRadius=function(a){return arguments.length?(l=a,m):l},m.isSelectedNode=function(a){return m.hasFilter(a.key)},m.anchor(b,d)},function(){function b(a){return[0,a.length-1]}function c(b){return[a.quantile(b,.25),a.quantile(b,.5),a.quantile(b,.75)]}a.box=function(){function d(b){b.each(function(b,c){b=b.map(i).sort(a.ascending);var d=a.select(this),m=b.length,n=b[0],o=b[m-1],p=b.quartiles=k(b),q=j&&j.call(this,b,c),r=q&&q.map(function(a){return b[a]}),s=q?a.range(0,q[0]).concat(a.range(q[1]+1,m)):a.range(m),t=a.scale.linear().domain(h&&h.call(this,b,c)||[n,o]).range([f,0]),u=this.__chart__||a.scale.linear().domain([0,1/0]).range(t.range());this.__chart__=t;var v=d.selectAll("line.center").data(r?[r]:[]);v.enter().insert("line","rect").attr("class","center").attr("x1",e/2).attr("y1",function(a){return u(a[0])}).attr("x2",e/2).attr("y2",function(a){return u(a[1])}).style("opacity",1e-6).transition().duration(g).style("opacity",1).attr("y1",function(a){return t(a[0])}).attr("y2",function(a){return t(a[1])}),v.transition().duration(g).style("opacity",1).attr("y1",function(a){return t(a[0])}).attr("y2",function(a){return t(a[1])}),v.exit().transition().duration(g).style("opacity",1e-6).attr("y1",function(a){return t(a[0])}).attr("y2",function(a){return t(a[1])}).remove();var w=d.selectAll("rect.box").data([p]);w.enter().append("rect").attr("class","box").attr("x",0).attr("y",function(a){return u(a[2])}).attr("width",e).attr("height",function(a){return u(a[0])-u(a[2])}).transition().duration(g).attr("y",function(a){return t(a[2])}).attr("height",function(a){return t(a[0])-t(a[2])}),w.transition().duration(g).attr("y",function(a){return t(a[2])}).attr("height",function(a){return t(a[0])-t(a[2])});var x=d.selectAll("line.median").data([p[1]]);x.enter().append("line").attr("class","median").attr("x1",0).attr("y1",u).attr("x2",e).attr("y2",u).transition().duration(g).attr("y1",t).attr("y2",t),x.transition().duration(g).attr("y1",t).attr("y2",t);var y=d.selectAll("line.whisker").data(r||[]);y.enter().insert("line","circle, text").attr("class","whisker").attr("x1",0).attr("y1",u).attr("x2",e).attr("y2",u).style("opacity",1e-6).transition().duration(g).attr("y1",t).attr("y2",t).style("opacity",1),y.transition().duration(g).attr("y1",t).attr("y2",t).style("opacity",1),y.exit().transition().duration(g).attr("y1",t).attr("y2",t).style("opacity",1e-6).remove();var z=d.selectAll("circle.outlier").data(s,Number);z.enter().insert("circle","text").attr("class","outlier").attr("r",5).attr("cx",e/2).attr("cy",function(a){return u(b[a])}).style("opacity",1e-6).transition().duration(g).attr("cy",function(a){return t(b[a])}).style("opacity",1),z.transition().duration(g).attr("cy",function(a){return t(b[a])}).style("opacity",1),z.exit().transition().duration(g).attr("cy",function(a){return t(b[a])}).style("opacity",1e-6).remove();var A=l||t.tickFormat(8),B=d.selectAll("text.box").data(p);B.enter().append("text").attr("class","box").attr("dy",".3em").attr("dx",function(a,b){return 1&b?6:-6}).attr("x",function(a,b){return 1&b?e:0}).attr("y",u).attr("text-anchor",function(a,b){return 1&b?"start":"end"}).text(A).transition().duration(g).attr("y",t),B.transition().duration(g).text(A).attr("y",t);var C=d.selectAll("text.whisker").data(r||[]);C.enter().append("text").attr("class","whisker").attr("dy",".3em").attr("dx",6).attr("x",e).attr("y",u).text(A).style("opacity",1e-6).transition().duration(g).attr("y",t).style("opacity",1),C.transition().duration(g).text(A).attr("y",t).style("opacity",1),C.exit().transition().duration(g).attr("y",t).style("opacity",1e-6).remove()}),a.timer.flush()}var e=1,f=1,g=0,h=null,i=Number,j=b,k=c,l=null;return d.width=function(a){return arguments.length?(e=a,d):e},d.height=function(a){return arguments.length?(f=a,d):f},d.tickFormat=function(a){return arguments.length?(l=a,d):l},d.duration=function(a){return arguments.length?(g=a,d):g},d.domain=function(b){return arguments.length?(h=null===b?b:a.functor(b),d):h},d.value=function(a){return arguments.length?(i=a,d):i},d.whiskers=function(a){return arguments.length?(j=a,d):j},d.quartiles=function(a){return arguments.length?(k=a,d):k},d}}(),c.boxPlot=function(b,d){function e(a){return function(b){var c=b.quartiles[0],d=b.quartiles[2],e=(d-c)*a,f=-1,g=b.length;do++f;while(b[f]<c-e);do--g;while(b[g]>d+e);return[f,g]}}function f(a){var b=a.enter().append("g");b.attr("class","box").attr("transform",p).call(m).on("click",function(a){i.filter(a.key),i.redrawGroup()})}function g(b){c.transition(b,i.transitionDuration()).attr("transform",p).call(m).each(function(){a.select(this).select("rect.box").attr("fill",i.getColor)})}function h(a){a.exit().remove().call(m)}var i=c.coordinateGridMixin({}),j=1.5,k=e,l=k(j),m=a.box(),n=null,o=function(a,b){return i.isOrdinal()?i.x().rangeBand():a/(1+i.boxPadding())/b};i.yAxisPadding(12),i.x(a.scale.ordinal()),i.xUnits(c.units.ordinal),i.data(function(a){return a.all().map(function(a){return a.map=function(b){return b.call(a,a)},a}).filter(function(a){var b=i.valueAccessor()(a);return 0!==b.length})}),i.boxPadding=i._rangeBandPadding,i.boxPadding(.8),i.outerPadding=i._outerRangeBandPadding,i.outerPadding(.5),i.boxWidth=function(b){return arguments.length?(o=a.functor(b),i):o};var p=function(a,b){var c=i.x()(i.keyAccessor()(a,b));return"translate("+c+", 0)"};return i._preprocessData=function(){i.elasticX()&&i.x().domain([])},i.plotData=function(){var a=o(i.effectiveWidth(),i.xUnitCount());m.whiskers(l).width(a).height(i.effectiveHeight()).value(i.valueAccessor()).domain(i.y().domain()).duration(i.transitionDuration()).tickFormat(n);var b=i.chartBodyG().selectAll("g.box").data(i.data(),function(a){return a.key});f(b),g(b),h(b),i.fadeDeselectedArea()},i.fadeDeselectedArea=function(){i.hasFilter()?i.g().selectAll("g.box").each(function(a){i.isSelectedNode(a)?i.highlightSelected(this):i.fadeDeselected(this)}):i.g().selectAll("g.box").each(function(){i.resetHighlight(this)})},i.isSelectedNode=function(a){return i.hasFilter(a.key)},i.yAxisMin=function(){var b=a.min(i.data(),function(b){return a.min(i.valueAccessor()(b))});return c.utils.subtract(b,i.yAxisPadding())},i.yAxisMax=function(){var b=a.max(i.data(),function(b){return a.max(i.valueAccessor()(b))});return c.utils.add(b,i.yAxisPadding())},i.tickFormat=function(a){return arguments.length?(n=a,i):n},i.anchor(b,d)},c.abstractBubbleChart=c.bubbleMixin,c.baseChart=c.baseMixin,c.capped=c.capMixin,c.colorChart=c.colorMixin,c.coordinateGridChart=c.coordinateGridMixin,c.marginable=c.marginMixin,c.stackableChart=c.stackMixin,c.d3=a,c.crossfilter=b,c}if("function"==typeof define&&define.amd)define(["d3","crossfilter"],a);else if("object"==typeof module&&module.exports){var b=require("d3"),c=require("crossfilter");"function"!=typeof c&&(c=c.crossfilter),module.exports=a(b,c)}else this.dc=a(d3,crossfilter)}();
//# sourceMappingURL=dc.min.js.map