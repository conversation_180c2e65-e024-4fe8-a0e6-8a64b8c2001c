<svg version="1.1"
     baseProfile="full"
     xmlns="http://www.w3.org/2000/svg"
     xmlns:xlink="http://www.w3.org/1999/xlink"
     xmlns:ev="http://www.w3.org/2001/xml-events"
     width="100%" height="100%"
     onload='draw()'>
  <script><![CDATA[
    function draw() {
        var parts = [], mx = 0, mn = 999999;
        location.search.substr(1).split(",").forEach(function(n) {
            var integ = parseInt(n, 10);
            parts.push(integ);
            mx = Math.max(mx, integ);
            mn = Math.min(mn, integ);
        });
        var x1 = 0, y1 = 0, x2 = 0, y2 = 100 - (100 * (parts[0] - mn) / (mx - mn));
        for (var i=0; i<parts.length; i++) {
            var ln = document.createElementNS ("http://www.w3.org/2000/svg", "line");
            y1 = y2; x1 = x2;
            x2 = 100 * (i / parts.length), y2 = 100 - (100 * (parts[i] - mn) / (mx - mn));
            ln.setAttribute("x1", x1 + "%");
            ln.setAttribute("x2", x2 + "%");
            ln.setAttribute("y1", y1 + "%");
            ln.setAttribute("y2", y2 + "%");
            ln.setAttribute("stroke", "rgba(0,0,0,0.5)");
            ln.setAttribute("stroke-width", "1");
            document.getElementsByTagName("svg")[0].appendChild(ln);
        }
    }
  ]]></script>
</svg>
