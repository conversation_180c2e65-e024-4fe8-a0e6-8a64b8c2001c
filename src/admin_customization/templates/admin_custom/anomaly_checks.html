{% extends "admin/base_site.html" %}
{% load humanize %}
{% load admin_tags %}
{% load static %}
{% block extrahead %}

    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="{% static 'css/plugins/metisMenu/metisMenu.min.css' %}" rel="stylesheet">

    <!-- Timeline CSS -->
    <link href="{% static 'css/plugins/timeline.css' %}" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'css/sb-admin-2.css' %}" rel="stylesheet">

    <!-- Morris Charts CSS -->
    <link href="{% static 'css/plugins/morris.css' %}" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="{% static 'font-awesome-4.1.0/css/font-awesome.min.css' %}" rel="stylesheet" type="text/css">
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
        <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
        <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->

{% endblock %}

{% block title %}Anomaly checks{% endblock %}

{% block content %}

<div id="wrapper">
    <div class="row">
        <div class="col-lg-6">
            <h1 class="page-header">Anomaly checks</h1>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-6">
            <div class="panel panel-default"{% if invoice_anomalies|length == 0 %} style="background-color: #dff0d8"{% endif %}>
                <div class="panel-heading">
                    Invoice Numbering
                </div>
                <div class="panel-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered table-hover">
                            {% for error_name, invoices in invoice_anomalies.items %}
                                <tr>
                                    <td>{{ forloop.counter }}.</td>
                                    <td><b>Numbering for {{ error_name }}</b></td>
                                </tr>
                                {% for invoice in invoices %}
                                    <tr>
                                        <td><i>{{ forloop.counter }}.</i></td>
                                        <td>Difference in numbering {{ invoice.0 }} between {{ invoice.1 }} {{ invoice.2 }}</td>
                                    </tr>
                                {% endfor %}
                            {% endfor %}
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock %}
