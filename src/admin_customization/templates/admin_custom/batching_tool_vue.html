{% extends "admin/base_site.html" %}
{% load i18n %}
{% load static %}
{% load webpack_loader %}

{% block extrahead %}

<link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">

<style>
#content{
    padding: 0;
}
#footer, br.clear{
    display: none;
}

.bt-col {
    max-width: 300px;
    transition: all 0.5s ease;
    box-sizing: border-box;
}

.bt-card-nav {
    float: right;
}

.bt-card-nav .glyphicon{
    margin-left: 15px;
}

.bt-col .panel-primary {
    overflow: hidden;
    min-width: 270px;
    box-sizing: border-box;
}

.bt-col-being-deleted {
    max-width: 0;
    opacity: 0;
    padding: 0;
}

.bt-lead {
    margin-bottom: 0;
}

.bt-input-title {
    margin-bottom: 1px;
}

.bt-input-group {
    width: 200px;
    margin-bottom: 10px;
}

.bt-input-group-half {
    width: 82px;
}

.bt-checkbox-group, .bt-form-group, .bt-input-group {
    float: left;
    margin-right: 35px;
}

.bt-checkbox-group-title, .bt-input-title {
    text-decoration: underline;
    text-transform: capitalize;
}

.bt-checkbox-group .bt-checkbox {
    display: block;
}

.bt-checkbox {
    width: 20%;
    display: inline-block;
}



</style>

{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a> &rsaquo;
    Batching Tool - now batching for producer: <strong>{{ producer_name }}</strong>
</div>
{% endblock %}

{% block content %}
    {% csrf_token %}
    <div id="root"></div>
    {% render_bundle 'batchingToolVue' %}
{% endblock %}
