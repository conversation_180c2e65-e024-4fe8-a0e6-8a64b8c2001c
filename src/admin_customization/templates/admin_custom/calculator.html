{% extends "admin/base_site.html" %}
{% load humanize %}
{% load static %}
{% block extrahead %}

    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="{% static 'css/plugins/metisMenu/metisMenu.min.css' %}" rel="stylesheet">

    <!-- Timeline CSS -->
    <link href="{% static 'css/plugins/timeline.css' %}" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'css/sb-admin-2.css' %}" rel="stylesheet">

    <!-- Morris Charts CSS -->
    <link href="{% static 'css/plugins/morris.css' %}" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="{% static 'font-awesome-4.1.0/css/font-awesome.min.css' %}" rel="stylesheet" type="text/css">

    <style>
        #panel-refresh .panel-heading { border: 1px solid; cursor: pointer;}
        #panel-refresh .panel-heading:hover { background-color: #F5F5F5; }
        #panel-refresh-icon { position: relative; top: 10px; }
    </style>

    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
        <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
        <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->

    <script language="javascript">
    </script>

{% endblock %}

{% block content_title %}{% endblock %}


{% block content %}
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-4">
                <form action="#" method="POST">{% csrf_token %}
                    {{ form.as_p }}
                    <button type="submit">Recalculate</button>
                </form>
            </div>
            <div class="col-md-4">
                <table class="table">
                    <thead>
                    <tr>
                        <td>Euro netto</td>
                        <td>Euro brutto</td>
                        <td>Local currency</td>
                        <td>Local netto</td>
                        <td>Local brutto</td>

                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>{{ kwoty.euro_netto }} €</td>
                        <td>{{ kwoty.euro_brutto }} €</td>
                        <td>{{ kwoty.local_currency }}</td>
                        <td>{{ kwoty.local_netto }} {{ kwoty.local_currency }}</td>
                        <td>{{ kwoty.local_brutto }} {{ kwoty.local_currency }}</td>
                    </tr>
                    </tbody>
                </table>
                <!-- /.panel-body -->
            </div>
        </div>
    </div>

{% endblock %}
