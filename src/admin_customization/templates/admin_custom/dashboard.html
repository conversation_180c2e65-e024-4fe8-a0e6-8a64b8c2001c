{% load humanize %}
{% load admin_tags %}
{% load static %}
<!DOCTYPE html>
<html lang="en">

<head>

    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <meta name="description" content="">
    <meta name="author" content="">

    <title>Tylko dashboard</title>

    <link rel="shortcut icon" href="{% static 'icons/be_icon.ico' %}"/>

    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="{% static 'css/plugins/metisMenu/metisMenu.min.css' %}" rel="stylesheet">

    <!-- Timeline CSS -->
    <link href="{% static 'css/plugins/timeline.css' %}" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'css/sb-admin-2.css' %}" rel="stylesheet">

    <!-- <PERSON> Charts CSS -->
    <link href="{% static 'css/plugins/morris.css' %}" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="{% static 'font-awesome-4.1.0/css/font-awesome.min.css' %}" rel="stylesheet" type="text/css">
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
        <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
        <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->


    <style>
        body {
            font-size: 12px;
        }

        {% if not IS_PRODUCTION %}
            #page-wrapper {
                background: #ccc;
            }
            nav.navbar {
                background: black;
                color: #ffffff;
                border: none;
                border-radius: 0;
            }
            nav.navbar a.navbar-brand {
                color: #ffffff;
            }
        {% endif %}
        .panel-description {
            white-space: initial;

        }
    </style>
</head>
<body>
<div id="wrapper">

    <!-- Navigation -->
    <nav class="navbar navbar-default" role="navigation" style="margin-bottom: 0">
        <div class="navbar-header">
            <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                <span class="sr-only">Toggle navigation</span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
                <span class="icon-bar"></span>
            </button>
            <a class="navbar-brand" href="/admin/">{% if IS_PRODUCTION %}Tylko admin{% else %}
                <span>DEVELOPMENT MODE - Tylko admin</span>{% endif %}</a>
        </div>
        <!-- /.navbar-header -->

        {% block navigation_left %}
        <div class="navbar-default sidebar" role="navigation">
            <div class="sidebar-nav navbar-collapse">
                <ul class="nav" id="side-menu">
                    <li class="sidebar-search">
                        <form action="search" method="get">
                            <div class="input-group custom-search-form">
                                <input class="form-control" name="q" pattern=".{2,}" placeholder="Search..." required
                                       title="2 characters minimum" type="text"/>
                                <span class="input-group-btn">
                            <button class="btn btn-default" type="submit">
                                <i class="fa fa-search"></i>
                            </button>
                        </span>
                            </div>
                        </form>
                    </li>
                    <li>
                        <a class="active" href="{% url "admin:index" %}"><i class="fa fa-dashboard fa-fw"></i> Dashboard</a>
                    </li>
                    <li>
                        <a class="active" href="{% url "admin:samples_index" %}"><i
                                class="fa fa-dashboard fa-fw"></i> Samples Dashboard</a>
                    </li>


                    {% if perms.kpi.view_kpivalue %}
                        <li>
                            <a href="{% url "admin:kpis" board_name="bi" time_interval="monthly" %}"><i
                                    class="fa fa-table fa-fw"></i> Kpis</a>
                        </li>

                        <li>
                            <a href="#"><i class="fa fa-files-o fa-fw"></i> Kpis<span class="fa arrow"></span></a>
                            <ul class="nav nav-second-level">
                                <li>
                                    <a href="{% url "admin:kpis" board_name="bi" time_interval="monthly" %}"><i
                                            class="fa fa-table fa-fw"></i> Monthly</a>
                                </li>
                                <li>
                                    <a href="{% url "admin:kpis" board_name="bi" time_interval="daily" %}"><i
                                            class="fa fa-table fa-fw"></i> Daily</a>
                                </li>
                                <li>
                                    <a href="{% url "admin:kpis" board_name="bi" time_interval="weekly" %}"><i
                                            class="fa fa-table fa-fw"></i> Weekly</a>
                                </li>
                            </ul>
                            <!-- /.nav-second-level -->
                        </li>
                    {% endif %}
                    {% if perms.abtests.view_abtest %}
                        <li>
                            <a href="{% url "admin:abtests_abtest_changelist" %}"><i
                                    class="fa fa-table fa-fw"></i> Ab results</a>
                        </li>
                    {% endif %}
                    {% if perms.user_profile.view_userlibraries %}
                        <li>
                            <a href="{% url "admin:user_profile_userlibraries_changelist" %}"><i
                                    class="fa fa-table fa-fw"></i> Libraries</a>
                        </li>
                    {% endif %}
                    <li>
                        <a href="#"><i class="fa fa-files-o fa-fw"></i> Clients<span class="fa arrow"></span></a>
                        <ul class="nav nav-second-level">
                            {% if perms.auth.view_user %}
                                <li>
                                    <a href="{% url "admin:auth_user_changelist" %}"><i class="fa fa-table fa-fw"></i>
                                        Users</a>
                                </li>
                            {% endif %}

                            {% if perms.user_profile.view_userlibraries %}
                                <li>
                                    <a href="{% url "admin:user_profile_userlibraries_changelist" %}"><i
                                            class="fa fa-table fa-fw"></i> Libraries</a>
                                </li>
                            {% endif %}

                            {% if perms.orders.view_cartorders %}
                                <li>
                                    <a href="{% url "admin:carts_cart_changelist" %}"><i
                                            class="fa fa-table fa-fw"></i> Carts</a>
                                </li>
                            {% endif %}
                        </ul>
                        <!-- /.nav-second-level -->
                    </li>
                    {% if perms.orders.view_order %}
                        <li>
                            <a href="{% url "admin:orders_order_changelist" %}?emptycart=notemptycart&o=-7.-1"><i
                                    class="fa fa-table fa-fw"></i> Orders</a>
                        </li>
                    {% endif %}
                    {% if perms.orders.view_cartorders %}
                        <li>
                            <a href="{% url "admin:carts_cart_changelist" %}?empty=not_empty&o=-10.-1"><i
                                    class="fa fa-table fa-fw"></i> Not empty carts</a>
                        </li>
                    {% endif %}
                    <li>
                        <a href="#"><i class="fa fa-files-o fa-fw"></i> Manufactoring<span class="fa arrow"></span></a>
                        <ul class="nav nav-second-level">
                            {% if perms.producers.view_productbatch %}
                                <li>
                                    <a href="{% url "admin:producers_productbatch_changelist" %}">Batches</a>
                                </li>
                            {% endif %}
                            {% if perms.producers.view_product %}

                                <li>
                                    <a href="{% url "admin:producers_product_changelist" %}">Items in production</a>
                                </li>
                            {% endif %}
                        </ul>
                        <!-- /.nav-second-level -->
                    </li>
                    <li>
                        <a href="#"><i class="fa fa-files-o fa-fw"></i> Gallery<span class="fa arrow"></span></a>
                        <ul class="nav nav-second-level">
                            {% if perms.gallery.view_jetty %}
                                <li>
                                    <a href="{% url "admin:app_list" "gallery" %}">All</a>
                                </li>
                            {% endif %}
                            {% if perms.gallery.view_jetty %}
                                <li>
                                    <a href="{% url "admin:gallery_jetty_changelist" %}">Ivy</a>
                                </li>
                            {% endif %}
                        </ul>
                        <!-- /.nav-second-level -->
                    </li>
                    {% if perms.mailing.view_mailingflowstatus %}
                        <li>
                            <a href=" {% url "admin:mailing_list" %}"><i class="fa fa-dashboard fa-fw"></i> Mailings</a>
                        </li>
                    {% endif %}
                    {% if perms.producers.view_product %}
                        <li>
                            <a href=" {% url "admin:batching_tool" %}"><i class="fa fa-th fa-fw"></i> Batching Tool</a>
                        </li>
                    {% endif %}
                    {% if perms.invoice.view_invoice %}
                        <li>
                            <a href="{% url "admin:invoice_invoice_changelist" %}"><i class="fa fa-money fa-fw"></i>
                                Invoices</a>
                        </li>
                    {% endif %}
                    {% if perms.vouchers.add_voucher %}
                        <li>
                            <a href="{% url 'cs_create_promocode' %}"><i class="fa fa-money fa-fw"></i> Create a
                                promocode</a>
                        </li>
                    {% endif %}
                    {% if perms.promotions.add_promotion %}
                        <li>
                            <a href="{% url "admin:promotions_promotionconfig_add" %}"><i
                                class="fa fa-tag fa-fw"></i>Configure Promotion</a>
                        </li>
                    {% endif %}
                    {% if perms.shortener.view_shorturl %}
                        <li>
                            <a href="{% url "admin:shorturl_add" %}"><i class="fa fa-external-link fa-fw"></i> Create a
                                short url</a>
                        </li>
                    {% endif %}

                    <li>
                        <a href=" {% url "admin:admin_all_index" %}"><i class="fa fa-dashboard fa-fw"></i> All apps</a>
                    </li>

                    <li>
                        <a href="{% url "admin:queue_status" %}"><i class="fa fa-dashboard fa-fw"></i>
                            Queue status</a>
                    </li>
                    <li>
                        <a href="{% url "admin:translate_with_deepl" %}"><i class="fa fa-dashboard fa-fw"></i>
                            Multilingual translator</a>
                    </li>
                </ul>
                {% if git_branch %}
                    <div>Branch: {{ git_branch }}</div>
                {% endif %}
                {% if git_commit %}
                    <div>Commit: {{ git_commit }}</div>
                {% endif %}
            </div>
            <!-- /.sidebar-collapse -->
        </div>
        <!-- /.navbar-static-side -->
        {% endblock %}
    </nav>

    <div id="page-wrapper">
        <div class="row">
        </div>
{% block top_panel %}
        <div class="row">
            <div class="col-lg-3 col-md-6">
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        <div class="row">
                            <div class="col-xs-3">
                                <i class="fa fa-euro fa-5x"></i>
                            </div>
                            <div class="col-xs-9 text-right">
                                <div class="huge">{{ order_total_value }}k</div>
                                <div>This month netto sale, {{ target_percentage|floatformat:-2 }}% of target
                                    ({{ actual_target }}k) / {{ month_percentage|floatformat:-2 }}% of month
                                    <br/>It`s should be {{ target_per_day_left|floatformat:-2 }}k per day left
                                </div>
                            </div>
                        </div>
                    </div>
                    {% if perms.orders.view_paidorders %}
                        <a href="{% url "admin:orders_paidorders_changelist" %}?o=-5.-1">
                            <div class="panel-footer">
                                <span class="pull-left">View Details</span>
                                <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                                <div class="clearfix"></div>
                            </div>
                        </a>
                    {% endif %}
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        <div class="row">
                            <div class="col-xs-3">
                                <i class="fa fa-euro fa-5x"></i>
                            </div>
                            <div class="col-xs-9 text-right">
                                <div class="huge">{{ order_daily_value }}k</div>
                                <div>Today netto sale
                                    <br/>In this month it was {{ was_per_day_left|floatformat:-2 }}k daily
                                </div>
                            </div>
                        </div>
                    </div>
                    {% if perms.orders.view_paidorders %}
                        <a href="{% url "admin:orders_paidorders_changelist" %}?o=-5.-1">
                            <div class="panel-footer">
                                <span class="pull-left">View Details</span>
                                <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                                <div class="clearfix"></div>
                            </div>
                        </a>
                    {% endif %}
                </div>
            </div>
<!--            <div class="col-lg-3 col-md-6">-->
<!--                <div class="panel panel-primary">-->
<!--                    <div class="panel-heading">-->
<!--                        <div class="row">-->
<!--                            <div class="col-xs-3">-->
<!--                                <i class="fa fa-smile-o fa-5x"></i>-->
<!--                            </div>-->
<!--                            <div class="col-xs-9 text-right">-->
<!--                                <div class="huge">{{ review_all_score|floatformat:-2 }}/5</div>-->

<!--                                <div>Overall review score-->
<!--                                </div>-->
<!--                            </div>-->
<!--                        </div>-->
<!--                    </div>-->
<!--                    {% if perms.orders.view_paidorders %}-->
<!--                        <a href="{% url "admin:orders_paidorders_changelist" %}?o=-5.-1">-->
<!--                            <div class="panel-footer">-->
<!--                                <span class="pull-left">View Details</span>-->
<!--                                <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>-->
<!--                                <div class="clearfix"></div>-->
<!--                            </div>-->
<!--                        </a>-->
<!--                    {% endif %}-->
<!--                </div>-->
<!--            </div>-->
            <div class="col-lg-3 col-md-6">
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        <div class="row">
                            <div class="col-xs-3">
                                <i class="fa fa-trophy fa-5x"></i>
                            </div>
                            <div class="col-xs-9 text-right">
                                <div class="huge">{{ wardrobes_in_last_7_days }}</div>
                                <div>Tone and Edge furniture added to production in last 7 days.
                                    <br/>{{ wardrobes_in_last_30_days }} in last 30 days.
                                </div>
                            </div>
                        </div>
                    </div>
                    <a href="{% url "admin:producers_product_changelist" %}?cached_product_type=watty">
                        <div class="panel-footer">
                            <span class="pull-left">View Details</span>
                            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                            <div class="clearfix"></div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="col-lg-3 col-md-6">
                <div class="panel panel-green">
                    <div class="panel-heading">
                        <div class="row">
                            <div class="col-xs-3">
                                <i class="fa fa-cogs fa-5x"></i>
                            </div>
                            <div class="col-xs-9 text-right">
                                <div class="huge"> {{ orders_waiting_for_producer }}</div>
                                <div>Not batched items</div>
                            </div>
                        </div>
                        {% if perms.orders.view_paidorders %}
                        <div class="row">
                            <div class="col-xs-12 text-right">
                                <div>Connected to: {{ ps_create }}</div>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                    {% if perms.orders.view_paidorders %}
                    <a href="{% url "admin:producers_product_changelist" %}">
                        <div class="panel-footer">
                            <span class="pull-left">View Details</span>
                            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                            <div class="clearfix"></div>
                        </div>
                    </a>
                    {% endif %}
                </div>
            </div>
        </div>
        <div class="row">
            {% for shelf_type, delivery_data in delivery_entries reversed %}
                <div class="col-lg-3 col-md-6">
                    <div class="panel panel-default">
                        <table class="table table-striped table-condensed">
                            <caption><b>{{ shelf_type }}</b></caption>
                            <tbody>
                            {% for deliver in delivery_data %}
                                <tr>
                                    <td>{{ deliver.0 }}</td>
                                    <td>{{ deliver.1 }}</td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            {% endfor %}
        </div>
        <!-- /.row -->
{% endblock %}
{% block content %}

        <div class="row">
            <div class="col-lg-6 col-md-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        Latest payed orders
                    </div>
                    <!-- /.panel-heading -->

                    <table class="table table-responsive table-stripped">
                        <thead>
                        <tr>
                            <th>Customer id</th>
                            <th>Source</th>
                            <th>Country</th>
                            <th>Price</th>
                            <th>Items</th>
                            <th>Date</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for order in orders|slice:":10" %}
                            <tr>

                                <td>
                                    {% if perms.orders.view_order %}<a href="/admin/user_profile/userlibraries/{{ order.owner.profile.id }}/">{% endif %}
                                    {% if order.owner.profile.user_type == 2 %}
                                        {{ order.owner.profile.pk|truncatechars:60 }}
                                    {% else %}
                                        guest
                                    {% endif %}
                                 {% if perms.orders.view_order %}</a>{% endif %}</td>
                                <td>{{ order.get_order_source_display }}</td>
                                <td>{{ order.get_country_short }}</td>

                                <td>
                                    {% if perms.orders.view_order %}<a href="{% url 'admin:order-overview' order.id %}">
                                    {% endif %}{{ order.get_base_total_value }}
                                        &euro;{% if perms.orders.view_order %}</a>{% endif %}
                                </td>
                                <td>{{ order.get_items_as_string|safe }}</td>
                                <td>{{ order.paid_at|date:"d/m/y" }} <br/> {{ order.paid_at|naturaltime }} </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>

                    <a href="{% url "admin:orders_paidorders_changelist" %}">
                        <div class="panel-footer">
                            <span class="pull-left">View all</span>
                            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                            <div class="clearfix"></div>
                        </div>
                    </a>
                    <!-- /.panel-body -->
                </div>
            </div>
            <div class="col-lg-6 col-md-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        Latest failed payment notifications
                    </div>
                    <!-- /.panel-heading -->
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Order</th>
                                    <th>Status</th>
                                    <th>Amount</th>
                                    <th>Method</th>
                                </tr>
                                </thead>
                                <tbody>
                                {% for transactions in payment_notifications_error %}
                                    <tr class="warning">
                                        <td>{{ transactions.event_date|date:"d/m/y" }}<br/> {{ transactions.event_date|naturaltime }}
                                        </td>
                                        <td>
                                            {% if perms.orders.view_order %}
                                            <a href="{% url 'admin:order-overview' transactions.merchant_reference|slice:"6:" %}">
                                                {% endif %}
                                                {{ transactions.merchant_reference }}
                                            {% if perms.orders.view_order %}</a>{% endif %}
                                        </td>
                                        <td>{{ transactions.reason|truncatechars:40 }}</td>
                                        <td>{{ transactions.amount_value|cents_to_euros }} &euro;</td>
                                        <td>{{ transactions.payment_method }}</td>
                                    </tr>
                                {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <!-- /.table-responsive -->
                    </div>
                    <!-- /.panel-body -->
                </div>
            </div>
        </div>
        <div class="row">
            <div class="col-lg-4 col-md-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        Latest registered users
                    </div>
                    <!-- /.panel-heading -->

                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                            <tr>
                                <th>ID</th>
                                <th>Added</th>
                            </tr>
                            </thead>
                            <tbody>
                            {% for user in customers %}
                                <tr>
                                    <td>
                                        {% if perms.auth.view_users %}<a href="{% url "admin:auth_user_change" user.id %}">{{ user.pk }}{% endif %}{% if perms.auth.view_users %}</a>{% endif %}
                                    </td>
                                    <td>{{ user.date_joined|date:"d/m/y" }} <br/> {{ user.date_joined|naturaltime }}
                                    </td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% if perms.auth.view_users %}
                        <a href="{% url "admin:auth_user_changelist" %}">
                            <div class="panel-footer">
                                <span class="pull-left">View all</span>
                                <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                                <div class="clearfix"></div>
                            </div>
                        </a>
                    {% endif %}
                    <!-- /.panel-body -->
                </div>
            </div>
            <div class="col-lg-8 col-md-6">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        Latest success payment notifications
                    </div>
                    <!-- /.panel-heading -->
                    <div class="panel-body">
                        <div class="table-responsive">
                            <table class="table">
                                <thead>
                                <tr>
                                    <th>Date</th>
                                    <th>Order</th>
                                    <th>Status</th>
                                    <th>Amount</th>
                                    <th>Method</th>
                                </tr>
                                </thead>
                                <tbody>
                                {% for transactions in payment_notifications_ok %}
                                    <tr class="success">
                                        <td>{{ transactions.event_date|date:"d/m/y" }}<br/> {{ transactions.event_date|naturaltime }}
                                        </td>
                                        <td>
                                            {% if perms.orders.view_order %}<a href="{% url "admin:orders_order_change" transactions.merchant_reference|slice:"6:" %}">{% endif %}{{ transactions.merchant_reference }}

                                        {% if perms.orders.view_order %}</a>{% endif %}
                                        </td>
                                        <td>
                                            {% if perms.payments.view_notification %}
                                            <a href="{% url "admin:payments_notification_change" transactions.id %}">{% endif %}
                                                {{ transactions.code }}{% if perms.payments.view_notification %}</a>{% endif %}
                                        </td>
                                        <td>{{ transactions.amount_value|cents_to_euros }} &euro;</td>
                                        <td>{{ transactions.payment_method }}</td>
                                    </tr>
                                {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        <!-- /.table-responsive -->
                    </div>
                    <!-- /.panel-body -->
                </div>
            </div>
        </div>
{% endblock %}
        <!-- /.row -->
    </div>
    <!-- /.row -->

</div>
<!-- /#page-wrapper -->

<!-- jQuery Version 1.11.0 -->
<script src="{% static 'js/jquery-1.11.0.js' %}"></script>

<!-- Bootstrap Core JavaScript -->
<script src="{% static 'js/bootstrap.min.js' %}"></script>

<!-- Metis Menu Plugin JavaScript -->
<script src="{% static 'js/plugins/metisMenu/metisMenu.min.js' %}"></script>

<!-- Morris Charts JavaScript -->

<!-- Custom Theme JavaScript -->
<script src="{% static 'js/sb-admin-2.js' %}"></script>
</body>
