{% extends 'admin_custom/dashboard.html' %}
{% load humanize %}
{% load admin_tags %}

{% block top_panel %}
        <div class="row">
            <div class="col-lg-4 col-md-6">
                <div class="panel panel-green">
                    <div class="panel-heading">
                        <div class="row">
                            <div class="col-xs-3">
                                <i class="fa fa-cogs fa-5x"></i>
                            </div>
                            <div class="col-xs-9 text-right">
                                <div class="huge"> {{ samples_in_cart }}</div>
                                <div>Sample in carts</div>
                            </div>
                        </div>
                    </div>
                    <a href="{% url "admin:gallery_samplebox_changelist" %}">
                        <div class="panel-footer">
                            <span class="pull-left">Details</span>
                            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                            <div class="clearfix"></div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        <div class="row">
                            <div class="col-xs-3">
                                <i class="fa fa-cogs fa-5x"></i>
                            </div>
                            <div class="col-xs-9 text-right">
                                <div class="huge">{{ samples_ordered_7_days }}</div>
                                <div>Ordered samples in last 7 days</div>
                            </div>
                        </div>
                    </div>
                    <a href="{% url "admin:gallery_samplebox_changelist" %}?o=-5.-1">
                        <div class="panel-footer">
                            <span class="pull-left">View list</span>
                            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                            <div class="clearfix"></div>
                        </div>
                    </a>
                </div>
            </div>
            <div class="col-lg-4 col-md-6">
                <div class="panel panel-primary">
                    <div class="panel-heading">
                        <div class="row">
                            <div class="col-xs-3">
                                <i class="fa fa-cogs fa-5x"></i>
                            </div>
                            <div class="col-xs-9 text-right">
                                <div class="huge">{{ samples_ordered_30_days }}</div>
                                <div>Ordered samples last 30 days</div>
                            </div>
                        </div>
                    </div>
                    <a href="{% url "admin:gallery_samplebox_changelist" %}">
                        <div class="panel-footer">
                            <span class="pull-left">View list</span>
                            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                            <div class="clearfix"></div>
                        </div>
                    </a>
                </div>
            </div>
        </div>

{% endblock %}

{% block content %}

        <div class="row">
            <div class="col-lg-12 col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        Set Sales
                    </div>
                    <!-- /.panel-heading -->

                    <table class="table table-responsive table-bordered">
                        <thead>
                        <tr>
                            <th>Name</th>
                            <th>In stock</th>
                            <th>Enough for</th>
                            <th>Sold last 30 days</th>
                            <th>Sold 4 weeks ago</th>
                            <th>3 weeks ago</th>
                            <th>2 weeks ago</th>
                            <th>last 7 days</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for sample_box_summary in sample_box_summaries %}
                            <tr
                                {% if sample_box_summary.bought_in_last_7_days < 2 %}
                                    class='text-muted'
                                {% elif sample_box_summary.enough_for < 14 %}
                                    class='text-danger'
                                {% endif %}
                            >
                                <td>{{ sample_box_summary.name }}</td>
                                <td>{{ sample_box_summary.in_stock }}</td>
                                <td>{{ sample_box_summary.enough_for|floatformat:2 }} days</td>
                                <td>{{ sample_box_summary.bought_in_last_30_days }}</td>
                                <td>{{ sample_box_summary.bought_4_weeks_ago }}</td>
                                <td>{{ sample_box_summary.bought_3_weeks_ago }}</td>
                                <td>{{ sample_box_summary.bought_2_weeks_ago }}</td>
                                <td>{{ sample_box_summary.bought_in_last_7_days }}</td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>

                    <a href="{% url "admin:warehouse_stocksamplebox_changelist" %}">
                        <div class="panel-footer">
                            <span class="pull-left">View all</span>
                            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                            <div class="clearfix"></div>
                        </div>
                    </a>
                    <!-- /.panel-body -->
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12 col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        Set definitions
                    </div>
                    <!-- /.panel-heading -->

                    <table class="table table-responsive table-bordered">
                        <thead>
                        <tr>
                            <th>Id</th>
                            <th>Name</th>
                            <th>Elements</th>
                            <th>In stock</th>
                            <th>Bought but not sent</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for sample_box_summary in sample_box_summaries %}
                            <tr>
                                <td>{{ sample_box_summary.id }}</td>
                                <td>{{ sample_box_summary.name }}</td>
                                <td>
                                    {% for element in sample_box_summary.elements %}
                                        {{ element }}
                                        {% if not forloop.last %}
                                            <br/>
                                        {% endif %}
                                    {% endfor %}
                                </td>
                                <td>{{ sample_box_summary.in_stock }}</td>
                                <td>{{ sample_box_summary.bought_but_not_delivered }}</td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>

                    <a href="{% url "admin:warehouse_stocksamplebox_changelist" %}">
                        <div class="panel-footer">
                            <span class="pull-left">View all</span>
                            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                            <div class="clearfix"></div>
                        </div>
                    </a>
                    <!-- /.panel-body -->
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12 col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        Sample elements summary
                    </div>
                    <!-- /.panel-heading -->

                    <table class="table table-responsive table-bordered">
                        <thead>
                        <tr>
                            <th>Name</th>
                            <th>In stock</th>
                            <th>Enough for typical days:</th>
                            <th>Bought in last 30 days</th>
                            <th>Bought 4 weeks ago</th>
                            <th>Bought 3 weeks ago</th>
                            <th>Bought 2 weeks ago</th>
                            <th>Bought in last 7 days</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for sample_element_summary in sample_elements_summaries %}
                            <tr {% if sample_element_summary.enough_for < 14 %}class='text-danger'{% endif %}>
                                <td>{{ sample_element_summary.name }}</td>
                                <td>{{ sample_element_summary.in_stock }}</td>
                                <td>{{ sample_element_summary.enough_for|floatformat:2 }}</td>
                                <td>{{ sample_element_summary.bought_in_last_30_days }}</td>
                                <td>{{ sample_element_summary.bought_4_weeks_ago }}</td>
                                <td>{{ sample_element_summary.bought_3_weeks_ago }}</td>
                                <td>{{ sample_element_summary.bought_2_weeks_ago }}</td>
                                <td>{{ sample_element_summary.bought_in_last_7_days }}</td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>

                    <a href="{% url "admin:warehouse_stocksamplebox_changelist" %}">
                        <div class="panel-footer">
                            <span class="pull-left">View all</span>
                            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                            <div class="clearfix"></div>
                        </div>
                    </a>
                    <!-- /.panel-body -->
                </div>
            </div>
        </div>

        <div class="row">
            <div class="col-lg-12 col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        Latest payed orders
                    </div>
                    <!-- /.panel-heading -->

                    <table class="table table-responsive table-stripped">
                        <thead>
                        <tr>
                            <th>Customer id</th>
                            <th>Source</th>
                            <th>Country</th>
                            <th>Price</th>
                            <th>Items</th>
                            <th>Date</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for order in orders_with_samples %}
                            <tr>
                                <td>
                                    {% if perms.orders.view_order %}<a href="/admin/user_profile/userlibraries/{{ order.owner.profile.id }}/">{% endif %}
                                    {% if order.owner.profile.user_type == 2 %}
                                        {{ order.owner.profile.pk|truncatechars:60 }}
                                    {% else %}
                                        guest
                                    {% endif %}
                                 {% if perms.orders.view_order %}</a>{% endif %}</td>
                                <td>{{ order.get_order_source_display }}</td>
                                <td>{{ order.get_country_short }}</td>

                                <td>
                                    {% if perms.orders.view_order %}<a href="{% url 'admin:order-overview' order.id %}">
                                    {% endif %}{{ order.get_base_total_value }}
                                        &euro;{% if perms.orders.view_order %}</a>{% endif %}
                                </td>
                                <td>{{ order.get_items_as_string|safe }}</td>
                                <td>{{ order.paid_at|date:"d/m/y" }} <br/> {{ order.paid_at|naturaltime }} </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>

                    <a href="{% url "admin:orders_paidorders_changelist" %}">
                        <div class="panel-footer">
                            <span class="pull-left">View all</span>
                            <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                            <div class="clearfix"></div>
                        </div>
                    </a>
                    <!-- /.panel-body -->
                </div>
            </div>
        </div>
{% endblock %}
