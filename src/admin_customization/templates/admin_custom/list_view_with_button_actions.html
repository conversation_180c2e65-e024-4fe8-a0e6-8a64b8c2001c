{% extends "admin/change_list.html" %}
{% load i18n admin_list %}

{% block object-tools-items %}
    {{ block.super }}

    {% for action_url, action_name in button_actions %}
    <li style="--object-tools-bg: orangered;" >
        <form action="{{ action_url }}/" method="POST">
            {% csrf_token %}
            <a href="" onclick="this.closest('form').submit();return false;">{{ action_name }}</a>
        </form>
    </li>
    {% endfor %}
{% endblock %}
