{% extends "admin/base_site.html" %}
{% load i18n %}
{% load static %}

{% block extrahead %}
<link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">
<script src="https://ajax.googleapis.com/ajax/libs/jquery/2.1.4/jquery.min.js"></script>
<script>
    $(function() {
        $('.preview-link').click(function() {
            var mailing_name = $(this).attr('rel');
            var language = new String($(this).text()).toLowerCase();
            $('#preview-frame').attr('src', '/admin/mailing_preview/' + mailing_name + '/' + language);
        });

        $('.test-link').click(function() {
            var mailing_name = $(this).attr('rel');
            var language = new String($(this).children().first().attr('title')).toLowerCase();
            $('#preview-frame').attr('src', '/admin/mailing_test/' + mailing_name + '/' + language);
        });

        //Highlights currently clicked mail-template
        var $previewLink = $(".preview-link");

        $previewLink.closest( $('tr') ).on("click", function(e){
            e.preventDefault();
            var thisIndex = $(this).index();
            $previewLink.closest( $('tr') ).eq(thisIndex).css({background:'#cbe3ef'}).siblings().css({background:'none'});
            // Returns the protocl switch to default state, on email-template-change.
            imageProtocolSwitch( 'disable' );

            //Only on localhost, riggers autoClick on Show-Images button.
            if ( window.location.href.indexOf('localhost') > -1 ) {
                setTimeout(function () {
                    $('.js-image-protocol-switch').trigger('click');
                }, 1000);
            }

        });

        // Show-Hide images in iframe ( src: http -> https ).
        var protocolSafe = 'https://';
        var protocolUnsafe = 'http://';

        function switchImageProtocol( currentProtocol, newProtocol ) {

            var $iframe = $('iframe');

            $iframe.contents().find('img').each( function() {
                $(this).attr(
                    'src',
                    $(this).attr('src').replace( currentProtocol , newProtocol )
                )
            });
        }

        function imageProtocolSwitch( action ) {
            var $protocolSwitch = $('.js-image-protocol-switch');
            var $switchTitle = $('.js-image-protocol-switch .fixed-switch__title');
            var textOn = 'Show Images';
            var textOff = 'Hide Images';

            if ( action === 'enable' ) {
                switchImageProtocol( protocolSafe, protocolUnsafe );
                $protocolSwitch.addClass('fixed-switch--active');
                $switchTitle.text(textOff)

            } else if ( action === 'disable' ) {
                switchImageProtocol( protocolUnsafe, protocolSafe );
                $protocolSwitch.removeClass('fixed-switch--active');
                $switchTitle.text(textOn)
            } else {
                return false;
            }
        }

        function getMails(langCode) {

            const langCodeFormatted = langCode.toUpperCase();

            const decision = confirm(`This will download all the '${langCodeFormatted}' mails. \nIt will FREEZE your machine for about 30 seconds. \nAre you sure?`);
            if (!decision) {
                console.log('Declined');
                return;
            }

            const $packLinks = $('.pack-link');
            const len = $packLinks.length;
            let count = 0;

            for (let i = 0; i < len; i++) {
                if ($packLinks.eq(i).text() === `${langCodeFormatted}-MC`) {
                    const url = $packLinks.eq(i).attr('href');
                    window.open(url, '_blank');
                    count += 1;
                }
            }
            console.log(`${count} mails with '${langCode}' language code downloaded.`);
        }

        $('.js-image-protocol-switch').on('click', function() {
            if ( !$(this).hasClass('fixed-switch--active') ) {
                imageProtocolSwitch( 'enable' );
            } else {
                imageProtocolSwitch( 'disable' );
            }
        });

        $('.js-download-mails').on('click', function() {
            const selectedLanguage = $(this).data('lang');
            getMails(selectedLanguage);
        });
        //End - Iframe images.

    });
</script>

    <style>

    /* Fixed Switch ( admin / mailing_list ) */
    .fixed-switch {
        position: fixed;
        width: 68px;
        padding: 10px 10px 15px;
        z-index: 10;
        background-color: #f5f5f5;
        border: 1px solid #ddd;
        cursor: pointer;
    }

    .fixed-switch:hover {
        background-color: #eaeaea;
    }

    .fixed-switch--right {
        margin-left: 800px;
        border-top-right-radius: 8px;
        border-bottom-right-radius: 8px;
        border-left: 0
    }

    .fixed-switch__title {
        color: #666;
        font-size: 11px;
        font-weight: bold;
        text-align: center;
        text-transform: uppercase;
    }

    .fixed-switch__indicator {
        width: 15px;
        height: 15px;
        margin: 0 auto;
        padding-top: 5px;
        background-color: #ff3c00;
        border-radius: 50%;
        transition: .5s ease color;
    }

    .fixed-switch--active .fixed-switch__indicator {
        background-color: #5cb85c;
    }

    .admin-mailing-links a {
        font-size: 10px;
        text-transform: lowercase;
    }

    .admin-mailing-links .preview-link {
        margin: 0 0 0 1px;
    }

    .admin-mailing-links .pack-link {
        background-color: rgba(217, 172, 44, 0.4);
        border-radius: 8px;
    }

    .admin-mailing-links .test-link img {
        border-radius: 8px;
        position: relative;
        top: -1px;
    }


    /* End - Fixed Switch ( admin / mailing_list ) */

    </style>
{% endblock %}

{% block breadcrumbs %}
<div class="breadcrumbs">
    <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a> &rsaquo;
    Mailings list
</div>
{% endblock %}

{% block content %}

<div class="row">
    <div class="col-lg-4 col-md-6">
        <div class="panel panel-default">
            <div class="panel-heading">
                Available mailings
            </div>
            {% if not IS_PRODUCTION %}
                <a href="{% url 'admin:mailing_flow_test' %}" class="btn-cta btn-cta--border w-full">Test Flows</a>
            {% endif %}
            <div class="table-responsive">
                <table class="table">
                    <thead>
                    <tr>
                        <th>Mailing name</th>
                        <th></th>
                        <th>Language</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for mailing in mailings %}<tr>
                        <td>
                            <strong style="cursor: pointer;">{{mailing}}</strong>
                        </td>
                        <td></td>
                        <td class="admin-mailing-links">
                            {% for lang in languages %}
                                <a class="preview-link" rel="{{ mailing }}" style="cursor:pointer" target="_blank">{{ lang|upper }}</a>
                                <a class="test-link" rel="{{ mailing }}" style="cursor:pointer">
                                    <img src="{% static 'contest-mail.png' %}" title="{{ lang|upper }}" width="15" height="15"/>
                                </a>
                            {% endfor %}
                            <br/><br/>
                                <a class="preview-link" rel="{{ mailing }}" style="cursor:pointer" target="_blank">xx</a>
                        </td>
                    </tr>{% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="col-lg-6">
        {% if 'localhost' in request.build_absolute_uri  %}
        <div class="fixed-switch fixed-switch--right js-image-protocol-switch">
            <p class="fixed-switch__title">Show Images</p>
            <div class="fixed-switch__indicator"></div>
        </div>
        {% endif %}

        <div class="fixed-switch fixed-switch--right js-download-mails" data-lang="en" style="top: 220px;">
            <p class="fixed-switch__title">GET ALL <span style="color: #d50000; opacity: 0.5;">ENGLISH</span> MAILS</p>
        </div>

        <div class="fixed-switch fixed-switch--right js-download-mails" data-lang="de" style="top: 310px;">
            <p class="fixed-switch__title">GET ALL <span style="color: #d5a100; opacity: 0.7;">GERMAN</span> MAILS</p>
        </div>

        <div class="fixed-switch fixed-switch--right js-download-mails" data-lang="fr" style="top: 400px;">
            <p class="fixed-switch__title">GET ALL <span style="color: #0079d5; opacity: 0.5;">FRENCH</span> MAILS</p>
        </div>

        <iframe id="preview-frame" src="" width="800px" height="900px" style="position:fixed"></iframe>
    </div>
</div>

{% endblock %}
