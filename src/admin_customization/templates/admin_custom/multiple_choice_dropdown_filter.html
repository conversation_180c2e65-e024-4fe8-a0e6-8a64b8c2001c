<h3>By {{ title }}</h3>
<select id="{{ title.split|join:"-" }}" multiple>
    {% for choice in choices %}
        {% if not choice.display == 'All' %}
            <option value="{{ choice.value }}">{{ choice.display }}</option>
        {% endif %}
    {% endfor %}
</select>

<script src="https://cdnjs.cloudflare.com/ajax/libs/slim-select/1.26.1/slimselect.min.js"></script>
<link href="https://cdnjs.cloudflare.com/ajax/libs/slim-select/1.26.1/slimselect.min.css" rel="stylesheet"></link>

<script>
    (function () {
        const selectId = "{{ title.split|join:"-" }}";
        const parameterName = "{{ choices.0.parameter_name }}";
        const urlParams = new URLSearchParams(window.location.search);
        const filterQueryParam = urlParams.get(parameterName) ? urlParams.get(parameterName).split(',') : [];

        const select = new SlimSelect({
          select: `#${selectId}`,
          onChange: (info) => {
              const paramsAfterChange = []
              info.forEach(item => { paramsAfterChange.push(item.value) })
              if (JSON.stringify(filterQueryParam) !== JSON.stringify(paramsAfterChange)) {
                  urlParams.set(parameterName, paramsAfterChange.join(','))
                  window.location.search = urlParams.toString();
              }
          }
        })

        select.set(filterQueryParam);
    })();
</script>
