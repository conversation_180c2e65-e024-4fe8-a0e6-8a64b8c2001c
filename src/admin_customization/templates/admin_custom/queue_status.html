{% extends 'admin_custom/dashboard.html' %}
{% load humanize %}
{% load admin_tags %}

{% block top_panel %}
    <div class="row">
        <div class="col-lg-12 col-md-12">
            <div class="panel panel-primary">
                <div class="panel-heading">
                    <div class="row">
                        <div class="col-xs-3">
                            <i class="fa fa-cogs fa-5x"></i>
                        </div>
                        <div class="col-xs-9 text-right">
                            <div class="huge">{{ celery_tasks_count }}</div>
                            <div>Celery tasks in queue</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

{% endblock %}

{% block content %}
    <div class="row">
        <div class="col-lg-12 col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    Celery apps
                </div>
                <table class="table table-responsive table-bordered">
                    <tbody>
                    <tr>
                        {% for name in celery_apps %}
                            <td>{{ name }}</td>
                        {% endfor %}
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    Celery most common tasks
                </div>
                <table class="table table-responsive table-bordered">
                    <thead>
                    <tr>
                        <th>Name</th>
                        <th>Number of tasks in queue</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for name, count in celery_most_common_tasks %}
                        <tr>
                            <td>{{ name }}</td>
                            <td>{{ count }}</td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-lg-6 col-md-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    Celery next tasks
                </div>
                <table class="table table-responsive table-bordered">
                    <thead>
                    <tr>
                        <th>Name</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for name in celery_head_tasks %}
                        <tr>
                            <td>{{ name }}</td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>

        <div class="col-lg-6 col-md-6">
            <div class="panel panel-default">
                <div class="panel-heading">
                    Celery last added tasks
                </div>
                <table class="table table-responsive table-bordered">
                    <thead>
                    <tr>
                        <th>Name</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for name in celery_tail_tasks %}
                        <tr>
                            <td>{{ name }}</td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
{% endblock %}
