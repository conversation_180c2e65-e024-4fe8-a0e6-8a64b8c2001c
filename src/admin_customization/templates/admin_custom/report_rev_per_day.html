{% extends "admin/base_site.html" %}
{% load humanize %}
{% load static %}
{% load util_tags %}

{% block extrahead %}

    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="{% static 'css/plugins/metisMenu/metisMenu.min.css' %}" rel="stylesheet">

    <!-- Timeline CSS -->
    <link href="{% static 'css/plugins/timeline.css' %}" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'css/sb-admin-2.css' %}" rel="stylesheet">

    <!-- Morris Charts CSS -->
    <link href="{% static 'css/plugins/morris.css' %}" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="{% static 'font-awesome-4.1.0/css/font-awesome.min.css' %}" rel="stylesheet" type="text/css">
    <link rel="stylesheet" type="text/css" href="{% static 'datatables.min.css' %}"/>

    <script type="text/javascript" src="{% static 'datatables.js' %}"></script>

    <script type="text/javascript" src="{% static 'dataTables.buttons.min.js' %}"></script>
    <style>
        #panel-refresh .panel-heading { border: 1px solid; cursor: pointer;}
        #panel-refresh .panel-heading:hover { background-color: #F5F5F5; }
        #panel-refresh-icon { position: relative; top: 10px; }
    </style>

    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
        <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
        <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->

    <script type="text/javascript">

        $(document).ready(function() {
            $('#example').DataTable({
                scrollY: true,
                scrollX: true,
                scrollCollapse: true,
                "lengthMenu": [ [25, 50, 100, 200, -1], [25, 50, 100, 200, "All"] ],
                info: false,
                dom: 'Bflrtip',
                buttons: [
                    'csv', 'excel', 'print'
                ],
                initComplete:function () {
                    this.api().columns('.selected-filter').every( function () {
                        var column = this;
                        var select = $('<select><option value=""></option></select>')
                                .appendTo( $(column.footer()).empty() )
                                .on( 'change', function () {
                                    var val = $.fn.dataTable.util.escapeRegex(
                                            $(this).val()
                                    );

                                    column
                                            .search( val ? '^'+val+'$' : '', true, false )
                                            .draw();
                                } );

                        column.data().unique().sort().each( function ( d, j ) {
                            select.append( '<option value="'+d+'">'+d+'</option>' )
                        } );
                    } );
                }
            });

            $('#example2').DataTable({
                scrollY: true,
                scrollX: true,
                scrollCollapse: true,
                "lengthMenu": [ [25, 50, 100, 200, -1], [25, 50, 100, 200, "All"] ],
                info: false,
                dom: 'Bflrtip',
                buttons: [
                    'csv', 'excel', 'print'
                ],
                "columnDefs": [
                    { "type": "date", "targets": 0 }
                ],
                initComplete:function () {
                    this.api().columns('.selected-filter').every( function () {
                        var column = this;
                        var select = $('<select><option value=""></option></select>')
                                .appendTo( $(column.footer()).empty() )
                                .on( 'change', function () {
                                    var val = $.fn.dataTable.util.escapeRegex(
                                            $(this).val()
                                    );

                                    column
                                            .search( val ? '^'+val+'$' : '', true, false )
                                            .draw();
                                } );

                        column.data().unique().sort().each( function ( d, j ) {
                            select.append( '<option value="'+d+'">'+d+'</option>' )
                        } );
                    } );
                }
            });

        } );

    </script>

{% endblock %}

{% block title %}Rev per day/month{% endblock %}

{% block content %}
    <h2> Rev per month</h2>
    <table id="example" class="display table table-striped table-bordered" cellspacing="0" width="100%">
        <thead>
        <tr>
            <th>Date</th>
            <th>Revenue</th>
            <th>Orders</th>
            <th>Per countries</th>
        </tr>
        </thead>
        <tfoot>
        <tr>
            <th>Date</th>
            <th>Revenue</th>
            <th>Orders</th>
            <th>Per countries</th>
        </tr>
        </tfoot>
        <tbody>
        {% for item in orders_and_months %}
            <tr>
                <td>{{ item.0 }}</td>
                <td>{{ item.1 }}</td>
                <td>{{ item.2 }}</td>
                <td>{% for key,value in item.3.items %}{{ key }}: {{ value.0 }} EUR / {{ value.1 }} orders <br/>{% endfor %}</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
    <h2> Rev per day</h2>
    <table id="example2" class="display table table-striped table-bordered" cellspacing="0" width="100%">
        <thead>
        <tr>
            <th>Date</th>
            <th>Revenue</th>
            <th>Orders</th>
            <th>Per countries</th>
        </tr>
        </thead>
        <tfoot>
        <tr>
            <th>Date</th>
            <th>Revenue</th>
            <th>Orders</th>
            <th>Countries</th>
        </tr>
        </tfoot>
        <tbody>
        {% for item in orders_and_dates %}
            <tr>
                <td>{{ item.0.isoformat }}</td>
                <td>{{ item.1 }}</td>
                <td>{{ item.2 }}</td>
                <td>{% for key,value in item.3.items %}{{ key }}: {{ value.0 }} EUR / {{ value.1 }} orders <br/>{% endfor %}</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>


{% endblock %}
