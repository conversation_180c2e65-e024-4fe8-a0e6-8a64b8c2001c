{% extends "admin/base_site.html" %}
{% load humanize %}
{% load static %}
{% load admin_tags %}
{% block extrahead %}

    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="{% static 'css/plugins/metisMenu/metisMenu.min.css' %}" rel="stylesheet">

    <!-- Timeline CSS -->
    <link href="{% static 'css/plugins/timeline.css' %}" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'css/sb-admin-2.css' %}" rel="stylesheet">

    <!-- Morris Charts CSS -->
    <link href="{% static 'css/plugins/morris.css' %}" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="{% static 'font-awesome-4.1.0/css/font-awesome.min.css' %}" rel="stylesheet" type="text/css">
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
        <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
        <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->

{% endblock %}

{% block title %}Search results for: "{{ query }}"{% endblock %}

{% block content %}

<div id="wrapper">
    <div class="row">
        <div class="col-lg-4">
            <form action="search" method="get">
                <div class="input-group custom-search-form">
                    <input class="form-control" name="q_order" pattern=".{2,}" placeholder="Order search..." required
                           title="2 characters minimum" type="text"/>
                        <span class="input-group-btn">
                            <button class="btn btn-default" type="submit">
                                <i class="fa fa-search"></i>
                            </button>
                        </span>
                </div>
            </form>
        </div>
    </div>
        <div class="row">
        <div class="col-lg-4">
            <form action="search" method="get">
                <div class="input-group custom-search-form">
                    <input class="form-control" name="q_user" pattern=".{2,}" placeholder="User search..." required
                           title="2 characters minimum" type="text"/>
                        <span class="input-group-btn">
                            <button class="btn btn-default" type="submit">
                                <i class="fa fa-search"></i>
                            </button>
                        </span>
                </div>
            </form>
        </div>
    </div>
        <div class="row">
        <div class="col-lg-4">
            <form action="search" method="get">
                <div class="input-group custom-search-form">
                    <input class="form-control" name="q_product" pattern=".{2,}" placeholder="Product search..." required
                           title="2 characters minimum" type="text"/>
                        <span class="input-group-btn">
                            <button class="btn btn-default" type="submit">
                                <i class="fa fa-search"></i>
                            </button>
                        </span>
                </div>
            </form>
        </div>
    </div>
        <div class="row">
        <div class="col-lg-4">
            <form action="search" method="get">
                <div class="input-group custom-search-form">
                    <input class="form-control" name="q_invoice" pattern=".{2,}" placeholder="Invoice search..." required
                           title="2 characters minimum" type="text"/>
                        <span class="input-group-btn">
                            <button class="btn btn-default" type="submit">
                                <i class="fa fa-search"></i>
                            </button>
                        </span>
                </div>
            </form>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">Search results for: {{ query }}</h1>
        </div>
    </div>
    <div class="row">{% for q_type, q_results in results.items %}
        {% if q_results|length > 0 %}
        <div class="col-lg-3">
            <div class="panel panel-default">
                <div class="panel-heading">
                    {{ q_type|capfirst }}
                </div>
                <div class="panel-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered table-hover">{% for r in q_results %}
                            <tr>
                                <td width="30px">{{ forloop.counter }}</td>
                                <td><a href="{{ r.get_absolute_url }}" target="_blank">{{ r }}</a></td>
                                {% if q_type == "orders" %}<td><a href="/admin/accounting/order_info/{{ r.id }}/">Overview</a></td>{% endif %}
                            </tr>
                        {% endfor %}</table>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    {% endfor %}</div>
</div>

{% endblock %}
