{% extends "admin/base_site.html" %}
{% load static %}

{% block title %}Add short url{% endblock %}

{% block extrahead %}

    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="{% static 'css/plugins/metisMenu/metisMenu.min.css' %}" rel="stylesheet">

    <!-- Timeline CSS -->
    <link href="{% static 'css/plugins/timeline.css' %}" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'css/sb-admin-2.css' %}" rel="stylesheet">

    <!-- Morris Charts CSS -->
    <link href="{% static 'css/plugins/morris.css' %}" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="{% static 'font-awesome-4.1.0/css/font-awesome.min.css' %}" rel="stylesheet" type="text/css">
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
        <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
        <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" type="text/css" href="{% static 'admin/css/forms.css' %}" />

{% endblock %}

{% block content_title %}{% endblock %}

{% block content %}

    <div class="panel panel-default">
        <div class="panel-body">
            <div class="row">
                <div class="col-lg-3">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            <div class="row">
                                <div class="col-xs-3">
                                    <i class="fa fa-external-link fa-5x"></i>
                                </div>
                                <div class="col-xs-9 text-right">
                                    <div class="huge">{{ active_shorturls }}</div>
                                    <div>Short urls</div>
                                </div>
                            </div>
                        </div>
                        <a href="/admin/shortener/shorturl/">
                            <div class="panel-footer">
                                <span class="pull-left">View Details</span>
                                <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                                <div class="clearfix"></div>
                            </div>
                        </a>
                    </div>
                </div>
                <div class="col-lg-3">
                    <div class="panel panel-green">
                        <div class="panel-heading">
                            <div class="row">
                                <div class="col-xs-3">
                                    <i class="fa fa-user fa-5x"></i>
                                </div>
                                <div class="col-xs-9 text-right">
                                    <div class="huge">{{ hits }}</div>
                                    <div>Url hits</div>
                                </div>
                            </div>
                        </div>
                        <a href="/admin/shortener/redirecteduserinfo/">
                            <div class="panel-footer">
                                <span class="pull-left">View Details</span>
                                <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                                <div class="clearfix"></div>
                            </div>
                        </a>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-3">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h2>Add a short url</h2>
                        </div>
                        <div class="panel-body">
                            <form action="." method="post" class="js-validate" data-script="Forms">
                                {% csrf_token %}
                                <div>
                                    <fieldset class="module aligned ">{% for field in form %}
                                        <div class="form-row {% if field.name %} field-{{ field.name }}{% endif %}">
                                            <div class="fieldBox{% if field.name %} field-{{ field.name }}{% endif %}{% if not field.is_readonly and field.errors %} errors{% endif %}{% if field.is_hidden %} hidden{% endif %}">
                                                {{ field.errors }}
                                                {% if field.is_checkbox %}
                                                    {{ field.field }}{{ field.label_tag }}
                                                {% else %}
                                                    {{ field.label_tag }}
                                                    {% if field.is_readonly %}
                                                        <p>{{ field.contents }}</p>
                                                    {% else %}
                                                        {{ field }}
                                                    {% endif %}
                                                {% endif %}
                                                {% if field.help_text %}
                                                    <p class="help">{{ field.help_text|safe }}</p>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endfor %}</fieldset>
                                </div>
                                <input type="submit" value="Save" class="default" name="_save">
                            </form>
                        </div>
                    </div>
                </div>
                {% if form.is_valid %}<div class="col-lg-6">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h2>New short urls</h2>
                        </div>
                        <div class="table-responsive">
                            <table class="table" style="margin:0">
                                <thead>
                                    <tr>
                                        <th>Name</th>
                                        <th>Description</th>
                                        <th>URL</th>
                                        <th>Type</th>
                                    </tr>
                                </thead>
                                <tbody>{% for shorturl in shorturls %}
                                    <tr>
                                        <td>{{ shorturl.name }}</td>
                                        <td>{{ shorturl.description }}</td>
                                        <td>{{ shorturl.url }}</td>
                                        <td>{{ shorturl.get_redirect_type_display }}</td>
                                    </tr>
                                {% endfor %}</tbody>
                            </table>
                        </div>
                    </div>
                </div>{% endif %}
            </div>
        </div>
    </div>

{% endblock %}
