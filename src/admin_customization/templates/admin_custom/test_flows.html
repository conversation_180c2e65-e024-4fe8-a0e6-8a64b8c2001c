{% extends "admin/base_site.html" %}{% load static %}

{% block extrastyle %}
    {{ block.super }}
    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">
    <style>
        .small-table td {
            padding: 10px;
        }

        @media print {
            .table td,
            .table th {
                background-color: inherit !important;
            }

            .table-striped tr:nth-of-type(odd) {
                background-color: #E6E6E6 !important;
                -webkit-print-color-adjust: exact;
            }

            .table-striped td:nth-of-type(odd) {
                border-right: 2px solid !important;
                -webkit-print-color-adjust: exact;
            }
        }

    </style>
{% endblock %}

{% block content %}
    <form method="post">
        {% csrf_token %}
        {{ form }}
        <input type="hidden" name="action" value="process_flows_with_time" class="select-across">
        <button type="submit" class="button" title="Run the selected action" name="index" value="0">Run flows</button>
    </form>
    <br>
    Format: YYYY-MM-DD MM:HH
{% endblock %}
