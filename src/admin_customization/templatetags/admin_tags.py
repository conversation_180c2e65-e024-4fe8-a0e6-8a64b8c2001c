from django import template
from django.conf import settings
from django.contrib.auth.models import User
from django.contrib.contenttypes.models import ContentType
from django.db.models.options import Options

register = template.Library()


@register.filter
def can_change(user: User, opts: Options) -> bool:
    model_name = opts.model_name
    content_type = ContentType.objects.get(
        model__iexact=model_name,
        app_label=opts.app_label,
    )
    perm = f'{content_type.app_label}.change_{model_name.lower()}'
    return (
        user.is_superuser
        or user.groups.filter(name=settings.ADMIN_EDITOR_GROUP).exists()
        or user.has_perm(perm)
    )


@register.filter
def cents_to_euros(amount):
    return round(amount / 100.0, 2)
