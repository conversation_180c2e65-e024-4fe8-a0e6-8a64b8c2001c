from unittest.mock import Mock

from django.conf import settings
from django.contrib.auth.models import (
    Group,
    Permission,
)
from django.contrib.contenttypes.models import ContentType

import pytest

from admin_customization.templatetags.admin_tags import can_change


@pytest.mark.django_db
class TestAdminTags:
    def test_can_change_should_return_true_when_user_is_superuser(self, user_factory):
        superuser = user_factory(is_superuser=True)
        assert can_change(superuser, Mock(app_label='invoice', model_name='invoice'))

    def test_can_change_should_return_true_when_user_belong_to_editor_group(
        self, user_factory
    ):
        user_with_group = user_factory(is_superuser=False)
        admin_editor_group, _ = Group.objects.get_or_create(
            name=settings.ADMIN_EDITOR_GROUP
        )
        user_with_group.groups.add(admin_editor_group)
        assert can_change(
            user_with_group, Mock(app_label='invoice', model_name='invoice')
        )

    def test_can_change_should_return_true_when_user_has_perm(self, user_factory):
        user_with_perm = user_factory(is_superuser=False)
        invoice_content_type, _ = ContentType.objects.get_or_create(
            app_label='invoice',
            model='invoice',
        )
        change_invoice_permission, _ = Permission.objects.get_or_create(
            name='Can change invoice',
            codename='change_invoice',
            content_type=invoice_content_type,
        )
        user_with_perm.user_permissions.add(change_invoice_permission)
        assert can_change(
            user_with_perm, Mock(app_label='invoice', model_name='invoice')
        )

    def test_can_change_should_return_false_when_user_has_nothing(self, user_factory):
        user_without_access = user_factory(is_superuser=False)
        assert not can_change(
            user_without_access, Mock(app_label='invoice', model_name='invoice')
        )
