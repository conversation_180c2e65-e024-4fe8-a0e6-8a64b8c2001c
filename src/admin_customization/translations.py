from django.conf import settings

import deepl

from custom.enums import LanguageEnum


def get_deepl_language_codes() -> list:
    """all LanguageEnum, but use 'nb' for Norwegian"""
    lang = ['nb' if lang == LanguageEnum.NO else lang for lang in LanguageEnum.values]
    # remove English, this is the base language
    lang.remove(LanguageEnum.EN)
    return lang


def translate_by_deepl(text: str) -> dict:
    translator = deepl.Translator(settings.DEEPL_KEY)
    return {
        target_lang: translator.translate_text(
            text,
            target_lang=target_lang,
        ).text
        for target_lang in get_deepl_language_codes()
    }
