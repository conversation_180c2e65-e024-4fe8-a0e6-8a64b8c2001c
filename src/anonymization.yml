tables:
  accounting_moneycashback:
    columns:
      bank_account_number: ('bank_account_number')
      additional_info: ('[]')
      client_name: ('client_name')
  accounting_moneytransfer: delete
  auth_user:
    columns:
      username:
        type: literal
        value: "'username_' || id"
        where: &ordinary_user >-
          is_staff = FALSE
          and not username ilike '%tylko.com'
          and id not in
          (select user_id from user_profile_userprofile where user_type=1 or user_type=6 or user_type=7)
      first_name:
        type: literal
        value: "'first_name ' || id"
        where: *ordinary_user
      last_name:
        type: literal
        value: "'last_name ' || id"
        where: *ordinary_user
      email:
        type: literal
        value: "id || '@example.com'"
        where: *ordinary_user
#     grant admin privileges to all users from tylko.com domain
      is_superuser:
        type: literal
        value: TRUE
        where: &tylko_user username ilike '%tylko.com'
      is_staff:
        type: literal
        value: TRUE
        where: *tylko_user
  authtoken_token: delete
  complaints_customercontact:
    columns:
      first_name: ('first_name')
      email: ('<EMAIL>')
  customer_service_cscorrectionaddressrequest:
    columns:
      first_name: ('first_name')
      last_name: ('last_name')
      street_address_1: ('street_address_1')
      street_address_2: ('street_address_2')
      company_name: ('company_name')
      city: ('city')
      postal_code: ('55555')
      vat: ('vat')
  customer_service_csorder:
    columns:
      first_name: ('first_name ' || id)
      last_name: ('last_name ' || id)
      city: ('city ' || id)
      company_name: ('company_name ' || id)
      email: (id || '@example.com')
      invoice_city: ('invoice_city ' || id)
      invoice_company_name: ('invoice_company_name ' || id)
      invoice_email: (id || '@example.com')
      invoice_first_name: ('invoice_first_name ' || id)
      invoice_last_name: ('invoice_last_name ' || id)
      invoice_street_address_1: ('invoice_street_address_1 ' || id)
      invoice_street_address_2: ('invoice_street_address_2 ' || id)
      owner_email: (id || '@example.com')
      owner_username: ('username_' || id)
      phone: ('phone ' || id)
      street_address_1: ('street_address_1 ' || id)
      street_address_2: ('street_address_2 ' || id)
  customer_service_csuserprofile:
    columns:
      city: ('city ' || id)
      company_name: ('company_name ' || id)
      first_name: ('first_name ' || id)
      last_name: ('last_name ' || id)
      phone: ('phone ' || id)
      street_address_1: ('street_address_1 ' || id)
      street_address_2: ('street_address_2 ' || id)
      invoice_city: ('invoice_city ' || id)
      invoice_street_address_1: ('invoice_street_address_1 ' || id)
      invoice_street_address_2: ('invoice_street_address_2 ' || id)
      email: (id || '@example.com')
      user_email: (id || '@example.com')
      user_username: ('username_' || id)
  django_admin_log: delete
  django_mailer_blacklist: delete
  django_mailer_message:
    columns:
      to_address: ('<EMAIL>')
      encoded_message: ('anonimize ....')
      subject: ('anonymize_django_mails')
  django_mailer_queuedmessage: delete
  invoice_invoice:
    columns:
      additional_address_1: ('additional_address_1')
      additional_address_2: ('additional_address_2')
      corrected_notes: ('corrected_notes')
      cached_to_dict: ('{}')
  invoice_invoicecorrectionchange:
    columns:
      previous_state: ('previous_state')
      current_state: ('current_state')
  invoice_invoicehistory: delete
  kpi_kpivalue: delete
  logger_log: delete
  mailing_mailchimptaskretry: delete
  mailing_mailingflowstatus:
    columns:
      email_address: ('<EMAIL>')
      subject: ('anonymize_django_mails')
  mailing_productdeliveredblacklistedemail: delete
  mailing_retargetingblacklist: delete
  orders_order:
    columns:
      first_name: ('first_name ' || id)
      last_name: ('last_name ' || id)
      city: ('city ' || id)
      company_name: ('company_name ' || id)
      email: (id || '@example.com')
      invoice_city: ('invoice_city ' || id)
      invoice_company_name: ('invoice_company_name ' || id)
      invoice_email: (id || '@example.com')
      invoice_first_name: ('invoice_first_name ' || id)
      invoice_last_name: ('invoice_last_name ' || id)
      invoice_postal_code: (id::varchar)
      invoice_street_address_1: ('invoice_street_address_1 ' || id)
      invoice_street_address_2: ('invoice_street_address_2 ' || id)
      invoice_vat: ('invoice_vat ' || id)
      order_notes: ('order_notes ' || id)
      phone: ('phone ' || id)
      postal_code: (id::varchar)
      street_address_1: ('street_address_1 ' || id)
      street_address_2: ('street_address_2 ' || id)
      vat: ('vat')
  orders_orderstatuscheckhistory:
    columns:
      postal_code: ('55555')
      email: ('<EMAIL>')
  producers_manufactor:
    columns:
      invoice_tax_id: ('invoice_tax_id')
      invoice_street_address_1: ('invoice_street_address_1')
      invoice_street_address_2: ('invoice_street_address_2')
      invoice_city: ('invoice_city')
      invoice_postal_code: ('invoice_postal_code')
      phone: ('phone')
  reviews_review:
    columns:
      name: ('name')
      email: ('<EMAIL>')
  shortener_redirecteduserinfo:
    columns:
      ip: ('*********')
      user_agent: ('SomeBrowser 7.0')
  user_consents_cookieconsent: delete
  user_profile_loginaccesstoken: delete
  user_profile_passwordresettoken: delete
  user_profile_retargetingblacklisttoken: delete
  user_profile_userprofile:
    columns:
      city:
        type: literal
        value: ('city ' || id)
        where: &prof_where user_type != 1
      company_name:
        type: literal
        value: ('company_name ' || id)
        where: *prof_where
      first_name:
        type: literal
        value: ('first_name ' || id)
        where: *prof_where
      last_name:
        type: literal
        value: ('last_name ' || id)
        where: *prof_where
      phone:
        type: literal
        value: ('phone ' || id)
        where: *prof_where
      postal_code:
        type: literal
        value: (id::varchar)
        where: *prof_where
      street_address_1:
        type: literal
        value: ('street_address_1 ' || id)
        where: *prof_where
      street_address_2:
        type: literal
        value: ('street_address_2 ' || id)
        where: *prof_where
      invoice_city:
        type: literal
        value: ('invoice_city ' || id)
        where: *prof_where
      invoice_company_name:
        type: literal
        value: ('invoice_company_name ' || id)
        where: *prof_where
      invoice_postal_code:
        type: literal
        value: (id::varchar)
        where: *prof_where
      invoice_street_address_1:
        type: literal
        value: ('invoice_street_address_1 ' || id)
        where: *prof_where
      invoice_street_address_2:
        type: literal
        value: ('invoice_street_address_2 ' || id)
        where: *prof_where
      email:
        type: literal
        value: (id || '@example.com')
        where: *prof_where
      invoice_vat:
        type: literal
        value: ('invoice_vat ' || id)
        where: *prof_where
      notes:
        type: literal
        value: ('notes ' || id)
        where: *prof_where
      invoice_email:
        type: literal
        value: (id || '@example.com')
        where: *prof_where
      invoice_first_name:
        type: literal
        value: ('invoice_first_name ' || id)
        where: *prof_where
      invoice_last_name:
        type: literal
        value: ('invoice_last_name ' || id)
        where: *prof_where
      account_name:
        type: literal
        value: ('account_name ' || id)
        where: *prof_where
      vat:
        type: literal
        value: ('vat')
        where: *prof_where
      registration_user_agent:
        type: literal
        value: ('SomeBrowser 7.0')
        where: *prof_where
  user_profile_userprospect:
    columns:
      email: ('<EMAIL>')
  vouchers_voucher:
    columns:
      for_email: ('<EMAIL>')
      code: ('slawek_szef_' || id)
  waiting_list_waitinglistentry:
    columns:
      email_address: ('<EMAIL>')
  warehouse_sampleboxelementsdelivery:
    columns:
      additional_info: empty
