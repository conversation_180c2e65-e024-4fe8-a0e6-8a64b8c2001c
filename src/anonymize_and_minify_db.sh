#!/bin/bash

set -e

MEDIA_DIR="/app/media"

export PGHOST="${POSTGRES_HOST:-postgres}"
export PGPORT="${POSTGRES_PORT:-5432}"
export PGUSER="postgres"
export POSTGRES_DB="anonymize"
export MINI_DB="mini_anonymize"
export PGPASSWORD="$POSTGRES_PASSWORD"

cleanup() {
    echo "Cleaning up files from app directory..."
    rm -rf \
        /app/file_list.json \
        /app/fixtures/cstm_data.json \
        /app/fixtures/migration_plan.json \
        /app/fixtures/static_files

    echo "Cleaning up temporary files..."
    rm -rf "/app/tmp"
}

LATEST_DUMP=$(aws s3 ls s3://tylko-contabo-backup/ --profile s3-tylko-backup-ro | sort -n | awk '{ print $4 }' | grep -E "cstm-.*" | tail -1)

LATEST_DUMP_NAME="${LATEST_DUMP%%.*}"  # strip extension
ANONYMIZED_DUMP_FILENAME="anonymized_$LATEST_DUMP_NAME"
MINI_DB_FIXTURE_FILENAME="mini_fixture_$LATEST_DUMP_NAME"
PRODUCTS_FIXTURE_FILENAME="products_fixture_$LATEST_DUMP_NAME"
MINI_DB_DUMP_FILENAME="mini_dump_$LATEST_DUMP_NAME"

if aws s3 ls s3://anonymized-dumps/ --profile tylko-s3-rw | grep -qE "$ANONYMIZED_DUMP_FILENAME.*"; then
    echo "Target dump $LATEST_DUMP_NAME has already been anonymized and uploaded to S3"
    exit 1
fi

cleanup
mkdir -p "/app/tmp"
cd "/app"

echo "$(date +'[%F %T]') Fetch latest dump: $LATEST_DUMP ..."
aws s3 cp --no-progress --profile s3-tylko-backup-ro "s3://tylko-contabo-backup/$LATEST_DUMP" "/app/tmp/$LATEST_DUMP"

echo "$(date +'[%F %T]') Clean up and prepare db..."
psql -c "CREATE ROLE cstm;" || true
psql -c "CREATE ROLE datadog;" || true
psql -c "DROP DATABASE IF EXISTS $POSTGRES_DB;"
psql -c "CREATE DATABASE $POSTGRES_DB;"


echo "$(date +'[%F %T]') Extract latest dump: $LATEST_DUMP ..."
tar -xf "/app/tmp/$LATEST_DUMP" -C /app/tmp
rm -f "/app/tmp/$LATEST_DUMP"

echo "$(date +'[%F %T]') Prepare dump file list"
pg_restore -l "/app/tmp/$LATEST_DUMP_NAME" | sed '/google_columnar_engine/d;/google_db_advisor/d;/hypopg/d;/pg_hint_plan/d;/google_auto_hints/d;/hints_properties/d' > db.list

echo "$(date +'[%F %T]') Restore db..."
pg_restore -p "$PGPORT" -h "$PGHOST" -Fd -j6 -d "$POSTGRES_DB" --no-privileges -L db.list "/app/tmp/$LATEST_DUMP_NAME"
rm -rf "/app/tmp/$LATEST_DUMP_NAME"

echo "$(date +'[%F %T]') Anonymize db..."
pynonymizer \
    --strategy anonymization.yml \
    --db-user "$PGUSER" \
    --db-password "$POSTGRES_PASSWORD" \
    --db-type postgres \
    --db-host "$PGHOST" \
    --db-port "$PGPORT" \
    --db-name "$POSTGRES_DB" \
    --only-step ANONYMIZE_DB

echo "$(date +'[%F %T]') Drop pglogical extension..."
psql -c "DROP EXTENSION IF EXISTS pglogical;" "$POSTGRES_DB"

echo "$(date +'[%F %T]') Dump anonymized db..."
pg_dump -Fd -j8 -x -b -f "/app/tmp/dump" "$POSTGRES_DB"

echo "$(date +'[%F %T]') Pack anonymized dump..."
tar -czf "/app/tmp/$ANONYMIZED_DUMP_FILENAME.tar.gz" -C "/app/tmp/" dump
rm -rf "/app/tmp/$ANONYMIZED_DUMP_FILENAME"

echo "$(date +'[%F %T]') Upload anonymized dump to S3..."
aws s3 cp --no-progress --profile tylko-s3-rw "/app/tmp/$ANONYMIZED_DUMP_FILENAME.tar.gz" "s3://anonymized-dumps/"
rm -f "/app/tmp/$ANONYMIZED_DUMP_FILENAME.tar.gz"

echo "$(date +'[%F %T]') Apply migrations..."
python manage.py migrate

echo "$(date +'[%F %T]') Run list_mini_db_files..."
python manage.py list_mini_db_files

echo "$(date +'[%F %T]') Delete excessive media files..."
comm -2 -3 <(find "$MEDIA_DIR" -type f | sed "s|$MEDIA_DIR/||" | sort) <(sort "/app/file_list.json" | uniq) \
    | sed -e "s|^|$MEDIA_DIR/|" | xargs -n 100 rm -f

echo "$(date +'[%F %T]') Fetch missing media files..."
comm -1 -3 <(find "$MEDIA_DIR" -type f | sed "s|$MEDIA_DIR/||" | sort) <(sort "/app/file_list.json" | uniq) \
    | xargs -I {} -P 12 -n 1 aws s3 cp s3://tylko-s3-media-prod/media/{} $MEDIA_DIR/{} --profile tylko-s3-media-prod-ro --only-show-errors \
    || true

echo "$(date +'[%F %T]') Run dump_margin_tests_products..."
python manage.py dump_margin_tests_products

echo "$(date +'[%F %T]') Pack products fixture..."
tar -czf "/app/tmp/$PRODUCTS_FIXTURE_FILENAME.tar.gz" \
    fixtures/products

echo "$(date +'[%F %T]') Upload products fixture to S3..."
aws s3 cp --no-progress --profile tylko-s3-rw "/app/tmp/$PRODUCTS_FIXTURE_FILENAME.tar.gz" "s3://products-fixtures/"
rm -f "/app/tmp/$PRODUCTS_FIXTURE_FILENAME.tar.gz"
rm -rf fixtures/products

echo "$(date +'[%F %T]') Run dump_mini_db..."
python manage.py dump_mini_db

echo "$(date +'[%F %T]') Clean up and prepare mini db..."
psql -c "DROP DATABASE IF EXISTS $MINI_DB;"
psql -c "CREATE DATABASE $MINI_DB;"

echo "$(date +'[%F %T]') Load mini db from fixture"
POSTGRES_DB=$MINI_DB python manage.py load_mini_db

echo "$(date +'[%F %T]') Dump mini db..."
mkdir -p "/app/tmp/$MINI_DB_DUMP_FILENAME"
pg_dump -Fd -j8 -x -b -f "/app/tmp/$MINI_DB_DUMP_FILENAME/dump" "$MINI_DB"

echo "$(date +'[%F %T]') Pack mini dump..."
tar -czf "/app/tmp/$MINI_DB_DUMP_FILENAME.tar.gz" \
    fixtures/static_files \
    -C "/app/tmp/$MINI_DB_DUMP_FILENAME" dump

echo "$(date +'[%F %T]') Upload mini dump to S3..."
aws s3 cp --no-progress --profile tylko-s3-rw "/app/tmp/$MINI_DB_DUMP_FILENAME.tar.gz" "s3://mini-dumps-2/"
TIME_ELAPSED=$(date -u -d "0 $SECONDS seconds" +"%T")

echo "$(date +'[%F %T]') Finished in $TIME_ELAPSED"

body=$(cat  << EOF
{
    "text": "Anonimizacja zakończona w $TIME_ELAPSED :feelsgoodman:",
    "channel": "devops-anonymization",
    "username": "happy-pepe",
    "icon_emoji": ":yep:"
}
EOF
)

curl -X POST -H "content-type: application/json" -d "$body" "$SLACK_WEBHOOK"
