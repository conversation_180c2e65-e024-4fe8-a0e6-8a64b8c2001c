import json

from django import forms
from django.contrib import admin
from django.shortcuts import render

from automatic_batching.models import (
    AutoBatchMailReceiver,
    BatchConnection,
    BatchingOrder,
    ComplaintBatchingSetting,
    TypeAndColor,
)
from automatic_batching.services.complaint_batching import ComplaintBatching
from custom.enums import ShelfType


class OnlyReadManufactorPermissionMixin:
    def get_formset(self, request, obj=None, **kwargs):
        formset = super().get_formset(request, obj, **kwargs)
        field = formset.form.base_fields['manufactor']
        field.widget.can_add_related = False
        field.widget.can_change_related = False
        field.widget.can_delete_related = False
        return formset


class BatchingOrderInline(OnlyReadManufactorPermissionMixin, admin.TabularInline):
    model = BatchingOrder
    ordering = ('ordinal_number',)
    extra = 1


class BatchConnectionInline(OnlyReadManufactorPermissionMixin, admin.TabularInline):
    model = BatchConnection
    ordering = ('manufactor',)
    extra = 1


class AutoBatchMailReceiverInline(
    OnlyReadManufactorPermissionMixin, admin.TabularInline
):
    model = AutoBatchMailReceiver
    ordering = ('manufactor',)
    extra = 1


class MagicColorForm(forms.ModelForm):
    # just add select and magic will happen in change_form_template
    color = forms.ChoiceField(choices=((i, i) for i in range(0, 20)))


class TypeAndColorAdmin(admin.ModelAdmin):
    change_form_template = 'admin/type_and_color_change_form.html'

    list_display = ('type', 'color', 'all_colors')
    fields = ('type', 'color', 'all_colors')

    form = MagicColorForm

    def changeform_view(self, request, object_id=None, form_url='', extra_context=None):
        shelf_types_to_colors = {
            shelf_type.value: [list(color) for color in shelf_type.colors.choices()]
            for shelf_type in ShelfType
        }
        extra_context = extra_context or {}
        extra_context['shelf_types_to_colors'] = json.dumps(shelf_types_to_colors)
        return super().changeform_view(
            request,
            object_id=object_id,
            form_url=form_url,
            extra_context=extra_context,
        )


class MainSettingAdmin(admin.ModelAdmin):
    change_form_template = 'admin/main_setting_change_form.html'

    list_display = ('name', 'is_active', 'batching_days', 'batching_time', 'last_run')
    inlines = (AutoBatchMailReceiverInline, BatchConnectionInline, BatchingOrderInline)

    def has_add_permission(self, request):
        return False

    def has_delete_permission(self, request, obj=None):
        return False

    def preview_batches(self, request, object_id):
        service = ComplaintBatching()
        data = service.get_grouped_products_as_serializable_dict()
        opts = self.model._meta
        app_label = opts.app_label
        return render(
            request,
            'admin/preview_batches.html',
            {
                'data': dict(data),
                'opts': opts,
                'app_label': app_label,
            },
        )

    def changeform_view(self, request, object_id=None, form_url='', extra_context=None):
        if request.method == 'POST' and '_preview_batches' in request.POST:
            obj = self.get_object(request, object_id)
            return self.preview_batches(request, obj)

        return super().changeform_view(
            request,
            object_id=object_id,
            form_url=form_url,
            extra_context=extra_context,
        )


admin.site.register(ComplaintBatchingSetting, MainSettingAdmin)
admin.site.register(TypeAndColor, TypeAndColorAdmin)
