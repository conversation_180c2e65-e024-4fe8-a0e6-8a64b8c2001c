from celery.schedules import crontab

automatic_batching_tasks_scheduler = {
    'auto_create_complaint_batches': {
        'task': 'automatic_batching.tasks.auto_create_complaint_batches',
        'schedule': crontab(hour='*', minute='0,30'),
    },  # task check if this is moment set by user for running batching.
    # Batching will be executed once a day.
}


automatic_batching_tasks_scheduler_dev = {
    'auto_create_complaint_batches': {
        'task': 'automatic_batching.tasks.auto_create_complaint_batches',
        'schedule': crontab(hour='*', minute='0,30'),
    },
}
