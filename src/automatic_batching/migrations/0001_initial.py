# Generated by Django 4.1.9 on 2023-10-04 10:37

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)

import automatic_batching.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        (
            'producers',
            '0041_remove_priority_complaint_for_products_squashed_0091_alter_manufactor_invoice_company_name_and_more',  # noqa E501
        ),
    ]

    operations = [
        migrations.CreateModel(
            name='ComplaintBatchingSetting',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('last_run', models.DateTimeField(auto_created=True)),
                ('is_active', models.BooleanField(default=False)),
                ('mail_body', models.TextField(blank=True)),
                ('name', models.CharField(max_length=50)),
                ('batching_time', models.TimeField(blank=True, null=True)),
                (
                    'batching_days',
                    automatic_batching.models.ModifiedArrayField(
                        base_field=models.IntegerField(
                            blank=True,
                            choices=[
                                (0, 'Monday'),
                                (1, 'Tuesday'),
                                (2, 'Wednesday'),
                                (3, 'Thursday'),
                                (4, 'Friday'),
                                (5, 'Saturday'),
                                (6, 'Sunday'),
                            ],
                            null=True,
                        ),
                        blank=True,
                        default=list,
                        size=None,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='TypeAndColor',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'type',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, 'TYPE01'),
                            (1, 'TYPE02'),
                            (2, 'VENEER TYPE01'),
                            (3, 'TYPE03'),
                            (4, 'TYPE13'),
                        ]
                    ),
                ),
                ('color', models.PositiveIntegerField(blank=True, null=True)),
                ('all_colors', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name': 'Type and color',
                'verbose_name_plural': 'Types and colors',
            },
        ),
        migrations.CreateModel(
            name='BatchingOrder',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('ordinal_number', models.IntegerField(unique=True)),
                (
                    'excluded_features',
                    automatic_batching.models.ModifiedArrayField(
                        base_field=models.IntegerField(
                            choices=[(0, 'DESK'), (1, 'S_PLUS')]
                        ),
                        blank=True,
                        default=list,
                        size=None,
                    ),
                ),
                ('original_product_producer', models.BooleanField()),
                ('is_active', models.BooleanField()),
                (
                    'main_setting',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='automatic_batching.complaintbatchingsetting',
                    ),
                ),
                (
                    'manufactor',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='producers.manufactor',
                    ),
                ),
                (
                    'type_and_colors',
                    models.ManyToManyField(
                        related_name='batching_orders',
                        to='automatic_batching.typeandcolor',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Batching order',
                'verbose_name_plural': 'Batching orders',
                'ordering': ('ordinal_number',),
            },
        ),
        migrations.CreateModel(
            name='BatchConnection',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('product_type', models.BooleanField(default=True)),
                ('unique_color', models.BooleanField(default=False)),
                (
                    'main_setting',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='automatic_batching.complaintbatchingsetting',
                    ),
                ),
                (
                    'manufactor',
                    models.OneToOneField(
                        null=True,
                        on_delete=django.db.models.deletion.SET,
                        to='producers.manufactor',
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='AutoBatchMailReceiver',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('email', models.TextField()),
                ('cc_email', models.TextField()),
                (
                    'manufactor',
                    models.OneToOneField(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='producers.manufactor',
                    ),
                ),
                (
                    'settings',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='mail_receivers',
                        to='automatic_batching.complaintbatchingsetting',
                    ),
                ),
            ],
        ),
    ]
