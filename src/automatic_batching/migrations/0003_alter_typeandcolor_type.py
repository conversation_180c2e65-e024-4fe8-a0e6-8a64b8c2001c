# Generated by Django 4.1.9 on 2024-05-14 14:09

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('automatic_batching', '0002_alter_typeandcolor_type'),
    ]

    operations = [
        migrations.AlterField(
            model_name='typeandcolor',
            name='type',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (0, 'TYPE01'),
                    (1, 'TYPE02'),
                    (2, 'VENEER TYPE01'),
                    (3, 'TYPE03'),
                    (4, 'TYPE13'),
                    (5, 'VENEER TYPE13'),
                    (6, 'TYPE23'),
                    (7, 'TYPE24'),
                    (8, 'TYPE25'),
                ]
            ),
        ),
    ]
