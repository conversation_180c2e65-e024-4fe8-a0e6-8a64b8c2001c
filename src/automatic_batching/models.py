import enum

from django import forms
from django.contrib.postgres.fields import <PERSON><PERSON>y<PERSON>ield
from django.core.exceptions import ValidationError
from django.db import models

from custom.enums import ShelfType
from producers.enums import FeatureEnum


class ModifiedArrayField(ArrayField):
    # https://gist.github.com/danni/f55c4ce19598b2b345ef?permalink_comment_id=3408902
    def formfield(self, **kwargs):
        defaults = {
            'form_class': forms.TypedMultipleChoiceField,
            'choices': self.base_field.choices,
            **kwargs,
        }
        return super(ArrayField, self).formfield(**defaults)

    def to_python(self, value):
        res = super().to_python(value)
        if isinstance(res, list):
            value = [self.base_field.to_python(val) for val in res]
        return value

    def validate(self, value, model_instance):
        if not self.editable:
            # Skip validation for non-editable fields.
            return

        if self.choices is not None and value not in self.empty_values:
            if set(value).issubset({option_key for option_key, _ in self.choices}):
                return
            raise ValidationError(
                self.error_messages['invalid_choice'],
                code='invalid_choice',
                params={'value': value},
            )

        if value is None and not self.null:
            raise ValidationError(self.error_messages['null'], code='null')

        if not self.blank and value in self.empty_values:
            raise ValidationError(self.error_messages['blank'], code='blank')


class ComplaintBatchingSetting(models.Model):
    WEEK_DAYS = (
        (0, 'Monday'),
        (1, 'Tuesday'),
        (2, 'Wednesday'),
        (3, 'Thursday'),
        (4, 'Friday'),
        (5, 'Saturday'),
        (6, 'Sunday'),
    )

    is_active = models.BooleanField(default=False)
    mail_body = models.TextField(blank=True)
    name = models.CharField(max_length=50)
    last_run = models.DateTimeField(auto_created=True)
    batching_time = models.TimeField(null=True, blank=True)
    batching_days = ModifiedArrayField(
        models.IntegerField(null=True, blank=True, choices=WEEK_DAYS),
        blank=True,
        default=list,
    )

    def __str__(self):
        return self.name


class AutoBatchMailReceiver(models.Model):
    manufactor = models.OneToOneField(
        'producers.Manufactor', null=True, on_delete=models.SET_NULL
    )
    settings = models.ForeignKey(
        ComplaintBatchingSetting,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='mail_receivers',
    )
    email = models.TextField()
    cc_email = models.TextField()

    @property
    def email_list(self):
        if self.email:
            return self.email.split(',')
        return []

    @property
    def cc_email_list(self):
        if self.cc_email:
            return self.cc_email.split(',')
        return []


class BatchConnection(models.Model):
    main_setting = models.ForeignKey(
        ComplaintBatchingSetting,
        on_delete=models.SET_NULL,
        null=True,
    )
    manufactor = models.OneToOneField(
        'producers.Manufactor',
        on_delete=models.SET,
        null=True,
    )
    product_type = models.BooleanField(default=True)  # probably to remove
    unique_color = models.BooleanField(default=False)

    def __str__(self):
        return f'{self.manufactor} {self.product_type} {self.unique_color}'


class TypeAndColor(models.Model):
    type = models.PositiveSmallIntegerField(choices=ShelfType.choices())
    color = models.PositiveIntegerField(null=True, blank=True)
    all_colors = models.BooleanField(default=False)

    def __str__(self):
        return f'{self.type_option.name} {self.color_name}'

    @property
    def type_option(self) -> ShelfType:
        return ShelfType(self.type)

    @property
    def color_name(self) -> str:
        if self.all_colors:
            return 'all colors'
        elif self.color is None:
            return 'Error'
        else:
            return self.type_option.colors(self.color).name

    def does_type_and_color_match(
        self, shelf_type_option: ShelfType, color_option: enum.IntEnum
    ) -> bool:
        if not shelf_type_option == self.type_option:
            return False
        if self.all_colors or color_option.name == self.color_name:
            return True
        return False

    class Meta:
        verbose_name = 'Type and color'
        verbose_name_plural = 'Types and colors'


class BatchingOrder(models.Model):
    main_setting = models.ForeignKey(
        ComplaintBatchingSetting,
        on_delete=models.SET_NULL,
        null=True,
    )
    ordinal_number = models.IntegerField(unique=True)
    manufactor = models.ForeignKey(
        'producers.Manufactor',
        on_delete=models.SET_NULL,
        null=True,
    )
    type_and_colors = models.ManyToManyField(
        TypeAndColor, related_name='batching_orders'
    )
    excluded_features = ModifiedArrayField(
        models.IntegerField(choices=FeatureEnum.choices()),
        blank=True,
        default=list,
    )
    original_product_producer = models.BooleanField()
    is_active = models.BooleanField()

    def __str__(self):
        return f'{self.manufactor} - {self.ordinal_number}'

    def does_type_and_color_match(
        self, shelf_type_option: ShelfType, color_option: enum.IntEnum
    ) -> bool:
        if not self.type_and_colors.exists():
            return True
        for type_and_color in self.type_and_colors.all():
            if type_and_color.does_type_and_color_match(
                shelf_type_option, color_option
            ):
                return True
        return False

    class Meta:
        verbose_name = 'Batching order'
        verbose_name_plural = 'Batching orders'
        ordering = ('ordinal_number',)
