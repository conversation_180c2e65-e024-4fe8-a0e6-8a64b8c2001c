from django.utils import timezone

from celery import shared_task

from automatic_batching.models import ComplaintBatchingSetting
from automatic_batching.services.complaint_batching import ComplaintBatching
from custom.utils.slack import notify_about_automatic_batching_status
from producers.models import (
    Manufactor,
    Product,
    ProductBatch,
)


@shared_task
def auto_create_complaint_batches():
    main_setting = ComplaintBatchingSetting.objects.filter(
        is_active=True
    ).first()  # should be only one
    now = timezone.now()

    if not main_setting:
        return

    if main_setting.last_run.date() == now.date():
        return

    batching_days = [day for day in main_setting.batching_days]
    if now.weekday() not in batching_days:
        return
    if now.time() < main_setting.batching_time:
        return
    notify_about_automatic_batching_status('Batchuje reklamacje')
    service = ComplaintBatching()
    service.create_batches_and_send_mail()
    main_setting.last_run = now
    main_setting.save(update_fields=['last_run'])
    notify_about_automatic_batching_status('Skonczylem batchowac reklamacje')


@shared_task
def create_complaint_batches(product_ids):
    query = Product.objects.filter(id__in=product_ids)
    service = ComplaintBatching()
    service.create_batches(query)


@shared_task
def send_auto_email_with_files_to_manufactor(manufactor_id, batch_ids):
    manufactor = Manufactor.objects.get(id=manufactor_id)
    query = ProductBatch.objects.filter(id__in=batch_ids)
    service = ComplaintBatching()
    service.send_mail_to_producers(manufactor, query)
