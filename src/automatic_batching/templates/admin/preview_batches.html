{% extends "admin/base_site.html" %}
{% load i18n l10n %}
{% load admin_urls %}

{% block breadcrumbs %}
    <div class="breadcrumbs">
        <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
        &rsaquo; <a href="{% url 'admin:app_list' app_label=app_label %}">{{ app_label|capfirst|escape }}</a>
        &rsaquo; <a href="{% url opts|admin_urlname:'changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
        &rsaquo; Batch selected products
    </div>
{% endblock %}

{% block content %}

{% csrf_token %}

<p>Products to batch:</p>
{% for ordering, batches in data.items %}
    <p>{{ ordering }} </p>
    {% for batch_name, products in batches.items %}
        <li>{{ batch_name }}: {{ products }} </li>
    {% endfor %}
{% endfor %}

{% endblock %}
