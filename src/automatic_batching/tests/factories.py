import factory

from automatic_batching.models import (
    BatchConnection,
    BatchingOrder,
    ComplaintBatchingSetting,
    TypeAndColor,
)


class ComplaintBatchingSettingFactory(factory.django.DjangoModelFactory):
    name = 'test_main_setting'
    last_run = factory.Faker('date_time')

    class Meta:
        model = ComplaintBatchingSetting


class TypeAndColorFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = TypeAndColor


class BatchConnectionFactory(factory.django.DjangoModelFactory):
    main_setting = factory.SubFactory(ComplaintBatchingSettingFactory)

    class Meta:
        model = BatchConnection


class BatchingOrderFactory(factory.django.DjangoModelFactory):
    main_setting = factory.SubFactory(ComplaintBatchingSettingFactory)
    ordinal_number = 1

    class Meta:
        model = BatchingOrder
