import pytest

from automatic_batching.services.complaint_batching import ComplaintBatching
from custom.enums import ShelfType
from producers.choices import ProductStatus
from producers.enums import FeatureEnum

type1 = ShelfType.TYPE01
type2 = ShelfType.TYPE02


@pytest.mark.django_db
@pytest.mark.parametrize(
    'product_type, unique_color, expected',
    [
        (True, True, f'{type1.name}_{type1.colors.WHITE.name}'),
        (True, False, f'{type1.name}'),
        (False, False, 'all'),
    ],
)
def test_get_batch_group_name(
    product_type,
    unique_color,
    expected,
    manufactor,
    batch_connection_factory,
    product_factory,
):
    batch_connection = batch_connection_factory(
        manufactor=manufactor,
        product_type=product_type,
        unique_color=unique_color,
    )
    product = product_factory(
        order_item__order_item__shelf_type=type1.value,
        order_item__order_item__material=type1.colors.WHITE.value,
    )
    result = ComplaintBatching.get_batch_group_name(batch_connection, product)
    assert result == expected


@pytest.mark.django_db
def test_does_product_match_type_and_color(type_and_color_factory, product_factory):
    type_and_color = type_and_color_factory(type=type1.value, all_colors=True)
    product = product_factory(
        order_item__order_item__shelf_type=type1.value,
        order_item__order_item__material=type1.colors.WHITE.value,
    )
    result = type_and_color.does_type_and_color_match(
        product.shelf_type_option, product.color_option
    )
    assert result is True


@pytest.mark.django_db
def test_create_batches(
    manufactor,
    complaint_batching_setting,
    batch_connection_factory,
    batching_order_factory,
    type_and_color_factory,
    product_factory,
):

    batch_connection_factory(
        manufactor=manufactor,
        main_setting=complaint_batching_setting,
        product_type=True,
        unique_color=True,
    )
    ordering = batching_order_factory(
        main_setting=complaint_batching_setting,
        ordinal_number=1,
        manufactor=manufactor,
        original_product_producer=False,
        excluded_features=[FeatureEnum.DESK.value],
        is_active=True,
    )
    type_and_color = type_and_color_factory(type=type1.value, all_colors=True)
    ordering.type_and_colors.set([type_and_color])

    products_1 = product_factory.create_batch(
        status=ProductStatus.NEW,
        order_item__order_item__shelf_type=type1.value,
        order_item__order_item__material=type1.colors.WHITE.value,
        copy_of=product_factory(),
        size=2,
    )

    # created desk that should be excluded from batching
    product_factory(
        status=ProductStatus.NEW,
        order_item__order_item__shelf_type=type1.value,
        order_item__order_item__material=type1.colors.WHITE.value,
        copy_of=product_factory(),
        is_desk=True,
    )
    products_2 = product_factory.create_batch(
        status=ProductStatus.NEW,
        order_item__order_item__shelf_type=type1.value,
        order_item__order_item__material=type1.colors.RED.value,
        copy_of=product_factory(),
        size=2,
    )

    results = ComplaintBatching.get_grouped_products()
    expected = {
        ordering: {
            f'{type1.name}_{type1.colors.WHITE.name}': products_1,
            f'{type1.name}_{type1.colors.RED.name}': products_2,
        }
    }
    assert results == expected
