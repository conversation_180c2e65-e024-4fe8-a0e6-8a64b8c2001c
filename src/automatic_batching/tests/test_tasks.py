from datetime import (
    datetime,
    timedelta,
)
from unittest.mock import patch

import pytest

from freezegun import freeze_time

from automatic_batching.tasks import auto_create_complaint_batches


@pytest.mark.django_db
@freeze_time('2023-09-26 12:00:00')  # tuesday
@patch(
    'automatic_batching.services.complaint_batching.ComplaintBatching.create_batches'
)
def test_not_create_complaint_batches_if_already_run_in_that_day(
    create_batches_mock, complaint_batching_setting_factory
):
    complaint_batching_setting_factory(
        is_active=True,
        last_run=datetime.now() - timedelta(minutes=50),
    )
    auto_create_complaint_batches()
    create_batches_mock.assert_not_called()


@pytest.mark.django_db
@freeze_time('2023-09-26 12:00:00')  # tuesday = 1
@patch(
    'automatic_batching.services.complaint_batching.ComplaintBatching.create_batches'
)
def test_not_create_complaint_batches_in_wrong_day(
    create_batches_mock, complaint_batching_setting_factory
):
    complaint_batching_setting_factory(
        is_active=True,
        last_run=datetime.now() - timedelta(days=5),
        batching_days=[3, 5, 6],
    )
    auto_create_complaint_batches()
    create_batches_mock.assert_not_called()


@pytest.mark.django_db
@freeze_time('2023-09-26 12:00:00')  # tuesday = 1
@patch(
    'automatic_batching.services.complaint_batching.ComplaintBatching.create_batches'
)
def test_not_create_complaint_batches_in_wrong_time(
    create_batches_mock, complaint_batching_setting_factory
):
    complaint_batching_setting_factory(
        is_active=True,
        last_run=datetime.now() - timedelta(days=5),
        batching_days=[1, 5, 6],
        batching_time=datetime.now() + timedelta(minutes=30),
    )
    auto_create_complaint_batches()
    create_batches_mock.assert_not_called()


@pytest.mark.django_db
@freeze_time('2023-09-26 12:00:00')  # tuesday = 1
@patch(
    'automatic_batching.services.complaint_batching.ComplaintBatching.create_batches'
)
def test_create_complaint_batches(
    create_batches_mock, complaint_batching_setting_factory
):
    main_setting = complaint_batching_setting_factory(
        is_active=True,
        last_run=datetime.now() - timedelta(days=5),
        batching_days=[1, 5, 6],
        batching_time=datetime.now() - timedelta(minutes=30),
    )
    auto_create_complaint_batches()
    create_batches_mock.assert_called_once()
    main_setting.refresh_from_db()
    assert main_setting.last_run.date() == datetime.now().date()
