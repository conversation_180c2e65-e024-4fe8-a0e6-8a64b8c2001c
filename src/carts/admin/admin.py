from django.contrib import admin

from rangefilter.filters import DateRangeFilter

from carts.admin.actions import (
    export_carts_as_csv,
    recalculate_cart,
)
from carts.admin.filters import CartItemShelfTypeFilter
from carts.models import (
    Cart,
    CartItem,
)
from custom.admin_filters import renamed_filter
from orders.admin.admin import (
    item_inline_preview,
    items_list_preview,
)
from orders.admin.filters import (
    NoItemsFilter,
    UserProfileFilter,
)


class CartItemInline(admin.StackedInline):
    readonly_fields = (item_inline_preview,)
    fields = (
        item_inline_preview,
        'region',
        (
            'object_id',
            'content_type',
        ),
        (
            'quantity',
            'with_assembly',
        ),
        (
            'price',
            'price_net',
            'assembly_price',
            'delivery_price',
            'vat_amount',
        ),
        (
            'region_price',
            'region_price_net',
            'region_assembly_price',
            'region_delivery_price',
            'region_vat_amount',
            'region_promo_value',
            'region_delivery_promo_value',
            'recycle_tax_value',
        ),
    )

    model = CartItem
    extra = 0
    raw_id_fields = ('cart',)


class CartAdmin(admin.ModelAdmin):
    show_full_result_count = False

    inlines = [CartItemInline]
    fieldsets = (
        (
            'General information',
            {
                'fields': (
                    'owner',
                    'order',
                    'status',
                    'cart_source',
                    'region',
                    'promo_text',
                    'used_promo',
                    'assembly',
                    'user_email',
                    'profile_language',
                    'vat',
                    'invoice_country',
                ),
            },
        ),
        (
            'Price related',
            {
                'fields': (
                    'total_price',
                    'total_price_net',
                    'region_total_price',
                    'region_total_price_net',
                    'promo_amount',
                    'promo_amount_net',
                    'region_promo_amount',
                    'region_promo_amount_net',
                    'region_vat',
                    'vat_type',
                    'vat_rate',
                ),
            },
        ),
        (
            'Timestamps',
            {
                'fields': (
                    'created_at',
                    'updated_at',
                    'first_item_added_at',
                    'items_changed_at',
                    'price_updated_at',
                )
            },
        ),
    )
    readonly_fields = (
        'created_at',
        'updated_at',
        'user_email',
        'profile_language',
        'promo_text',
    )
    list_display = (
        'id',
        'owner',
        'user_email',
        'promo_text',
        'total_price',
        'region',
        'created_at',
        'updated_at',
        'profile_language',
        items_list_preview,
    )
    list_display_links = ('id',)
    list_filter = (
        ('created_at', DateRangeFilter),
        ('updated_at', DateRangeFilter),
        'created_at',
        'updated_at',
        'first_item_added_at',
        ('used_promo__origin', renamed_filter('Promo Code Origin')),
        NoItemsFilter,
        CartItemShelfTypeFilter,
        UserProfileFilter,
        'owner__profile__language',
    )
    raw_id_fields = (
        'owner',
        'used_promo',
        'order',
    )
    search_fields = (
        'id',
        'owner__username',
        'owner__email',
        'used_promo__code',
        'order__id',
    )
    actions = [recalculate_cart, export_carts_as_csv]
    list_select_related = ['owner', 'owner__profile']

    def get_queryset(self, request):
        queryset = super().get_queryset(request)
        queryset = queryset.prefetch_related('items__cart_item')
        return queryset

    @staticmethod
    def get_rangefilter_created_at_title(*args, **kwargs):
        return 'Created at'

    @staticmethod
    def get_rangefilter_updated_at_title(*args, **kwargs):
        return 'Updated at'

    @admin.display(description='Owner language')
    def profile_language(self, obj):
        return obj.owner.profile.language

    @admin.display(description='Owner email')
    def user_email(self, obj):
        return obj.owner.email or obj.owner.profile.email or ''


admin.site.register(Cart, CartAdmin)
