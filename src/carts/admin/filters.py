from django.contrib import admin
from django.db.models import (
    Case,
    Exists,
    OuterRef,
    When,
)

from custom.enums import (
    Furniture,
    ShelfType,
)
from gallery.models import (
    <PERSON><PERSON>,
    <PERSON><PERSON>,
    <PERSON><PERSON>,
)


class CartItemShelfTypeFilter(admin.SimpleListFilter):
    title = 'Shelf Type'
    parameter_name = 'shelf_type'

    def lookups(self, request, model_admin):
        return ShelfType.choices()

    def queryset(self, request, queryset):
        if not self.value():
            return queryset

        shelf_type = ShelfType(int(self.value()))
        conditions = []
        if shelf_type in ShelfType.get_jetty_shelf_types():
            jetties = Jetty.objects.filter(
                shelf_type=shelf_type, id=OuterRef('items__object_id')
            )
            conditions.append(
                When(
                    items__content_type__model=Furniture.jetty.value,
                    then=Exists(jetties),
                )
            )
        if shelf_type in ShelfType.get_sotty_shelf_types():
            # all sotties are SOFA_TYPE01 and shelf type is not a database field
            sotties = Sotty.objects.filter(id=OuterRef('items__object_id'))
            conditions.append(
                When(
                    items__content_type__model=Furniture.sotty.value,
                    then=Exists(sotties),
                )
            )
        if shelf_type in ShelfType.get_watty_shelf_types():
            watties = Watty.objects.filter(
                shelf_type=shelf_type, id=OuterRef('items__object_id')
            )
            conditions.append(
                When(
                    items__content_type__model=Furniture.watty.value,
                    then=Exists(watties),
                )
            )

        return queryset.annotate(
            has_item_with_given_shelf_type=Case(*conditions, default=False)
        ).filter(has_item_with_given_shelf_type=True)
