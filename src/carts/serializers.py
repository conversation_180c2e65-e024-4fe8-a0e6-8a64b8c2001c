from decimal import (
    ROUND_HALF_UP,
    Decimal,
)
from functools import lru_cache
from operator import itemgetter

from django.db.models import Sum
from django.db.models.functions import Coalesce

from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from abtests.constants import EXTENDED_DELIVERY_TEST_NAME
from carts.models import (
    Cart,
    CartItem,
)
from carts.services.cart_service import CartService
from custom.enums import Furniture
from custom.fields import (
    AttributeNotNullBooleanField,
    CurrencyNumberField,
)
from custom.serializers import CamelizeSelectedKeysMixin
from custom.views import is_ab_test_enabled
from dynamic_delivery.serializers import ProductionTimeSerializer
from ecommerce_api.serializers import BaseSerializerContext
from gallery.enums import SellableItemContentTypes
from gallery.models import SellableItemAbstract
from gallery.serializers.furniture.cart import (
    JettyCartSerializer,
    SampleBoxCartSerializer,
    SottyCartSerializer,
    WattyCartSerializer,
)
from gallery.serializers.furniture.mailing import MailingSellableItemSerializer
from gallery.services.prices_for_serializers import (
    RegionCurrencySerializerMixin,
    get_region_price_in_euro,
    get_region_price_with_discount,
    get_region_price_with_discount_in_euro,
)
from pricing_v3.models import SamplePriceSettings
from pricing_v3.omnibus import OmnibusCalculator
from pricing_v3.serializers import CartPricingSerializer
from promotions.models import Promotion
from regions.utils import is_t03_available
from user_profile.choices import UserType


class AssemblySerializer(serializers.Serializer):
    assembly = serializers.BooleanField(default=False)


class OldSofaCollectionSerializer(serializers.Serializer):
    old_sofa_collection = serializers.BooleanField(default=False)


class CartItemSerializer(
    CamelizeSelectedKeysMixin,
    RegionCurrencySerializerMixin,
    BaseSerializerContext,
    serializers.ModelSerializer,
):
    sellable_item = serializers.SerializerMethodField()
    content_type = serializers.CharField(source='content_type.model')
    item_id = serializers.IntegerField(source='object_id')
    assembly_enabled = serializers.BooleanField(
        source='with_assembly',
        help_text='Did client choose the assembly service or is it required',
    )
    assembly_required = serializers.SerializerMethodField()
    assembly_price = serializers.DecimalField(
        coerce_to_string=False, decimal_places=2, max_digits=12, read_only=True
    )
    region_assembly_price = serializers.DecimalField(
        coerce_to_string=False, decimal_places=2, max_digits=12, read_only=True
    )
    delivery_price = CurrencyNumberField(
        source='delivery_price_without_discount',
    )
    region_delivery_price = serializers.SerializerMethodField()
    region_delivery_price_with_discount = serializers.SerializerMethodField()
    delivery_promo = serializers.SerializerMethodField()
    region_price = serializers.SerializerMethodField()
    region_price_with_discount = serializers.SerializerMethodField()
    region_delivery_promo_price = serializers.SerializerMethodField()
    region_price_in_euro = serializers.SerializerMethodField()
    region_price_with_discount_in_euro = serializers.SerializerMethodField()
    currency_code = serializers.SerializerMethodField()
    promotion = serializers.SerializerMethodField()
    is_strikethrough_promo = serializers.SerializerMethodField()

    # TODO: probably redundant
    item_price_regionalized = serializers.SerializerMethodField()
    item_price_without_discount = serializers.SerializerMethodField()
    item_price_regionalized_number = serializers.SerializerMethodField()
    item_price_without_discount_regionalized_number = CurrencyNumberField(
        source='aggregate_region_price',
    )

    price_netto = CurrencyNumberField(source='price_net')
    price_without_discount = CurrencyNumberField(
        source='get_base_price_without_discount'
    )
    price_netto_without_discount = CurrencyNumberField(
        source='get_base_price_net_without_discount'
    )
    price_with_discount = serializers.SerializerMethodField()
    omnibus_price = serializers.SerializerMethodField()
    item_revenue_brutto = CurrencyNumberField(source='price')

    class Meta:
        model = CartItem
        fields = (
            'id',
            'quantity',
            'assembly_enabled',
            'assembly_required',
            'assembly_price',
            'region_assembly_price',
            'region_delivery_price',
            'sellable_item',
            'content_type',
            'delivery_price',
            'region_delivery_price_with_discount',
            'delivery_promo',
            'region_delivery_promo_price',
            'item_id',
            'region_price',
            'region_price_with_discount',
            'region_price_in_euro',
            'region_price_with_discount_in_euro',
            'currency_code',
            'promotion',
            'item_price_regionalized',
            'item_price_without_discount',
            'is_strikethrough_promo',
            'item_price_regionalized_number',
            'item_price_without_discount_regionalized_number',
            'price_netto',
            'price_without_discount',
            'price_netto_without_discount',
            'price_with_discount',
            'omnibus_price',
            'item_revenue_brutto',
        )
        read_only_fields = (
            'assembly_enabled',
            'assembly_price',
            'region_assembly_price',
            'content_type',
            'item_id',
            'item_price_without_discount_regionalized_number',
            'price_netto',
            'price_without_discount',
            'price_netto_without_discount',
            'item_revenue_brutto',
        )

    CAMEL_CASE_FIELDS = (
        'item_id',
        'assembly_enabled',
        'assembly_required',
        'assembly_price',
        'region_assembly_price',
        'region_delivery_price',
        'delivery_price',
        'delivery_promo',
        'region_delivery_promo_price',
        'region_delivery_price_with_discount',
    )

    @property
    def active_promotion(self) -> Promotion | None:
        return self.context.get('active_promotion', None)

    def to_representation(self, instance: CartItem) -> dict:
        data = super().to_representation(instance)

        sellable_item_serialization = data.pop('sellable_item', {})
        data.update(sellable_item_serialization)
        return data

    def get_sellable_item(self, obj: CartItem) -> dict:
        content_type_serializer_mapping = {
            'jetty': JettyCartSerializer,
            'sotty': SottyCartSerializer,
            'watty': WattyCartSerializer,
            'samplebox': SampleBoxCartSerializer,
        }
        serializer = content_type_serializer_mapping[obj.content_type.model]
        return serializer(obj.sellable_item, context=self.context).data

    def get_assembly_required(self, obj: CartItem) -> bool:
        return obj.sellable_item.is_assembly_service_required

    def get_region_price(self, obj: CartItem) -> Decimal:
        return obj.sellable_item.get_regionalized_price(
            region=self.region,
            region_calculations_object=self.region_calculations_object,
        )

    def get_region_price_with_discount(self, obj: CartItem) -> Decimal:
        return get_region_price_with_discount(
            furniture=obj.sellable_item,
            region=self.region,
            region_calculations_object=self.region_calculations_object,
            promotion=self.active_promotion,
        )

    def get_region_price_in_euro(self, obj: CartItem) -> Decimal:
        return get_region_price_in_euro(
            furniture=obj.sellable_item,
            currency_rate=self.currency_rate,
            region=self.region,
            region_calculations_object=self.region_calculations_object,
        )

    def get_region_price_with_discount_in_euro(self, obj: CartItem) -> Decimal:
        return get_region_price_with_discount_in_euro(
            furniture=obj.sellable_item,
            currency_rate=self.currency_rate,
            region=self.region,
            region_calculations_object=self.region_calculations_object,
            promotion=self.active_promotion,
        )

    def get_region_delivery_price_with_discount(self, obj: CartItem) -> Decimal:
        return (
            obj.aggregate_region_delivery_price
            - obj.aggregate_region_delivery_promo_value
        )

    def get_region_delivery_price(self, obj: CartItem) -> Decimal:
        return self.region_calculations_object.calculate_regionalized(
            base_value=obj.aggregate_delivery_price_without_discount
        )

    def get_currency_code(self, _) -> str:
        return self.region.currency_code

    def get_promotion(self, obj: CartItem) -> int | None:
        if self._does_voucher_apply(obj.sellable_item):
            voucher = self.context['strikethrough_voucher']
            return voucher.get_discount_value_for_item(obj.sellable_item)

    def get_delivery_promo(self, obj: CartItem) -> bool:
        # for items with paid delivery if promo for delivery is on
        if (
            obj.aggregate_delivery_price_without_discount > 0
            and obj.parent.used_promo
            and obj.parent.used_promo.delivery_discounts.exists()
        ):
            return True
        return False

    def get_region_delivery_promo_price(self, obj: CartItem) -> Decimal:
        if obj.parent.used_promo and obj.parent.used_promo.delivery_discounts.exists():
            return (
                obj.aggregate_region_delivery_price
                - obj.aggregate_region_delivery_promo_value
            )
        return self.region_calculations_object.calculate_regionalized(
            base_value=obj.aggregate_delivery_price_without_discount
        )

    def get_item_price_regionalized(self, obj: CartItem) -> str:
        return obj.parent.display_regionalized(
            self.get_item_price_regionalized_number(obj)
        )

    def get_item_price_regionalized_number(self, obj: CartItem) -> Decimal:
        if self._does_voucher_apply(obj.sellable_item):
            voucher = self.context['strikethrough_voucher']
            return round(
                voucher.calculate_price_for_furniture(
                    obj.sellable_item, obj.aggregate_region_price
                )
            )

        return obj.aggregate_region_price

    def get_price_with_discount(self, obj: CartItem) -> Decimal:
        price = obj.sellable_item.get_sale_price(
            regular_price=Decimal(obj.sellable_item.price),
        )
        return price.quantize(Decimal('1.'), rounding=ROUND_HALF_UP)

    def get_item_price_without_discount(self, obj: CartItem) -> str:
        return obj.sellable_item.get_regionalized_price_display(
            self.region, self.region_calculations_object
        )

    def get_is_strikethrough_promo(self, obj: CartItem) -> bool:
        return self._is_strikethrough_promo(obj)

    def get_omnibus_price(self, obj: CartItem) -> Decimal | str:
        if (
            obj.is_service
            or obj.sellable_item.furniture_type == Furniture.sample_box.value
            or not self._is_strikethrough_promo(obj)
            or not self.region
        ):
            return ''

        price_calculator = OmnibusCalculator.get_instance(self.region)
        return str(price_calculator.calculate_lowest_price(geometry=obj.sellable_item))

    @lru_cache
    def _is_strikethrough_promo(self, obj: CartItem) -> bool:
        sellable_item = obj.sellable_item
        if obj.is_service:
            return False

        elif sellable_item.furniture_type != Furniture.sample_box.value:
            return self._does_voucher_apply(sellable_item=sellable_item)

        elif (
            hasattr(sellable_item, 'box_variant')
            and sellable_item.box_variant.is_sofa_sample
        ):
            return SamplePriceSettings.is_sofa_sample_promo_active()
        return SamplePriceSettings.is_storage_sample_promo_active()

    @lru_cache
    def _does_voucher_apply(self, sellable_item: SellableItemAbstract) -> bool:
        strikethrough_voucher = self.context['strikethrough_voucher']
        has_strikethrough_promo_applied = self.context[
            'has_strikethrough_promo_applied'
        ]

        if not strikethrough_voucher or not has_strikethrough_promo_applied:
            return False

        if strikethrough_voucher.is_geometry_affected(sellable_item):
            return True


class CartSerializer(
    BaseSerializerContext,
    CamelizeSelectedKeysMixin,
    RegionCurrencySerializerMixin,
    serializers.ModelSerializer,
):
    cart_items = CartItemSerializer(many=True, source='material_items')
    region_name = serializers.CharField(source='region.name')
    cart_items_count = serializers.SerializerMethodField()
    cart_used_promo = AttributeNotNullBooleanField(source='used_promo')
    cart_used_assembly = serializers.BooleanField(source='assembly', default=False)
    promo_code_name = serializers.CharField(source='promo_text', default='')
    has_assembly_possible = serializers.BooleanField(source='possible_shelf_assembly')
    order_pricing = CartPricingSerializer(source='*')
    has_lighting = serializers.SerializerMethodField()
    library_items = serializers.SerializerMethodField()
    signed_in = serializers.SerializerMethodField()
    sale_enabled = serializers.SerializerMethodField()
    shelf_items_count = serializers.SerializerMethodField()
    has_t03 = serializers.SerializerMethodField()
    has_t03_samples = serializers.SerializerMethodField()
    cart_ribbon_enabled = serializers.SerializerMethodField()
    t03_available = serializers.SerializerMethodField()

    class Meta:
        model = Cart
        fields = (
            'cart_items',
            'region_name',
            'cart_items_count',
            'cart_used_promo',
            'cart_used_assembly',
            'promo_code_name',
            'has_assembly_possible',
            'has_old_sofa_collection',
            'order_pricing',
            'has_lighting',
            'library_items',
            'signed_in',
            'sale_enabled',
            'shelf_items_count',
            'has_t03',
            'has_t03_samples',
            'cart_ribbon_enabled',
            't03_available',
        )
        read_only_fields = (
            'region_name',
            'cart_items_count',
            'cart_used_promo',
            'cart_used_assembly',
            'promo_code_name',
            'has_assembly_possible',
            'order_pricing',
        )

    CAMEL_CASE_FIELDS = (
        'cart_items',
        'cart_items_count',
        'cart_used_promo',
        'promo_code_name',
        'has_assembly_possible',
        'has_old_sofa_collection',
        'signed_in',
        'sale_enabled',
        'shelf_items_count',
        'has_t03',
        'has_t03_samples',
        'cart_ribbon_enabled',
        't03_available',
    )

    def _get_extended_delivery_context(self) -> bool:
        try:
            return is_ab_test_enabled(
                request=self.request, codename=EXTENDED_DELIVERY_TEST_NAME
            )
        except AttributeError:
            return False

    def get_cart_items_count(self, obj: Cart) -> int:
        service = CartService(cart=obj)
        return service.get_cart_size()

    @staticmethod
    def get_has_lighting(obj: Cart) -> bool:
        return any(
            item.sellable_item.lighting
            for item in obj.items.all()
            if item.content_type.model == SellableItemContentTypes.WATTY
        )

    @staticmethod
    def get_has_old_sofa_collection(obj: Cart) -> bool:
        return obj.has_old_sofa_collection

    @staticmethod
    def get_library_items(obj: Cart) -> int:
        return obj.owner.profile.get_library_item_number()

    @staticmethod
    def get_signed_in(obj: Cart) -> bool:
        return obj.owner.profile.user_type == UserType.CUSTOMER

    def get_sale_enabled(self, _) -> bool:
        return bool(self.context.get('active_promotion_config', False))

    @staticmethod
    def get_shelf_items_count(obj: Cart) -> int:
        return obj.items.filter(
            content_type__model__in=SellableItemContentTypes.get_furniture_choices(),
            free_assembly_service=False,
        ).aggregate(total_quantity=Coalesce(Sum('quantity'), 0))['total_quantity']

    @staticmethod
    def get_has_t03(obj: Cart) -> bool:
        return any(
            item.sellable_item.is_t03_wardrobe
            for item in obj.items.all()
            if not item.is_service
        )

    @staticmethod
    def get_has_t03_samples(obj: Cart) -> bool:
        return any(
            item.sellable_item.box_variant.is_type_03_variant
            for item in obj.items.all()
            if not item.is_service
            and item.sellable_item.furniture_type == Furniture.sample_box
        )

    def get_cart_ribbon_enabled(self, _) -> bool:
        if promotion_config := self.context.get('active_promotion_config'):
            return promotion_config.cart_ribbon_enabled

    def to_representation(self, instance):
        rep = super().to_representation(instance)
        rep['deliveryTime'] = self._get_delivery_time(rep)
        return rep

    def _get_delivery_time(self, rep) -> str:
        """
        To avoid duplicate queries, determine the delivery time for the entire cart,
        based on the maximum delivery time among all items.
        """
        cart_items = rep.get('cartItems', [])
        if not cart_items:
            min_delivery, max_delivery = 0, 0
        else:
            max_delivery = max(
                cart_items, key=itemgetter('delivery_time'), default={}
            ).get('delivery_time', 0)
            min_delivery = max(max_delivery - 1, 0)

        delivery_range = {'min': min_delivery, 'max': max_delivery}
        serializer = ProductionTimeSerializer(
            delivery_range, context={'use_extended_delivery': False}
        )

        return serializer.get_translations_filled().get('text', '')

    @staticmethod
    def get_t03_available(obj: Cart) -> bool:
        return is_t03_available(obj.owner)


class EmptyCartSerializer(CartSerializer):
    cart_items = serializers.ListSerializer(child=serializers.CharField(), default=[])
    region_name = serializers.SerializerMethodField()
    cart_items_count = serializers.IntegerField(default=0)
    cart_used_promo = AttributeNotNullBooleanField(default=False)
    cart_used_assembly = serializers.BooleanField(default=False)
    promo_code_name = serializers.CharField(default='')
    has_assembly_possible = serializers.BooleanField(default=False)
    has_lighting = serializers.BooleanField(default=False)
    library_items = serializers.IntegerField(default=0)
    signed_in = serializers.BooleanField(default=False)
    sale_enabled = serializers.SerializerMethodField()
    shelf_items_count = serializers.IntegerField(default=0)
    has_t03 = serializers.BooleanField(default=False)
    has_t03_samples = serializers.BooleanField(default=False)
    t03_available = serializers.SerializerMethodField()

    class Meta:
        model = Cart
        fields = CartSerializer.Meta.fields

    def get_region_name(self, _) -> str:
        return self.region.name

    def get_t03_available(self, _) -> bool | None:
        if self.request:
            return is_t03_available(self.request.user)


class CartItemIdentifierSerializer(serializers.Serializer):
    sellable_item_id = serializers.IntegerField()
    content_type = serializers.ChoiceField(
        choices=SellableItemContentTypes.choices,
    )


class CreateCartItemSerializer(CartItemIdentifierSerializer):
    quantity = serializers.IntegerField(default=1)


class CartItemAsssemblySerializer(serializers.ModelSerializer):
    assembly_enabled = serializers.BooleanField(source='with_assembly')

    class Meta:
        model = CartItem
        fields = (
            'id',
            'assembly_enabled',
        )

    def validate_assembly_enabled(self, value: bool) -> bool:
        if not value and self.instance.sellable_item.is_assembly_service_required:
            raise ValidationError(
                f'Cart Item [id={self.instance.id}] requires assembly service'
            )
        return value


class MailingCartItemSerializer(serializers.ModelSerializer):
    sellable_item = MailingSellableItemSerializer()
    region_price_display = serializers.SerializerMethodField()

    class Meta:
        model = CartItem
        fields = (
            'sellable_item',
            'region_price_display',
        )

    def to_representation(self, instance: CartItem) -> dict:
        data = super().to_representation(instance)

        sellable_item_serialization = data.pop('sellable_item', {})
        data.update(sellable_item_serialization)
        return data

    @staticmethod
    def get_region_price_display(obj) -> str:
        return obj.parent.display_regionalized(obj.region_price)


class MailingCartSerializer(serializers.ModelSerializer):
    items = MailingCartItemSerializer(many=True)
    items_count = serializers.IntegerField(source='aggregate_items_count')
    total_price_display = serializers.CharField(source='get_total_price')

    class Meta:
        model = Cart
        fields = (
            'items',
            'items_count',
            'total_price_display',
        )
