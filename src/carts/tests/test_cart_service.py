from decimal import (
    ROUND_HALF_UP,
    Decimal,
)
from typing import Union
from unittest import mock
from unittest.mock import PropertyMock

from django.contrib.auth.models import User
from django.utils import timezone

import pytest

from freezegun import freeze_time
from pytest_cases import (
    case,
    parametrize_with_cases,
)

from carts.choices import CartStatusChoices
from carts.exceptions import (
    InactiveCartException,
    MinCartItemSizeError,
)
from carts.models import (
    Cart,
    CartItem,
)
from carts.services.cart_service import CartService
from gallery.constants import (
    ONE_MODULE_OR_LESS,
    TWO_MODULES,
)
from gallery.models import (
    Jetty,
    SampleBox,
    Sotty,
    Watty,
)
from orders.choices import OrderSource
from orders.enums import OrderStatus
from orders.models import (
    Order,
    OrderItem,
)
from regions.mixins import RegionCalculationsObject
from services.constants import OLD_SOFA_COLLECTION_PRICE_MAPPER
from vouchers.enums import (
    ServiceType,
    VoucherType,
)


class TestCartServiceCases:
    @case(tags=['test_has_products'])
    def case_has_limited_products_in_cart(self, cart_factory, cart_item_factory):
        cart = cart_factory(
            items=[
                cart_item_factory(is_sofa_corduroy=True),
                cart_item_factory(is_tone_wardrobe=True),
            ],
        )
        return cart, True

    @case(tags=['test_has_products'])
    def case_doesnt_have_limited_products_in_cart(
        self, cart_factory, cart_item_factory
    ):
        cart = cart_factory(
            items=[
                cart_item_factory(is_jetty=True),
                cart_item_factory(is_edge_wardrobe=True),
            ],
        )
        return cart, False


@pytest.mark.django_db
class TestCartService:
    def test_create_cart_for_user(self, user):
        assert user.carts.count() == 0

        cart = CartService.create_cart(user)

        assert user.carts.count() == 1
        assert cart.owner == user

    def test_retrieve_existing_cart(self, user, cart_factory):
        cart_factory(owner=user)
        assert user.carts.count() == 1

        cart = CartService.get_cart(user)

        assert cart == cart
        assert user.carts.count() == 1

    @pytest.mark.parametrize(
        'sellable_item_fixture_name',
        ['jetty_factory', 'sotty_factory', 'watty_factory', 'sample_box_factory'],
    )
    def test_add_to_cart_active(
        self,
        sellable_item_fixture_name,
        request,
        region_de,
    ):
        sellable_item = request.getfixturevalue(sellable_item_fixture_name)(
            owner__profile__region=region_de,
        )

        cart = CartService.create_cart(sellable_item.owner)
        cart.status = CartStatusChoices.ACTIVE

        CartService(cart=cart).add_to_cart(sellable_item)
        cart_item = cart.items.last()

        assert cart_item.sellable_item == sellable_item

    @pytest.mark.parametrize(
        'sellable_item_fixture_name',
        ['jetty_factory', 'sotty_factory', 'watty_factory', 'sample_box_factory'],
    )
    def test_add_to_cart(
        self,
        sellable_item_fixture_name,
        request,
        region_de,
    ):
        sellable_item = request.getfixturevalue(sellable_item_fixture_name)(
            owner__profile__region=region_de,
        )
        cart = CartService.create_cart(sellable_item.owner)
        CartService(cart=cart).add_to_cart(sellable_item)
        assert not cart.is_empty
        assert cart.owner == sellable_item.owner
        cart_item = cart.items.last()
        assert cart_item.sellable_item == sellable_item

    @pytest.mark.parametrize(
        'sellable_item_fixture_name',
        ['jetty_factory', 'sotty_factory', 'watty_factory', 'sample_box_factory'],
    )
    def test_add_to_cart_status_pending(
        self,
        sellable_item_fixture_name,
        request,
        region_de,
    ):
        sellable_item = request.getfixturevalue(sellable_item_fixture_name)(
            owner__profile__region=region_de,
        )
        cart = CartService.create_cart(sellable_item.owner)
        cart.status = CartStatusChoices.PAYMENT_PENDING
        with pytest.raises(InactiveCartException):
            CartService(cart=cart).add_to_cart(sellable_item)

    @pytest.mark.parametrize(
        'sellable_item_fixture_name',
        ['jetty_factory', 'sotty_factory', 'watty_factory', 'sample_box_factory'],
    )
    def test_add_to_cart_status_ordered(
        self,
        sellable_item_fixture_name,
        request,
        region_de,
    ):
        sellable_item = request.getfixturevalue(sellable_item_fixture_name)(
            owner__profile__region=region_de,
        )

        cart = CartService.create_cart(sellable_item.owner)
        cart.status = CartStatusChoices.ORDERED
        with pytest.raises(InactiveCartException):
            CartService(cart=cart).add_to_cart(sellable_item)

    @pytest.mark.parametrize(
        'sellable_item_fixture_name',
        ['jetty_factory', 'sotty_factory', 'watty_factory', 'sample_box_factory'],
    )
    def test_add_to_cart_first_item_added_at(
        self,
        sellable_item_fixture_name,
        request,
        region_de,
    ):
        sellable_item = request.getfixturevalue(sellable_item_fixture_name)(
            owner__profile__region=region_de,
        )
        cart = CartService.create_cart(sellable_item.owner)
        service = CartService(cart=cart)
        now = timezone.now()
        with freeze_time(now):
            service.add_to_cart(sellable_item)

        assert cart.first_item_added_at == now

        service.add_to_cart(sellable_item)
        assert cart.first_item_added_at == now

    @pytest.mark.parametrize(
        'sellable_item_factory_name',
        ['jetty_factory', 'sotty_factory', 'watty_factory', 'sample_box_factory'],
    )
    def test_add_to_cart_update_for_strikethrough_promo(
        self,
        sellable_item_factory_name,
        global_strikethrough_promo_30,
        request,
        region_de,
    ):
        global_strikethrough_promo_30.configs.last().enabled_regions.add(region_de)

        sellable_item = request.getfixturevalue(sellable_item_factory_name)(
            owner__profile__region=region_de,
        )

        cart = CartService.create_cart(sellable_item.owner)
        CartService(cart=cart).add_to_cart(sellable_item)

        cart.refresh_from_db()
        assert cart.promo_text == global_strikethrough_promo_30.promo_code.code
        assert cart.used_promo == global_strikethrough_promo_30.promo_code

    @pytest.mark.parametrize(
        'sellable_item_factory_name',
        ['jetty_factory', 'sotty_factory', 'watty_factory', 'sample_box_factory'],
    )
    def test_add_to_cart_recalculates_cart(
        self,
        sellable_item_factory_name,
        request,
        cart_factory,
        region_de,
    ):

        sellable_item = request.getfixturevalue(sellable_item_factory_name)(
            owner__profile__region=region_de,
        )
        cart = cart_factory(owner=sellable_item.owner)
        CartService.recalculate_cart(cart)
        total_price_before = cart.total_price
        region_total_price_before = cart.region_total_price

        CartService(cart=cart).add_to_cart(sellable_item)

        cart.refresh_from_db()
        assert cart.total_price != total_price_before
        assert cart.region_total_price != region_total_price_before

    @pytest.mark.parametrize(
        'sellable_item_factory_name',
        ['jetty_factory', 'sotty_factory', 'watty_factory', 'sample_box_factory'],
    )
    def test_add_to_cart_updated_analytics_fields(
        self,
        sellable_item_factory_name,
        request,
        region_de,
    ):
        sellable_item = request.getfixturevalue(sellable_item_factory_name)(
            owner__profile__region=region_de,
        )
        cart = CartService.create_cart(sellable_item.owner)
        service = CartService(cart=cart)
        now = timezone.now()
        with freeze_time(now):
            service.add_to_cart(sellable_item)

        cart.refresh_from_db()
        assert service.cart.items_changed_at == now
        assert service.cart.price_updated_at == now

        service.add_to_cart(sellable_item)
        cart.refresh_from_db()

        assert cart.items_changed_at != now
        assert cart.price_updated_at != now

    def test_add_to_cart_quantity(self, user_factory, jetty_factory, cart_factory):
        user: User = user_factory()
        sellable_item = jetty_factory(owner=user)
        cart = cart_factory(owner=user, items=[])

        CartService(cart=cart).add_to_cart(sellable_item, quantity=69)

        cart_item = cart.items.first()
        assert cart_item.quantity == 69
        assert cart_item.cart_item == sellable_item

    @pytest.mark.parametrize(
        ['sellable_item_fixture_name', 'content_type', 'model'],
        [
            ['jetty_factory', 'jetty', Jetty],
            ['sotty_factory', 'sotty', Sotty],
            ['watty_factory', 'watty', Watty],
            ['sample_box_factory', 'samplebox', SampleBox],
        ],
    )
    def test_delete_from_cart_various_items(
        self,
        sellable_item_fixture_name,
        content_type,
        model,
        request,
        cart_factory,
        cart_item_factory,
        region_de,
    ):
        sellable_item = request.getfixturevalue(sellable_item_fixture_name)(
            owner__profile__region=region_de,
        )
        cart = cart_factory(items=[], owner=sellable_item.owner)
        cart_item = cart_item_factory(
            cart=cart,
            cart_item=sellable_item,
        )

        service = CartService(cart=cart)
        service.delete_from_cart(sellable_item.id, content_type)

        assert not cart.items.exists()
        assert CartItem.objects.filter(id=cart_item.id).count() == 0
        assert model.objects.filter(id=sellable_item.id).count() == 0

        # cart item should be soft deleted
        assert CartItem.all_objects.filter(id=cart_item.id).count() == 1

    @pytest.mark.parametrize(
        ['sellable_item_fixture_name', 'content_type'],
        [
            ['jetty_factory', 'jetty'],
            ['sotty_factory', 'sotty'],
            ['watty_factory', 'watty'],
            ['sample_box_factory', 'samplebox'],
        ],
    )
    def test_delete_from_cart_updated_analytics_fields(
        self,
        sellable_item_fixture_name,
        content_type,
        cart_factory,
        cart_item_factory,
        request,
        region_de,
    ):
        sellable_item = request.getfixturevalue(sellable_item_fixture_name)(
            owner__profile__region=region_de,
        )
        cart = cart_factory(items=[], owner=sellable_item.owner)
        cart_item_factory(
            cart=cart,
            cart_item=sellable_item,
        )

        service = CartService(cart=cart)
        now = timezone.now()
        with freeze_time(now):
            service.delete_from_cart(sellable_item.id, content_type)

        cart.refresh_from_db()
        assert cart.items_changed_at == now
        assert cart.price_updated_at == now

    def test_convert_to_order(self, cart_factory, user):
        cart = cart_factory(owner=user)
        service = CartService(cart=cart)

        order = service.sync_with_order()

        assert isinstance(order, Order)
        assert order.cart == cart
        assert order.owner == user
        assert order.region == cart.region
        assert order.first_item_added_at == cart.first_item_added_at
        assert order.items_changed_at == cart.items_changed_at
        assert order.price_updated_at == cart.price_updated_at
        assert order.total_price == cart.total_price
        assert order.total_price_net == cart.total_price_net
        assert order.region_total_price == cart.region_total_price
        assert order.region_total_price_net == cart.region_total_price_net
        assert order.promo_amount == cart.promo_amount
        assert order.promo_amount_net == cart.promo_amount_net
        assert order.region_promo_amount == cart.region_promo_amount
        assert order.region_promo_amount_net == cart.region_promo_amount_net
        assert order.assembly == cart.assembly
        assert order.region_vat == cart.region_vat
        assert order.vat_type == cart.vat_type
        assert order.region == cart.region
        assert order.used_promo == cart.used_promo
        assert order.promo_text == cart.promo_text

    def test_convert_to_order_creates_order_items(
        self,
        cart_factory,
        user,
        cart_item_factory,
    ):
        cart = cart_factory(owner=user, items=[], order__items=[])
        cart_item_factory(is_jetty=True, cart=cart)
        cart_item_factory(is_watty=True, cart=cart)
        cart_item_factory(is_sample_box=True, cart=cart)

        assert cart.items.count() == 3
        assert cart.order.items.count() == 0

        service = CartService(cart=cart)
        order = service.sync_with_order(source=OrderSource.WEB_DESKTOP)

        assert cart.items.count() == order.items.count() == 3

        for cart_item in cart.items.prefetch_related('cart_item'):
            order_item = order.items.get(
                object_id=cart_item.object_id,
                content_type__model=cart_item.content_type.model,
            )

            assert order_item.order == order
            assert order_item.order_item == cart_item.cart_item
            assert order_item.price == cart_item.price
            assert order_item.price_net == cart_item.price_net
            assert order_item.region_price == cart_item.region_price
            assert order_item.region_price_net == cart_item.region_price_net
            assert order_item.assembly_price == cart_item.assembly_price
            assert order_item.region_assembly_price == cart_item.region_assembly_price
            assert order_item.delivery_price == cart_item.delivery_price
            assert order_item.region_delivery_price == cart_item.region_delivery_price
            assert order_item.vat_amount == cart_item.vat_amount
            assert order_item.region_vat_amount == cart_item.region_vat_amount
            assert order_item.recycle_tax_value == cart_item.recycle_tax_value
            assert order_item.free_assembly_service == cart_item.free_assembly_service
            assert order_item.region == cart_item.region

    def test_handle_paid_order(self, order_factory, cart_factory, user):
        cart = cart_factory(owner=user, status=CartStatusChoices.ACTIVE)
        order = order_factory(owner=user, cart=cart, status=OrderStatus.IN_PRODUCTION)

        CartService(cart=cart).handle_paid_order()

        order.refresh_from_db()
        cart.refresh_from_db()

        deleted_cart = Cart.all_objects.get(id=cart.id)
        assert deleted_cart.deleted
        assert deleted_cart.status == CartStatusChoices.ORDERED
        assert user.carts.last().id != cart.id
        assert user.carts.last().status == CartStatusChoices.ACTIVE

    def test_handle_paid_order_moves_unpaid_items_to_new_cart(
        self,
        order_factory,
        cart_factory,
        user,
        cart_item_factory,
        order_item_factory,
        jetty_factory,
        jetty,
    ):
        cart = cart_factory(owner=user, status=CartStatusChoices.ACTIVE, items=[])
        order = order_factory(
            owner=user,
            cart=cart,
            status=OrderStatus.IN_PRODUCTION,
            items=[],
        )
        cart_item_factory(cart=cart, cart_item=jetty)
        unpaid_cart_item = cart_item_factory(cart=cart, cart_item=jetty_factory())
        order_item_factory(order=order, order_item=jetty)

        CartService(cart=cart).handle_paid_order()

        cart.refresh_from_db()
        new_cart = user.carts.last()

        assert new_cart.items.count() == 1
        assert unpaid_cart_item in new_cart.items.all()
        assert new_cart.id != cart.id

    def test__sync_items(self, cart_factory, cart_item_factory):
        cart = cart_factory(items=[])
        cart_item = cart_item_factory(cart=cart)
        service = CartService(cart=cart)

        service._sync_items(order=cart.order)

        order_item = cart.order.items.first()
        assert order_item.price == cart_item.price
        assert order_item.price_net == cart_item.price_net
        assert order_item.region_price == cart_item.region_price
        assert order_item.region_price_net == cart_item.region_price_net
        assert order_item.assembly_price == cart_item.assembly_price
        assert order_item.region_assembly_price == cart_item.region_assembly_price
        assert order_item.delivery_price == cart_item.delivery_price
        assert order_item.region_delivery_price == cart_item.region_delivery_price
        assert order_item.vat_amount == cart_item.vat_amount
        assert order_item.region_vat_amount == cart_item.region_vat_amount
        assert order_item.region_promo_value == cart_item.region_promo_value
        assert order_item.recycle_tax_value == cart_item.recycle_tax_value
        assert order_item.free_assembly_service == cart_item.free_assembly_service
        assert order_item.region == cart_item.region
        assert order_item.quantity == cart_item.quantity
        assert order_item.with_assembly == cart_item.with_assembly

    @parametrize_with_cases(
        ('cart', 'expected_boolean'),
        cases=TestCartServiceCases,
        has_tag='test_has_products',
    )
    def test_has_products(self, cart, expected_boolean):
        service = CartService(cart=cart)

        assert service.has_corduroy is expected_boolean
        assert service.has_s01 is expected_boolean
        assert service.has_t03 is expected_boolean

    def test_increase_item_quantity_active(self, cart_item):
        cart = cart_item.cart
        cart.status = CartStatusChoices.ACTIVE
        CartService(cart=cart).increase_item_quantity(cart_item)
        assert cart_item.quantity == 2

    def test_increase_item_quantity_draft(self, cart_item):
        cart = cart_item.cart
        cart.status = CartStatusChoices.DRAFT
        CartService(cart=cart).increase_item_quantity(cart_item)
        assert cart_item.quantity == 2

    def test_increase_item_quantity_pending(self, cart_item):
        cart = cart_item.cart
        cart.status = CartStatusChoices.PAYMENT_PENDING
        with pytest.raises(InactiveCartException):
            CartService(cart=cart).increase_item_quantity(cart_item)

    def test_increase_item_quantity_ordered(self, cart_item):
        cart = cart_item.cart
        cart.status = CartStatusChoices.ORDERED
        with pytest.raises(InactiveCartException):
            CartService(cart=cart).increase_item_quantity(cart_item)

    def test_decrease_item_quantity_active(self, cart_item):
        cart = cart_item.cart
        cart.status = CartStatusChoices.ACTIVE
        cart_item.quantity = 2

        CartService(cart=cart).decrease_item_quantity(cart_item)
        assert cart_item.quantity == 1

    def test_decrease_item_quantity_draft(self, cart_item):
        cart = cart_item.cart
        cart.status = CartStatusChoices.DRAFT
        cart_item.quantity = 2

        CartService(cart=cart).decrease_item_quantity(cart_item)
        assert cart_item.quantity == 1

    def test_decrease_item_quantity_pending(self, cart_item):
        cart = cart_item.cart
        cart.status = CartStatusChoices.PAYMENT_PENDING
        cart_item.quantity = 2

        with pytest.raises(InactiveCartException):
            CartService(cart=cart).decrease_item_quantity(cart_item)

    def test_decrease_item_quantity_ordered(self, cart_item):
        cart = cart_item.cart
        cart.status = CartStatusChoices.ORDERED
        cart_item.quantity = 2

        with pytest.raises(InactiveCartException):
            CartService(cart=cart).decrease_item_quantity(cart_item)

    def test_decrease_item_quantity_min_active(self, cart_item):
        cart = cart_item.cart
        cart.status = CartStatusChoices.ACTIVE
        cart_item.quantity = 1

        with pytest.raises(MinCartItemSizeError):
            CartService(cart=cart).decrease_item_quantity(cart_item)

    def test_decrease_item_quantity_min_draft(self, cart_item):
        cart = cart_item.cart
        cart.status = CartStatusChoices.DRAFT
        cart_item.quantity = 1

        with pytest.raises(MinCartItemSizeError):
            CartService(cart=cart).decrease_item_quantity(cart_item)

    def test_delete_from_cart_active(self, cart_item):
        cart = cart_item.cart
        cart.status = CartStatusChoices.ACTIVE

        CartService(cart=cart).delete_from_cart(
            cart_item.sellable_item.id, cart_item.content_type.model
        )
        assert not cart.items.filter(id=cart_item.id).exists()
        assert CartItem.objects.filter(id=cart_item.id).count() == 0
        assert CartItem.all_objects.filter(id=cart_item.id).count() == 1  # soft deleted

    def test_delete_from_cart_draft(self, cart_item):
        cart = cart_item.cart
        cart.status = CartStatusChoices.DRAFT

        CartService(cart=cart).delete_from_cart(
            cart_item.sellable_item.id, cart_item.content_type.model
        )
        assert not cart.items.filter(id=cart_item.id).exists()
        assert CartItem.objects.filter(id=cart_item.id).count() == 0
        assert CartItem.all_objects.filter(id=cart_item.id).count() == 1  # soft deleted

    def test_delete_from_cart_pending(self, cart_item):
        cart = cart_item.cart
        cart.status = CartStatusChoices.PAYMENT_PENDING

        with pytest.raises(InactiveCartException):
            CartService(cart=cart).delete_from_cart(
                cart_item.sellable_item.id, cart_item.content_type.model
            )

    def test_delete_from_cart_ordered(self, cart_item):
        cart = cart_item.cart
        cart.status = CartStatusChoices.ORDERED

        with pytest.raises(InactiveCartException):
            CartService(cart=cart).delete_from_cart(
                cart_item.sellable_item.id, cart_item.content_type.model
            )

    def test_change_region_active(self, cart, region):
        cart.status = CartStatusChoices.ACTIVE
        CartService(cart=cart).change_region(region)
        assert cart.region == region

    def test_change_region_draft(self, cart, region):
        cart.status = CartStatusChoices.DRAFT
        CartService(cart=cart).change_region(region)
        assert cart.region == region

    def test_change_region_pending(self, cart, region):
        cart.status = CartStatusChoices.PAYMENT_PENDING
        with pytest.raises(InactiveCartException):
            CartService(cart=cart).change_region(region)

    def test_change_region_ordered(self, cart, region):
        cart.status = CartStatusChoices.ORDERED
        with pytest.raises(InactiveCartException):
            CartService(cart=cart).change_region(region)

    def test_change_assembly_active(self, cart):
        cart.status = CartStatusChoices.ACTIVE
        CartService(cart=cart).change_assembly(True)
        assert cart.assembly is True

    def test_change_assembly_draft(self, cart):
        cart.status = CartStatusChoices.DRAFT
        CartService(cart=cart).change_assembly(True)
        assert cart.assembly is True

    def test_change_assembly_pending(self, cart):
        cart.status = CartStatusChoices.PAYMENT_PENDING
        with pytest.raises(InactiveCartException):
            CartService(cart=cart).change_assembly(True)

    def test_change_assembly_ordered(self, cart):
        cart.status = CartStatusChoices.ORDERED
        with pytest.raises(InactiveCartException):
            CartService(cart=cart).change_assembly(True)

    def test_reset_promo_active(self, cart, voucher_factory):
        cart.status = CartStatusChoices.ACTIVE
        voucher = voucher_factory()
        cart.used_promo = voucher
        CartService(cart=cart).reset_promo()
        assert cart.promo_text == ''

    def test_reset_promo_draft(self, cart):
        cart.status = CartStatusChoices.DRAFT
        CartService(cart=cart).reset_promo()
        assert cart.promo_text == ''

    def test_reset_promo_pending(self, cart):
        cart.status = CartStatusChoices.PAYMENT_PENDING
        with pytest.raises(InactiveCartException):
            CartService(cart=cart).reset_promo()

    def test_reset_promo_ordered(self, cart):
        cart.status = CartStatusChoices.ORDERED
        with pytest.raises(InactiveCartException):
            CartService(cart=cart).reset_promo()

    def test_add_voucher_active(self, cart, voucher):
        cart.status = CartStatusChoices.ACTIVE
        CartService(cart=cart).add_voucher(voucher)
        assert cart.used_promo == voucher

    def test_add_voucher_draft(self, cart, voucher):
        cart.status = CartStatusChoices.DRAFT
        CartService(cart=cart).add_voucher(voucher)
        assert cart.used_promo == voucher

    def test_add_voucher_pending(self, cart, voucher):
        cart.status = CartStatusChoices.PAYMENT_PENDING
        with pytest.raises(InactiveCartException):
            CartService(cart=cart).add_voucher(voucher)

    def test_add_voucher_ordered(self, cart, voucher):
        cart.status = CartStatusChoices.ORDERED
        with pytest.raises(InactiveCartException):
            CartService(cart=cart).add_voucher(voucher)

    def test_recalculate_items_promo_on_diff_cart(
        self, cart_factory, cart_item_factory, voucher_factory, rates_neutral_region
    ):
        voucher = voucher_factory(value=27, kind_of=VoucherType.PERCENTAGE)
        cart = cart_factory(
            items=[],
            region=rates_neutral_region,
            region_promo_amount=Decimal('2'),
            region_total_price=Decimal('10'),
            used_promo=voucher,
        )
        cart_item_factory(
            cart=cart,
            is_jetty=True,
            price_net=Decimal('10') / Decimal('1.23'),
            region_promo_value=Decimal('2'),
        )
        CartService(cart=cart).reset_promo()
        cart.refresh_from_db()
        assert cart.region_promo_amount == Decimal('0.0')
        assert (
            sum(cart.items.values_list('region_promo_value', flat=True))
            == cart.region_promo_amount
        )


class TestCartServiceAddVoucherWithDeliveryDiscountCases:
    @staticmethod
    def _round_half_up(value: Union[Decimal, float, str, int]) -> Decimal:
        return Decimal(value).quantize(Decimal('1'), rounding=ROUND_HALF_UP)

    @pytest.mark.parametrize(
        'module_trait, delivery_price, delivery_promo, promo_value',
        [
            ('one_module', Decimal('39.00'), Decimal(100), Decimal(50)),
            ('one_module', Decimal('39.00'), Decimal(20), Decimal(20)),
            ('two_modules', Decimal('69.00'), Decimal(100), Decimal(50)),
            ('two_modules', Decimal('69.00'), Decimal(20), Decimal(20)),
            ('three_modules', Decimal('99.00'), Decimal(100), Decimal(50)),
            ('three_modules', Decimal('99.00'), Decimal(50), Decimal(31)),
            ('three_modules', Decimal('99.00'), Decimal(20), Decimal(20)),
        ],
    )
    def test_add_voucher_with_delivery_discount(
        self,
        module_trait,
        delivery_price,
        delivery_promo,
        promo_value,
        cart_factory,
        sotty_factory,
        item_discount_factory,
        region_pl,
        voucher_factory,
    ):
        sotty = sotty_factory(**{module_trait: True})

        cart = cart_factory(items=[], region=region_pl)
        cart_service = CartService(cart)

        voucher = voucher_factory(value=promo_value, kind_of=VoucherType.PERCENTAGE)
        free_delivery_discount = item_discount_factory(
            value=delivery_promo,
            kind_of=VoucherType.PERCENTAGE,
            service_type=ServiceType.DELIVERY,
        )
        voucher.discounts.add(*[free_delivery_discount])

        base_price = Decimal('1000.00')
        with mock.patch(
            'pricing_v3.services.item_price_calculators.ItemPriceCalculatorBase._get_base_price',
            return_value=base_price,
        ):
            cart_service.add_to_cart(sotty)
            order = cart_service.sync_with_order()
            cart_service.add_voucher(voucher=voucher, check_vat=True)

        cart_item = CartItem.objects.get(cart=cart)
        order_item = OrderItem.objects.get(order=order)
        rco = RegionCalculationsObject(region=region_pl)

        product_discount = base_price * (promo_value / 100)
        region_delivery_price = rco.calculate_regionalized(delivery_price)
        delivery_discount = delivery_price * (delivery_promo / 100)

        # calculated in calculate_percentage_promo_price
        region_promo_amount = rco.calculate_regionalized(
            product_discount + delivery_discount
        )
        # calculated in calculate_percentage_promo_price
        delivery_price_with_discount = self._round_half_up(
            free_delivery_discount.calculate_price(delivery_price)
        )
        region_delivery_price_with_discount = self._round_half_up(
            free_delivery_discount.calculate_price(region_delivery_price)
        )
        region_delivery_promo_value = (
            region_delivery_price - region_delivery_price_with_discount
        )
        # calculated in _update_promo_amounts
        total_price = self._round_half_up(
            sum([voucher.calculate_price(base_price), delivery_price_with_discount])
        )
        region_total_price = self._round_half_up(
            voucher.calculate_price(rco.calculate_regionalized(base_price))
            + free_delivery_discount.calculate_price(
                rco.calculate_regionalized(delivery_price)
            )
        )
        # calculated in _get_income_promo_amount
        promo_amount = rco.calculate_base(region_promo_amount) * rco.region_rate

        for item in [cart_item, order_item]:
            assert item.price == base_price
            assert item.region_price == rco.calculate_regionalized(base_price)
            assert item.region_promo_value == rco.calculate_regionalized(
                product_discount
            )
            assert item.delivery_price == delivery_price
            assert item.region_delivery_price == region_delivery_price
            assert item.region_delivery_promo_value == region_delivery_promo_value

        for obj in [cart, order]:
            assert obj.total_price == total_price
            assert obj.region_total_price == region_total_price
            assert obj.promo_amount == promo_amount
            assert obj.region_promo_amount == region_promo_amount


@pytest.mark.django_db
class TestCartServiceWithOldSofaRemoval:
    """
    1. Has sofa and OSC, changes region, keeps sofa, looses OSC
    2. Has sofa and OSC, removes sofa, looses OSC
    3. Has two sofas and OSC, removes one sofa, keeps OSC
    4. Has one sofa and OSC, adds another sofa, keeps OSC, cost correct
    """

    def test_change_region_keeps_sofa_looses_osc(
        self,
        cart_with_sofa_and_old_sofa_collection,
        region_uk,
    ):
        cart = cart_with_sofa_and_old_sofa_collection
        cart_service = CartService(cart)
        cart_service.change_region(region_uk)
        cart.refresh_from_db()
        assert cart.items.count() == 1
        assert cart.has_s01
        assert not cart.has_old_sofa_collection

    def test_remove_sofa_looses_osc(self, cart_with_sofa_and_old_sofa_collection):
        cart = cart_with_sofa_and_old_sofa_collection
        assert cart.items.count() == 2
        cart_service = CartService(cart)
        sotty = cart.items.get(content_type__model='sotty')
        cart_service.delete_from_cart(sotty.object_id, 'sotty')
        cart.refresh_from_db()
        assert cart.items.count() == 0
        assert not cart.has_old_sofa_collection

    def test_has_two_sofas_removes_one_keeps_osc(
        self,
        cart_with_sofa_and_old_sofa_collection,
        cart_item_factory,
    ):
        cart = cart_with_sofa_and_old_sofa_collection
        cart_item_factory(cart=cart, is_sotty=True)
        assert cart.items.count() == 3
        assert cart.has_old_sofa_collection
        cart_service = CartService(cart)
        sotty = cart.items.filter(content_type__model='sotty').first()
        cart_service.delete_from_cart(sotty.object_id, 'sotty')
        cart.refresh_from_db()
        assert cart.items.count() == 2
        assert cart.has_old_sofa_collection

    def test_add_sofa_keeps_osc_right_price(
        self,
        cart_with_sofa,
        sotty_factory,
    ):
        cart_service = CartService(cart_with_sofa)
        with mock.patch(
            'gallery.models.Sotty.modules_number',
            new_callable=PropertyMock,
            return_value=1,
        ):
            cart_service.change_old_sofa_collection(activate=True)
        osc = cart_with_sofa.items.get(content_type__model='additionalservice')
        assert osc.price == OLD_SOFA_COLLECTION_PRICE_MAPPER[ONE_MODULE_OR_LESS]
        new_sofa = sotty_factory()
        with mock.patch(
            'gallery.models.Sotty.modules_number',
            new_callable=PropertyMock,
            return_value=1,
        ):
            cart_service.add_to_cart(new_sofa)
        cart_with_sofa.refresh_from_db()
        osc.refresh_from_db()
        assert osc.price == OLD_SOFA_COLLECTION_PRICE_MAPPER[TWO_MODULES]
