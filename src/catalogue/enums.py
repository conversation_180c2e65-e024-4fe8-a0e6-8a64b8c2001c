from enum import Enum
from typing import Optional

from django.db.models import (
    IntegerChoices,
    QuerySet,
    TextChoices,
)

from regions.cached_region import CachedRegionData
from regions.models import Region
from regions.types import RegionLikeObject
from render_tasks.enums import InteriorType


class FurnitureAttributesEnum(TextChoices):
    # Product attributes
    DRAWERS = 'drawers'
    DOORS = 'doors'
    LEGS = 'legs'
    OPEN = 'open'
    PLINTH = 'plinth'
    VISIBLE_BACKPANELS = 'visibleBackpanels'
    EXTRA_STORAGE = 'extraStorage'
    SLANTED_STYLE = 'slantedStyle'
    CABLE_MANAGEMENT = 'cableManagement'
    MIXED_HEIGHT = 'mixedHeight'
    ARMREST = 'armrest'  # has both armrests
    SEMI_OPENEND = 'semiOpenend'
    OPENEND = 'openend'

    # Finish material
    PLYWOOD = 'plywood'
    MATTE = 'matte'
    CLASSIC_MATTE = 'classicMatte'  # Type02
    WOODEN_EFFECT = 'woodenEffect'  # Type13 Veneer
    CORDUROY = 'corduroy'
    WOOL = 'wool'

    FULLY_OPEN = 'fullyOpen'
    PARTIALLY_OPEN = 'partiallyOpen'
    FULLY_CLOSED = 'fullyClosed'
    OPEN_COMPARTMENTS = 'openCompartments'
    INTERNAL_DRAWERS = 'internalDrawers'
    EXTERNAL_DRAWERS = 'externalDrawers'
    HANG = 'hang'
    DOUBLE_HANG = 'doubleHang'
    FRONT_FACING_RAIL = 'frontFacingRail'
    LIGHTING = 'lighting'
    HANGING = 'hanging'

    SEATING = 'seating'
    CORNER = 'corner'
    CHAISE_LONGUE = 'chaiseLongue'
    EXTENDED_CHAISE_LONGUE = 'extendedChaiseLongue'

    # Business attributes
    SALE = 'sale'
    NEW_ARRIVAL = 'newArrival'
    TOP_SELLER = 'topSeller'
    SPECIAL_EDITION = 'specialEdition'
    ALL_SHELVES_PROMOTED = 'allShelvesPromoted'
    CATEGORY_PROMOTED = 'categoryPromoted'
    DESIGNER = 'designer'
    SPECIAL = 'special'

    # Color attributes
    WHITE = 'white'
    BLACK = 'black'
    GREY = 'grey'
    BLUE = 'blue'
    PINK = 'pink'
    WOODEN = 'wooden'
    YELLOW = 'yellow'
    RED = 'red'
    BEIGE = 'beige'
    BURGUNDY = 'burgundy'
    GRAPHITE = 'graphite'
    GREEN = 'green'
    BROWN = 'brown'
    MIX = 'mix'

    # Pattern attributes
    SLANT = 'Slant'
    GRADIENT = 'Gradient'
    PATTERN = 'Pattern'
    GRID = 'Grid'
    FRAME = 'Frame'
    PIXEL = 'Pixel'
    MOSAIC = 'Mosaic'

    # Orientation
    ORIENTATION_SYMMETRICAL = 'orientationSymmetrical'
    ORIENTATION_LEFT = 'orientationLeft'
    ORIENTATION_RIGHT = 'orientationRight'

    @classmethod
    def get_pattern_attributes(cls) -> list['FurnitureAttributesEnum']:
        return [
            cls.SLANT,
            cls.GRADIENT,
            cls.PATTERN,
            cls.GRID,
            cls.FRAME,
            cls.PIXEL,
            cls.MOSAIC,
        ]


class ColorGroup(TextChoices):
    NUDE = 'nude'
    MILD = 'mild'
    BOLD = 'bold'


class ListingOrder(TextChoices):
    ALL_PRODUCTS = 'all_products'
    COLLECTIVE_CATEGORY = 'collective_category'
    CATEGORY = 'category'


class StrategyEnum(TextChoices):
    NONE = 'none'
    PROFIT_NETTO = 'profit_netto'

    @classmethod
    def valid_strategies(cls) -> set:
        return {cls.PROFIT_NETTO}

    @classmethod
    def valid_choices(cls) -> list[tuple[str, str]]:
        return [('None', 'None'), ('Profit netto', 'Profit netto')]

    def get_shiam_field(
        self,
        region_name: Optional[str] = None,
        country_code: Optional[str] = None,
    ) -> Optional[str]:
        map_dict = {self.NONE: None}
        field = map_dict.get(self, self.value)
        return (
            self._get_regional_field(field, region_name, country_code)
            if region_name and country_code
            else field
        )

    def get_order_field(
        self,
        region_name: Optional[str] = None,
        country_code: Optional[str] = None,
    ) -> str:
        field = f'{self.value}_order' if self != self.NONE else 'order'
        return (
            self._get_regional_field(field, region_name, country_code)
            if region_name and country_code
            else field
        )

    def get_category_order_field(
        self,
        region_name: Optional[str] = None,
        country_code: Optional[str] = None,
    ) -> str:
        field = (
            f'{self.value}_category_order' if self != self.NONE else 'category_order'
        )
        return (
            self._get_regional_field(field, region_name, country_code)
            if region_name and country_code
            else field
        )

    def get_alt_order_field(self) -> str:
        if self == self.NONE:
            raise ValueError(f'{StrategyEnum.NONE} does not have alt_order field')
        return f'{self.value}_alt_order'

    def get_alt_category_order_field(self) -> str:
        if self == self.NONE:
            raise ValueError(
                f'{StrategyEnum.NONE} does not have alt_category_order field'
            )
        return f'{self.value}_alt_category_order'

    def get_test_order_field(
        self,
        region_name: Optional[str] = None,
        country_code: Optional[str] = None,
    ) -> str:
        field = f'test_{self.value}_order' if self != self.NONE else 'test_order'
        return (
            self._get_regional_field(field, region_name, country_code)
            if region_name and country_code
            else field
        )

    def get_test_alt_order_field(self) -> str:
        if self == self.NONE:
            raise ValueError(f'{StrategyEnum.NONE} does not have test_alt_order field')
        return f'test_{self.value}_alt_order'

    def get_test_category_order_field(
        self,
        region_name: Optional[str] = None,
        country_code: Optional[str] = None,
    ) -> str:
        field = (
            f'test_{self.value}_category_order'
            if self != self.NONE
            else 'test_category_order'
        )
        return (
            self._get_regional_field(field, region_name, country_code)
            if region_name and country_code
            else field
        )

    def get_test_alt_category_order_field(self) -> str:
        if self == self.NONE:
            raise ValueError(
                f'{StrategyEnum.NONE} does not have test_alt_category_order field'
            )
        return f'test_{self.value}_alt_category_order'

    @classmethod
    def strategies_handling_regions(cls) -> set:
        return {cls.PROFIT_NETTO}

    def get_test_dynamic_order(
        self, listing: ListingOrder, region: RegionLikeObject | None = None
    ) -> str:
        if self == self.NONE:
            raise ValueError(
                f'{StrategyEnum.NONE} does not have test_alt_category_order field'
            )
        region_code = self.get_region_code(region)
        return f'test_profit_netto_dynamic_order__{region_code}__{listing}'

    def get_dynamic_order(
        self, listing: ListingOrder, region: RegionLikeObject | None = None
    ) -> str:
        if self == self.NONE:
            raise ValueError(
                f'{StrategyEnum.NONE} does not have test_alt_category_order field'
            )
        region_code = self.get_region_code(region)
        return f'profit_netto_dynamic_order__{region_code}__{listing}'

    def get_region_code(self, region: RegionLikeObject | None = None) -> str:
        if region is None or region.name not in MainMarketsEnum:
            return 'other'

        if isinstance(region, Region):
            return region.country.code.lower()
        elif isinstance(region, CachedRegionData):
            return region.country_code.lower()
        return 'other'

    def _get_regional_field(
        self,
        field: str,
        region_name: str,
        country_code: str,
    ) -> str:
        if (
            self not in StrategyEnum.strategies_handling_regions()
            or region_name not in MainMarketsEnum.values
        ):
            return field
        region_code = country_code.lower()
        return f'{field}_{region_code}'


class HeightRange(TextChoices):
    LOW = 'low'
    MEDIUM = 'medium'
    TALL = 'tall'


class WidthRange(TextChoices):
    NARROW = 'narrow'
    MEDIUM = 'medium'
    WIDE = 'wide'


class TopsellerFirstPageCount(IntegerChoices):
    MINIMAL = 7
    MAXIMAL = 10


class CategoryHeightRange(TextChoices):
    LOW = 'low'
    TALL = 'tall'
    NONE = 'none'  # Vinyl Storage cannot be assigned to a height range


class MainMarketsEnum(TextChoices):
    GERMANY = 'germany'
    FRANCE = 'france'
    NETHERLANDS = 'netherlands'
    UNITED_KINGDOM = 'united_kingdom'
    SWITZERLAND = 'switzerland'
    SWEDEN = 'sweden'
    NORWAY = 'norway'
    DENMARK = 'denmark'

    @classmethod
    def get_regions(cls) -> QuerySet[Region]:
        return Region.objects.filter(name__in=cls.values)

    @classmethod
    def get_region_codes(cls) -> set[str]:
        return {market.region_code for market in cls if market.region_code} | {'other'}

    @property
    def region(self) -> Optional[Region]:
        regions = {
            self.GERMANY: Region.objects.get(name=self.GERMANY),
            self.FRANCE: Region.objects.get(name=self.FRANCE),
            self.NETHERLANDS: Region.objects.get(name=self.NETHERLANDS),
            self.UNITED_KINGDOM: Region.objects.get(name=self.UNITED_KINGDOM),
            self.SWITZERLAND: Region.objects.get(name=self.SWITZERLAND),
            self.SWEDEN: Region.objects.get(name=self.SWEDEN),
            self.NORWAY: Region.objects.get(name=self.NORWAY),
            self.DENMARK: Region.objects.get(name=self.DENMARK),
        }
        return regions.get(self)

    @property
    def region_code(self) -> str | None:
        return {
            self.GERMANY: 'de',
            self.FRANCE: 'fr',
            self.NETHERLANDS: 'nl',
            self.UNITED_KINGDOM: 'uk',
            self.SWITZERLAND: 'ch',
            self.SWEDEN: 'se',
            self.NORWAY: 'no',
            self.DENMARK: 'dk',
        }.get(self)

    @property
    def code(self) -> Optional[str]:
        return self.region.country.code.lower() if self.region else None


class PLPImageKind(str, Enum):
    PRODUCT = 'product'
    LIFESTYLE = 'lifestyle'

    @property
    def field_name(self) -> str:
        return {
            self.PRODUCT: 'product_unreal_image_webp',
            self.LIFESTYLE: 'lifestyle_unreal_image_webp',
        }.get(self)

    @classmethod
    def get_by_value(cls, value: str) -> 'PLPImageKind':
        for member in cls:
            if member.value == value:
                return member
        raise ValueError(f'{value} is not a valid value for {cls.__name__}')

    @property
    def unreal_interior_type(self):
        return {
            self.PRODUCT: InteriorType.STUDIO,
            self.LIFESTYLE: InteriorType.SCENE,
        }.get(self)
