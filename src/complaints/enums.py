import enum

from django.db import models

from producers.enums import (
    ShelfElemTypeEnum,
    WardrobeElemTypeEnum,
)

NOT_PASSED = object()


def choices_from_values(members, include_empty_choice=False):
    choices = [
        (member.value, member.value.replace('_', ' ').capitalize())
        for member in members
    ]
    if include_empty_choice:
        choices.insert(0, (None, '-----'))
    return choices


class ComplaintStatus(enum.StrEnum):
    VERIFICATION = 'verification'
    REJECTED = 'rejected'
    NEW = 'new'
    IN_PROGRESS = 'in progress'
    SHIPPING = 'shipping'
    DONE = 'done'
    ABORTED = 'aborted'

    @classmethod
    def choices(cls):
        return choices_from_values(cls)


class DamageFormStatus(models.IntegerChoices):
    DRAFT = 1
    READY = 2


class ContactTopic(enum.Enum):
    PRODUCT_INFO = 'product_info'
    ORDER_STATUS = 'order_status'
    SPECIAL_ORDER = 'special_order'
    FEEDBACK = 'feedback'
    CHANGE_ORDER = 'change_order'
    FREE_RETURN = 'free_return'
    ISSUE_DAMAGES = 'issue_damages'
    ISSUE_DELIVERY_PROBLEM = 'issue_delivery_problem'
    ISSUE_OTHER = 'issue_other'
    ASSEMBLY_SERVICE = 'assembly_service'
    OTHER = 'other'

    @classmethod
    def choices(cls):
        return choices_from_values(cls)


class ImageType(enum.Enum):
    DAMAGED_PACKAGE = 'damaged_package'
    DAMAGED_ELEMENTS = 'damaged_elements'

    @classmethod
    def choices(cls):
        return choices_from_values(cls)


class ComplaintPriorityChoices(models.IntegerChoices):
    NORMAL = 1
    PRIORITY = 2


class ShelfElement(enum.IntEnum):
    A = 1
    AH = 2
    AN = 3
    AHN = 4
    AS = 5
    B = 101
    BL = 102
    BR = 103
    BH = 104
    BN = 105
    BS = 106
    BHN = 106
    BHR = 107
    BLR = 108
    BLN = 109
    C = 201
    CL = 202
    CR = 203
    CH = 204
    CN = 205
    CHN = 206
    CHR = 207
    CLR = 208
    CLN = 209
    CS = 210

    @classmethod
    def choices(cls):
        return [(member.value, member.name.capitalize()) for member in cls]

    # TODO: refactor it and make adjust with `custom.enums` implementations
    @classmethod
    def get(cls, name, fallback=NOT_PASSED):
        """Get enum's member by it's name.

        Raises
        ------
        AttributeError
            when member with given `name` does not exist
        """
        name = name.upper()
        member = cls.__members__.get(name, fallback)
        if member is NOT_PASSED:
            raise AttributeError(name)
        return member


class ElementCategory(enum.IntEnum):
    REGULAR = 1
    FEATURE = 2
    OTHER = 3

    @classmethod
    def for_elem_type(cls, elem_type):
        regular_elem_names = ShelfElemTypeEnum.ELEMENTS.regular.union(
            WardrobeElemTypeEnum.ELEMENTS.regular,
        )
        feature_elem_names = ShelfElemTypeEnum.ELEMENTS.feature.union(
            WardrobeElemTypeEnum.ELEMENTS.feature,
        )
        if elem_type in regular_elem_names:
            return cls.REGULAR
        if elem_type in feature_elem_names:
            return cls.FEATURE
        return cls.OTHER


class TNTComplaintType(models.TextChoices):
    DAMAGED = 'damaged', 'damaged'
    LOST = 'lost', 'lost'
    DAMAGED_AND_LOST = 'damaged and lost', 'damaged and lost'
    RETURNED_DUE_TO_CUST_ABSENCE = (
        'returned due to Cust. absence',
        'returned due to Cust. absence',
    )
    DAMAGED_FREE_RETURN = 'damaged (free return)', 'damaged (free return)'


class TNTNotificationChannel(models.TextChoices):
    NONE = '', ''
    WWW = 'www', 'www'
    EMAIL = 'e-mail', 'e-mail'
    LINK = 'link', 'link'


class TNTNotificationByCustomer(models.TextChoices):
    YES = 'tak', 'tak'
    NO = 'nie', 'nie'
    CHECK = 'sprawdzamy', 'sprawdzamy'


class TNTStatus(models.TextChoices):
    IN_PROGRESS = 'w toku', 'w toku'
    APPROVED = 'uznane', 'uznane'
    DENIED = 'odrzucone', 'odrzucone'


class ReplaceableShelfFittingsStockSide(models.IntegerChoices):
    LEFT = 1
    RIGHT = 2
    NO_SIDE = 3, 'No side'


BOOL_CHOICES = ((True, 'Yes'), (False, 'No'))
