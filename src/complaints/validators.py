import typing

from django.core.exceptions import ValidationError

from complaints.constants import (
    EXPRESS_REPLACEMENT_SUPPORTING_MANUFACTORS_NAMES,
    EXPRESS_REPLACEMENT_SUPPORTING_MANUFACTORS_NAMES_FOR_FITTINGS_ONLY,
)
from custom.enums import ShelfType
from producers.models import Manufactor

if typing.TYPE_CHECKING:
    from complaints.models import Complaint


class ExpressReplacementComplaintValidator:
    def __call__(self, complaint):
        self.validate_is_reproduction(complaint)
        if complaint.fittings_only:
            self.validate_product_manufactor(
                complaint,
                EXPRESS_REPLACEMENT_SUPPORTING_MANUFACTORS_NAMES_FOR_FITTINGS_ONLY,
            )
        else:
            self.validate_product_manufactor(
                complaint, EXPRESS_REPLACEMENT_SUPPORTING_MANUFACTORS_NAMES
            )
            self.exclude_veneer_for_S93(complaint)
            self.validate_all_products_elements_are_reproducible(complaint)

    def validate_is_reproduction(self, complaint):
        if not complaint.reproduction:
            raise ValidationError(
                message=(
                    'Express Replacement is possible only for `reproduction` '
                    'complaints'
                ),
                code='not_reproduction_complaint',
            )

    def validate_product_manufactor(
        self, complaint: 'Complaint', allowed_manufactors: tuple[str, ...]
    ):
        manufactor_name = getattr(complaint.product.manufactor, 'name', '')
        if manufactor_name not in allowed_manufactors:
            raise ValidationError(
                message=('Manufactor "{}" does not support express replacement').format(
                    manufactor_name or '<NO MANUFACTOR>'
                ),
                code='unsupported_manufactor',
            )

    def exclude_veneer_for_S93(self, complaint: 'Complaint'):
        manufactor_name = getattr(complaint.product.manufactor, 'name', '')
        if (
            manufactor_name == Manufactor.S93
            and complaint.product.shelf_type_option == ShelfType.VENEER_TYPE01
        ):
            raise ValidationError(
                message='S93 does not support veneer express replacement',
                code='veneer_unsupported',
            )

    def validate_all_products_elements_are_reproducible(self, complaint):
        express_replacement_elements = (
            complaint.product.get_express_replacement_elements()
        )

        self.assert_all_elements_are_reproducible(
            complaint.elements_for_reproduction,
            express_replacement_elements,
        )

    def assert_all_elements_are_reproducible(
        self,
        element_names,
        express_replacement_names,
    ):
        not_reproducible_elements = {name.lower() for name in element_names} - {
            name.lower() for name in express_replacement_names
        }
        if not_reproducible_elements:
            validation_errors = []
            for element_name in not_reproducible_elements:
                validation_errors.append(
                    ValidationError(
                        message='Element "{}" is not reproducible'.format(
                            element_name.capitalize(),
                        ),
                        code='non_reproducible_element',
                    ),
                )
            raise ValidationError(validation_errors)
