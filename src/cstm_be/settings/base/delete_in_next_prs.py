import datetime

from typing import Final

from gallery.constants import SOTTY_DELIVERY_REGIONS_MAP

from .common import env

# ASSEMBLY SERVICE
ASSEMBLY_REGION_KEYS = (
    'austria',
    'belgium',
    'denmark',
    'france',
    'germany',
    'luxembourg',
    'netherlands',
    'poland',
    'switzerland',
    'united_kingdom',
)

# Regions with T03 availability
T03_REGION_KEYS = ASSEMBLY_REGION_KEYS

# Regions with S01 (sofa) availability
S01_REGION_KEYS: Final[frozenset[str]] = frozenset(SOTTY_DELIVERY_REGIONS_MAP.keys())
DOORSTEP_SOTTY_DELIVERY_REGIONS: Final[frozenset[str]] = frozenset(
    {
        'sweden',
        'finland',
        'ireland',
        'norway',
    }
)

# Regions with "Old Sofa Collection" service available
OLD_SOFA_COLLECTION_REGIONS: Final[frozenset[str]] = frozenset(
    {
        'austria',
        'belgium',
        'france',
        'germany',
        'luxembourg',
        'netherlands',
    }
)

# Regions with corduroy restrictions
CORDUROY_RESTRICTED_REGIONS: Final[frozenset[str]] = frozenset({'united_kingdom'})

# PRICING
POLISH_VAT_FACTOR = 1.23

# PRODUCT FEEDS
ACCEPTED_SHELF_DEPTHS = frozenset((240, 320, 400, 500))
ACCEPTED_SHELF_ROWS = frozenset((200, 300, 400))

DEFAULT_REPRODUCTION_TIME_FOR_ELEMENT = 10


MAILCHIMP_WAITING_LIST = '1b70c97ce5'
MAILCHIMP_T03_OTHER_COUNTRIES_LIST = '51453cb696'
MAILCHIMP_T03_COUNTRIES_INTERESTS = {
    'greece': '9941c63d87',
    '_other': '28890e7b65',
    'italy': 'ba58f3e538',
    'czech': 'd360b24f02',
    'denmark': 'd6c81bd838',
    'romania': '59d3678e3b',
    'austria': 'bc50b29d37',
    'belgium': '96a4b2bc51',
    'slovenia': '2d0af66ffd',
    'germany': 'b669622d53',
    'ireland': 'd0c6f795dc',
    'poland': '50a67da651',
    'switzerland': '15b65c788b',
    'finland': '59afcbba07',
    'netherlands': '6f61bf0d3f',
    'france': '2428efdfc1',
    'croatia': '81f5e36673',
    'estonia': '2e84f0bc00',
    'latvia': '229ed2cdce',
    'bulgaria': 'a7792a182c',
    'united_kingdom': 'cadf934760',
    'norway': 'c77cc146f1',
    'slovakia': '8e279af27c',
    'lithuania': 'f126202b3b',
    'luxembourg': 'ba777c00f7',
    'spain': '6caff4c749',
    'hungary': '70faf7fe38',
    'sweden': '6dbfa677f4',
    'portugal': '8f647fed1b',
}

MAILING_FLOWS_MODULE = 'mailing.flows'

# EMAIL24
EMAIL24_VALID_FOR_DAYS = 2

ON_HOLD_PRIORITY_DAYS_THRESHOLD = 3

CUSTOM_AUDIENCE_CLEAN_THRESHOLD_DAYS = 30

WAITING_LIST_COOKIE_AND_SESSION_NAME = env.str(
    'WAITING_LIST_COOKIE_AND_SESSION_NAME',
    default='waiting_list_expire_date',
)


# FACEBOOK
FB_ACCOUNT_IDS = {
    'Tylko DE': 'act_548178842014036',
    'Tylko_EU': 'act_770154106483174',
    'Tylko_UK': 'act_769647186533866',
    'Tylko_FR': 'act_876245905873993',
    'Tylko_SM': 'act_862420607256523',
    'Tylko_new': 'act_906978046134112',
    'Tylko_CH': 'act_1952747341514795',
    'Tylko_AT': 'act_441036049983265',
    'Tylko_NL': 'act_560124021188514',
    'Tylko_BE': 'act_2432304177001590',
    'Tylko_FI': 'act_323185892128533',
    'Tylko_NO': 'act_338810833924083',
    'Tylko_SW': 'act_2434354913535939',
    'Tylko_OLD': 'act_427587467406508',
}
FB_APP_ID = env.str('FB_APP_ID', default='')
FB_APP_SECRET = env.str('FB_APP_SECRET', default='')
# has ads_management and read_insights permissions
FB_APP_TOKEN = env.str('FB_APP_TOKEN', default='')
# has ads_management and catalog_management permissions
FB_APP_SYSTEM_TOKEN = env.str('FB_APP_SYSTEM_TOKEN', default='')
FB_API_VERSION = env.str('FB_API_VERSION', default='v13.0')
FB_PRODUCT_CATALOGS = {
    'united_kingdom': env.str('FB_GB_CATALOG_ID', '569200614448969'),
    'germany': env.str('FB_DE_CATALOG_ID', '930403427816450'),
    'belgium': env.str('FB_BE_CATALOG_ID', '177866037670541'),
    'france': env.str('FB_FR_CATALOG_ID', '934719427108889'),
    'netherlands': env.str('FB_NL_CATALOG_ID', '218559126823420'),
    'switzerland': env.str('FB_CH_CATALOG_ID', '***************'),
    'austria': env.str('FB_AT_CATALOG_ID', '****************'),
}

EDD_TIME_ADJUSTMENT_DAYS = env.int('EDD_TIME_ADJUSTMENT_DAYS', default=14)

# If user is in this group, he can edit in admin panel,
# but only if he has right permissions set
ADMIN_EDITOR_GROUP = 'AdminEditor'


# A/B TESTS SETTINGS
AB_TESTS_TOKEN_SETTINGS = {
    'query_parameter': 'ab_tests',
    'signing_key': env.str('AB_TESTS_TOKEN_SECRET_KEY', default=''),
    'algorithm': 'HS256',
}
ACCOUNT_PASSWORD_MIN_LENGTH = env.int('ACCOUNT_PASSWORD_MIN_LENGTH', default=8)
ACCOUNT_USERNAME_MIN_LENGTH = env.int('ACCOUNT_USERNAME_MIN_LENGTH', default=3)
ACCOUNT_USERNAME_MAX_LENGTH = env.int('ACCOUNT_USERNAME_MAX_LENGTH', default=150)


# FRONTEND CMS
CONTACT_FILE_SIZE_LIMIT = env.int(
    'CONTACT_FILE_SIZE_LIMIT',
    15 * 1024 * 1024,
)  # 15MB
CONTACT_FILE_CLEANUP_TIME = env.int(
    'CONTACT_FILE_CLEANUP_TIME',
    int(datetime.timedelta(hours=5).total_seconds()),
)

# ACTSTREAM
ACTSTREAM_SETTINGS = {
    'USE_JSONFIELD': True,
}

FAST_TRACK_PRICE = env.int('FAST_TRACK_PRICE', default=79)
FAST_TRACK_START_DATE = env.str('FAST_TRACK_START_DATE', default='2022-08-23')
FAST_TRACK_END_DATE = env.str('FAST_TRACK_END_DATE', default='2022-09-06')
FASTTRACK_ORDERS_LIMIT = 3
