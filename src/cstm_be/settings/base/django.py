from django.contrib.messages import constants as messages_constants

from custom.enums import LanguageEnum

from .common import (
    APPS_DIR,
    env,
)

# DEBUG
DEBUG = env.bool('DJANGO_DEBUG', default=False)


# DATABASES
DEFAULT_DB_ENGINE = env.str(
    'DJANGO_POSTGRES_ENGINE',
    default='django.db.backends.postgresql',
)
DEFAULT_DB_USER = env.str('POSTGRES_USER', default='tylko')
DEFAULT_DB_PASSWORD = env.str('POSTGRES_PASSWORD', default='tylko')
DEFAULT_DB_PORT = env.int('POSTGRES_PORT', default=5432)
DEFAULT_DB_NAME = env.str('POSTGRES_DB', default='tylko')
DEFAULT_DB_HOST = env.str('POSTGRES_HOST', default='localhost')
DEFAULT_DB_CONN_MAX_AGE = env.int('POSTGRES_CONN_MAX_AGE', default=60)

DATABASES = {
    'default': {
        'ENGINE': DEFAULT_DB_ENGINE,
        'NAME': DEFAULT_DB_NAME,
        'USER': DEFAULT_DB_USER,
        'PASSWORD': DEFAULT_DB_PASSWORD,
        'HOST': DEFAULT_DB_HOST,
        'PORT': DEFAULT_DB_PORT,
        'CONN_MAX_AGE': DEFAULT_DB_CONN_MAX_AGE,
    }
}

DEFAULT_AUTO_FIELD = 'django.db.models.AutoField'

# APPLICATIONS
DJANGO_APPS = [
    # to turn off autodiscover, so we could use custom urls
    # NOTE: following app is used to properly replace
    # ``django.contrib.admin.site``
    'cstm_be.apps.CSTMAdminConfig',
    'django.contrib.contenttypes',
    'django.contrib.auth',
    'django.contrib.humanize',
    'django.contrib.messages',
    'django.contrib.postgres',
    'django.contrib.sessions',
    'django.contrib.sitemaps',
    'django.contrib.sites',
    'django.contrib.staticfiles',
]
THIRD_PARTY_APPS = [
    'actstream',
    'adminsortable2',
    'cloudinary',
    'crispy_forms',
    'django_extensions',
    'django_filters',
    'django_json_widget',
    'django_mailer',
    'django_object_actions',
    'django_requestlogging',
    'django_user_agents',
    'rangefilter',
    'rest_framework',
    'rest_framework.authtoken',
    'dj_rest_auth',
    'drf_spectacular',
    'sorl.thumbnail',
    'webpack_loader',
    'fixture_magic',
    'sortedm2m',
    'svg',
    'taggit',
    'django_fsm',
    'django_fsm_log',
    'safedelete',
    'simple_history',
    'django_admin_lightweight_date_hierarchy',
    'nested_admin',
    # Health checks:
    # You may want to enable other checks as well,
    # see: https://github.com/KristianOellegaard/django-health-check
    'health_check',
    'health_check.db',
    'health_check.storage',
    'ckeditor',
    'deepl',
    'allauth',
    'allauth.account',
    'allauth.socialaccount',
    'allauth.socialaccount.providers.apple',
    'allauth.socialaccount.providers.facebook',
    'allauth.socialaccount.providers.google',
]
LOCAL_APPS = [
    'abtests',
    'accounting',
    'admin_customization',
    'automatic_batching',
    'b2b',
    'carts',
    'catalogue',
    'checkout',
    'complaints',
    'custom',
    'custom_audiences',
    'customer_service',
    'dixa',
    'dynamic_delivery',
    'ecommerce_api',
    'events',
    'feeds',
    'free_returns',
    'frontend_cms',
    'gallery',
    'gallery_editor',
    'internal_api',
    'invoice',
    'items_for_render',
    'kpi',
    'logger',
    'loose_ends',
    'mailing',
    'material_recovery',
    'model_transfers',
    'orders',
    'payments',
    'pricing_v3',
    'producers',
    'product_feeds',
    'production_margins',
    'promotions',
    'rating_tool',
    'regions',
    'render_tasks',
    'rest_auth',
    'reviews',
    'services',
    'shortener',
    'showrooms',
    'socials',
    'taskapp.celery.CeleryConfig',
    'user_consents',
    'user_profile',
    'vouchers',
    'waiting_list',
    'warehouse',
]
INSTALLED_APPS = DJANGO_APPS + THIRD_PARTY_APPS + LOCAL_APPS

# MIDDLEWARES
MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'django.contrib.sessions.middleware.SessionMiddleware',
    'django.middleware.locale.LocaleMiddleware',
    'django.middleware.common.CommonMiddleware',
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'custom.middleware.DisableCSRF',
    'custom.middleware.SaveUTMInSession',
    'custom.middleware.SaveRefererInSession',
    'custom.middleware.ThreadLocalMiddleware',
    'custom.middleware.AdminLocaleURLMiddleware',
    'custom.middleware.ViewMetricsMiddleware',
    'custom.middleware.LocaleURLMiddleware',
    'django_user_agents.middleware.UserAgentMiddleware',
    'user_profile.middleware.LoginAccessTokenMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',
    'user_profile.middleware.ProcessAnonymousMiddleware',
    'user_profile.middleware.ForceRegionMiddleware',
    'regions.middleware.RegionMiddleware',
    'custom.middleware.PromoRegisterMiddleware',
    'abtests.middleware.ABTestMiddleware',
    'custom.middleware.LogRequestInfoMiddleware',
    'regions.middleware.ProfileRegionCacheMiddleware',
    'waiting_list.middleware.WaitingListTokenMiddleware',
]

# TEMPLATES
TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [
            str(APPS_DIR.path('templates')),
            str(APPS_DIR.path('reviews/static')),
            str(APPS_DIR.path('custom_audiences/static')),
            str(APPS_DIR.path('producers/static')),
        ],
        'OPTIONS': {
            'loaders': [
                (
                    'django.template.loaders.cached.Loader',
                    (
                        'django.template.loaders.filesystem.Loader',
                        'django.template.loaders.app_directories.Loader',
                    ),
                ),
            ],
            'context_processors': (
                'django.contrib.auth.context_processors.auth',
                'django.template.context_processors.request',
                'django.template.context_processors.i18n',
                'django.contrib.messages.context_processors.messages',
                'django.template.context_processors.csrf',
                'django.template.context_processors.static',
                'custom.context_processors.combined_custom_context',
                # please Add any new context processors directly to the
                # combined_custom_context configuration.
            ),
        },
    },
]

# CACHES
CACHE_REDIS_URL = env.str(
    'CACHE_REDIS_URL',
    default='redis://127.0.0.1:6379/0',
)
CACHE_KEY_PREFIX = env.str('CACHE_KEY_PREFIX', default='')
CACHES = {
    'default': {
        'BACKEND': 'django_redis.cache.RedisCache',
        'LOCATION': CACHE_REDIS_URL,
        'KEY_PREFIX': CACHE_KEY_PREFIX,
        'OPTIONS': {
            'CLIENT_CLASS': 'django_redis.client.DefaultClient',
        },
    }
}

# IN MEMORY CACHE
REGIONS_CACHE_TTL_SECONDS = env.int(
    'REGIONS_CACHE_TTL_SECONDS',
    default=600,
)
ABTESTS_CACHE_TTL_SECONDS = env.int(
    'ABTESTS_CACHE_TTL_SECONDS',
    default=600,
)
GRID_DATA_API_CACHE_TTL_SECONDS = env.int(
    'GRID_DATA_API_CACHE_TTL_SECONDS',
    default=600,
)
SOTTY_COVERS_CACHE_TTL_SECONDS = env.int(
    'SOTTY_COVERS_CACHE_TTL_SECONDS',
    default=60 * 60 * 24,
)

# AUTHENTICATION
AUTHENTICATION_BACKENDS = (
    'django.contrib.auth.backends.ModelBackend',
    'allauth.account.auth_backends.AuthenticationBackend',
)
LOGIN_URL = '/pages/producer_login/'
LOGIN_REDIRECT_URL = '/pages/producer_login/'
OLD_PASSWORD_FIELD_ENABLED = True

# MESSAGES
MESSAGE_TAGS = {
    messages_constants.DEBUG: 'debug alert-dark',
    messages_constants.INFO: 'info alert-info',
    messages_constants.SUCCESS: 'success alert-success',
    messages_constants.WARNING: 'warning alert-warning',
    messages_constants.ERROR: 'error alert-danger',
}

# HTTP
DATA_UPLOAD_MAX_MEMORY_SIZE = 15 * 1024 * 1024  # 15 MB
WSGI_APPLICATION = 'cstm_be.wsgi.application'
DATA_UPLOAD_MAX_NUMBER_FIELDS = 4096
FILE_UPLOAD_PERMISSIONS = 0o644

# SESSIONS
SESSION_SERIALIZER = 'django.contrib.sessions.serializers.JSONSerializer'

# URLs
ROOT_URLCONF = 'cstm_be.urls'
APPEND_SLASH = True


# STATIC FILES
STATIC_ROOT = env('DJANGO_STATIC_ROOT', default=str(APPS_DIR.path('r_static')))
STATICFILES_DIRS = []
STATICFILES_STORAGE = env.str(
    'DJANGO_STATICFILES_STORAGE', default='cstm_be.storage.HashedSymlinkFilesMixin'
)
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
    'django.contrib.staticfiles.finders.FileSystemFinder',
]

# MEDIA FILES
MEDIA_ROOT = env('DJANGO_MEDIA_ROOT', default=str(APPS_DIR.path('media')))
MEDIA_URL = '/uploaded/'
LOCAL_MEDIA_URL_PREFIX = MEDIA_URL

PRIVATE_FILE_STORAGE = 'django.core.files.storage.FileSystemStorage'
WHATSAPP_FILES_STORAGE = 'django.core.files.storage.FileSystemStorage'

# SECURITY
CSRF_FAILURE_VIEW = 'custom.views.csrf_failure'
CSRF_TRUSTED_ORIGINS = env.list(
    'DJANGO_CSRF_TRUSTED_ORIGINS', default=['https://*.tylko.com']
)
X_FRAME_OPTIONS = 'SAMEORIGIN'


AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': (
            'django.contrib.auth.password_validation.'
            'UserAttributeSimilarityValidator'
        ),
    },
    {
        'NAME': ('django.contrib.auth.password_validation.MinimumLengthValidator'),
    },
    {
        'NAME': ('django.contrib.auth.password_validation.CommonPasswordValidator'),
    },
    {
        'NAME': ('django.contrib.auth.password_validation.NumericPasswordValidator'),
    },
]

PASSWORD_HASHERS = [
    'django.contrib.auth.hashers.PBKDF2PasswordHasher',
    'django.contrib.auth.hashers.PBKDF2SHA1PasswordHasher',
    'django.contrib.auth.hashers.Argon2PasswordHasher',
    'django.contrib.auth.hashers.BCryptSHA256PasswordHasher',
    'django.contrib.auth.hashers.BCryptPasswordHasher',
]

# Urls Hashid Converter
HASHIDS_SALT = env.str('HASHIDS_SALT', default='')
HASHIDS_MIN_LENGTH = env.int('HASHIDS_MIN_LENGTH', default=32)


# GLOBALIZATION (I18N/L10N)
USE_I18N = True
USE_L10N = False
USE_TZ = False

DATE_FORMAT = 'd/m/Y'
DATETIME_FORMAT = 'd/m/Y H:i'
DECIMAL_SEPARATOR = '.'
TIME_ZONE = 'Europe/Warsaw'
LANGUAGE_CODE = 'en'
LANGUAGES = LanguageEnum.choices
LOCALE_PATHS = [
    str(APPS_DIR.path('locale')),
]

# WEBPACK LOADER
WEBPACK_LOADER = {
    'DEFAULT': {
        'CACHE': not DEBUG,
        'BUNDLE_DIR_NAME': 'dist_vue/',  # must end with slash
        'STATS_FILE': str(
            APPS_DIR.path('frontend_cms/static/dist_vue/webpack-stats.json')
        ),
        'POLL_INTERVAL': 0.1,
        'TIMEOUT': None,
        'IGNORE': [r'.+\.hot-update.js', r'.+\.map'],
    },
    'ES5': {
        'CACHE': not DEBUG,
        'BUNDLE_DIR_NAME': 'dist_vue/',  # must end with slash
        'STATS_FILE': str(
            APPS_DIR.path('frontend_cms/static/dist_vue/webpack-stats-es5.json')
        ),
        'POLL_INTERVAL': 0.1,
        'TIMEOUT': None,
        'IGNORE': [r'.+\.hot-update.js', r'.+\.map'],
    },
}


# DJANGO TEST MIGRATIONS
# NOTE: some of `django-test-migrations` settings are provided here,
# so there can be only 1 place to edit them
DTM_IGNORED_MIGRATIONS = frozenset(
    (
        ('authtoken', '0002_auto_20160226_1747'),
        ('reviews', '0003_auto_20200410_1150'),
        ('silk', '0002_auto_update_uuid4_id_field'),
        ('django_fsm_log', '0002_auto_20151207_1521'),
        ('taggit', '0002_auto_20150616_2121'),
        ('taggit', '0005_auto_20220424_2025'),
        ('token_blacklist', '0003_auto_20171017_2007'),
        ('token_blacklist', '0004_auto_20171017_2013'),
        ('token_blacklist', '0006_auto_20171017_2113'),
        ('token_blacklist', '0007_auto_20171017_2214'),
        ('django_fsm_log', '0004_auto_20190131_0341'),
    )
)

# minidb
SERIALIZATION_MODULES = {'cstm_json': 'model_transfers.serializers.cstm_json'}
