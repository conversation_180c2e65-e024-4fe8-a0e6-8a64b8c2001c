import csv
import tempfile

from datetime import datetime
from functools import update_wrapper

from django.contrib import admin
from django.contrib.admin.utils import label_for_field
from django.http import HttpResponse
from django.urls import re_path
from django.utils.functional import cached_property

from openpyxl import Workbook


class ChangeListExporterMixin:
    """
    Add export option for admin view.
    Inspired on https://github.com/bendavis78/django-admin-csv
    """

    change_list_template = 'admin/change_list_exporter.html'
    exporter_exclude = ()

    def get_urls(self):
        def wrap(view):
            def wrapper(*args, **kwargs):
                return self.admin_site.admin_view(view)(*args, **kwargs)

            return update_wrapper(wrapper, view)

        opts = self.model._meta
        url_name = '{}_{}'.format(opts.app_label, opts.model_name)
        urlpatterns = [
            re_path(
                '^csv/$', wrap(self.csv_export), name='{}_csvdownload'.format(url_name)
            ),
            re_path(
                '^xlsx/$',
                wrap(self.xlsx_export),
                name='{}_xlsxdownload'.format(url_name),
            ),
        ]
        return urlpatterns + super(ChangeListExporterMixin, self).get_urls()

    def csv_header_for_field(self, field_name):
        return label_for_field(field_name, self.model, self)

    def get_filename(self):
        model_name = self.model._meta.verbose_name_plural.replace(' ', '_')
        return '{}_{}'.format(
            model_name,
            datetime.now().strftime('%Y-%m-%d_%H%M'),
        )

    def csv_export(self, request, *args, **kwargs):
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename={0}.csv'.format(
            self.get_filename()
        )
        writer = csv.writer(response)
        self._extract_data_and_save(request, writer.writerow)
        return response

    def xlsx_export(self, request, *args, **kwargs):
        wb = Workbook()
        ws = wb.active
        self._extract_data_and_save(request, ws.append)
        with tempfile.NamedTemporaryFile() as f:
            wb.save(f.name)
            f.seek(0)
            response = HttpResponse(
                content=f,
                content_type=(
                    'application/vnd.'
                    'openxmlformats-officedocument.'
                    'spreadsheetml.sheet'
                ),
            )
            response['Content-Disposition'] = 'attachment; filename={0}.xlsx'.format(
                self.get_filename()
            )
            return response

    @cached_property
    def exporter_list(self):
        return [name for name in self.list_display if name not in self.exporter_exclude]

    def _extract_data_and_save(self, request, writer):
        writer(self.exporter_list)
        cl_response = self.changelist_view(request)
        cl = cl_response.context_data.get('cl')
        queryset = cl.get_queryset(request)
        for instance in queryset:
            data = []
            for name in self.exporter_list:
                display_method_name = 'get_{0}_display'.format(name)
                if hasattr(instance, display_method_name):
                    cell = getattr(instance, display_method_name)
                elif hasattr(instance, name):
                    cell = getattr(instance, name)
                elif hasattr(self, name):
                    cell = getattr(self, name)(instance)
                else:
                    raise ValueError('Unknown field: {0}'.format(name))
                if callable(cell):
                    data.append(cell())
                else:
                    data.append(cell)
            writer(data)


class ViewOnlyAdminMixin:
    # Cannot edit existing model
    def has_change_permission(self, request, obj=None):
        return False

    # Cannot delete existing model
    def has_delete_permission(self, request, obj=None):
        return False

    def has_add_permission(self, request, obj=None):
        return False


class DistinctDeleteAdmin(admin.ModelAdmin):
    def delete_queryset(self, request, queryset):
        """
        Fix for delete admin models with distinct on queryset,
        Think about better solution
        """
        self.model.objects.filter(id__in=queryset.values_list('id', flat=True)).delete()
