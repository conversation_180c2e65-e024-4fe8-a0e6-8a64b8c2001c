from django.utils.module_loading import import_string

CUSTOM_CONTEXT_PROCESSORS_CONFIG = {
    'custom.context_processors.settings_cp': ('api', 'accounts'),
    'regions.context_processors.countries': ('admin', 'api', 'accounts'),
    'promotions.context_processors.promotion_cp': (
        'admin',
        'catalogue',
        'production-time',
        'accounts',
    ),
    'frontend_cms.context_processors.t03_availability': ('admin', 'api', 'accounts'),
}


def combined_custom_context(request):
    request_path = request.META['PATH_INFO']
    context = {}

    for processor_path, excluded_keywords in CUSTOM_CONTEXT_PROCESSORS_CONFIG.items():
        if any(keyword in request_path for keyword in excluded_keywords):
            continue

        processor = import_string(processor_path)
        context.update(processor(request))

    return context
