from django.contrib.auth.models import User
from django.core.management import call_command

import pytest

from rest_framework.authtoken.models import Token

from custom.models import GlobalSettings
from promotions.models import (
    Promotion,
    PromotionConfig,
)
from user_profile.choices import UserType
from vouchers.models import Voucher


@pytest.fixture(autouse=True)
def admin():
    return User.objects.create(username='admin', password='pass')


@pytest.mark.django_db
class TestCreateDeveloperData:
    @pytest.fixture(autouse=True)
    def setup(self, admin, voucher_group, region_factory):
        region_factory(germany=True)

    def test_turns_off_live_payment(self):
        GlobalSettings.set_payment_live()
        assert GlobalSettings.objects.first().is_live_payment is True

        call_command('create_developer_data')

        assert GlobalSettings.objects.first().is_live_payment is False

    def test_creates_promotion_config_with_voucher(self, voucher_group):
        call_command('create_developer_data')

        voucher = Voucher.objects.get(code='gosia20')
        promotion = Promotion.objects.get(promo_code=voucher)
        assert voucher_group in promotion.promo_group.all()
        assert PromotionConfig.objects.filter(promotion=promotion).exists()

    def test_creates_influencers_voucher(self, voucher_group):
        call_command('create_developer_data')

        voucher = Voucher.objects.filter(code=Voucher.INFLUENCER_GENERIC_CODE)
        assert voucher.exists()

    def test_creates_parametric_tests_user(self):
        call_command('create_developer_data')

        user = User.objects.get(username='<EMAIL>')
        assert user.profile.user_type == UserType.STAFF
        assert Token.objects.filter(user=user).exists()
