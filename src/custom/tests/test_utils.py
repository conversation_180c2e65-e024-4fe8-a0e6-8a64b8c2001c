import enum

from datetime import (
    date,
    timedelta,
)
from decimal import Decimal
from unittest.mock import patch

import pytest

from custom.utils.adyen import get_current_payment_settings
from custom.utils.fields import Nullable<PERSON>numField
from custom.utils.import_object import import_object
from custom.utils.strings import (
    decrypt_string,
    encrypt_string,
)


class TestGetCurrentPaymentSettings:
    sandbox_text = 'mr sandbox, bring me a dream'
    live_text = "It's ALIIIIIVE"

    @pytest.fixture(autouse=True)
    def use_dummy_adyen(self, settings):
        settings.ADYEN = self.sandbox_text
        settings.ADYEN_LIVE = self.live_text

    @patch('custom.utils.adyen.GlobalSettings.live_payment_switch')
    def test_returns_sandbox_settings_if_live_switch_false(self, mock_live):
        mock_live.return_value = False
        assert get_current_payment_settings() == self.sandbox_text

    @patch('custom.utils.adyen.GlobalSettings.live_payment_switch')
    def test_returns_live_settings_if_live_switch_true(self, mock_live):
        mock_live.return_value = True
        assert get_current_payment_settings() == self.live_text


class TestImportObject:
    def test_raises_ValueError_when_object_path_is_relative(self):
        with pytest.raises(ValueError):
            import_object('.test_enums._TestingColor')

    def test_raises_ImportError_when_object_path_module_part_is_invalid(self):
        with pytest.raises(ImportError):
            import_object('ni.knights.JohnCleese')

    def test_raises_ImportError_when_object_path_object_name_part_is_invalid(self):
        with pytest.raises(ImportError):
            import_object('user_profile.models.NiKnight')

    def test_returns_imported_object_when_object_path_is_module(self):
        orders_models_module = import_object('orders.models')
        assert hasattr(orders_models_module, 'Order')
        assert hasattr(orders_models_module, 'OrderItem')


class TestStringEncryption:
    @pytest.fixture(autouse=True)
    def use_dummy_secret_key(self, settings):
        settings.SECRET_KEY = 'no^!uy6y0wfv%=c*uuiew3a$!s4$d060o&!@nn87b8)@d!e^qf'

    def test_encrypt_string_works_as_with_pycrypto(self):
        encrypted_value = encrypt_string('nobody expects  ')
        assert encrypted_value == b'd07a55757685cd18fd547d4fa7d80c49'

    def test_decrypt_string_works_as_with_pycrypto(self):
        decrypted_value = decrypt_string('6107f32486e9627f819b6afd4b7131de')
        assert decrypted_value == b'expects'

    def test_encrypt_decrypt_string_returns_input_string(self):
        enc_dec_value = decrypt_string(encrypt_string('spanish'))
        assert enc_dec_value == b'spanish'


@pytest.mark.django_db
class TestGetVatFactory:
    @pytest.fixture()
    def germany(self, country_factory):
        return country_factory(name='germany', region__name='germany')

    @pytest.fixture()
    def france(self, country_factory):
        return country_factory(name='france', region__name='france')

    @pytest.fixture()
    def _create_region_vat_de(self, germany, country_region_vat_factory):
        date_start = date.today()
        country_region_vat_factory(
            country=germany,
            vat=Decimal('0.1'),
            valid_date_range=(date_start, date_start + timedelta(days=1)),
        )
        country_region_vat_factory(
            country=germany,
            vat=Decimal('0.5'),
            valid_date_range=(
                date_start + timedelta(days=1),
                date_start + timedelta(days=2),
            ),
        )


class MockEnum(enum.StrEnum):
    VALUE_1 = '1'
    VALUE_NULL = '0'


class MockEnumWithMissing(enum.StrEnum):
    VALUE_1 = '1'
    VALUE_NULL = '0'

    @classmethod
    def _missing_(cls, value):
        return cls.VALUE_NULL


class TestNullableEnumField:
    @pytest.fixture()
    def mock_enum_field(self):
        return NullableEnumField(
            enum_class=MockEnum,
            null_value=MockEnum.VALUE_NULL,
        )

    @pytest.fixture()
    def mock_enum_with_missing_field(self):
        return NullableEnumField(enum_class=MockEnumWithMissing)

    @pytest.mark.parametrize(
        ('value', 'data'),
        [
            ('1', '1'),
            ('0', None),
        ],
    )
    def test_to_representation(self, mock_enum_field, value, data):
        assert mock_enum_field.to_representation(value) == data

    @pytest.mark.parametrize(
        ('value', 'data'),
        [
            ('1', '1'),
            ('0', None),
        ],
    )
    def test_to_representation_missing(self, mock_enum_with_missing_field, value, data):
        assert mock_enum_with_missing_field.to_representation(value) == data

    @pytest.mark.parametrize(
        ('value', 'data'),
        [
            ('1', '1'),
            ('0', '0'),
            ('0', None),
        ],
    )
    def test_to_internal_value(self, mock_enum_field, value, data):
        assert mock_enum_field.to_internal_value(data) == value

    def test_to_internal_value_raises_error_with_wrong_data(self, mock_enum_field):
        with pytest.raises(ValueError):
            mock_enum_field.to_internal_value('wrong_data')

    @pytest.mark.parametrize(
        ('value', 'data'),
        [
            ('1', '1'),
            ('0', '0'),
            ('0', None),
            ('0', 'wrong_value'),
        ],
    )
    def test_to_internal_value_missing(self, mock_enum_with_missing_field, value, data):
        assert mock_enum_with_missing_field.to_internal_value(data) == value
