from django import forms

from customer_service.models import CSCorrectionRequest
from invoice.enums import InvoiceItemTag
from invoice.utils import update_invoice_items_and_corrected_notes


class CSCorrectionRequestForm(forms.ModelForm):
    tag = forms.ChoiceField(choices=InvoiceItemTag.choices())
    regenerate_correction_invoice_pdf = forms.BooleanField(
        required=False,
        initial=True,
    )

    class Meta:
        model = CSCorrectionRequest
        fields = ('tag',)

    def save(self, commit=True):
        correction_request = super().save(commit)
        correction_invoice = update_invoice_items_and_corrected_notes(
            correction_request.correction_invoice,
            correction_request.tag,
        )
        if self.cleaned_data['regenerate_correction_invoice_pdf']:
            correction_invoice.cached_to_dict = {}
            correction_invoice.save(update_fields=['cached_to_dict'])
            correction_invoice.create_pdf()
