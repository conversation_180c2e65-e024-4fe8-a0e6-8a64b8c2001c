import datetime

from django import forms
from django.db.models import Q
from django.utils import timezone

import django_filters as filters

from django_filters.constants import EMPTY_VALUES

from carts.models import Cart
from customer_service.models import (
    CSCorrectionRequest,
    CSOrder,
    CSUserProfile,
)
from invoice.models import Invoice
from orders.models import Order
from user_profile.models import UserProfile


class SynonymousFieldsFilterMixin:
    def __init__(self, field_names, *args, **kwargs):
        super(SynonymousFieldsFilterMixin, self).__init__(*args, **kwargs)
        self.field_names = field_names

    def filter(self, qs, value):
        if value in EMPTY_VALUES:
            return qs

        filter_conditions = Q()
        for field_name in self.field_names:
            lookup = '%s__%s' % (field_name, self.lookup_expr)
            filter_conditions |= Q(**{lookup: value})

        if self.distinct:
            qs = qs.distinct()

        return qs.filter(filter_conditions)


class SynonymousCharFieldsFilter(
    SynonymousFieldsFilterMixin,
    filters.CharFilter,
):
    def __init__(self, field_names, **kwargs):
        if kwargs.get('min_length', None) is None:
            kwargs['min_length'] = 3
        super(SynonymousCharFieldsFilter, self).__init__(field_names, **kwargs)


class ArrayNumberFieldsFilter(
    filters.NumberFilter,
):
    field_class = forms.IntegerField

    def filter(self, qs, value):
        if value in EMPTY_VALUES:
            return qs
        if self.distinct:
            qs = qs.distinct()
        lookup = '%s__%s' % (self.field_name, self.lookup_expr)
        qs = self.get_method(qs)(**{lookup: [value]})
        return qs


class ProxySearchMixin(filters.FilterSet):
    @property
    def qs(self):
        queryset = super().qs
        return self.Meta.direct_model.objects.filter(
            pk__in=queryset.values_list('pk', flat=True)
        )


class CSOrderSearchFilterSet(ProxySearchMixin):
    q_orderid_direct = filters.NumberFilter(
        field_name='id',
        method='filter_by_orderid',
        help_text='Go directly to order id..',
    )
    q_firstname = SynonymousCharFieldsFilter(
        field_name='first_name',
        field_names=('first_name', 'invoice_first_name'),
        lookup_expr='iexact',
    )
    q_lastname = SynonymousCharFieldsFilter(
        field_name='last_name',
        field_names=('last_name', 'invoice_last_name'),
        lookup_expr='iexact',
    )
    q_company_name = SynonymousCharFieldsFilter(
        field_name='company_name',
        label='company name',
        field_names=('company_name', 'invoice_company_name'),
        lookup_expr='icontains',
    )
    q_email = SynonymousCharFieldsFilter(
        field_name='email',
        field_names=('email', 'invoice_email'),
        lookup_expr='iexact',
    )
    q_phone = filters.CharFilter(
        field_name='phone',
        label='phone number',
        lookup_expr='icontains',
        min_length=3,
    )
    q_city = SynonymousCharFieldsFilter(
        field_name='city',
        label='city',
        field_names=('city', 'invoice_city'),
        lookup_expr='icontains',
    )
    q_promo_text = SynonymousCharFieldsFilter(
        field_name='promo_text',
        label='promo text',
        field_names=('promo_text',),
        lookup_expr='icontains',
    )
    q_street = SynonymousCharFieldsFilter(
        field_name='address',
        label='street address',
        field_names=(
            'street_address_1',
            'street_address_2',
            'invoice_street_address_1',
            'invoice_street_address_2',
        ),
        lookup_expr='icontains',
    )
    q_productid = ArrayNumberFieldsFilter(
        field_name='product_ids', label='product id', lookup_expr='contains'
    )

    @property
    def qs(self):
        queryset = super().qs
        if not any(self.form.cleaned_data.values()):
            return queryset.none()
        return queryset

    class Meta:
        direct_model = Order
        model = CSOrder
        fields = (
            'q_orderid_direct',
            'q_firstname',
            'q_lastname',
            'q_company_name',
            'q_email',
            'q_phone',
            'q_city',
            'q_street',
            'q_productid',
            'q_promo_text',
        )

    def filter_by_orderid(self, queryset, name, value):
        proxy_queryset = self.Meta.model.objects.filter(id=value)
        if proxy_queryset.exists():
            return proxy_queryset
        return self.Meta.direct_model.objects.filter(id=value)


class CSUserProfileSearchFilterSet(ProxySearchMixin):
    q_firstname = filters.CharFilter(
        field_name='first_name',
        lookup_expr='iexact',
        min_length=3,
    )
    q_lastname = filters.CharFilter(
        field_name='last_name',
        lookup_expr='iexact',
        min_length=3,
    )
    q_email = SynonymousCharFieldsFilter(
        field_name='email',
        field_names=('user_email', 'user_username'),
        lookup_expr='iexact',
    )
    q_phone = filters.CharFilter(
        field_name='phone',
        label='phone number',
        lookup_expr='icontains',
    )
    q_city = SynonymousCharFieldsFilter(
        field_name='city',
        label='city',
        field_names=('city', 'invoice_city'),
        lookup_expr='icontains',
    )
    q_street = SynonymousCharFieldsFilter(
        field_name='address',
        label='street address',
        field_names=(
            'street_address_1',
            'street_address_2',
            'invoice_street_address_1',
            'invoice_street_address_2',
        ),
        lookup_expr='icontains',
    )

    @property
    def qs(self):
        queryset = super().qs.select_related('user')
        if not any(self.form.cleaned_data.values()):
            return queryset.none()
        return queryset

    class Meta:
        direct_model = UserProfile
        model = CSUserProfile
        fields = (
            'q_firstname',
            'q_lastname',
            'q_email',
            'q_phone',
            'q_city',
            'q_street',
        )


class CSInvoiceSearchFilterSet(filters.FilterSet):
    q_invoice_pretty_id = filters.CharFilter(
        field_name='pretty_id',
        label='Invoice pretty ID',
        lookup_expr='icontains',
        min_length=3,
        help_text='Invoice pretty ID',
    )

    @property
    def qs(self):
        queryset = super().qs
        if not any(self.form.cleaned_data.values()):
            return queryset.none()
        return queryset

    class Meta(object):
        model = Invoice
        fields = ('q_invoice_pretty_id',)


class CSCartSearchFilterSet(filters.FilterSet):
    q_promo_text = SynonymousCharFieldsFilter(
        field_name='used_promo',
        label='promo text',
        field_names=('used_promo',),
        lookup_expr='code__icontains',
    )
    q_cartid = filters.NumberFilter(
        field_name='id',
        method='filter_by_cart_id',
        help_text='Go directly to cart id..',
    )

    @property
    def qs(self):
        queryset = super().qs
        if not any(self.form.cleaned_data.values()):
            return queryset.none()
        return queryset

    def filter_by_cart_id(self, queryset, name, value):
        return queryset.filter(id=value)

    class Meta(object):
        model = Cart
        fields = ('q_promo_text', 'q_cartid')


class CSCorrectionRequestFilterSet(filters.FilterSet):
    from_last_days = filters.NumberFilter(
        field_name='updated_at',
        method='filter_from_last_days',
    )

    class Meta(object):
        model = CSCorrectionRequest
        fields = ('from_last_days',)

    def filter_from_last_days(self, queryset, name, value):
        datetime_n_days_ago = timezone.now() - datetime.timedelta(days=int(value))
        return queryset.filter(updated_at__gte=datetime_n_days_ago)
