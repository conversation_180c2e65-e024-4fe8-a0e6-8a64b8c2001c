from django.contrib import admin

from custom.filters import MultipleChoiceDropdownFilter
from orders.choices import OrderSource
from regions.models import Country


class ReturningCustomerOrderCountFilter(admin.SimpleListFilter):
    title = 'Order Count'
    parameter_name = 'order_count'

    def lookups(self, request, model_admin):
        return (
            (1, '1'),
            (2, '2'),
            (3, '3'),
            (4, '4'),
            (5, '5'),
            (6, '6'),
            (7, '7'),
            (8, '8'),
            (9, '9'),
            (10, '10'),
            (11, '10+'),
        )

    def queryset(self, request, queryset):
        if not self.value():
            return queryset
        if self.value() == '11':
            return queryset.filter(
                orders_data__customer_order_details__orders_count__gt=10
            )
        return queryset.filter(
            orders_data__customer_order_details__orders_count=int(self.value())
        )


class ReturningCustomerHasAssemblyFilter(admin.SimpleListFilter):
    title = 'Has assembly'
    parameter_name = 'assembly'

    def lookups(self, request, model_admin):
        return (
            (1, 'Yes'),
            (2, 'No'),
        )

    def queryset(self, request, queryset):
        if not self.value():
            return queryset
        if int(self.value()) == 1:
            return queryset.filter(
                orders_data__customer_order_details__assembly__contains=True
            )
        if int(self.value()) == 2:
            return queryset.filter(
                orders_data__customer_order_details__assembly__contains=False
            )


class ReturningCustomerOrderSourceFilter(admin.SimpleListFilter):
    title = 'Order source'
    parameter_name = 'order_source'

    def lookups(self, request, model_admin):
        return OrderSource.choices

    def queryset(self, request, queryset):
        if not self.value():
            return queryset
        return queryset.filter(
            orders_data__customer_order_details__order_source__contains=int(
                self.value()
            )
        )


class ReturningCustomerCountriesFilter(MultipleChoiceDropdownFilter):
    title = 'Countries'
    parameter_name = 'country'

    def lookups(self, request, model_admin):
        return ((obj.name, obj.name) for obj in self.get_queryset())

    @staticmethod
    def get_queryset():
        return Country.objects.all()

    def queryset(self, request, queryset):
        if not self.value():
            return queryset

        if len(self.value().split(',')) > 1:
            countries_query = self.value().split(',')
        else:
            countries_query = self.value()

        return queryset.filter(
            orders_data__customer_order_details__countries__contains=countries_query
        )
