from decimal import Decimal

from django.conf import settings

from apiclient.client import APIClient
from apiclient.decorates import endpoint

from custom.internal_api.clients import JsonAPIClient
from customer_service.internal_api.serializers import TransportCostSerializer


@endpoint(base_url=settings.LOGISTIC_URL)
class GetTransportCostBaseOnValueAndWeightAPIEndpoint(APIClient):
    detail = 'internal-api/v1/get-transport-cost-base-on-value-and-weight/'


class GetTransportCostBaseOnValueAndWeightAPIClient(JsonAPIClient):
    def get_cost(self, country_name, cost, weight, packages):
        data = TransportCostSerializer(
            {
                'country_name': country_name,
                'cost': cost,
                'weight': weight,
                'packages': packages,
            }
        ).data
        response = self.post(
            GetTransportCostBaseOnValueAndWeightAPIEndpoint.detail,
            data=data,
        )
        transport_cost = Decimal(response['transport_cost'])
        return transport_cost
