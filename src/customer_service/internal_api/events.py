from dataclasses import dataclass
from typing import TYPE_CHECKING

from events.domain_events.internal_events import InternalEventBase
from orders.internal_api.clients import LogisticOrderAPIClient

if TYPE_CHECKING:
    from orders.models import Order


@dataclass
class LogisticOrderMailingDisabledEvent(InternalEventBase):
    order: 'Order'
    logistic_order_id: int

    @property
    def properties(self) -> dict:
        properties = {
            'logistic_order': self.logistic_order_id,
            'order': self.order.id,
        }
        return properties

    def execute(self):
        logistic_order_api = LogisticOrderAPIClient()
        logistic_order = logistic_order_api.disable_mailing(self.logistic_order_id)
        self.order.set_serialized_logistic_info(logistic_order)
        return self.order
