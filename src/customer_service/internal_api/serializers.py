from rest_framework import serializers


class PackagingSerializer(serializers.Serializer):
    dim_x = serializers.IntegerField()
    dim_y = serializers.IntegerField()
    dim_z = serializers.IntegerField()
    id_production = serializers.CharField()
    weight = serializers.FloatField()


class TransportCostSerializer(serializers.Serializer):
    packages = PackagingSerializer(many=True)
    cost = serializers.DecimalField(max_digits=8, decimal_places=2)
    weight = serializers.DecimalField(max_digits=8, decimal_places=2)
    country_name = serializers.CharField(max_length=255)
