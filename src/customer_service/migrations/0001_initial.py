# Generated by Django 1.11.24 on 2020-02-17 21:54
from __future__ import unicode_literals

import django.db.models.deletion

from django.conf import settings
from django.db import (
    migrations,
    models,
)

import regions.mixins


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='CSActivityLog',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'activity_context',
                    models.CharField(blank=True, max_length=1024, null=True),
                ),
                (
                    'activity_type',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, 'logged out'),
                            (0, 'logged in'),
                            (2, 'viewed a page'),
                            (3, 'queried'),
                            (4, 'payment link'),
                        ]
                    ),
                ),
                ('timestamp', models.DateTimeField(auto_now_add=True, db_index=True)),
            ],
        ),
        migrations.CreateModel(
            name='CSCorrectionAddressRequest',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'first_name',
                    models.CharField(
                        blank=True, max_length=256, null=True, verbose_name='first name'
                    ),
                ),
                (
                    'last_name',
                    models.CharField(
                        blank=True, max_length=256, null=True, verbose_name='last name'
                    ),
                ),
                (
                    'street_address_1',
                    models.CharField(
                        blank=True,
                        max_length=256,
                        null=True,
                        verbose_name='street address 1',
                    ),
                ),
                (
                    'street_address_2',
                    models.CharField(
                        blank=True,
                        max_length=256,
                        null=True,
                        verbose_name='street address 2',
                    ),
                ),
                (
                    'company_name',
                    models.CharField(
                        blank=True,
                        max_length=256,
                        null=True,
                        verbose_name='company name',
                    ),
                ),
                (
                    'city',
                    models.CharField(
                        blank=True, max_length=256, null=True, verbose_name='city'
                    ),
                ),
                (
                    'postal_code',
                    models.CharField(
                        blank=True, max_length=20, null=True, verbose_name='postal code'
                    ),
                ),
                (
                    'country',
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=50,
                        null=True,
                        verbose_name='country',
                    ),
                ),
                (
                    'vat',
                    models.CharField(
                        blank=True,
                        max_length=256,
                        null=True,
                        verbose_name='tax id (vat)',
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='CSCorrectionRequest',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'correction_amount_gross',
                    models.DecimalField(decimal_places=2, max_digits=12, null=True),
                ),
                ('correction_context', models.TextField(blank=True, null=True)),
                (
                    'correction_vat',
                    models.PositiveSmallIntegerField(
                        blank=True,
                        choices=[
                            (0, 'Normal VAT'),
                            (1, 'VAT 0% - WDT'),
                            (2, 'Switzerland VAT'),
                        ],
                        help_text='To change vat type',
                        null=True,
                    ),
                ),
                ('invoice_reason', models.TextField()),
                (
                    'status',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, 'new'),
                            (1, 'accepted'),
                            (2, 'rejected'),
                            (3, 'sent'),
                        ],
                        default=0,
                    ),
                ),
                (
                    'type_cs',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, 'Amount'),
                            (1, 'Items'),
                            (2, 'Address'),
                            (3, 'Neutralization'),
                            (4, 'Normal VAT to B2B'),
                            (5, 'B2B to normal VAT'),
                            (6, 'Add assembly'),
                            (7, 'Recalculation'),
                        ],
                        default=0,
                    ),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
        ),
        migrations.CreateModel(
            name='CSCustomShelf',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('description_long', models.TextField(blank=True, null=True)),
                ('description_short', models.CharField(max_length=1024)),
                ('price_gross', models.DecimalField(decimal_places=2, max_digits=12)),
                ('enabled', models.BooleanField(default=True)),
                (
                    'region_price_gross',
                    models.DecimalField(decimal_places=2, default=0, max_digits=12),
                ),
            ],
            bases=(regions.mixins.RegionalizedMixin, models.Model),
        ),
        migrations.CreateModel(
            name='CSItemFollow',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('item_object_id', models.CharField(db_index=True, max_length=255)),
                ('timestamp', models.DateTimeField(auto_now_add=True, db_index=True)),
            ],
        ),
        migrations.CreateModel(
            name='CSUnsuccessfulPayments',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'status',
                    models.IntegerField(
                        choices=[(0, 'New'), (1, 'Payed'), (2, 'Rejected')], default=0
                    ),
                ),
                ('note', models.TextField(blank=True, null=True)),
                ('timestamp', models.DateTimeField(auto_now_add=True, db_index=True)),
            ],
        ),
        migrations.CreateModel(
            name='CSUserToIntercomLink',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('intercom_user_id', models.CharField(max_length=1024)),
                ('manual', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                (
                    'user',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
