# Generated by Django 1.11.24 on 2020-02-17 21:54
from __future__ import unicode_literals

import django.db.models.deletion

from django.conf import settings
from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('contenttypes', '0002_remove_content_type_name'),
        ('orders', '0001_initial'),
        ('regions', '0001_initial'),
        ('invoice', '0002_dependant_foreign_keys'),
        ('gallery', '0001_initial'),
        ('customer_service', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='csunsuccessfulpayments',
            name='order',
            field=models.OneToOneField(
                on_delete=django.db.models.deletion.CASCADE, to='orders.Order'
            ),
        ),
        migrations.AddField(
            model_name='csunsuccessfulpayments',
            name='user',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name='csitemfollow',
            name='item_content_type',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='item',
                to='contenttypes.ContentType',
            ),
        ),
        migrations.AddField(
            model_name='csitemfollow',
            name='user',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
        migrations.AddField(
            model_name='cscustomshelf',
            name='region',
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to='regions.Region',
            ),
        ),
        migrations.AddField(
            model_name='cscustomshelf',
            name='shelf',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to='gallery.Jetty'
            ),
        ),
        migrations.AddField(
            model_name='cscorrectionrequest',
            name='cs_correction_request',
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to='customer_service.CSCorrectionAddressRequest',
            ),
        ),
        migrations.AddField(
            model_name='cscorrectionrequest',
            name='deleted_invoice_items',
            field=models.ManyToManyField(blank=True, to='invoice.InvoiceItem'),
        ),
        migrations.AddField(
            model_name='cscorrectionrequest',
            name='invoice',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to='invoice.Invoice'
            ),
        ),
        migrations.AddField(
            model_name='cscorrectionrequest',
            name='issuer',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                related_name='issued_correction_requests',
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name='cscorrectionrequest',
            name='reviewer',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='reviewed_correction_requests',
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AddField(
            model_name='csactivitylog',
            name='user',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL
            ),
        ),
    ]
