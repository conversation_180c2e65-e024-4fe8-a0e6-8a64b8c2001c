# Generated by Django 1.11.24 on 2020-08-14 10:31
from __future__ import unicode_literals

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0003_update_orderitem_free_return_on_delete'),
        ('customer_service', '0002_dependant_foreign_keys'),
    ]

    operations = [
        migrations.CreateModel(
            name='RefundInfo',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('email', models.EmailField(max_length=254)),
                (
                    'status',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, 'NEW'),
                            (2, 'WAITING'),
                            (3, 'READY_TO_REFUND'),
                            (4, 'REFUNDED'),
                        ],
                        default=1,
                    ),
                ),
                (
                    'order',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE, to='orders.Order'
                    ),
                ),
            ],
        ),
    ]
