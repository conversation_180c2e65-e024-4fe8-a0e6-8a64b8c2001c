# Generated by Django 1.11.29 on 2020-12-03 09:43
from __future__ import unicode_literals

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('customer_service', '0003_refundinfo'),
    ]

    operations = [
        migrations.AddField(
            model_name='cscorrectionrequest',
            name='tag',
            field=models.IntegerField(
                blank=True,
                choices=[
                    (1, 'DISCOUNT QUALITY DISSATISFACTION'),
                    (2, 'DISCOUNT REFERRAL'),
                    (3, 'DISCOUNT PRICE ADJUSTMENT DUE TO ANY OTHER REASON'),
                    (4, 'CANCELLATION'),
                    (5, 'FREE RETURN'),
                    (6, 'CHANGE OF ADDRESS'),
                    (7, 'WRONG VAT'),
                    (8, 'PRICE INCREASE'),
                    (9, 'WRONG NAME'),
                    (10, 'OTHER INVOICE ADJUSTMENT'),
                    (11, 'ADDITIONAL SERVICE'),
                ],
                default=None,
                null=True,
            ),
        ),
        migrations.AlterField(
            model_name='cscorrectionrequest',
            name='cs_correction_request',
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to='customer_service.CSCorrectionAddressRequest',
            ),
        ),
    ]
