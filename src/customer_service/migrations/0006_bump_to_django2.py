# Generated by Django 2.0.13 on 2021-01-15 15:17

import django.db.models.deletion

from django.conf import settings
from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('customer_service', '0005_remove_invoice_reason'),
    ]

    operations = [
        migrations.AlterField(
            model_name='csitemfollow',
            name='item_content_type',
            field=models.ForeignKey(
                limit_choices_to=models.Q(
                    models.Q(('app_label', 'invoice'), ('model', 'invoice')),
                    models.Q(('app_label', 'orders'), ('model', 'order')),
                    models.Q(('app_label', 'producers'), ('model', 'product')),
                    _connector='OR',
                ),
                on_delete=django.db.models.deletion.CASCADE,
                related_name='item',
                to='contenttypes.ContentType',
            ),
        ),
        migrations.AlterField(
            model_name='csitemfollow',
            name='user',
            field=models.ForeignKey(
                limit_choices_to={'profile__user_type': 4},
                on_delete=django.db.models.deletion.CASCADE,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
        migrations.AlterField(
            model_name='csunsuccessfulpayments',
            name='user',
            field=models.ForeignKey(
                limit_choices_to={'profile__user_type__in': [4, 0]},
                on_delete=django.db.models.deletion.CASCADE,
                to=settings.AUTH_USER_MODEL,
            ),
        ),
    ]
