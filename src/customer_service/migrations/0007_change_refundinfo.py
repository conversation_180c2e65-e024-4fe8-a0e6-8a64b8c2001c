# Generated by Django 1.11.29 on 2021-03-11 16:53
from __future__ import unicode_literals

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('customer_service', '0006_bump_to_django2'),
    ]

    operations = [
        migrations.AddField(
            model_name='refundinfo',
            name='retry',
            field=models.PositiveSmallIntegerField(default=20),
        ),
        migrations.AlterField(
            model_name='refundinfo',
            name='order',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                to='orders.Order',
            ),
        ),
        migrations.AlterField(
            model_name='refundinfo',
            name='status',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (0, 'NOT_FOUND'),
                    (1, 'NEW'),
                    (2, 'WAITING'),
                    (3, 'READY_TO_REFUND'),
                    (4, 'REFUNDED'),
                ],
                default=1,
            ),
        ),
    ]
