# Generated by Django 3.1.8 on 2021-04-21 16:41

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('customer_service', '0007_change_refundinfo'),
    ]

    operations = [
        migrations.AlterField(
            model_name='refundinfo',
            name='retry',
            field=models.PositiveSmallIntegerField(
                default=20,
                help_text=(
                    'Only to RefundInfo with status Not found, Check how many times '
                    'we can try to match email with order.'
                ),
            ),
        ),
    ]
