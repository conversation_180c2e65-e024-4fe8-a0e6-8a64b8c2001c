# Generated by Django 3.2.9 on 2022-03-15 23:17

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('invoice', '0009_invoiceitem_recycle_tax_value'),
        ('customer_service', '0011_returningcustomer'),
    ]

    operations = [
        migrations.AddField(
            model_name='cscorrectionrequest',
            name='correction_invoice',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='correction_correction_requests',
                to='invoice.invoice',
            ),
        ),
    ]
