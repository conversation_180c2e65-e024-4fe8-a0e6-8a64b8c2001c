# Generated by Django 3.2.12 on 2022-08-01 14:11

import django.contrib.postgres.fields
import django.db.models.functions.text

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('customer_service', '0013_alter_csactivitylog_activity_type'),
    ]

    operations = [
        migrations.CreateModel(
            name='CSOrder',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('order_pretty_id', models.CharField(blank=True, max_length=50)),
                (
                    'status',
                    models.IntegerField(
                        choices=[
                            (0, 'cancelled'),
                            (1, 'draft'),
                            (2, 'payment pending'),
                            (3, 'in production'),
                            (4, 'shipped'),
                            (5, 'delivered'),
                            (6, 'payment failed'),
                            (7, 'to be shipped'),
                            (9, 'cart'),
                        ],
                        db_index=True,
                        default=1,
                    ),
                ),
                (
                    'first_name',
                    models.Char<PERSON>ield(
                        blank=True, max_length=256, verbose_name='first name'
                    ),
                ),
                (
                    'invoice_first_name',
                    models.CharField(
                        blank=True, max_length=256, verbose_name='invoice first name'
                    ),
                ),
                (
                    'last_name',
                    models.CharField(
                        blank=True, max_length=256, verbose_name='last name'
                    ),
                ),
                (
                    'invoice_last_name',
                    models.CharField(
                        blank=True, max_length=256, verbose_name='invoice last name'
                    ),
                ),
                (
                    'company_name',
                    models.CharField(
                        blank=True, max_length=256, verbose_name='company name'
                    ),
                ),
                (
                    'invoice_company_name',
                    models.CharField(
                        blank=True, max_length=256, verbose_name='company name'
                    ),
                ),
                ('email', models.EmailField(blank=True, max_length=254)),
                (
                    'invoice_email',
                    models.CharField(max_length=256, verbose_name='invoice email'),
                ),
                (
                    'phone',
                    models.CharField(
                        blank=True,
                        db_index=True,
                        max_length=30,
                        verbose_name='phone number',
                    ),
                ),
                (
                    'city',
                    models.CharField(blank=True, max_length=256, verbose_name='city'),
                ),
                (
                    'invoice_city',
                    models.CharField(
                        blank=True, max_length=256, verbose_name='invoice city'
                    ),
                ),
                (
                    'street_address_1',
                    models.CharField(
                        blank=True, max_length=256, verbose_name='street address 1'
                    ),
                ),
                (
                    'invoice_street_address_1',
                    models.CharField(
                        blank=True,
                        max_length=256,
                        verbose_name='invoice street address 1',
                    ),
                ),
                (
                    'street_address_2',
                    models.CharField(
                        blank=True, max_length=256, verbose_name='street address 2'
                    ),
                ),
                (
                    'invoice_street_address_2',
                    models.CharField(
                        blank=True,
                        max_length=256,
                        verbose_name='invoice street address 2',
                    ),
                ),
                (
                    'product_ids',
                    django.contrib.postgres.fields.ArrayField(
                        base_field=models.IntegerField(), size=None
                    ),
                ),
                ('owner_username', models.CharField(max_length=150)),
            ],
        ),
        migrations.CreateModel(
            name='CSUserProfile',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'user_type',
                    models.IntegerField(
                        choices=[
                            (0, 'staff'),
                            (1, 'producer'),
                            (2, 'customer'),
                            (3, 'guest'),
                            (4, 'customer service'),
                        ],
                        default=2,
                    ),
                ),
                ('user_id', models.IntegerField()),
                ('email', models.EmailField(blank=True, max_length=254)),
                (
                    'first_name',
                    models.CharField(
                        blank=True, max_length=256, verbose_name='first name'
                    ),
                ),
                (
                    'last_name',
                    models.CharField(
                        blank=True, max_length=256, verbose_name='last name'
                    ),
                ),
                ('user_email', models.EmailField(blank=True, max_length=254)),
                ('user_username', models.CharField(max_length=150)),
                (
                    'company_name',
                    models.CharField(
                        blank=True, max_length=256, verbose_name='company name'
                    ),
                ),
                (
                    'phone',
                    models.CharField(
                        blank=True, max_length=30, verbose_name='phone number'
                    ),
                ),
                (
                    'city',
                    models.CharField(blank=True, max_length=256, verbose_name='city'),
                ),
                (
                    'invoice_city',
                    models.CharField(
                        blank=True, max_length=256, verbose_name='invoice city'
                    ),
                ),
                (
                    'street_address_1',
                    models.CharField(
                        blank=True, max_length=256, verbose_name='street address 1'
                    ),
                ),
                (
                    'invoice_street_address_1',
                    models.CharField(
                        blank=True, max_length=256, verbose_name='street address 1'
                    ),
                ),
                (
                    'street_address_2',
                    models.CharField(
                        blank=True, max_length=256, verbose_name='street address 2'
                    ),
                ),
                (
                    'invoice_street_address_2',
                    models.CharField(
                        blank=True, max_length=256, verbose_name='street address 2'
                    ),
                ),
            ],
        ),
        migrations.AddIndex(
            model_name='csuserprofile',
            index=models.Index(
                django.db.models.functions.text.Upper('first_name'),
                name='upper_csuserprofile_first_name',
            ),
        ),
        migrations.AddIndex(
            model_name='csuserprofile',
            index=models.Index(
                django.db.models.functions.text.Upper('last_name'),
                name='upper_csuserprofile_last_name',
            ),
        ),
        migrations.AddIndex(
            model_name='csuserprofile',
            index=models.Index(
                django.db.models.functions.text.Upper('email'),
                name='upper_csuserprofile_email',
            ),
        ),
        migrations.AddIndex(
            model_name='csuserprofile',
            index=models.Index(
                django.db.models.functions.text.Upper('user_email'),
                name='upper_csuserprofile_user_email',
            ),
        ),
        migrations.AddIndex(
            model_name='csuserprofile',
            index=models.Index(
                django.db.models.functions.text.Upper('user_username'),
                name='upper_csuserprofile_username',
            ),
        ),
        migrations.AddIndex(
            model_name='csuserprofile',
            index=models.Index(
                django.db.models.functions.text.Upper('phone'),
                name='upper_csuserprofile_phone',
            ),
        ),
        migrations.AddIndex(
            model_name='csorder',
            index=models.Index(
                django.db.models.functions.text.Upper('first_name'),
                name='upper_csorder_first_name',
            ),
        ),
        migrations.AddIndex(
            model_name='csorder',
            index=models.Index(
                django.db.models.functions.text.Upper('invoice_first_name'),
                name='upper_cs_invoice_first_name',
            ),
        ),
        migrations.AddIndex(
            model_name='csorder',
            index=models.Index(
                django.db.models.functions.text.Upper('last_name'),
                name='upper_csorder_last_name',
            ),
        ),
        migrations.AddIndex(
            model_name='csorder',
            index=models.Index(
                django.db.models.functions.text.Upper('invoice_last_name'),
                name='upper_cs_invoice_last_name',
            ),
        ),
        migrations.AddIndex(
            model_name='csorder',
            index=models.Index(
                django.db.models.functions.text.Upper('email'),
                name='upper_csorder_email',
            ),
        ),
        migrations.AddIndex(
            model_name='csorder',
            index=models.Index(
                django.db.models.functions.text.Upper('invoice_email'),
                name='upper_csorder_invoice_email',
            ),
        ),
        migrations.AddIndex(
            model_name='csorder',
            index=models.Index(
                django.db.models.functions.text.Upper('phone'),
                name='upper_csorder_phone',
            ),
        ),
    ]
