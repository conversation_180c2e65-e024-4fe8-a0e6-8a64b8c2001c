# Generated by Django 3.2.14 on 2022-08-02 12:45

import django.contrib.postgres.indexes

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('customer_service', '0014_add_cs_order_cs_userprofile'),
    ]

    operations = [
        migrations.AddIndex(
            model_name='csorder',
            index=django.contrib.postgres.indexes.GinIndex(
                fields=['product_ids'], name='gin_product_ids'
            ),
        ),
    ]
