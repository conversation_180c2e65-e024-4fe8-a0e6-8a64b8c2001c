# Generated by Django 3.2.16 on 2022-12-14 15:23

import django.core.files.storage

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('customer_service', '0018_remove_custom_shelf_model'),
    ]

    operations = [
        migrations.AlterField(
            model_name='mentionmefile',
            name='file',
            field=models.FileField(
                blank=True,
                null=True,
                storage=django.core.files.storage.FileSystemStorage(),
                upload_to='logistic/mention_me/%Y/%m',
            ),
        ),
    ]
