# Generated by Django 3.2.16 on 2022-12-01 15:06

import django.db.models.functions.text

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('customer_service', '0019_alter_mentionmefile_file'),
    ]

    operations = [
        migrations.AddField(
            model_name='csorder',
            name='owner_email',
            field=models.EmailField(blank=True, max_length=254),
        ),
        migrations.AddField(
            model_name='csorder',
            name='promo_text',
            field=models.CharField(
                blank=True, max_length=400, verbose_name='Promo code text'
            ),
        ),
        migrations.AddIndex(
            model_name='csorder',
            index=models.Index(
                django.db.models.functions.text.Upper('promo_text'),
                name='upper_promo_text',
            ),
        ),
    ]
