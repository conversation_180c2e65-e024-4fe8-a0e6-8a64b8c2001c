# Generated by Django 3.2.16 on 2023-01-11 16:08

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('orders', '0029_extend_order'),
        ('customer_service', '0021_delete_csusertointercomlink'),
    ]

    operations = [
        migrations.AddField(
            model_name='refundinfo',
            name='campaign_name',
            field=models.TextField(blank=True, max_length=200),
        ),
        migrations.RenameField(
            model_name='refundinfo',
            old_name='order_info',
            new_name='referee_order_info',
        ),
        migrations.CreateModel(
            name='MentionMeB2BReward',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('referer_email', models.EmailField(blank=True, max_length=254)),
                ('referee_email', models.EmailField(blank=True, max_length=254)),
                ('campaign_name', models.CharField(blank=True, max_length=200)),
                ('amount', models.IntegerField(default=0)),
                ('currency_code', models.CharField(default='EUR', max_length=5)),
                ('created_at', models.DateTimeField(auto_now=True)),
                (
                    'referee_order_info',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, 'NOT_MATCHED_ORDER'),
                            (2, 'NOT_DELIVERED_YET'),
                            (3, 'RETURN'),
                            (4, 'NOT_ENOUGH_DAYS_FROM_DELIVERY'),
                            (5, 'ONLY_SAMPLES'),
                            (6, 'TO_MANY_REFUNDS'),
                            (7, 'TO_HIGH_REFUNDS'),
                            (8, 'INCORRECT_REFERER_ORDER'),
                            (9, 'OK'),
                        ],
                        default=1,
                    ),
                ),
                (
                    'origin_file',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='customer_service.mentionmefile',
                    ),
                ),
                (
                    'referee_order',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='referee_b2b_reward',
                        to='orders.order',
                    ),
                ),
                (
                    'status',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, 'REFEREE_ORDER_NOT_FOUND'),
                            (2, 'WAITING'),
                            (3, 'READY_TO_PAY_OUT'),
                            (4, 'PAID'),
                        ],
                        default=1,
                    ),
                ),
            ],
            options={
                'abstract': False,
                'unique_together': {('referee_email', 'referer_email')},
            },
        ),
    ]
