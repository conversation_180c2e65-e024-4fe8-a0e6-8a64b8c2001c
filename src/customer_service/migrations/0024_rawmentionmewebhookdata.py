# Generated by Django 3.2.16 on 2023-01-19 13:26

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('customer_service', '0023_alter_refundinfo_campaign_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='RawMentionMeWebHookData',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('json_data', models.JSONField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
            ],
        ),
    ]
