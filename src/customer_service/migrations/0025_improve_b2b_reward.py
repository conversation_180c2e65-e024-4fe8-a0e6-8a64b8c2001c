# Generated by Django 3.2.16 on 2023-03-06 09:37

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('customer_service', '0024_rawmentionmewebhookdata'),
    ]

    operations = [
        migrations.AlterField(
            model_name='mentionmeb2breward',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True),
        ),
        migrations.AlterField(
            model_name='mentionmeb2breward',
            name='status',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (1, 'REFEREE_ORDER_NOT_FOUND'),
                    (5, 'WAITING'),
                    (10, 'READY_TO_PAY_OUT'),
                    (15, 'EMAIL_SENT_TO_CUSTOMER'),
                    (20, 'INVOICE_SENT_TO_ACCOUNTING'),
                    (25, 'PAID'),
                ],
                default=1,
            ),
        ),
        migrations.AlterField(
            model_name='refundinfo',
            name='created_at',
            field=models.DateTimeField(auto_now_add=True),
        ),
    ]
