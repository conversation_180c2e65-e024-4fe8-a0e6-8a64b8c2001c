# Generated by Django 4.1.10 on 2023-09-07 12:42

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('invoice', '0013_invoiceitem_discount_tag'),
        ('customer_service', '0027_cscorrectionrequest_discount_tag'),
    ]

    operations = [
        migrations.CreateModel(
            name='KlarnaAdjustment',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(auto_now_add=True, help_text='Creation date'),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, help_text='Update date'),
                ),
                (
                    'change_type',
                    models.PositiveSmallIntegerField(
                        choices=[(0, 'increase'), (1, 'discount')]
                    ),
                ),
                ('amount', models.DecimalField(decimal_places=2, max_digits=12)),
                (
                    'conversation_link',
                    models.CharField(
                        blank=True, max_length=256, verbose_name='Dixa link'
                    ),
                ),
                ('reason', models.TextField(blank=True)),
                (
                    'invoice',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        related_name='adjustments',
                        to='invoice.invoice',
                    ),
                ),
                (
                    'finished_at',
                    models.DateTimeField(blank=True, editable=False, null=True),
                ),
            ],
            options={
                'get_latest_by': 'updated_at',
                'abstract': False,
            },
        ),
    ]
