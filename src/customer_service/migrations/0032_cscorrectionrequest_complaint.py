# Generated by Django 4.1.9 on 2024-01-30 16:13

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('complaints', '0050_add_complaintcosts_and_more'),
        ('customer_service', '0031_alter_csuserprofile_user_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='cscorrectionrequest',
            name='complaint',
            field=models.OneToOneField(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='correction_request',
                to='complaints.complaint',
            ),
        ),
    ]
