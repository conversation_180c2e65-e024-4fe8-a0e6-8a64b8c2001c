# Generated by Django 4.1.13 on 2024-09-18 09:38

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('customer_service', '0033_alter_cscorrectionrequest_status'),
    ]

    operations = [
        migrations.AlterField(
            model_name='cscorrectionaddressrequest',
            name='city',
            field=models.CharField(
                blank=True, default='', max_length=256, verbose_name='city'
            ),
        ),
        migrations.AlterField(
            model_name='cscorrectionaddressrequest',
            name='company_name',
            field=models.CharField(
                blank=True, default='', max_length=256, verbose_name='company name'
            ),
        ),
        migrations.AlterField(
            model_name='cscorrectionaddressrequest',
            name='country',
            field=models.CharField(
                blank=True,
                db_index=True,
                default='',
                max_length=50,
                verbose_name='country',
            ),
        ),
        migrations.AlterField(
            model_name='cscorrectionaddressrequest',
            name='first_name',
            field=models.CharField(
                blank=True, default='', max_length=256, verbose_name='first name'
            ),
        ),
        migrations.AlterField(
            model_name='cscorrectionaddressrequest',
            name='last_name',
            field=models.CharField(
                blank=True, default='', max_length=256, verbose_name='last name'
            ),
        ),
        migrations.AlterField(
            model_name='cscorrectionaddressrequest',
            name='postal_code',
            field=models.CharField(
                blank=True, default='', max_length=20, verbose_name='postal code'
            ),
        ),
        migrations.AlterField(
            model_name='cscorrectionaddressrequest',
            name='street_address_1',
            field=models.CharField(
                blank=True, default='', max_length=256, verbose_name='street address 1'
            ),
        ),
        migrations.AlterField(
            model_name='cscorrectionaddressrequest',
            name='street_address_2',
            field=models.CharField(
                blank=True, default='', max_length=256, verbose_name='street address 2'
            ),
        ),
        migrations.AlterField(
            model_name='cscorrectionaddressrequest',
            name='vat',
            field=models.CharField(
                blank=True, default='', max_length=256, verbose_name='tax id (vat)'
            ),
        ),
    ]
