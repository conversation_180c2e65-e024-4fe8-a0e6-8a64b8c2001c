# Generated by Django 4.1.13 on 2025-06-11 13:59

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('customer_service', '0036_klarnaadjustment_source'),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='mentionmeb2breward',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='mentionmeb2breward',
            name='origin_file',
        ),
        migrations.RemoveField(
            model_name='mentionmeb2breward',
            name='referee_order',
        ),
        migrations.DeleteModel(
            name='RawMentionMeWebHookData',
        ),
        migrations.AlterUniqueTogether(
            name='refundinfo',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='refundinfo',
            name='origin_file',
        ),
        migrations.RemoveField(
            model_name='refundinfo',
            name='referee_order',
        ),
        migrations.RemoveField(
            model_name='refundinfo',
            name='referer_order',
        ),
        migrations.RemoveField(
            model_name='refundsetting',
            name='currency',
        ),
        migrations.DeleteModel(
            name='MentionMeB2BReward',
        ),
        migrations.DeleteModel(
            name='MentionMeFile',
        ),
        migrations.DeleteModel(
            name='RefundInfo',
        ),
        migrations.DeleteModel(
            name='RefundSetting',
        ),
    ]
