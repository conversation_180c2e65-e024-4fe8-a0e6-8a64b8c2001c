{% extends 'admin/change_form.html' %}
{% load util_tags %}
{% load static %}
{% block extrahead %}
    <link href="{% static 'css/bootstrap.min.css' %}” rel=“stylesheet">
    <link href="{% static 'css/bootcards-desktop.css' %}" rel="stylesheet">
    <script src="{% static 'js/bootcards.min.js' %}"></script>
    <!-- MetisMenu CSS -->
    <link href="{% static 'css/plugins/metisMenu/metisMenu.min.css' %}" rel="stylesheet">
    <!-- Timeline CSS -->
    <link href="{% static 'css/plugins/timeline.css' %}" rel="stylesheet">
    <!-- Custom CSS -->
    <link href="{% static 'css/sb-admin-2.css' %}" rel="stylesheet">
    <!-- Morris Charts CSS -->
    <link href="{% static 'css/plugins/morris.css' %}" rel="stylesheet">
    <!-- Custom Fonts -->
    <link href="{% static 'font-awesome-4.1.0/css/font-awesome.min.css' %}" rel="stylesheet" type="text/css">
    <script src="{% static 'js/plugins/morris/raphael.min.js' %}"></script>
    <script src="{% static 'js/plugins/morris/morris.js' %}"></script>
    <style>
    ul li {
      list-style-type: None;
    }
    </style>
{% endblock %}
{% block content %}
  <div class="row">
    <div class="col-sm-6 bootcards-cards hidden-xs">
      <div class="panel panel-default">
        <div class="list-group">
        {% for data in order_data %}
          {% if not data|get_dict_value:'orders' %}
            {% if forloop.first %}
            <div class="panel-heading clearfix">
                <h3 class="panel-title pull-left">Could not match any orders with given data:</h3>
            </div>
            {% endif %}
            <div class="list-group-item">
                <p class="list-group-item-heading">{{ data|get_dict_value:'email' }}</p>
            </div>
          {% endif %}
        {% endfor %}
        </div>
      <a href="{% url 'admin:customer_service_refundinfo_changelist' %}" class="btn btn-success">Complete</a>
      </div>
    </div>
  </div>
{% endblock %}
