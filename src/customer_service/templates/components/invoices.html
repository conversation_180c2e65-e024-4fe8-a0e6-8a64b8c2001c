{% load cs_tags %}

{% for invoice in order.invoice_set.all|filter_statuses|dictsortreversed:'status'|dictsort:'issued_at' %}
    <div class="list-group d-flex">
        <div class="list-group-item d-flex-full">
            {% if order.earliest_invoice_domestic_version_supported %}
                <h4>Internal Invoices:</h4>
                <p class="text-muted">
                    You can request correction to it, but cannot send it to customer
                </p>
            {% endif %}
            {% with invoice_dict=invoice.to_dict %}
                <label>Invoice {{ invoice.pretty_id }}<br/>
                    Issued at: {{ invoice_dict.issued_at }}<br/>
                    Sell at: {{ invoice_dict.sell_at }}<br/>
                    Sent at: {{ invoice.sent_invoice_at|default_if_none:"" }} to {{ order.email }}
                    <br/><br/>
                    {% if invoice.pdf %}
                        <a href="{{ invoice.pdf.url }}">PDF</a>
                    {% else %}
                        missing pdf
                    {% endif %}
                    <br/>
                </label>
                <br/>
                <table class="table table-condensed">
                    <thead>
                    <tr>
                        <td>Currency</td>
                        <td>Netto</td>
                        <td>VAT</td>
                        <td>Brutto</td>
                    </tr>
                    </thead>
                    <tbody>
                    <tr>
                        <td>{{ invoice.currency_symbol }}</td>
                        <td>{{ invoice_dict.net_value }}</td>
                        <td>{{ invoice_dict.vat_value }}</td>
                        <td>{{ invoice_dict.total_value }}</td>
                    </tr>

                    {% if invoice.status == 4 %}
                        <tr>
                            <td>Correction diffs</td>
                        </tr>
                        {% with correction_dict=invoice.to_diff_dict %}
                            <tr>
                                <td>{{ invoice.currency_symbol }}</td>
                                <td>{{ correction_dict.net_value }}</td>
                                <td>{{ correction_dict.vat_value }}</td>
                                <td>{{ correction_dict.total_value }}</td>
                            </tr>
                        {% endwith %}
                    {% endif %}
                    </tbody>
                </table>

                {% if invoice.status == 4 %}
                    <table class="table table-condensed">
                        <tr>
                            <td>Money transfer</td>
                        </tr>
                        {% for m in invoice.moneycashback_set.all %}
                            <tr>
                                <td>Type</td>
                                <td>{{ m.type }} </td>
                            </tr>
                            <tr>
                                <td>When</td>
                                <td>
                                    {% if m.cash_back_date %}
                                        {{ m.cash_back_date }}
                                    {% else %}
                                        pending
                                    {% endif %}
                                </td>
                            </tr>
                        {% endfor %}
                        {% if not invoice.moneycashback_set %}
                            No cashback registered
                        {% endif %}
                    </table>
                {% endif %}
                {% if cs == True %}
                    {% if invoice.status == 2 %}
                        <a href="{% url 'cs_edit_proforma' invoice.id %}" class="btn btn-info">
                            Edit proforma invoice
                        </a>
                        {% if invoice.pdf %}
                            <a href="{{ invoice.pdf.url }}" class="btn btn-info">
                                Download proforma pdf
                            </a>
                        {% endif %}
                    {% endif %}
                    <p>Pending correction request count: {{ invoice.pending_correction_requests.count }}</p>
                    {% for m in invoice.moneycashback_set.all %}
                        <div class="panel panel-body" id="cashback_plain">
                            <a href="/admin/accounting/moneycashback/{{ m.id }}/change">
                                Cashback with date:
                                {{ m.cash_back_date|default_if_none:"..." }} and
                                info: {{ m.additional_info|default_if_none:"..." }}
                            </a>
                        </div>
                    {% endfor %}

                    {% if invoice.correction_info != "-" %}
                        <a href="" disabled='disabled' class="btn btn-block-fit btn-info">
                            Correction already created/requested
                        </a>
                    {% elif not forloop.last %}
                        <a href="" disabled='disabled' class="btn btn-block-fit btn-info">
                            Correction possible only for newest invoice
                        </a>
                    {% elif invoice|corrected_to_0 %}
                        <a href="" disabled='disabled' class="btn btn-info">
                            Last invoice corrected to 0, generate invoice from order
                        </a>
                    {% elif invoice|pending_corrections %}
                        <a href="" disabled='disabled' class="btn btn-info">
                            Pending correction request
                        </a>
                    {% elif invoice|can_make_correction %}
                        {% if order.is_switch_status_in_progress %}
                            <p>Switch is in progress. You can't request next Correction Request until previous one is processed.</p>
                        {% elif order.is_extra_discount_in_progress  %}
                            <p>Add Extra Discount is in progress. You can't request next Correction Request until previous one is processed.</p>
                        {% endif %}
                        <a href="{% url 'cs_request_correction' invoice.id %}" class="btn btn-block-fit btn-info{% if order.is_switch_status_in_progress or order.is_extra_discount_in_progress %} disabled{% endif %}">
                            Request correction invoice
                        </a>
                        <a href="{% url 'cs_request_correction_invoice_item' invoice.id %}" class="btn btn-block-fit btn-info{% if order.is_switch_status_in_progress or order.is_extra_discount_in_progress %} disabled{% endif %}">
                            Request correction invoice remove Items
                        </a>
{#                        <a href="{% url 'cs_request_correction_address' invoice.id %}" class="btn btn-info">#}
{#                            Request correction invoice address#}
{#                        </a>#}
                        <a href="{% url 'cs_order_add_extra_discount_recalculation' invoice.order.id %}" class="btn btn-block-fit btn-info{% if order.is_switch_status_in_progress %} disabled{% endif %}">
                            Add Extra Discount
                        </a>

                        {% if invoice.get_total_net != 0 %}
                            <a href="{% url 'cs_neutralize_invoice' invoice.id %}" class="btn btn-block-fit btn-info{% if order.is_switch_status_in_progress or order.is_extra_discount_in_progress %} disabled{% endif %}">
                                Neutralize invoice
                            </a>
                        {% endif %}
                    {% elif invoice.status == 2 %}
                        <a href="" disabled='disabled' class="btn btn-info">Pending proforma</a>
                        {% if order.is_klarna_payment and order.get_invoices|length == 0 %}
                            {% if invoice.has_pending_klarna_adjustment %}
                                <a class="btn btn-info disabled" style="margin-left: 15rem">
                                    Pending adjustment
                                </a>
                            {% else %}
                                <a href="{% url 'cs_klarna_adjustment' invoice.id %}" class="btn btn-block-fit btn-info">
                                    Klarna adjustment
                                </a>
                                <a href="{% url 'cs_order_add_extra_discount_recalculation' invoice.order.id %}" class="btn btn-block-fit btn-info">
                                    Add Extra Discount
                                </a>
                            {% endif %}
                        {% endif %}
                    {% endif %}
                    <br/><br/>
                    {% if not order.earliest_invoice_domestic_version_supported %}
                        <a href="{% url 'cs_sent_invoice_again' invoice.id %}" class="btn btn-info">
                            Sent again to {{ order.email }}
                        </a>
                    {% endif %}
                    {% if invoice.status == 0 or invoice.status == 7 %}
                    {% endif %}
                {% endif %}
            {% endwith %}
        </div>

        {% if order.earliest_invoice_domestic_version_supported %}
            <div class="list-group-item d-flex-full">
            <h4>External Invoices:</h4>
            <p class="text-muted">
                You cannot request correction to it, but can send it to customer
            </p>
            {% with invoice=invoice.get_domestic_invoice %}
                {% with invoice_dict=invoice.to_dict %}
                    <label>Invoice {{ invoice.pretty_id }}<br/>
                        Issued at: {{ invoice_dict.issued_at }}<br/>
                        Sell at: {{ invoice_dict.sell_at }}<br/>
                        Sent at: {{ invoice.sent_invoice_at|default_if_none:"" }} to {{ order.email }}
                        <br/><br/>
                        {% if invoice.pdf %}
                            <a href="{{ invoice.pdf.url }}">PDF</a>
                        {% else %}
                            missing pdf
                        {% endif %}
                        <br/>
                    </label>
                    <br/>
                    <table class="table table-condensed">
                        <thead>
                        <tr>
                            <td>Currency</td>
                            <td>Netto</td>
                            <td>VAT</td>
                            <td>Brutto</td>
                        </tr>
                        </thead>
                        <tbody>
                        <tr>
                            <td>{{ invoice.currency_symbol }}</td>
                            <td>{{ invoice_dict.net_value }}</td>
                            <td>{{ invoice_dict.vat_value }}</td>
                            <td>{{ invoice_dict.total_value }}</td>
                        </tr>

                        {% if invoice.status == 4 %}
                            <tr>
                                <td>Correction diffs</td>
                            </tr>
                            {% with correction_dict=invoice.to_diff_dict %}
                                <tr>
                                    <td>{{ invoice.currency_symbol }}</td>
                                    <td>{{ correction_dict.net_value }}</td>
                                    <td>{{ correction_dict.vat_value }}</td>
                                    <td>{{ correction_dict.total_value }}</td>
                                </tr>
                            {% endwith %}
                        {% endif %}
                        </tbody>
                    </table>

                    {% if cs == True %}
                        {% if invoice.status == 2 %}
                            {% if invoice.pdf %}
                                <a href="{{ invoice.pdf.url }}" class="btn btn-info">
                                    Download proforma pdf
                                </a>
                            {% endif %}
                        {% endif %}

                        <a href="{% url 'cs_sent_invoice_again' invoice.id %}" class="btn btn-info">
                            Sent again to {{ order.email }}</a>
                    {% endif %}
                {% endwith %}
            {% endwith %}
            </div>
        {% endif %}
    </div>
{% empty %}
    <div class="list-group">
        <div class="list-group-item">
            <label>Missing invoices</label>
        </div>
    </div>
{% endfor %}
