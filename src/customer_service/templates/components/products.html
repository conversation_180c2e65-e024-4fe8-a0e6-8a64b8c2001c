{% load cs_tags %}

{% for product in products %}
    <div class="list-group">
        <div class="list-group-item">
            <label>
                <a {% if cs == False %}href="/admin/producers/product/{{ product.id }}/"{% endif %}>
                Product: {{ product.id }}@ {{ product.manufactor }} - added {{ product.created_at }}
                {% if product.batch_id != None %}/ B: {{ product.batch_id }}{% endif %}</a>
            </label>
            {% if product.deleted %}
                <h3 class="status_color_6">Deleted</h3>
            {% endif %}
            <p>Actual status: {{ product.get_status_display }}</p>
            {% if product.order_item.deleted %}
                <p><strong>Switched item</strong></p>
            {% endif %}
            <div class="priority_color_{{ product.priority }}">
                <p id="actual-priority">
                    Actual priority:{% if product.priority == 5 %} Normal{% endif %} {{ product.get_priority_display }}
                </p>
                <p id="actual-priority">
                    Actual source priority:{% if product.get_source_priority_display == '' %} Normal {% endif %} {{ product.get_source_priority_display }}
                </p>
                <p id="actual-priority">
                    Actual delivery priority:{% if product.get_delivery_priority_display == '' %} Normal {% endif %} {{ product.get_delivery_priority_display }}
                </p>
                <div id="postpone-date-{{ product.id }}">
                    {% if product.priority == 2 %}
                        <p>Requested Postponed Delivery Date: {{ product.requested_postponed_delivery_date }}</p>
                        <p>Expected Postponed Release Date: {{ product.expected_postponed_release_date }}</p>
                    {% endif %}
                </div>
            </div>
            {% if product.status in product_statuses_to_cancel %}
                {% if product.status == 1 %}
                    <div class="priority-buttons">
                        <form action="{% url 'change_product_priority' product.id 1 %}" method="post">
                            <input type="submit" class="button btn-info" {% if product.priority == 1 %}disabled{% endif %} value="Hold">
                        </form>
                        <form action="{% url 'change_product_priority' product.id 5 %}" method="post">
                            <input type="submit" class="button btn-info" {% if product.priority == 5 %}disabled{% endif %} value="Normal">
                        </form>
                        <form action="{% url 'change_product_priority' product.id 10 %}" method="post">
                            <input type="submit" class="button btn-info" {% if product.priority == 10 %}disabled{% endif %} value="Important">
                        </form>
                        <form action="{% url 'change_source_priority' product.id 5 %}" method="post">
                            <input type="submit" class="button btn-info" {% if product.source_priority == 5 %}disabled{% endif %} value="B2B">
                        </form>
                        <form action="{% url 'change_source_priority' product.id 1 %}" method="post">
                            <input type="submit" class="button btn-info" {% if product.source_priority == 1 %}disabled{% endif %} value="VIP">
                        </form>
                    </div>
                {% endif %}
                <form action="{% url 'request-product-postpone' product.pk %}" class="postpone-form">
                    <input type="date" name="requsted_postponed_date" class="textinput textInput form-control"
                           required="" id="id_requested_postponed_delivery_date">
                    <input type="submit" class="btn btn-info" id="postpone_product" value="Postpone">
                </form>
                {% if order.aggregate_items_count == 1 and order.status in order_statuses_to_cancel and not order.has_pending_correction_request %}
                    <p>Abort Order(id={{ product.order_id }})</p>
                    <form action="{% url 'abort_order' product.id %}" method="post">
                        <input type="submit" class="button btn-danger order-abort" id="order_abort" value="Cancel">
                    </form>
                {% else %}
                    <p>
                        {% if order.has_pending_correction_request %}
                            <b>You cannot abort order because it has pending correction request.</b>
                        {% else %}
                            <b>
                                You cannot abort orders because it has more than one item or it has not
                                proper Product or Order status.
                            </b>
                        {% endif %}
                        <br>It has {{ order.aggregate_items_count }} items.
                        <br>Order status: {{ product.order.get_status_display }}
                        <br>Product status: {{ product.get_status_display }}
                    </p>
                {% endif %}
            {% endif %}
            <p>Product color: {{ product.color_en_name }} </p>

            <div>Assembly manual:
                {% if product.does_instruction_file_exist %}
                    <a type="button" class="btn btn-info" href="{{ product.details.instruction | file_url }}">
                        <span class="glyphicon glyphicon-download"></span>Instruction
                    </a>
                {% else %}
                    <form class="inline-form" action="{% url 'regenerate_assembly_manual' product.id %}" method="post">
                        <button type="submit" class="btn btn-info">
                            Regenerate Instruction
                        </button>
                    </form>
                {% endif %}
            </div>
            <p>Front view:
                {% if product.details.front_view %}
                    <a type="button" class="btn btn-info"
                       href="{{ product.details.front_view | file_url }}">
                        <span class="glyphicon glyphicon-download"></span> Front </a>
                {% else %}
                    <button type="button" class="btn btn-default " disabled="disabled">
                        Missing front
                    </button>
                {% endif %}
            </p>
            <p>Packing information:
                {% if product.details.packaging_instruction %}
                    <a type="button" class="btn btn-info"
                       href="{{ product.details.packaging_instruction | file_url }}">
                        <span class="glyphicon glyphicon-download"></span> Instruction
                    </a>
                {% else %}
                    <button type="button" class="btn btn-default " disabled="disabled">
                        Missing instruction
                    </button>
                {% endif %}
            </p>
            <p> Stickers:
                <a type="button" class="btn btn-info" href="{% url 'elements-labels-for-product' product.id %}">
                    <span class="glyphicon glyphicon-download"></span>
                    Stickers
                </a>
            </p>
            <p> Serialization Display:
                <a type="button" class="btn btn-info"
                   href="{% url 'admin:admin_json_production_view' product.id 'json' %}">
                    Serialization Display
                </a>
            </p>
            <p> Production Drawings:
                {% if product.details.production_drawings %}
                    <a type="button" class="btn btn-info"
                       href="{{ product.details.production_drawings | file_url }}">
                        <span class="glyphicon glyphicon-download"></span> Drawings
                    </a>
                {% else %}
                    <button type="button" class="btn btn-default " disabled="disabled">
                        Missing drawings
                    </button>
                {% endif %}
            </p>
            <div class="complaints-list">
            {% if product.complaint_set.count > 0 %}
                {% for complaint in product.complaint_set.all %}
                    <div class="complaints-list-element">
                        <h3>Complaint status: </h3>
                        <p>Id: {{ complaint.id }}</p>
                        <p>Status: {{ complaint.status }}, {% if complaint.reproduction_product %}Reproduction order:
                            {{ complaint.reproduction_order.id }} {% endif %}
                        </p>

                        <p>Reported to: {{ complaint.reporter }}</p>
                        <p>Elements: {{ complaint.get_elements_description }}</p>
                        <p>Reported at: {{ complaint.reported_date|default_if_none:'-' }}</p>
                        <p>Production ordered date: {{ complaint.production_ordered_date|default_if_none:'-' }}</p>
                        <p>Production released date: {{ complaint.production_released_date|default_if_none:'-' }}</p>
                        <p>Shipment date: {{ complaint.shipment_date|default_if_none:'-' }}</p>
                        <p>Delivered date: {{ complaint.delivered_date|default_if_none:'-' }}</p>
                        {% if complaint.reproduction %}
                            <p>
                                Reproduction:
                                <i class="text-success fa fa-check"></i>
                                {% if complaint.express_replacement %}
                                    <span style="color: #dc1e1e;">EXPRESS</span>
                                {% endif %}
                            </p>
                        {% endif %}
                        {% if complaint.refund %}
                            <p>
                                Refund:
                                <i class="text-success fa fa-check"></i>
                            </p>
                        {% endif %}
                        {% if complaint.fittings_only %}
                            <p>
                                Fittings Only:
                                <i class="text-success fa fa-check"></i>
                            </p>
                        {% endif %}
                        {% if complaint.is_repeated %}
                            <p>
                                Is repeated:
                                <i class="text-success fa fa-check"></i>
                            </p>
                        {% endif %}
                        {% if complaint.assembly_team_intervention %}
                            <p>
                                Assembly team intervention:
                                <i class="text-success fa fa-check"></i>
                            </p>
                        {% endif %}
                        {% if complaint.boxes_damaged %}
                            <p>
                                Boxes damaged:
                                <i class="text-success fa fa-check"></i>
                            </p>
                        {% endif %}
                        <div class="complaint-buttons">
                            <a type="button" class="btn btn-info" href="/admin/complaints/complaint/{{ complaint.id }}/">
                                <span class="glyphicon glyphicon-download"></span> Details
                            </a>
                            {% if complaint.reproduction and complaint.reproduction_product.batch or complaint.express_replacement or complaint.fittings_only %}
                                <a type="button" class="btn btn-danger" disabled>
                                    <span class="glyphicon glyphicon-download"></span>
                                        Can't Delete,
                                    {% if complaint.reproduction and complaint.reproduction_product.batch %}
                                        already batched!
                                    {% elif complaint.express_replacement %}
                                        express replacement!
                                    {% elif complaint.fittings_only %}
                                        fittings only!
                                    {% endif %}
                                </a>
                            {% elif complaint.has_deprecated_elements %}
                                <a type="button" class="btn btn-danger" href="{% url 'cs_delete_complaint' complaint.id %}">
                                    <span class="glyphicon glyphicon-download"></span>
                                    Delete
                                </a>
                            {% else %}
                                <a type="button" class="btn btn-info" href="{% url 'cs_update_complaint' complaint.id %}">
                                    <span class="glyphicon glyphicon-download"></span>
                                    Edit
                                </a>
                                <a type="button" class="btn btn-danger" href="{% url 'cs_delete_complaint' complaint.id %}">
                                    <span class="glyphicon glyphicon-download"></span>
                                    Delete
                                </a>
                            {% endif %}
                        </div>
                    </div>
                {% endfor %}
            {% endif %}
            </div>
            <br/><br/>
            <p>
                <a type="button" class="btn btn-info" href="{% url 'cs_create_complaint' product.id %}">
                    <span class="glyphicon glyphicon-download"></span>
                    Create complaint
                </a>
            </p>
            {% if order.order_type != 6 %}
                <p>
                    <a type="button" class="btn btn-info" href="{% url 'cs_create_damage_form' product.id %}">
                        <span class="glyphicon glyphicon-download"></span>
                        Create damage form
                    </a>
                </p>
                <button class="btn btn-info" type="button" data-toggle="collapse"
                        data-target="#collapseDamageFormLinks{{ product.id }}" aria-expanded="false"
                        aria-controls="collapseDamageFormLinks">
                    Show damage forms
                </button>
                <div class="collapse" id="collapseDamageFormLinks{{ product.id }}">
                <div class="well">{% damage_data_links product %}</div>
            {% endif %}
            </div>
        </div>
    </div>
{% empty %}
    <div class="list-group">
        <div class="list-group-item">
            <label>Missing products in production</label>
        </div>
    </div>
{% endfor %}
