{% load humanize %}
{% load region_tags %}

<div class="row">
    <div class="col-sm-12 hidden-xs">
        <!--contact details -->
        <div id="LibraryList">

            <div class="panel panel-default">
                <div class="panel-heading clearfix">
                    <h3 class="panel-title pull-left">
                        <a role="button" data-toggle="collapse" href="#collapsel_libr">Library</a>
                    </h3>
                    <h6 class="pull-right">
                        <a role="button" data-toggle="collapse" href="#collapsel_libr">
                            ({{ user.profile.library_jetty_set | length }} items)
                            <span pull-right class="glyphicon glyphicon-chevron-down"></span>
                        </a>
                    </h6>
                </div>
                <div id="collapsel_libr" class="panel-collapse collapse" role="tabpanel">{# panel body start #}
                    <div class="panel-body">
                        <div class="list-group">
                            {% for item in user.profile.library_jetty_set %}
                                <div class="list-group-item" href="{% comment %}/admin/gallery/jetty/{{ item.id }}/{% endcomment %}" style="padding: 7px 30px 7px 10px; margin-left: 0;">
                                    <div class="row">
                                        <div class="col-sm-3">
                                            <p class="list-group-item-text">Item gallery id: {{ item.id }}</p>
                                            <p class="list-group-item-text">Materials: {{ item.translated_material_name }} / {{ item.get_material }}</p>
                                            <p class="list-group-item-text">Dimensions: {% for dimension in item.get_dimensions %}{{ dimension.label }}&nbsp;{{ dimension.value|floatformat }}{% if not forloop.last %} {% endif %}{% endfor %} cm</p>
                                        </div>
                                        <div class="col-sm-3">
                                            <p class="list-group-item-text"> Added: {{ item.created_at|naturalday }}</p>
                                            <p class="list-group-item-text"> Price: {% regionalize_and_display_price item user.profile.region %} </p>
                                        </div>
                                        <div class="col-sm-6">
                                            <img style="width: 270px; height:200px" src="{{ item.preview | file_url }}">
                                        </div>
                                    </div>
                                </div>
                            {% endfor %}
                        </div>
                    </div><!--contact card-->
                </div></div>
        </div><!--list-details-->
    </div>
</div>
