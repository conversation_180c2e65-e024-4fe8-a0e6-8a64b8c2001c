{% load humanize %}
{% load cs_tags %}
<div class="panel panel-default">{# panel start #}
    <div class="panel-heading" role="tab" id="headingl{{ logistic_order.id }}"> {# panel head start #}
      <h4 class="panel-title">
        <a role="button" data-toggle="collapse" data-parent="#accordion" href="#collapsel{{ logistic_order.id }}" aria-expanded="true" aria-controls="collapsel{{ logistic_order.id }}">
            Summary for logistic order: {{ logistic_order.id }} - order:{{ order.id }}
            {% if logistic_order.is_assembly_service_in_planning %}
                - <span class="status_color_3">AS planning</span>
            {% endif %}
        </a>
      </h4>
    </div> {# panel head end #}
    <div id="collapsel{{ logistic_order.id }}" class="panel-collapse collapse" role="tabpanel" aria-labelledby="headingl{{ logistic_order.id }}">{# panel body start #}
        <div class="panel-body">
            <div class="row">
                <div class="col-sm-12 bootcards-cards hidden-xs">
                    <!--contact details -->
                    <div id="contactCard">
                        <div class="panel panel-default">
                            <div class="list-group">
                                <div class="list-group-item">
                                    <label>Order</label>
                                    <h4 class="list-group-item-heading">
                                        <p class="list-group-item-text">
                                        {% if not cs %}<a href="/admin/orders/order/{{ order.id }}">{% endif %}{{ order.id }}{% if not cs %}</a>{% endif %}
                                        {{ order.order_pretty_id }}
                                        </p>
                                    </h4>
                                </div>

                                {% if logistic_order.as_max_status_history %}
                                <div class="list-group-item as-max-status-table">
                                    <label>As Max status history</label>
                                    <table class="collapse-border">
                                        <tr>
                                            <th class="as-max-status-cell">New status</th>
                                            <th class="as-max-status-cell">Previous status</th>
                                            <th class="as-max-status-cell">Changed at</th>
                                            <th class="as-max-status-cell">Planned start date</th>
                                        </tr>
                                        {% for as_max_status_history_entry in logistic_order.as_max_status_history %}
                                            <tr>
                                                <td class="as-max-status-cell">{{ as_max_status_history_entry.new_status_display }}</td>
                                                <td class="as-max-status-cell">{{ as_max_status_history_entry.previous_status_display }}</td>
                                                <td class="as-max-status-cell">{{ as_max_status_history_entry.changed_at }}</td>
                                                <td class="as-max-status-cell">{{ as_max_status_history_entry.planned_start_date }}</td>
                                            </tr>
                                        {% endfor %}
                                    </table>
                                </div>
                                {% endif %}
                            {% if not cs %}
                                <div class="list-group-item">
                                    <label>Manufacturer</label>
                                    <h4 class="list-group-item-heading">{% get_manufactors_for_logistic_order logistic_order %}</h4>
                                </div>
                            {% endif %}
                                <div class="list-group-item">
                                    <label>Logistic order weight</label>
                                    <h4 class="list-group-item-heading">{{ logistic_order.total_brutto_weight_string }}</h4>
                                </div>
                                <div class="list-group-item">
                                    <label>Delivery address</label>
                                    <h4 class="list-group-item-heading"> {{ order.get_address_summary }}</h4>
                                </div>
                                <div class="list-group-item">
                                    <label>Items</label>
                                    <h4 class="list-group-item-heading">{% get_items_for_logistic_order logistic_order %}</h4>
                                </div>
                                <div class="list-group-item">
                                    <label>Packaging</label>
                                    <h4 class="list-group-item-heading">
                                        {% get_packaging_data logistic_order %}
                                    </h4>
                                </div>
                                <div class="list-group-item">
                                    <label>Cost in PLN</label>
                                    <h4 class="list-group-item-heading">
                                        {{ logistic_order.cost_in_pln }}
                                    </h4>
                                </div>
                                {% if order_region_has_assembly and not order.assembly %}
                                    <div class="list-group-item">
                                        <label>Assembly would cost:</label>
                                        <ul>
                                            <li>
                                                In Euro:
                                                <h4 class="list-group-item-heading">
                                                    {{ order.get_assembly_price_in_euro | safe }} € brutto
                                                </h4>
                                            </li>
                                            <li>
                                                Regionalized:
                                                <h4 class="list-group-item-heading">
                                                    {{ order.get_assembly_price | safe }}
                                                    {{ order.region.get_currency.symbol }} brutto
                                                </h4>
                                            </li>
                                        </ul>
                                    </div>
                                {% endif %}
                                <div class="list-group-item">
                                    <label>Sent to customer date</label>
                                    <h4 class="list-group-item-heading">
                                        {{ logistic_order.sent_to_customer|default:'' }}
                                    </h4>
                                </div>
                                <div class="list-group-item">
                                    <label>Carrier</label>
                                    <h4 class="list-group-item-heading">
                                        {{ logistic_order.carrier|default:'' }}
                                    </h4>
                                </div>

                                <div class="list-group-item">
                                    <label>Carrier phones</label>
                                    <h4 class="list-group-item-heading">
                                    <strong>
                                    {% for cmr in logistic_order.cmr_documents %}
                                        Phone: {{ cmr.get_contractor_phone_number|default:'' }} </br>
                                    {% endfor %}
                                    </strong>
                                    </h4>
                                </div>

                                <div class="list-group-item">
                                    <label>Tracking number</label>
                                    <h4 class="list-group-item-heading">{{ logistic_order.tracking_number|default:'' }} </h4>
                                    {% if logistic_order.tracking_number and logistic_order.carrier %}
                                        {% generate_tracking_url logistic_order.carrier logistic_order.tracking_number %}
                                    {% else %}
                                        <p>No tracking number provided.</p>
                                    {% endif %}
                                </div>

                                <div class="list-group-item">
                                    <label>Delivered date</label>
                                    <h4 class="list-group-item-heading">{{ logistic_order.delivered_date|default:'' }}</h4>
                                </div>

                                <div class="list-group-item">
                                    <label>Invoice nunmer</label>
                                    <h4 class="list-group-item-heading">{{ logistic_order.invoice_number|default:'' }}</h4>
                                </div>

                                <div class="list-group-item">
                                    <label>Additional info</label>
                                    <h4 class="list-group-item-heading">{{ logistic_order.additional_info|default:'' }}</h4>
                                </div>
                            </div>
                            {% if cs == False %}
                                <div class="panel-footer">
                                    <a class="btn btn-link btn-xs pull-right" href="/admin/logistic/logisticorder/{{ logistic_order.id }}/">
                                        <small class="pull-left">Go to logistic change view</small>
                                    </a>
                                </div>
                            {% endif %}
                        </div>

                    </div><!--contact card-->
                </div><!--list-details-->
            </div><!--row-->
        </div><!-- div class="panel-body" -->
    </div>{# panel body end #}
</div>{# panel end #}
