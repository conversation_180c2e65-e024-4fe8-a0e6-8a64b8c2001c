{% load humanize %}

<div class="row">
    <div class="col-sm-12 bootcards-cards hidden-xs">
        <!--contact details -->
        <div id="orderList">
            <div class="panel panel-default">
                <div class="panel-heading clearfix">
                    {% if not order.is_order %}
                        <h3 class="panel-title pull-left">
                            <a role="button" data-toggle="collapse" href="#collapsel_cart_{{ order.id }}">
                                Active cart id: {{ order.id }}
                            </a>
                        </h3>
                        <h6 class="pull-right">
                          <a
                            role="button"
                            data-toggle="collapse"
                            href="#collapsel_cart_{{ order.id }}"
                          >
                                ({{ order.items.all | length }} items)
                                <span pull-right class="glyphicon glyphicon-chevron-down"></span>
                            </a>
                        </h6>
                    {% else %}
                        <h3 class="panel-title pull-left">Order id: {{ order.id }}</h3>
                    {% endif %}
                </div>
                {% if order.status == 9 %}
                    <div id="collapsel_cart_{{ order.id }}" class="panel-collapse collapse" role="tabpanel">{# panel body start #}
                {% endif %}
                <div class="panel-heading clearfix">
                    {% if not order.is_order %}
                    <a href="{% url 'cs_user_overview' order.owner.id %}?payment_link=True&order_id={{ order.id }}&is_cart=True" class="btn btn-info">
                        Generate payment link
                    </a>
                    {% else %}
                        <a href="{% url 'cs_user_overview' order.owner.id %}?payment_link=True&order_id={{ order.id }}" class="btn btn-info">
                        Generate payment link
                    </a>
                    {% endif %}
                    <div class="btn-group pull-right">
                        {% if order.email %}
                            {% if not order.is_order %}
                                <a href="{% url 'cs_change_order_to_pending' order.id %}" class="btn btn-info">
                                    <i class="fa fa-money"></i>Change to order
                                </a>
                                <a href="{% url 'cs_change_order_to_pending' order.id %}" class="btn btn-success">
                                    <i class="fa fa-money"></i>Change to order and sent proforma
                                </a>
                            {% endif %}
                        {% elif order.is_order %}
                            <a href="{% url 'cs_update_order' order.id %}" class="btn btn-info">
                                <i class="fa fa-money"></i>Update cart data (missing email)
                            </a>
                        {% endif %}
                    </div>
                </div>
                <div class="panel-body">
                    <div class="list-group">

                        <div class="list-group-item" {% comment %}href="/admin/orders/order/{{ order.id }}/"{% endcomment %}>
                            <div class="row">
                                <div class="col-sm-3">
                                    <p class="list-group-item-text">Updated: {{ order.updated_at|naturalday }}</p>
                                    <p class="list-group-item-text">{% if order.is_order %}Order status: {% else %}Cart status: {% endif %}<span class="btn btn-success">{{ order.get_status_display }}</span></p>
                                <p class="list-group-item-text"> Total: {{ order.total_price }} €</p>
                                </div>
                                <div class="col-sm-3">
                                    <p class="list-group-item-text"> Promo amount: {{ order.promo_amount|safe }} €</p>
                                    <p class="list-group-item-text"> Promo text: {{ order.promo_text|default:"without promo" }} €</p>
                                    <p class="list-group-item-text"> Items: {{ order.get_items_as_string|safe }}</p>
                                </div>
                                <div class="col-sm-6">
                                    {% for item in order.items.all %}
                                        <img class="cart__item__order_item__preview" style="width: 135px; height:100px; float: left" src="{{ item.sellable_item.preview | file_url }}">
                                    {% endfor %}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            {% if order.status == 9 %}
                </div>
            {% endif %}
            </div>
        </div><!--contact card-->
    </div><!--list-details-->
</div>
