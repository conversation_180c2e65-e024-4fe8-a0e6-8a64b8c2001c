{% load humanize %}
<div class="">
    <div class="row">
        <h2>User info</h2>
        <div class="col-sm-3">
            <!--contact details -->
            <div id="contactCard">
                <div class="panel panel-default">
                    <div class="panel-heading clearfix">
                        <h3 class="panel-title pull-left">{{ user.profile.user.username }} details</h3>
                    </div>
                    <div class="list-group">
                         <div class="list-group-item">
                            <label>ID</label>
                            <h4 class="list-group-item-heading">{{ user.profile.user.pk }}</h4>
                        </div>
                        <div class="list-group-item">
                            <label>Name</label>
                            <h4 class="list-group-item-heading">{{ user.profile.first_name|default:"Not set" }} {{ user.profile.last_name|default:"Not set" }}</h4>
                        </div>
                        <div class="list-group-item">
                            <label>Account type</label>
                            <h4 class="list-group-item-heading">{{ user.profile.get_user_type_display }}</h4>
                        </div>

                        <div class="list-group-item">
                            <label>Is Business Client</label>
                            <h4 class="list-group-item-heading">{{ user.profile.is_business_type|yesno:"Yes,No" }}</h4>
                            <form action="{% url 'change_user_profile_is_business_type' user.profile.id %}" method = "post">
                                <input type="hidden" name="is_business_type" value="{% if user.profile.is_business_type %}False{% else %}True{% endif %}">
                                <input type="submit" class="btn btn-info" value="Switch B2B Type">
                            </form>
                        </div>

                        <div class="list-group-item">
                            <label>Account email</label>
                            <h4 class="list-group-item-heading">{{ user.profile.email }}</h4>
                        </div>

                        <div class="list-group-item">
                            <label>Gender</label>
                            <h4 class="list-group-item-heading">{{ user.profile.get_gender_display|default:"Not set" }}</h4>
                        </div>

                        <div class="list-group-item">
                            <label>Language</label>
                            <h4 class="list-group-item-heading">{{ user.profile.language }}</h4>
                            <a href="{% url 'change_user_lang' user.profile.user.id %}" class="btn btn-info">Change user language</a>
                        </div>

                        <div class="list-group-item">
                            <label>Phone</label>
                            <h4 class="list-group-item-heading">{{ user.profile.phone|default:"Not set" }}</h4>
                        </div>
                        <div class="list-group-item">
                            <label>First contacted with webpage</label>
                            <h4 class="list-group-item-heading">{{ user.profile.registration_first_contact }}</h4>
                        </div>
                        <div class="list-group-item">
                            <label>Registered</label>
                            <h4 class="list-group-item-heading">{{ user.profile.user.date_joined }}</h4>
                        </div>
                        <div class="list-group-item">
                            <label>Last login</label>
                            <h4 class="list-group-item-heading">{{ user.profile.user.last_login }}</h4>
                        </div>
                        <div class="list-group-item">
                            <label>Registration source</label>
                            <h4 class="list-group-item-heading">{{ user.profile.get_registration_source_display|default:"Not set" }}</h4>
                        </div>
                        <div class="list-group-item">
                            <label>Registration utm</label>
                            <h4 class="list-group-item-heading">{{ user.profile.registration_referrer|default:"Not set" }}</h4>
                        </div>
                        <div class="list-group-item">
                            <label>Registration referrer</label>
                            <h4 class="list-group-item-heading">{{ user.profile.registration_referrer_uri|default:"Not set" }}</h4>
                        </div>
                        <div class="list-group-item">
                            <label>Registration country</label>
                            <h4 class="list-group-item-heading">{{ user.profile.registration_country|default:"Not set" }}</h4>
                        </div>
                        <div class="list-group-item">
                            <label>Registration user agent</label>
                            <h4 class="list-group-item-heading">{{ user.profile.registration_user_agent|default:"Not set" }}</h4>
                        </div>
                    </div>
                </div>

                <div class="deactivate-user-account">
                    <a href="{% url 'deactivate_user_account' user.profile.id %}" class="btn btn-danger"
                       {% if not user.is_active %}disabled{% endif %}>Deactivate user account</a>
                </div>
            </div><!--contact card-->
        </div><!--list-details-->

        <div class="col-sm-3">
            <!--contact details -->
            <div id="contactHistoryCard">
                <div class="panel panel-default">
                    <div class="panel-heading clearfix">
                        <h3 class="panel-title pull-left">Address data</h3>
                    </div>
                        <div class="list-group">
                        <div class="list-group-item">
                            <label>Email</label>
                            <h4 class="list-group-item-heading">{{ user.profile.email }}</h4>
                        </div>
                        <div class="list-group-item">
                            <label>Name</label>
                            <h4 class="list-group-item-heading">{{ user.profile.first_name|default:"Not set" }} {{ user.profile.last_name|default:"Not set" }}</h4>
                        </div>

                        <div class="list-group-item">
                            <label>Company and vat</label>
                            <h4 class="list-group-item-heading">
                                {{ user.profile.company_name|default:"Not set" }}<br/>
                                {{ user.profile.vat|default:"" }}
                            </h4>
                        </div>
                        <div class="list-group-item">
                            <label>Delivery address</label>
                            <h4 class="list-group-item-heading">
                                {{ user.profile.street_address_1|default:"" }}<br/>
                                {{ user.profile.street_address_2|default:"" }}<br/>
                                {{ user.profile.postal_code|default:""}} {{ user.profile.city }}<br/>
                                {{ user.profile.country|default:"Not set" }}<br/>
                            </h4>
                        </div>
                        <div class="list-group-item">
                            <label>Diffrent invoice address</label>
                            <h4 class="list-group-item-heading">{{ user.profile.diffrent_invoice_addres_user }}</h4>
                        </div>


                        <div class="list-group-item">
                            <label>Invoice name</label>
                            <h4 class="list-group-item-heading">{{ user.profile.invoice_first_name|default:"Not set" }} {{ user.profile.invoice_last_name|default:"Not set" }}</h4>
                        </div>

                        <div class="list-group-item">
                            <label>Invoice Company and vat</label>
                            <h4 class="list-group-item-heading">
                                {{ user.profile.invoice_company_name|default:"Not set" }}<br/>
                                {{ user.profile.invoice_vat|default:"" }}
                            </h4>
                        </div>
                        <div class="list-group-item">
                            <label>Invoice address</label>
                            <h4 class="list-group-item-heading">
                                {{ user.profile.invoice_street_address_1|default:"" }}<br/>
                                {{ user.profile.invoice_street_address_2|default:"" }}<br/>
                                {{ user.profile.invoice_postal_code|default:""}} {{ user.profile.invoice_city }}<br/>
                                {{ user.profile.invoice_country|default:"Not set" }}<br/>
                            </h4>
                        </div>
                    </div>
                </div>
            </div><!--contact card-->
        </div><!--list-details-->

        <div class="col-sm-6">
            <!--contact details -->
            <div>
                <div class="panel panel-default">
                    <div class="panel-heading clearfix">
                        <h3 class="panel-title pull-left">Summary</h3>
                    </div>
                    <div class="list-group">
                        <div class="list-group-item">
                            <label>User summary</label>
                            <h4 class="list-group-item-heading">
                                {{ user.profile.get_user_summary|safe }}
                            </h4>
                        </div>
                    </div>
                </div>
                <div class="panel panel-default">
                    <div class="panel-heading clearfix">
                        <h3 class="panel-title pull-left">Reviews</h3>
                    </div>
                    <div class="list-group">
                        {% for review in user.profile.get_user_reviews %}
                            <div class="list-group-item">
                                <label>Summary</label>
                                <h4 class="list-group-item-heading">
                                    {{ review.score }}*, Sentiment: {{ review.get_review_sentiment_display }}, order: {{ review.order_id }}<br/>
                                    tags: {% for tag in review.tags.all %}{{ tag }}{% endfor %}
                                </h4>
                            </div>
                            <div class="list-group-item">
                                <label>Title and description</label>
                                <h4 class="list-group-item-heading">
                                    {{ review.title }} <br/>
                                </h4>
                                <h5>{{ review.description }}</h5>
                            </div>
                            <div class="list-group-item">
                                <label>Pictures</label>
                                <h4 class="list-group-item-heading">
                                    {% for photo in review.photos.all %}
                                        <a href="{{ photo.url }}">
                                            <img src="{{ photo.url }}" width="150px" height="150px"/>
                                        </a>
                                    {% endfor %}
                                </h4>
                            </div>
                            <div class="list-group-item">
                                <label><a href="/admin/reviews/review/{{ review.id }}/">Read it</a></label>
                            </div>
                        {% endfor %}
                    </div>
                </div>
            </div><!--contact card-->
        </div><!--list-details-->
    </div><!--row-->

    <div class="row">
        <div class="col-sm-12 hidden-xs">
            <!--contact details -->
            <div id="ConnectedList">
                <div class="panel panel-default">
                    <div class="panel-heading clearfix">
                        <h3 class="panel-title pull-left">
                            <a role="button" data-toggle="collapse" href="#collapsel_ConnectedList">
                            Connected accounts (registration/logins):
                            </a>
                        </h3>
                        <h6 class="pull-right">
                            <a role="button" data-toggle="collapse" href="#collapsel_ConnectedList">
                                ({{ user.profile.get_connected_accounts | length }} items)
                                <span pull-right class="glyphicon glyphicon-chevron-down"></span>
                            </a>
                        </h6>
                    </div>
                    <div id="collapsel_ConnectedList" class="panel-collapse collapse" role="tabpanel" >{# panel body start #}
                        <div class="panel-body">
                            <div class="list-group">
                                {% for account in user.profile.get_connected_accounts %}
                                    <a class="list-group-item" href="{% url "cs_user_overview" account.user_id %}" style="padding: 7px 30px 7px 10px; margin-left: 0;">
                                        <div class="row">
                                            <div class="col-sm-4">
                                                <p class="list-group-item-text">Username: {{ account.user.username }} / {{ account.get_user_type_display }}</p>
                                                <p class="list-group-item-text">First contact: {{ account.registration_first_contact }}</p>
                                                <p class="list-group-item-text">Registration date: {{ account.user.date_joined }}</p>
                                                <p class="list-group-item-text">Last login: {{ account.user.last_login }}</p>
                                            </div>
                                            <div class="col-sm-8">
                                                <p class="list-group-item-text">Registration source: {{ account.get_registration_source_display }}</p>
                                                <p class="list-group-item-text overflow-hidden">Registration utm: {{ account.registration_referrer }} </p>
                                                <p class="list-group-item-text">Registration referrer: {{ account.registration_referrer_uri }} </p>
                                            </div>
                                        </div>
                                    </a>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                </div>
            </div><!--contact card-->
        </div><!--list-details-->
    </div>
</div>
