{% extends "customer_service/base.html" %}
{% load i18n %}
{% load admin_urls %}
{% load activity_tags %}
{% load humanize %}
{% load admin_tags %}
{% load crispy_forms_tags %}
{% load static %}

{% block extrahead %}
    <link href="{% static "css/bootcards-desktop.css" %}" rel="stylesheet">
    <script src="{% static "js/bootcards.min.js" %}"></script>
{% endblock %}


{% block content %}
    <div>
      <h2>Damage Form<h2/>
    </div>
    <label for="order">Order:</label>
    <input type="text" id="order" name="order" disabled value={{ order }}>
    <form action="" method="post" enctype='multipart/form-data'>
        {% csrf_token %}
        {{ form|crispy }}
        <input type="submit" name="submit" class="btn btn-default" value="Create and Send" />
        <input type="submit" name="submit" class="btn btn-default" value="Save draft" />
    </form>
{% endblock %}
