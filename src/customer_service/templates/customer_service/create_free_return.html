{% extends "customer_service/base.html" %}
{% load i18n %}
{% load admin_urls %}
{% load activity_tags %}
{% load humanize %}
{% load static %}
{% load cs_tags %}
{% load crispy_forms_tags %}

{% block extrahead %}
    <link href="{% static 'css/bootcards-desktop.css' %}" rel="stylesheet">
    <script src="{% static 'js/bootcards.min.js' %}"></script>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/css/select2.min.css" rel="stylesheet" />
{% endblock %}


{% block content %}
    {% show_short_order_overview order %}
    <div class="row">
        <div class="col-md-6">
            <form action="" method="post">{% csrf_token %}
                {{ form|crispy }}
                <input type="submit" class="btn btn-default" value="Send" />
            </form>
        </div>
    </div>
{% endblock %}


{% block extrascripts %}
    {{ block.super }}
    <script src="https://cdnjs.cloudflare.com/ajax/libs/select2/4.0.6-rc.0/js/select2.min.js"></script>
    <script type="text/javascript">
        $(document).ready(function($) {
            $('#id_notification_date').datetimepicker(
                {
                    format: 'YYYY-MM-DD',
                    sideBySide: true
                }
            );
            $(document).ready(function() {
                $('#id_order_item').select2();
            });
            start_date = moment("{{ order.last_logistic_order.delivered_date|date:"c"|default:'error' }}", "YYYY-MM-DD");
            if (!start_date.isValid()) {
                $('#div_id_notification_date').append('<p class="sub">WARNING: no delivery date found</p>')
            }
            $('#id_notification_date').on('dp.change', function(e){
                if (start_date.isValid()) {
                    durr = moment.duration(e.date.diff(start_date))
                    $('#div_id_notification_date').children().remove('.sub');
                    div = $('#div_id_notification_date').append('<p class="sub">Last delivery date: '+ start_date.format('YYYY-MM-DD') + ' => '+
                        parseFloat(durr.asDays()).toFixed()
                            +' days difference'+
                    '</p>');
                    if (parseFloat(durr.asDays()) > 100) {
                        $('#div_id_notification_date p.sub').css('color', 'red')
                    }
                } else {
                    $('#div_id_notification_date').children().remove('.sub');
                    $('#div_id_notification_date').append('<p class="sub">WARNING: no delivery date found</p>')
                }

            })
        });
    </script>
{% endblock %}
