{% extends "customer_service/base.html" %}
{% load i18n %}
{% load admin_urls %}
{% load activity_tags %}
{% load humanize %}
{% load static %}
{% load cs_tags %}
{% load crispy_forms_tags %}


{% block content %}
    <div class="row">
        <div class="col-md-6">
            <fieldset>
            <h2>Klarna adjustment for proforma: {{ invoice.pretty_id }}</h2>
            <form action="" method="post">{% csrf_token %}
                {{ form.non_field_errors }}
                <div id="div_id_change_type" class="form-group col-md-12">
                    {{ form.change_type.errors }}
                    <label for="{{ form.change_type.id_for_label }}" class="control-label">Change type:</label>
                    <div class="controls">
                        {{ form.change_type }}
                    </div>
                </div>
                    <div id="div_id_amount" class="form-group col-md-6">
                        {{ form.amount.errors }}
                        <label for="{{ form.amount.id_for_label }}" class="control-label">Amount:</label>
                        <div class="controls">
                            {{ form.amount }}
                        </div>
                    </div>
                    <div id="div_id_amount" class="form-group col-md-6">
                        <label for="{{ form.currency_symbol.id_for_label }}" class="control-label">Currency:</label>
                        <div class="controls">
                            {{ form.currency_symbol }}
                        </div>
                    </div>
                <div id="div_id_conversation_link" class="form-group col-md-12">
                    {{ form.conversation_link.errors }}
                    <label for="{{ form.conversation_link.id_for_label }}" class="control-label">Dixa link:</label>
                    <div class="controls">
                        {{ form.conversation_link }}
                    </div>
                </div>

                <div id="div_id_reason" class="form-group col-md-12">
                    {{ form.reason.errors }}
                    <label for="{{ form.reason.id_for_label }}" class="control-label">Reason:</label>
                    <div class="controls">
                        {{ form.reason }}
                    </div>
                </div>
                {{ form.invoice }}
                <input type="submit" class="btn btn-default" value="Send"/>
            </form>
                </fieldset>
        </div>
    </div>
{% endblock %}

