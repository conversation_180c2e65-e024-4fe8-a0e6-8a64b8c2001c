{% extends "customer_service/base.html" %}
{% load humanize admin_tags %}

{% block title %}Customer contact{% endblock %}
{% block extrahead %}
    <style>
        .align-middle {
            vertical-align: middle!important;
        }
    </style>
{% endblock %}
{% block content %}

    <div class="row">
        <div class="col-lg-12 col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    <h1>
                        Customer Contact
                    </h1>
                </div>
                <div class="table-responsive">
                    <table class="table table-bordered table-hover table-sm">
                        <thead class="thead-dark">
                            <tr>
                                <th class="text-center align-middle">Id</th>
                                <th class="text-center align-middle">Email</th>
                                <th class="text-center align-middle">Order</th>
                                <th class="text-center align-middle">Topic</th>
                                <th class="text-center align-middle">Missing elements description</th>
                                <th class="text-center align-middle">Has missing elements</th>
                                <th class="text-center align-middle">Has damaged shelf elements</th>
                                <th class="text-center align-middle">Has damaged packaging</th>
                                <th class="text-center align-middle">Is reported to delivery company</th>
                                <th class="text-center align-middle">Created</th>
                                <th class="text-center align-middle">Damages description</th>
                                <th class="text-center align-middle">Thumbnail</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for contact in page_obj %}
                                <tr>
                                    <td class="text-center align-middle">
                                        <a href="{% url "cs_customer_contact_detail" pk=contact.id %}">{{ contact.id }}</a>
                                    </td>
                                    <td class="text-center align-middle">
                                        {{ contact.email }}
                                    </td>
                                    <td class="text-center align-middle">
                                        {% if contact.order %}
                                            {{ contact.order }}
                                        {% else %}
                                            -
                                        {% endif %}
                                    </td>
                                    <td class="text-center align-middle">
                                        {{ contact.topic }}
                                    </td>
                                    <td class="text-center align-middle">
                                        {{ contact.missing_elements_description }}
                                    </td>
                                    <td class="text-center align-middle">
                                        {% if contact.has_missing_elements %}
                                            <span class="label label-success">Yes</span>
                                        {% else %}
                                            <span class="label label-danger">No</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center align-middle">
                                        {% if contact.has_damaged_shelf_elements %}
                                            <span class="label label-success">Yes</span>
                                        {% else %}
                                            <span class="label label-danger">No</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center align-middle">
                                        {% if contact.has_damaged_packaging %}
                                            <span class="label label-success">Yes</span>
                                        {% else %}
                                            <span class="label label-danger">No</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center align-middle">
                                        {% if contact.is_reported_to_delivery_company %}
                                            <span class="label label-success">Yes</span>
                                        {% else %}
                                            <span class="label label-danger">No</span>
                                        {% endif %}
                                    </td>
                                    <td class="text-center align-middle">
                                        {{ contact.created }}
                                    </td>
                                    <td class="text-center align-middle">
                                        {{ contact.damages_description }}
                                    </td>
                                    <td class="text-center align-middle">
                                        {% for contact_image in contact.images.all %}
                                            <img
                                                src="{{ contact_image.image.url }}"
                                                alt="damaged-element-image-{{ contact_image.id }}"
                                                class="img-thumbnail"
                                            />
                                        {% endfor %}
                                    </td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        <div class="pagination">
            <span class="step-links">
                {% if page_obj.has_previous %}
                    <a href="?page=1">&laquo; first</a>
                    <a href="?page={{ page_obj.previous_page_number }}">previous</a>
                {% endif %}

                <span class="current">
                    Page {{ page_obj.number }} of {{ page_obj.paginator.num_pages }}.
                </span>

                {% if page_obj.has_next %}
                    <a href="?page={{ page_obj.next_page_number }}">next</a>
                    <a href="?page={{ page_obj.paginator.num_pages }}">last &raquo;</a>
                {% endif %}
            </span>
        </div>
    </div>
{% endblock %}