{% extends "customer_service/base.html" %}
{% load humanize %}
{% load admin_tags %}

{% block title %}Customer contact detail{% endblock %}
{% block extrahead %}
    <style>
        .damages-image {
            max-width: 200px;
        }
    </style>
{% endblock %}
{% block content %}
    <div class="panel">
        <div class="panel-heading">
            <h2>Customer contact, ID: {{ object.id }}</h2>
        </div>
        <div class="panel-body">
            <span class="label label-primary">
                First name
            </span>
            <div class="well well-sm">
                {{ object.first_name }}
            </div>
            <span class="label label-primary">
                Email
            </span>
            <div class="well well-sm">
                {{ object.email }}
            </div>
            <span class="label label-primary">
                Message
            </span>
            <div class="well well-sm">
                {{ object.message }}
            </div>
            <span class="label label-primary">
                Order
            </span>
            <div class="well well-sm">
                {% if object.order %}
                    {{ object.order }}
                {% else %}
                    -
                {% endif %}
            </div>
            <span class="label label-primary">
                Topic
            </span>
            <div class="well well-sm">
                {{ object.topic }}
            </div>
            <span class="label label-primary">
                 Missing elements
            </span>
            <div class="well well-sm">
                {% if object.has_missing_elements %}
                    Yes
                {% else %}
                    No
                {% endif %}
            </div>
            <span class="label label-primary">
                Missing elements Description
            </span>
            <div class="well well-sm">
                {% if object.missing_elements_description %}
                    {{ object.missing_elements_description }}
                {% else %}
                    -
                {% endif %}
            </div>
            <span class="label label-primary">
                Damaged shelf elements
            </span>
            <div class="well well-sm">
                {% if object.has_damaged_shelf_elements %}
                    Yes
                {% else %}
                    No
                {% endif %}
            </div>
            <span class="label label-primary">
                Damaged packaging
            </span>
            <div class="well well-sm">
                {% if object.has_damaged_packaging %}
                    Yes
                {% else %}
                    No
                {% endif %}
            </div>
            <span class="label label-primary">
                Reported to delivery company
            </span>
            <div class="well well-sm">
                {% if object.is_reported_to_delivery_company %}
                    Yes
                {% else %}
                    No
                {% endif %}
            </div>
            <span class="label label-primary">
                Created
            </span>
            <div class="well well-sm">
                {{ object.created|date }}
            </div>
            <span class="label label-primary">
                Damages description
            </span>
            <div class="well well-sm">
                {% if object.damages_description %}
                    {{ object.damages_description }}
                {% else %}
                    -
                {% endif %}
            </div>
            <span class="label label-primary">
                Damages elements images
            </span>
            <div class="well well-sm">
                {% for damaged_elements_image in damaged_elements_images %}
                        <a href="{{ damaged_elements_image.image.url }}" target="_blank" rel="noopener noreferrer">
                            <img
                                src="{{ damaged_elements_image.image.url }}"
                                alt="damaged-element-image-{{ damaged_elements_image.id }}"
                                class="img-thumbnail damages-image"
                            />
                        </a>
                {% endfor %}
            </div>
            <span class="label label-primary">
                Damages packaging images
            </span>
            <div class="well well-sm">
                {% for damaged_package_image in damaged_package_images %}
                        <a href="{{ damaged_package_image.image.url }}" target="_blank" rel="noopener noreferrer">
                            <img
                                src="{{ damaged_package_image.image.url }}"
                                alt="damaged-package-image-{{ damaged_package_image.id }}"
                                class="img-thumbnail damages-image"
                            />
                        </a>
                {% endfor %}
            </div>
        </div>
    </div>
{% endblock %}