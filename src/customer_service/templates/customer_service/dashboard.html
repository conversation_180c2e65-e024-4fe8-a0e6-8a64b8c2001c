{% extends "customer_service/base.html" %}
{% load humanize %}
{% load admin_tags %}

{% block extrascripts %}
    <script type="text/javascript">
        $(document).ready(function () {
            var cs_note_header = document.getElementById("cs_note_header");
            cs_note_header.ondblclick = function () {
                document.getElementById("cs_note_plain").classList.add('hidden');
                document.getElementById('cs_notes_editor').classList.remove('hidden');
            }
        });

    </script>
{% endblock %}

{% block content %}

    <div class="row">
        <div class="col-lg-12 col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading clearfix cs_note_header" id="cs_note_header">
                    <h3 class="panel-title pull-left">CS notes</h3>
                    <h6 class="panel-title pull-right">(double click to edit)</h6>
                </div>
                <div class="panel panel-body order_notes_plain" id="cs_note_plain">
                    {{ cs_note|default_if_none:"--"|safe }}
                </div>
                <div class="panel panel-body hidden" id="cs_notes_editor">
                    <form id="cs_note_form" action="/cs/" method="post">
                        {{ cs_note_form.as_p }}
                        {% csrf_token %}
                        <p>
                            <button type="submit" class="btn cs_note_editor_submit" id="cs_note_editor_submit">Save
                            </button>
                        </p>
                    </form>
                </div>
            </div>
        </div>
        <div class="col-lg-12 col-md-12">
            <div class="panel panel-default">

                <div class="panel-heading">
                    Correction request status - last 10
                </div>
                <!-- /.panel-heading -->

                <div class="table-responsive">
                    <table class="table">
                        <thead>
                        <tr>
                            <th>By</th>
                            <th>Added</th>
                            <th>Updated</th>
                            <th>Invoice</th>
                            <th>Amount</th>
                            <th>Amount € (estimated)</th>
                            <th>Reason of correction</th>
                            <th>Description</th>
                            <th>Status</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for correction in correction_requests %}
                            <tr>
                                <td>
                                    {{ correction.issuer.username }}
                                </td>
                                <td>
                                    {{ correction.created_at|date:"d/m/y" }}
                                    <br/> {{ correction.created_at|naturaltime }}
                                </td>
                                <td>
                                    {{ correction.updated_at|date:"d/m/y" }}
                                    <br/> {{ correction.updated_at|naturaltime }}
                                </td>
                                <td>
                                    <a href="{% url 'cs_user_overview' correction.invoice.order.owner_id %}">{{ correction.invoice.pretty_id }}</a>
                                </td>
                                <td>
                                    {{ correction.correction_amount_gross }} {{ correction.invoice.currency_symbol }}
                                </td>
                                <td>
                                    {{ correction.get_estimated_euro_price }} €
                                </td>
                                <td>
                                    {{ correction.tag_to_reason|safe }}
                                </td>
                                <td>
                                    {{ correction.correction_context|safe }}
                                </td>
                                <td>
                                    {{ correction.get_status_display }}
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="panel-footer">
                    <a href="{% url 'cs_pending_correction_list' %}?from_last_days=30">
                        <span class="pull-left">View all</span>
                        <span class="pull-right"><i class="fa fa-arrow-circle-right"></i></span>
                    </a>
                    <div class="clearfix"></div>
                </div>

                <!-- /.panel-body -->
            </div>
        </div>
        <div class="col-lg-12 col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    Latest failed payment notifications
                </div>
                <!-- /.panel-heading -->
                <div class="panel-body">
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                            <tr>
                                <th>Date</th>
                                <th>Order</th>
                                <th>Status</th>
                                <th>Amount</th>
                                <th>Method</th>
                            </tr>
                            </thead>
                            <tbody>
                            {% for transactions in payment_notifications_error %}
                                <tr class="warning">
                                    <td>{{ transactions.event_date|date:"d/m/y" }}<br/> {{ transactions.event_date|naturaltime }}
                                    </td>
                                    <td>
                                        {{ transactions.merchant_reference }}
                                    </td>
                                    <td>{{ transactions.reason }}</td>
                                    <td>{{ transactions.amount_value|cents_to_euros }} &euro;</td>
                                    <td>{{ transactions.payment_method }}</td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    <!-- /.table-responsive -->
                </div>
                <!-- /.panel-body -->
            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    Orders in to be shipped
                </div>
                <!-- /.panel-heading -->

                <table class="table table-responsive table-stripped">
                    <thead>
                    <tr>
                        <th>Id</th>
                        <th>Customer</th>
                        <th>Source</th>
                        <th>Country</th>
                        <th>Price</th>
                        <th>Items</th>
                        <th>Date</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for order in orders_to_be_shipped %}
                        <tr>
                            <td>{{ order.id }}</td>
                            <td><a href="{% url 'cs_user_overview' order.owner_id %}">
                                {% if order.owner.profile.user_type == 2 %}
                                    {{ order.owner.username|truncatechars:60 }} {{ order.first_name }}
                                    {{ order.last_name }}
                                {% else %}
                                    guest
                                {% endif %}
                                - {{ order.first_name }} {{ order.last_name }}
                            </a></td>
                            <td>{{ order.get_order_source_display }}</td>
                            <td>{{ order.get_country_short }}</td>

                            <td>
                                {{ order.total_price }} &euro;
                            </td>
                            <td>{{ order.get_items_as_string|safe }}</td>
                            <td>{{ order.created_at|date:"d/m/y" }} <br/> {{ order.created_at|naturaltime }} </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>

            </div>
        </div>

    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    Latest shipped orders
                </div>
                <!-- /.panel-heading -->

                <table class="table table-responsive table-stripped">
                    <thead>
                    <tr>
                        <th>Id</th>
                        <th>Customer</th>
                        <th>Source</th>
                        <th>Country</th>
                        <th>Price</th>
                        <th>Items</th>
                        <th>Date</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for order in orders_shipped %}
                        <tr>
                            <td>{{ order.id }}</td>
                            <td><a href="{% url 'cs_user_overview' order.owner_id %}">
                                {% if order.owner.profile.user_type == 2 %}
                                    {{ order.owner.username|truncatechars:60 }} {{ order.first_name }}
                                    {{ order.last_name }}
                                {% else %}
                                    guest
                                {% endif %}
                                - {{ order.first_name }} {{ order.last_name }}
                            </a></td>
                            <td>{{ order.get_order_source_display }}</td>
                            <td>{{ order.get_country_short }}</td>

                            <td>
                                <a href="{% url 'cs_user_overview' order.owner_id %}">{{ order.total_price }} &euro;</a>
                            </td>
                            <td>{{ order.get_items_as_string|safe }}</td>
                            <td>{{ order.created_at|date:"d/m/y" }} <br/> {{ order.created_at|naturaltime }} </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>

            </div>
        </div>
    </div>
    <div class="row">
        <div class="col-lg-12 col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    Latest delivered orders
                </div>
                <!-- /.panel-heading -->

                <table class="table table-responsive table-stripped">
                    <thead>
                    <tr>
                        <th>Id</th>
                        <th>Customer</th>
                        <th>Source</th>
                        <th>Country</th>
                        <th>Price</th>
                        <th>Items</th>
                        <th>Date</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for order in orders_delivered %}
                        <tr>
                            <td>{{ order.id }}</td>
                            <td><a href="{% url 'cs_user_overview' order.owner_id %}">
                                {% if order.owner.profile.user_type == 2 %}
                                    {{ order.owner.username|truncatechars:60 }}
                                {% else %}
                                    guest
                                {% endif %}
                                - {{ order.first_name }} {{ order.last_name }}
                            </a></td>
                            <td>{{ order.get_order_source_display }}</td>
                            <td>{{ order.get_country_short }}</td>

                            <td>
                                <a href="{% url 'cs_user_overview' order.owner_id %}">{{ order.total_price }} &euro;</a>
                            </td>
                            <td>{{ order.get_items_as_string|safe }}</td>
                            <td>{{ order.created_at|date:"d/m/y" }} <br/> {{ order.created_at|naturaltime }} </td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
                <!-- /.panel-body -->
            </div>
        </div>
    </div>



    <div class="row">
        <div class="col-lg-12 col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    VIP orders
                </div>
                <!-- /.panel-heading -->

                <table class="table table-responsive table-stripped">
                    <thead>
                    <tr>
                        <th>Id</th>
                        <th>Customer</th>
                        <th>Country</th>
                        <th>Price</th>
                        <th>Items</th>
                        <th>Date</th>
                        <th>Promo code</th>
                        <th>Delivery date</th>
                        <th>Estimated date</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% for order in vip_orders %}
                        <tr>
                            <td>{{ order.id }}</td>
                            <td><a href="{% url 'cs_user_overview' order.owner_id %}">
                                {% if order.owner.profile.user_type == 2 %}
                                    {{ order.owner.username|truncatechars:60 }}
                                {% else %}
                                    guest
                                {% endif %}
                                - {{ order.first_name }} {{ order.last_name }}
                            </a></td>
                            <td>{{ order.get_country_short }}</td>

                            <td>
                                <a href="{% url 'cs_user_overview' order.owner_id %}">{{ order.total_price }} &euro;</a>
                            </td>
                            <td>{{ order.get_items_as_string|safe }}</td>
                            <td>{{ order.created_at|date:"d/m/y" }} <br/> {{ order.created_at|naturaltime }} </td>
                            <td>{{ order.promo_text }}</td>
                            <td>{{ order.get_delivery_date|default_if_none:'not yet' }}</td>
                            <td>{{ order.get_estimated_delivery_time }}</td>
                        </tr>
                    {% endfor %}
                    </tbody>
                </table>
                <!-- /.panel-body -->
            </div>
        </div>
    </div>


    <!-- /.row -->
    </div>
{% endblock %}
