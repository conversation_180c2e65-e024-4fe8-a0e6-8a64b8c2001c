{% extends "customer_service/base.html" %}
{% load humanize %}
{% load cs_tags %}
{% load crispy_forms_tags %}

{% block content %}

    <div class="row">
        <div class="col-md-6">
            <h2>Proforma details:</h2>
            {% show_invoice_short_overview invoice %}
        </div>
        <div class="col-md-6">
            <h2>Edit proforma invoice: {{ invoice.pretty_id }}</h2>
            {% if form.errors %}
                <div id='form-errors'>
                    <h4>Errors</h4>
                    {{ form.errors }}
                </div>
            {% endif %}
            <form action="" method="post">{% csrf_token %}
                {{ forms.proforma_form|crispy }}
                <h4>Delivery address</h4>
                {{ forms.order_form|crispy }}
                <div id="invoice_address_section">
                    {{ forms.invoice_address_form|crispy }}
                </div>
                <h4>Items</h4>
                {{ forms.items_formset|crispy }}
                <input name="add_row" type="submit" class="btn btn-default" value="Add item" />
                <input name="submit" type="submit" class="btn btn-default" value="Save" />
            </form>
        </div>
    </div>
{% endblock %}

{% block extrascripts %}
    {{ block.super }}
    <script type="text/javascript">
        $(document).ready(function($) {
            $('.datetimeinput').datetimepicker(
                {
                    format: 'YYYY-MM-DD HH:mm:ss',
                    sideBySide: true
                }
            );
            $('#id_order_form-invoice_address_used').change(function() {
                $('#invoice_address_section').toggle(this.checked)
            }).change();
        });
    </script>
{% endblock %}
