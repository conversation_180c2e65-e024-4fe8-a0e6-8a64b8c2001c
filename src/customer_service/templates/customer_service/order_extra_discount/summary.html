{% extends "customer_service/base.html" %}
{% load cs_tags static %}


{% block extrascripts %}
    <script src="{% static 'js/preventDoubleClick.js' %}"></script>
{% endblock %}

{% block content %}
    <div class="row">
        <div class="col-md-12">
            <h2>Add Extra Discount - Status <b>"{{ order.get_extra_discount_status_display }}"</b></h2>
            <p class="bolder">
                <a href="{% url 'cs_user_overview' order.owner.id %}">
                    Order: {{ order.pk }}
                </a>
                / {{ order.first_name }} {{ order.last_name }}
            </p>
            <div>
                <p>Voucher:
                    <a href="{% url 'admin:vouchers_voucher_change' order.used_promo.pk %}">
                        {{ order.used_promo }}
                    </a>
                </p>

                <table class="table aligned-table table-bordered">
                    <thead>
                    <tr>
                        <th scope="col">Currency</th>
                        <th scope="col">Promo Netto</th>
                        <th scope="col">Promo Brutto amount</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% with order.region.get_currency as currency %}
                        {% if currency.code != 'EUR' %}
                            <tr>
                                <th scope="row">€</th>
                                <td>{{ order.promo_amount_net }}</td>
                                <td>{{ order.promo_amount }}</td>
                            </tr>
                        {% endif %}
                        <tr>
                            <th scope="row">{{ currency.symbol }}</th>
                            <td>{{ order.region_promo_amount_net }}</td>
                            <td>{{ order.region_promo_amount }}</td>
                        </tr>
                    {% endwith %}
                    </tbody>
                </table>

                <table class="table aligned-table table-bordered">
                    <thead>
                    <tr>
                        {% if order.used_promo.is_percentage %}
                            <th scope="col">Furniture Item</th>
                        {% endif %}
                        <th scope="col">Old discount</th>
                        <th scope="col">New discount</th>
                        <th scope="col">Difference</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% if order.used_promo.is_absolute %}
                        <tr>
                            <td>{{ order.completed_used_promos.last.value }}€</td>
                            <td>{{ order.used_promo.value }}€</td>
                            <td><b>
                                {{ order.voucher_value_difference }}{{ order.used_promo.kind_of|display_voucher_kind_of }}</b>
                            </td>
                        </tr>
                    {% else %}
                        {% for order_item in order.items.all %}
                            <tr>
                                <td>{{ order_item.order_item.product_type }}:
                                    <a href="{{ order_item.order_item.get_absolute_url }}">
                                        {{ order_item.order_item.id }}
                                    </a>
                                </td>
                                <td>{{ order.completed_used_promos.last|get_discount_value_for_item:order_item.order_item }}%</td>
                                <td>{{ order.used_promo|get_discount_value_for_item:order_item.order_item }}%</td>
                                <td><b>
                                    {{ order|voucher_value_difference_by_furniture_item:order_item.order_item }}{{ order.used_promo.kind_of|display_voucher_kind_of }}</b>
                                </td>
                            </tr>
                        {% endfor %}
                    {% endif %}
                    </tbody>
                </table>

                <p><b>Total Netto:</b></p>

                <table class="table aligned-table table-bordered">
                    <thead>
                    <tr>
                        <th scope="col">Currency</th>
                        <th scope="col">Source amount</th>
                        <th scope="col">Target amount</th>
                        <th scope="col">Difference</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% with order.region.get_currency as currency %}
                        {% if currency.code != 'EUR' %}
                            <tr>
                                <th scope="row">€</th>
                                <td>{{ order.source_extra_discount_total_price_net }}</td>
                                <td>{{ order.total_price_net }}</td>
                                <td>{{ order.total_price_difference.netto }}</td>
                            </tr>
                        {% endif %}
                        <tr>
                            <th scope="row">{{ currency.symbol }}</th>
                            <td>{{ order.source_extra_discount_region_total_price_net }}</td>
                            <td>{{ order.region_total_price_net }}</td>
                            <td>{{ order.region_total_price_difference.netto }}</td>
                        </tr>
                    {% endwith %}
                    </tbody>
                </table>

                <p><b>Total Brutto:</b></p>
                <table class="table aligned-table table-bordered">
                    <thead>
                    <tr>
                        <th scope="col">Currency</th>
                        <th scope="col">Source amount</th>
                        <th scope="col">Target amount</th>
                        <th scope="col">Difference</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% with order.region.get_currency as currency %}
                        {% if currency.code != 'EUR' %}
                            <tr>
                                <th scope="row">€</th>
                                <td>{{ order.source_extra_discount_total_price }}</td>
                                <td>{{ order.total_price }}</td>
                                <td>{{ order.total_price_difference.brutto }}</td>
                            </tr>
                        {% endif %}
                        <tr>
                            <th scope="row">{{ currency.symbol }}</th>
                            <td>{{ order.source_extra_discount_region_total_price }}</td>
                            <td>{{ order.region_total_price }}</td>
                            <td><b>{{ order.region_total_price_difference.brutto }}</b></td>
                        </tr>
                    {% endwith %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <form action="{% url 'cs_order_add_extra_discount_summary' order.pk %}" method="post">
                {% csrf_token %}
                <input type="submit" class="btn btn-info btn-next btn-prevent-doubleclick" value="Yes"/>
            </form>

            <form action="{% url 'cs_order_add_extra_discount_rollback_recalculation' order.pk %}" method="post">
                {% csrf_token %}
                <input type="submit" class="btn btn-info btn-cancel" value="Back"/>
            </form>
        </div>
    </div>
{% endblock %}
