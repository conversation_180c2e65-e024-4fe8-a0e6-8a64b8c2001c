{% extends "customer_service/base.html" %}
{% load crispy_forms_tags static %}

{% block extrascripts %}
    <script src="{% static 'js/preventDoubleClick.js' %}"></script>
    <script src="{% static 'js/addExtraDiscount.js' %}"></script>
{% endblock %}

{% block content %}
    <div class="row">
        <div class="col-md-10">
            <h2>Add Extra Discount - Status <b>"{{ order.get_extra_discount_status_display }}"</b></h2>
            <p class="bolder">
                <a href="{% url 'cs_user_overview' order.owner.id %}">
                    Order: {{ order.pk }}
                </a> / {{ order.first_name }} {{ order.last_name }}
            </p>
            <div>
                {% if order.used_promo %}
                    <p><span class="bolder">Voucher:</span>
                        <a href="{% url 'admin:vouchers_voucher_change' order.used_promo.pk %}">
                            {{ order.used_promo }}
                        </a>
                    </p>
                {% else %}
                    <p><span class="bolder">Voucher:</span> -</p>
                {% endif %}
                <div class="d-flex">
                    <div class="d-flex-full">
                        <span class="bolder">Previous Correction Requests:</span>
                        {% if order.correction_requests %}
                            <ul>
                                {% for correction_request in order.correction_requests %}
                                    <li>
                                        <a href="{% url 'admin:customer_service_cscorrectionrequest_change' correction_request.pk %}">
                                            {{ correction_request }}
                                        </a>
                                    </li>
                                {% endfor %}
                            </ul>
                        {% else %}
                            -
                        {% endif %}
                    </div>
                    <div class="d-flex-full">
                        <span class="bolder">Previous Klarna Adjustments:</span>
                        {% if order.klarna_adjustments %}
                            <ul>
                                {% for klarna_adjustment in order.klarna_adjustments %}
                                    <li>
                                        <a href="{% url 'admin:customer_service_klarnaadjustment_change' klarna_adjustment.pk %}">
                                            {{ klarna_adjustment }}
                                        </a>
                                    </li>
                                {% endfor %}
                            </ul>
                        {% else %}
                            -
                        {% endif %}
                    </div>
                </div>
                <table class="table aligned-table table-bordered mt-1">
                    <thead>
                    <tr>
                        <th scope="col">Currency</th>
                        <th scope="col">Promo Netto</th>
                        <th scope="col">Promo Brutto amount</th>
                    </tr>
                    </thead>
                    <tbody>
                    {% with order.region.get_currency as currency %}
                        {% if currency.code != 'EUR' %}
                            <tr>
                                <th scope="row">€</th>
                                <td>{{ order.promo_amount_net }}</td>
                                <td>{{ order.promo_amount }}</td>
                            </tr>
                        {% endif %}
                        <tr>
                            <th scope="row">{{ currency.symbol }}</th>
                            <td>{{ order.region_promo_amount_net }}</td>
                            <td>{{ order.region_promo_amount }}</td>
                        </tr>
                    {% endwith %}
                    </tbody>
                </table>

            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <form action="{% url 'cs_order_add_extra_discount_recalculation' order.pk %}" method="post">
                {% csrf_token %}
                {{ form|crispy }}
                <input type="submit" class="btn btn-info btn-next btn-prevent-doubleclick" value="Add"/>
            </form>

            <a href="{% url 'cs_user_overview' order.owner.pk %}" class="btn btn-info btn-cancel">Back</a>
        </div>
    </div>
{% endblock %}
