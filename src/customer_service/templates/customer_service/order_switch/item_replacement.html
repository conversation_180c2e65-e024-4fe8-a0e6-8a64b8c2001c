{% extends "customer_service/base.html" %}
{% load crispy_forms_tags static %}

{% block extrascripts %}
    <script src="{% static 'js/preventDoubleClick.js' %}"></script>
{% endblock %}

{% block content %}
    <div class="row">
        <div class="col-md-10">
            <h2>Switch Product - Status <b>"{{ order.get_switch_status_display }}"</b></h2>
            <p class="bolder">
                <a href="{% url 'cs_user_overview' order.owner.id %}">Order: {{ order.pk }}</a>
                / {{ order.first_name}} {{ order.last_name }}
            </p>
            <p>Assembly: {{ order.assembly|yesno:"Yes,No" }}</p>
            <div>
                <p>Estimated Delivery Time: {{ order.estimated_delivery_time }}</p>
                {% if order.used_promo %}
                    <hr/>
                    <p>Promo: {{ order.promo_text }}: <a href="{% url 'admin:vouchers_voucher_change' order.used_promo.pk %}">{{ order.used_promo }}</a></p>
                    {% with order.region.get_currency.symbol as currency %}
                        <p><b>Regionalized Promo:</b>
                            Netto: {{ order.region_promo_amount_net }} {{ currency }} /
                            Brutto: {{ order.region_promo_amount }} {{ currency }}
                        </p>
                    {% endwith %}
                    <p><b>In Euro:</b>
                        Netto: {{ order.promo_amount_net }} € /
                        Brutto: {{ order.promo_amount }} €
                    </p>
                {% endif %}
                <hr/>
                {% with order.region.get_currency.symbol as currency %}
                    <p>
                        Region Netto: {{ order.region_total_price_net }} {{ currency }}  /
                        Region Brutto: {{ order.region_total_price }} {{ currency }}
                    </p>
                {% endwith %}
                <p>
                    Netto: {{ order.total_price_net }} € /
                    Brutto: {{ order.total_price }} €
                </p>
            </div>
            {% include 'customer_service/order_switch/source_target_diff.html' %}
        </div>
    </div>

    <div class="row">
        <div class="col-md-8">
            <form action="{% url 'cs_order_switch_item_replacement' order.pk %}" method="post">
                {% csrf_token %}
                {{ form|crispy }}
                <input type="submit" class="btn btn-info btn-next btn-prevent-doubleclick" value="Recalculate Pricing" />
            </form>

            <form action="{% url 'cs_order_switch_rollback_item_on_hold' order.pk %}" method="post">
                {% csrf_token %}
                <input type="submit" class="btn btn-info btn-cancel" value="Cancel" />
            </form>
        </div>
    </div>
{% endblock %}

