{% extends "customer_service/base.html" %}
{% load humanize static %}
{% block extrascripts %}
    <script src="{% static 'js/exceptionalRelease.js' %}"></script>
{% endblock %}

{% block content %}
    <div class="row">
        <div class="col-md-10">
            <h2>Switch Product - Status <b>"{{ order.get_switch_status_display }}"</b></h2>
            <p class="bolder">
                <a href="{% url 'cs_user_overview' order.owner.id %}">Order: {{ order.pk }}</a>
                / {{ order.first_name}} {{ order.last_name }}
            </p>

            {% include 'customer_service/order_switch/source_target_diff.html' %}

            <form action="{% url 'cs_order_switch_commit_additional_payment' order.pk %}" method="post">
                {% csrf_token %}
                <input type="submit" class="btn btn-customer-paid btn-info btn-confirmation" value="Customer Paid"/>
            </form>
        </div>
    </div>
{% endblock %}
