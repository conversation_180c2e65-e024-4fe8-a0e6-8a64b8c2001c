{% extends "customer_service/base.html" %}
{% load static %}
{% block title %}Add promocode{% endblock %}

{% block extrahead %}

    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="{% static 'css/plugins/metisMenu/metisMenu.min.css' %}" rel="stylesheet">

    <!-- Timeline CSS -->
    <link href="{% static 'css/plugins/timeline.css' %}" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'css/sb-admin-2.css' %}" rel="stylesheet">

    <!-- Morris Charts CSS -->
    <link href="{% static 'css/plugins/morris.css' %}" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="{% static 'font-awesome-4.1.0/css/font-awesome.min.css' %}" rel="stylesheet" type="text/css">
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
        <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
        <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <link rel="stylesheet" type="text/css" href="{% static 'admin/css/forms.css' %}" />

{% endblock %}

{% block content_title %}{% endblock %}

{% block content %}

    <div class="panel panel-default">
        <div class="panel-body">
            <div class="row">
                <div class="col-lg-3">
                    <div class="panel panel-primary">
                        <div class="panel-heading">
                            <div class="row">
                                <div class="col-xs-3">
                                    <i class="fa fa-money fa-5x"></i>
                                </div>
                                <div class="col-xs-9 text-right">
                                    <div class="huge">{{ created_this_month }} / 500</div>
                                    <div>Vouchers created</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="row">
                <div class="col-lg-5">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h2>Add a Promocode</h2>
                        </div>
                        <div class="panel-body">
                            <form action="." method="post" class="js-validate" data-script="Forms">
                                {% csrf_token %}
                                <div>
                                    <fieldset class="module aligned ">{% for field in form %}
                                        <div class="form-row {% if field.name %} field-{{ field.name }}{% endif %}">
                                            <div class="fieldBox{% if field.name %} field-{{ field.name }}{% endif %}{% if not field.is_readonly and field.errors %} errors{% endif %}{% if field.is_hidden %} hidden{% endif %}">
                                                {{ field.errors }}
                                                {% if field.is_checkbox %}
                                                    {{ field.field }}{{ field.label_tag }}
                                                {% else %}
                                                    {{ field.label_tag }}
                                                    {% if field.is_readonly %}
                                                        <p>{{ field.contents }}</p>
                                                    {% else %}
                                                        {{ field }}
                                                    {% endif %}
                                                {% endif %}
                                                {% if field.help_text %}
                                                    <p class="help">{{ field.help_text|safe }}</p>
                                                {% endif %}
                                            </div>
                                        </div>
                                    {% endfor %}</fieldset>
                                </div>
                                <input type="submit" value="Save" class="default" name="_save">
                            </form>
                        </div>
                    </div>
                </div>
                {% if form.is_valid %}<div class="col-lg-5">
                    <div class="panel panel-default">
                        <div class="panel-heading">
                            <h2>New Promocodes</h2>
                        </div>
                        <div class="table-responsive">
                            <table class="table" style="margin:0">
                                <thead>
                                    <tr>
                                        <th>Kind of</th>
                                        <th>Code</th>
                                        <th>Value</th>
                                        <th>Uses</th>
                                        <th>End date</th>
                                        <th>Lower limit</th>
                                        <th>Upper limit</th>
                                    </tr>
                                </thead>
                                <tbody>{% for voucher in vouchers %}
                                    <tr>
                                        <td>{{ voucher.get_kind_of_display }}</td>
                                        <td>{{ voucher.code }}</td>
                                        <td>{{ voucher.value }}</td>
                                        <td>{{ voucher.quantity }}</td>
                                        <td>{{ voucher.end_date }}</td>
                                        <td>{{ voucher.amount_starts }}</td>
                                        <td>{{ voucher.amount_limit }}</td>
                                    </tr>
                                {% endfor %}</tbody>
                            </table>
                        </div>
                    </div>
                </div>{% endif %}
            </div>
        </div>
    </div>

{% endblock %}
