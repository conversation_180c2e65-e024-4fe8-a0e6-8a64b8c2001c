{% extends "customer_service/base.html" %}
{% load humanize %}
{% load cs_tags %}
{% load crispy_forms_tags %}

{% block content %}

    <div class="row">
        <div class="col-md-6">
            <h2>Invoice details:</h2>
            {% show_invoice_short_overview invoice correction %}
        </div>
        <div class="col-md-6">
            <h2>Request correction for invoice: {{ invoice.pretty_id }}</h2>
                {% if form.errors %}
                    <div id='form-errors'>
                    <h4>Errors</h4>
                        {{ form.errors }}
                    </div>
                {% endif %}
                <form action="" method="post">{% csrf_token %}
            {{ form|crispy }}
            <input type="submit" class="btn btn-default" value="Send" />
                </form>
        </div>
    </div>
{% endblock %}

{% block extrascripts %}
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const discount_tag_select = document.getElementById('id_discount_tag');
            const id_tag = document.getElementById('id_tag');
            id_tag.addEventListener('change', function (event) {
                if (id_tag.value !== '1') { // 1 = DISCOUNT QUALITY DISSATISFACTION
                    discount_tag_select.value = '---------';
                    discount_tag_select['disabled'] = true;
                } else {
                    discount_tag_select['disabled'] = false;
                }
            });
        });
    </script>
{% endblock %}

