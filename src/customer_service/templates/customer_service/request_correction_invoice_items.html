{% extends "customer_service/base.html" %}
{% load humanize %}
{% load cs_tags %}
{% load crispy_forms_tags %}

{% block content %}
<style>
 #id_deleted_invoice_items{
     width:auto;
 }
</style>
    <div class="row">
        <div class="col-md-6">
            <h2>Invoice details:</h2>
            {% show_invoice_short_overview invoice correction %}
        </div>
        <div class="col-md-6">
            <h2>Request correction for invoice: {{ invoice.pretty_id }}</h2>
                {% if form.errors %}
                    <div id='form-errors'>
                    <h4>Errors</h4>
                        {{ form.errors }}
                    </div>
                {% endif %}
                <form action="" method="post">{% csrf_token %}
            {{ form|crispy }}
            <input type="submit" class="btn btn-default" value="Send" />
                </form>
        </div>
    </div>
{% endblock %}
