{% extends "customer_service/base.html" %}
{% load humanize %}
{% load admin_tags %}

{% block title %}Pending Orders{% endblock %}

{% block content %}

<div class="row">
            <div class="col-lg-12 col-md-12">
                <div class="panel panel-default">
                    <div class="panel-heading">
                      Correction request status from
                      {% if from_datetime %}
                        last {{ from_datetime | timesince }}
                      {% else %}
                        all days
                      {% endif %}
                    </div>
                    <!-- /.panel-heading -->

                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                            <tr>
                                <th>By</th>
                                <th>Added</th>
                                <th>Updated</th>
                                <th>Invoice</th>
                                <th>Amount</th>
                                <th>Amount € (estimated)</th>
                                <th>Reason of correction</th>
                                <th>Description</th>
                                <th>Status</th>
                                <th>Changed by</th>
                            </tr>
                            </thead>
                            <tbody>
                            {% for correction in object_list %}
                                <tr>
                                    <td>
                                        {{ correction.issuer.username }}
                                    </td>
                                    <td>
                                        {{ correction.created_at|date:"d/m/y" }} <br/> {{ correction.created_at|naturaltime }}
                                    </td>
                                    <td>
                                        {{ correction.updated_at|date:"d/m/y" }} <br/> {{ correction.updated_at|naturaltime }}
                                    </td>
                                    <td>
                                        <a href="{% url 'cs_user_overview' correction.invoice.order.owner_id %}">{{ correction.invoice.pretty_id }}</a>
                                    </td>
                                    <td>
                                        {{ correction.correction_amount_gross }} {{ correction.invoice.currency_symbol }}
                                    </td>
                                    <td>
                                        {{ correction.get_estimated_euro_price }} €
                                    </td>
                                    <td>
                                        {{ correction.invoice_reason|safe }}
                                    </td>
                                    <td>
                                        {{ correction.correction_context|safe }}
                                    </td>
                                    <td>
                                        {{ correction.get_status_display}}
                                    </td>
                                    <td>
                                        {{ correction.reviewer.username}}
                                    </td>
                                </tr>
                            {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- /.panel-body -->
                </div>
            </div>
    </div>
{% endblock %}
