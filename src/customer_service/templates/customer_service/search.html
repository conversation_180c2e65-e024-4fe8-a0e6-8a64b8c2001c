{% extends "customer_service/base.html" %}
{% load humanize %}
{% load admin_tags %}
{% load static %}
{% block extrahead %}

    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="{% static 'css/plugins/metisMenu/metisMenu.min.css' %}" rel="stylesheet">

    <!-- Timeline CSS -->
    <link href="{% static 'css/plugins/timeline.css' %}" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'css/sb-admin-2.css' %}" rel="stylesheet">

    <!-- Morris Charts CSS -->
    <link href="{% static 'css/plugins/morris.css' %}" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="{% static 'font-awesome-4.1.0/css/font-awesome.min.css' %}" rel="stylesheet" type="text/css">
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
        <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
        <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->

{% endblock %}

{% block title %}Search {{ query }}"{% endblock %}

{% block content %}

<div id="wrapper">
     <div class="row">
        <div class="col-lg-12">
            <h1 class="page-header">Search results for {{ query }}</h1>
        </div>
    </div>
    <div class="row">{% for q_type, q_results in results.items %}
        {% if q_results|length > 0 %}
        <div class="col-lg-4">
            <div class="panel panel-default">
                <div class="panel-heading">
                    {{ q_type|capfirst }}
                </div>
                <div class="panel-body">
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered table-hover">{% for r in q_results %}
                            <tr>
                                <td width="30px">{{ forloop.counter }}</td>
                                {% if q_type == "userprofiles" %}
                                    {% if r.user_type == 3 %}
                                        <td>
                                            <a href="{% url "cs_user_overview" r.user_id %}" target="_blank">
                                                ID: {{ r.user_id }}, {{ r.get_user_type_display }} from {{ r.registration_country}}
                                            </a>
                                        </td>
                                    {% else %}
                                        <td>
                                            <a href="{% url "cs_user_overview" r.user_id %}" target="_blank">
                                                ID: {{ r.user_id }}, {{ r.user.username }} from {{ r.registration_country}}
                                            </a>
                                        </td>
                                        {% endif %}
                                {% elif q_type == "invoices"%}
                                    <td>
                                        <a href="{% url "cs_user_overview" r.order.owner_id %}" target="_blank">
                                            {{ r.pretty_id }} issued at {{ r.sell_at|naturalday }}
                                        </a>
                                    </td>
                                {% elif q_type == "carts"%}
                                    <td>
                                     <a href="{% url "cs_user_overview" r.owner_id %}" target="_blank">
                                            ID: {{ r.id }}, status: {{ r.get_status_display }}
                                            <br>
                                            Customer: {{ r.owner.id }} {{ r.owner.username }}
                                        </a>
                                    </td>
                                {% else %}
                                    <td>
                                        <a href="{% url "cs_user_overview" r.owner_id %}" target="_blank">
                                            ID: {{ r.id }}, {{ r.get_status_display }}, type: {{ r.get_order_type_display }}
                                            <br>
                                            Customer: {{ r.first_name }} {{ r.last_name }}
                                        </a>
                                    </td>
                                {% endif %}
                            </tr>
                        {% endfor %}</table>
                    </div>
                </div>
            </div>
        </div>
        {% endif %}
    {% endfor %}</div>
</div>

{% endblock %}
