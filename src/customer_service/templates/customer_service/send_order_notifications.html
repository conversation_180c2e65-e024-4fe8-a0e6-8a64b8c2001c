{% extends "customer_service/base.html" %}
{% load i18n %}
{% load static %}
{% load admin_urls %}
{% load activity_tags %}
{% load humanize %}
{% load admin_tags %}
{% load crispy_forms_tags %}

{% block extrahead %}
    <link href="{% static 'css/bootcards-desktop.css' %}" rel="stylesheet">
    <script src="{% static 'js/bootcards.min.js' %}"></script>
{% endblock %}


{% block content %}
    <div class="panel-body">
        <form action="" method="post">{% csrf_token %}
            {{ form|crispy }}
            <input type="submit" class="btn btn-danger" value="Send" />
            <a class="btn btn-warning" href="{% url 'cs_user_overview' owner_id %}">Back to user profile</a>
        </form>
    </div>
{% endblock %}
