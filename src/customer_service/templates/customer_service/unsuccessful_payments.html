{% extends "customer_service/base.html" %}
{% load humanize %}
{% load admin_tags %}

{% block title %}Pending Orders{% endblock %}

{% block content %}

    <div class="row">
        <div class="col-lg-12 col-md-12">
            <div class="panel panel-default">
                <div class="panel-heading">
                    Last 50 Pending Orders
                </div>
                <!-- /.panel-heading -->

                <div class="table-responsive">
                    <table class="table">
                        <thead>
                        <tr>
                            <th>Id</th>
                            <th>Customer</th>
                            <th>Source</th>
                            <th>Country</th>
                            <th>Price</th>
                            <th>Items</th>
                            <th>Updated date</th>
                        </tr>
                        </thead>
                        <tbody>
                        {% for order in object_list %}
                            <tr>
                                <td>
                                    {{ order.id }}
                                </td>
                                <td>
                                    <a href="{% url 'cs_user_overview' order.owner_id %}">
                                    {% if order.owner.profile.user_type == 2 %}
                                        {{ order.owner.username|truncatechars:60 }}
                                    {% else %}
                                        guest
                                    {% endif %}
                                     - {{ order.first_name }} {{ order.last_name }}
                                    </a>
                                </td>
                                <td>
                                    {{ order.get_order_source_display }}
                                </td>
                                <td>
                                    {{ order.get_country_short }}
                                </td>
                                <td>
                                    {{ order.total_price }} &euro;
                                </td>
                                <td>
                                    {{ order.get_items_as_string|safe }}
                                </td>
                                <td>
                                    {{ order.updated_at|date:"d/m/y" }} <br/> {{ order.updated_at|naturaltime }}
                                </td>
                            </tr>
                        {% endfor %}
                        </tbody>
                    </table>
                </div>

                <!-- /.panel-body -->
            </div>
        </div>
    </div>

{% endblock %}