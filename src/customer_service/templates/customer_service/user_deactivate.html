{% extends "customer_service/base.html" %}
{% block content %}
    <h1>Are you sure?</h1>
    <p> Are you sure you want to deactivate the {{ object.user }} account ?
        Here is the list of related orders and invoices for this user:</p>
    <h2>Orders</h2>
    <ul>
        {% for order in orders %}
            <li>{{ order }}</li>
        {% endfor %}
    </ul>
    <h2>Invoices</h2>
    <ul>
        {% for invoice in invoices %}
            <li>{{ invoice }}</li>
        {% endfor %}
    </ul>

    <form method="post">{% csrf_token %}
        <div>
            <input type="hidden" name="post" value="yes">
            <button class="btn btn-danger" type="submit">Yes, I’m sure</button>
            <a href="{% url 'cs_user_overview' object.user.id %}" class="btn btn-info">No, take me back</a>
        </div>
    </form>
{% endblock %}
