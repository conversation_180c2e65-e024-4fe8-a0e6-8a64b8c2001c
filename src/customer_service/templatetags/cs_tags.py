import itertools
import operator
import typing

from datetime import timed<PERSON>ta
from typing import Union

from django import template
from django.conf import settings
from django.contrib.sites.models import Site
from django.urls import reverse
from django.utils.html import format_html
from django.utils.safestring import mark_safe

from carts.choices import CartStatusChoices
from custom.logistic_enums import (
    ServiceDateProposalStatus,
    ServiceStatus,
)
from customer_service.enums import CSCorrectionRequestStatus
from customer_service.utils import TrackingLinkURLGenerator
from invoice.choices import InvoiceStatus
from orders.enums import OrderStatus
from producers.choices import ProductStatus
from producers.models import ProductBatch
from vouchers.enums import VoucherType

if typing.TYPE_CHECKING:
    from gallery.models.furniture_abstract import FurnitureAbstract
    from invoice.models import Invoice
    from orders.models import Order
    from vouchers.models import Voucher


register = template.Library()


SERVICE_STATUS_TO_DESCRIPTION_AND_COLOR = {
    ServiceStatus.TO_BE_RELEASED: {
        'description': '',
        'color_class': None,
    },
    ServiceStatus.IN_PROGRESS: {
        'description': 'No AS offered yet',
        'color_class': 'as_in_progress',
    },
    ServiceStatus.READY_TO_PROPOSE: {
        'description': 'Dates are ready to propose',
        'color_class': 'as_ready_to_propose',
    },
    ServiceStatus.PROPOSED_TO_CLIENT: {
        'description': 'Date proposed to client',
        'color_class': 'as_proposed_to_client',
    },
    ServiceStatus.ACCEPTED_BY_CLIENT: {
        'description': 'Date accepted by client',
        'color_class': 'as_accepted_by_client',
    },
    ServiceStatus.REJECTED_BY_CLIENT: {
        'description': 'Dates rejected by client',
        'color_class': 'as_rejected_by_client',
    },
    ServiceStatus.COMPLETED: {
        'description': (
            'The furniture is on its way to be assembled or '
            'the assembly has already been done'
        ),
        'color_class': None,
    },
    ServiceStatus.ABANDONED: {
        'description': '',
        'color_class': '',
    },
}


@register.filter
def pending_corrections(invoice: 'Invoice'):
    return invoice.cscorrectionrequest_set.filter(
        status=CSCorrectionRequestStatus.STATUS_NEW
    ).exists()


@register.filter
def corrected_to_0(invoice):
    return not bool(invoice.to_dict()['total_value'])


@register.filter
def filter_statuses(invoices):
    valid_statuses = {
        InvoiceStatus.ENABLED,
        InvoiceStatus.CORRECTING,
        InvoiceStatus.ENABLED_VAT_REGION_CORRECTION,
        InvoiceStatus.PROFORMA,
    }
    return invoices.filter(status__in=valid_statuses)


@register.filter
def filter_allowed_status_for_correction(invoice):
    return invoice.status in {
        InvoiceStatus.ENABLED,
        InvoiceStatus.CORRECTING,
        InvoiceStatus.ENABLED_VAT_REGION_CORRECTION,
    }


@register.filter
def has_correction(invoice):
    return invoice.corrections.filter(status=InvoiceStatus.CORRECTING).exists()


@register.filter
def can_make_correction(invoice):
    good_status = (
        filter_allowed_status_for_correction(invoice) and not invoice.is_domestic
    )
    return (
        good_status
        and not corrected_to_0(invoice)
        and not pending_corrections(invoice)
        and not has_correction(invoice)
    )


@register.simple_tag
def get_manufactors_for_logistic_order(logistic_order):
    batch_ids = set(
        [
            product.get_packages.get('batch_id')
            for product in logistic_order.get_packages
        ]
    )
    manufactors = ProductBatch.objects.filter(id__in=batch_ids).values_list(
        'manufactor__name', flat=True
    )
    mf = ''
    for manufactor_name in manufactors:
        if manufactor_name == 'Drewtur':
            mf += (
                '<span style="color:#9c2833;font-weight:bolder">'
                f'{manufactor_name}</span><br/>'
            )
        elif manufactor_name == 'Meblepl':
            mf += (
                '<span style="color:green;font-weight:bolder">'
                f'{manufactor_name}</span><br/>'
            )
        else:
            mf += manufactor_name + '<br/>'
    return format_html(mf)


@register.simple_tag
def get_items_for_logistic_order(logistic_order):
    return ', '.join(
        [product.get('product_id', '') for product in logistic_order.get_packages]
    )


@register.simple_tag
def get_packaging_data(logistic_order):
    resp = ''
    for product in logistic_order.get_packages:
        resp += f'<b>For product: {product["product_id"]} </b><br/>'
        product_weight = 0
        packaging_text = ''
        for package in product['packages']:
            product_weight += package['weight']
            packaging_text += f'{package["dimensions"]} {package["weight"]} kg <br/>'
            packaging_text += '<hr/>'
        resp += f'Total weight: {product_weight} kg <br/>'
        resp += packaging_text
    return mark_safe(resp)


@register.simple_tag
def damage_data_links(product):
    damage_data_list = product.damagedata_set.all()

    links = []

    for damage_data in damage_data_list:
        url = reverse('admin:complaints_damagedata_change', args=(damage_data.pk,))
        links.append(f'<a target="_blank" href="{url}">{str(damage_data)}</a>')

    return format_html('<br/>'.join(links))


@register.inclusion_tag('components/tag_short_order_overview.html')
def show_short_order_overview(order, payment_link=None, lat=None):
    return {
        'order': order,
        'payment_link': payment_link,
        'current_site': Site.objects.get_current().domain,
        'lat': lat,
    }


def _get_service_status_description(status):
    SERVICE_PLANNING_STATUS = [
        ServiceStatus.IN_PROGRESS.value,
        ServiceStatus.PROPOSED_TO_CLIENT.value,
        ServiceStatus.READY_TO_PROPOSE.value,
        ServiceStatus.ACCEPTED_BY_CLIENT.value,
        ServiceStatus.REJECTED_BY_CLIENT.value,
    ]

    SERVICE_COMPLETED_STATUS = [
        ServiceStatus.COMPLETED.value,
    ]
    if status in SERVICE_COMPLETED_STATUS:
        return 'completed'
    elif status in SERVICE_PLANNING_STATUS:
        return 'in progress'
    return ''


@register.inclusion_tag('components/tag_long_order_overview.html')
def show_long_order_overview(order, payment_link=None, lat=None, lat_order=None):
    from django_mailer.models import Message
    from producers.models import Product

    SERVICE_STATUSES_WITHOUT_DATES = [
        ServiceStatus.TO_BE_RELEASED.value,
        ServiceStatus.ABANDONED.value,
    ]

    mails = Message.objects.filter(to_address=order.email).order_by('id')
    dtfs = []
    email24_data = None
    carrier_name = '-'
    assembly_services_with_dates = []
    domestic_injection_mail_sent_at = None
    domestic_injection_mail_days = None
    domestic_injection_mail_contact_until_date = None

    if order.logistic_info:
        dtfs = itertools.chain(*[lo.dtf_proposals for lo in order.logistic_info])
        assembly_services = [
            logistic_order.assembly_service
            for logistic_order in order.logistic_info
            if logistic_order.assembly_service
        ]

        for assembly_service in assembly_services:
            if assembly_service.status in SERVICE_STATUSES_WITHOUT_DATES:
                continue
            service_status_description = _get_service_status_description(
                assembly_service.status
            )
            assembly_services_with_dates.append(
                {
                    'logistic_order': assembly_service.logistic_order,
                    'service_dates': sorted(
                        assembly_service.date_proposals, key=operator.attrgetter('id')
                    ),
                    'status': assembly_service.status,
                    'service_info': SERVICE_STATUS_TO_DESCRIPTION_AND_COLOR[
                        assembly_service.status
                    ],
                    'service_status_description': service_status_description,
                }
            )

        first_logistic_order = order.logistic_info[0]
        domestic_injection_mail_sent_at = (
            first_logistic_order.domestic_injection_mail_sent_at
        )
        if domestic_injection_mail_sent_at:
            domestic_injection_mail_days = first_logistic_order.domestic_injection_days
            domestic_injection_mail_contact_until_date = (
                domestic_injection_mail_sent_at
                + timedelta(days=domestic_injection_mail_days)
            )

        to_be_shipped_email24 = first_logistic_order.to_be_shipped_email24
        email24 = to_be_shipped_email24[0] if to_be_shipped_email24 else None
        if email24:
            email24_data = email24.get_data_for_customer_service_panel()
            carrier_name = first_logistic_order.carrier

        if domestic_injection_mail_sent_at or email24 or dtfs:
            carrier_name = first_logistic_order.carrier

    order_currency = order.currency or order.region.get_currency()
    cart = order.get_cart()

    return {
        'order': order,
        'order_currency': order_currency,
        'products': Product.all_objects.filter(order_id=order.id),
        'cs': True,
        'payment_link': payment_link,
        'current_site': Site.objects.get_current().domain,
        'lat': lat,
        'lat_order': lat_order,
        'mails': mails,
        'dtfs': dtfs,
        'assembly_services_with_dates': assembly_services_with_dates,
        'carrier_name': carrier_name,
        'email24': email24_data,
        'service_date_proposal_statuses': ServiceDateProposalStatus.__members__,
        'service_statuses': ServiceStatus.__members__,
        'product_statuses_to_cancel': [
            ProductStatus.NEW,
            ProductStatus.ASSIGNED_TO_PRODUCTION,
            ProductStatus.IN_PRODUCTION,
            ProductStatus.TO_BE_SHIPPED,
        ],
        'order_statuses_to_cancel': [
            OrderStatus.IN_PRODUCTION,
            OrderStatus.TO_BE_SHIPPED,
        ],
        'is_connected_to_active_cart': cart and cart.status == CartStatusChoices.ACTIVE,
        'domestic_injection_mail_sent_at': domestic_injection_mail_sent_at,
        'domestic_injection_mail_days': domestic_injection_mail_days,
        'domestic_injection_mail_contact_until_date': (
            domestic_injection_mail_contact_until_date
        ),
    }


@register.inclusion_tag('components/tag_long_logistic_overview.html')
def show_long_logistic_overview(order, logistic_order):
    # TODO: provide proper models and services for Assembly
    return {
        'order': order,
        'logistic_order': logistic_order,
        'order_region_has_assembly': order.region.name in settings.ASSEMBLY_REGION_KEYS,
        'cs': True,
    }


@register.inclusion_tag('components/tag_invoice_short_overview.html')
def show_invoice_short_overview(invoice, correction=None):
    strategy = invoice.get_render_strategy()
    context = strategy.create_context_for_pdf()
    context['corrections'] = []
    if correction:
        correction_c = strategy.create_context_for_pdf()
        context['invoice_items'] = correction_c['invoice_items']
        for attr in ('net_value', 'vat_value', 'total_value', 'currency_symbol'):
            context['invoice'][attr] = correction_c['invoice'][attr]
    for corr in invoice.corrections.order_by('created_at').all():
        context['corrections'].append(corr.to_dict())
    return context


@register.simple_tag
def generate_tracking_url(carrier_type: str, consignment: Union[str, int]) -> str:
    url = TrackingLinkURLGenerator(carrier_type).generate_url(consignment)
    if url:
        return mark_safe(f'<a target="_blank" href="{url}">Track Parcel</a>')
    return mark_safe('<p>Carrier does not support url tracking.</p>')


@register.simple_tag
def url_with_region_and_language(
    url_name: str,
    language_code: str,
    country_code: str,
    *args,
    **kwargs,
) -> str:
    url = reverse(url_name, args=args, kwargs=kwargs).split('/')
    url[1] = f'{language_code.lower()}-{country_code.lower()}'
    return '/'.join(url)


@register.filter
def display_voucher_kind_of(kind_of: int) -> str:
    return '€' if kind_of == VoucherType.ABSOLUTE else '%'


@register.filter
def voucher_value_difference_by_furniture_item(
    order: 'Order',
    furniture_item: 'FurnitureAbstract',
) -> float:
    return order.voucher_value_difference_by_furniture_item(furniture_item)


@register.filter
def get_discount_value_for_item(
    voucher: Union['Voucher', None],
    furniture_item: 'FurnitureAbstract',
) -> float:
    if voucher is None:
        return 0.0
    return voucher.get_discount_value_for_item(furniture_item)
