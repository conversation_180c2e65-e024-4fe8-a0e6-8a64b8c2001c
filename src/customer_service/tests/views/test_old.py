from collections import namedtuple

from django.urls import reverse

import pytest

from rest_framework import status

from customer_service.tests.factories import (
    CSCorrectionRequestFactory,
    CSOrderFactory,
)
from pricing_v3.services.price_calculators import OrderPriceCalculator

request = namedtuple('request', 'user')


def _set_up_correction(invoice, order, type_cs):
    correction_request = CSCorrectionRequestFactory(
        invoice=invoice,
        type_cs=type_cs,
        correction_amount_gross=sum(
            invoice_item.gross_price for invoice_item in invoice.invoice_items.all()
        ),
        issuer=order.owner,
    )
    return correction_request


@pytest.fixture
def order_with_fixed_price(
    db,
    mocker,
    currency_factory,
    currency_rate_factory,
    region_factory,
    region_rate_factory,
    order_item_factory,
    order_factory,
):
    mocker.patch('gallery.models.Jetty.get_shelf_price_as_number', return_value=125)
    currency = currency_factory(rates=[], code='EUR', name='Euro', symbol='€')
    currency_rate_factory(currency=currency, rate=1.0)
    region = region_factory(currency=currency, is_eu=True)
    region_rate_factory(region=region, rate=1.0)
    order = order_factory(
        items=None,
        country='Germany',
        region=region,
        status=1,
        owner__profile__region=region,
    )
    order_item = order_item_factory(
        region=region,
        order=order,
    )
    order.items.add(order_item)
    OrderPriceCalculator(order).calculate()
    return order


@pytest.fixture
def mock_3rd_party_dependencies(mocker):
    mocker.patch('kpi.kpis.google_analytics_funnel_fetch')
    mocker.patch('invoice.models.Invoice.create_pdf')
    mocker.patch(
        'invoice.models.Invoice._generate_pretty_id_db_sequence',
        return_value='00001/02/2021/1/DE',
    )
    mocker.patch('gallery.models.Jetty.get_weight', return_value=100)
    mocker.patch('gallery.models.Jetty.get_accurate_weight_gross', return_value=110)
    mocker.patch('gallery.models.Jetty.get_shelf_price_as_number', return_value=125)


@pytest.mark.nbp
@pytest.mark.google
class TestCSOrderPanel:
    @pytest.mark.usefixtures('mock_3rd_party_dependencies')
    def test_order_panel_available(
        self,
        order_with_fixed_price,
        admin_client,
    ):
        CSOrderFactory.create(
            id=order_with_fixed_price.id,
            owner_username=order_with_fixed_price.owner.username,
            status=order_with_fixed_price.status,
        )
        result = admin_client.get(
            f'{reverse("cs_search")}?q_orderid_direct={order_with_fixed_price.id}',
            follow=True,
        )
        assert result.status_code == status.HTTP_200_OK
        assert '/cs/user_overview/' in result.rendered_content

        order_panel = admin_client.get(
            reverse('cs_user_overview', args=[order_with_fixed_price.owner.id]),
            follow=True,
        )
        assert order_panel.status_code == status.HTTP_200_OK
        assert str(order_with_fixed_price.id) in order_panel.rendered_content
