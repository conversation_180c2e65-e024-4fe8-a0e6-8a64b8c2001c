from django.contrib import messages
from django.contrib.messages.views import SuccessMessageMixin
from django.shortcuts import redirect
from django.urls import reverse
from django.views.generic import FormView

from customer_service.correction_request_strategies import (
    get_correction_request_strategy,
)
from customer_service.enums import (
    CSCorrectionRequestStatus,
    CSCorrectionRequestType,
)
from customer_service.forms import (
    CSCorrectionAddressDataRequestForm,
    CSCorrectionAddressRequestForm,
    CSCorrectionInvoiceItemsRequestForm,
    CSCorrectionRequestForm,
    CSNeutralizeInvoiceForm,
)
from customer_service.utils import create_neutralize_invoice_correction_request
from customer_service.views import CustomerServiceMixinView
from invoice.choices import InvoiceStatus
from invoice.models import Invoice


class RequestCorrectionView(
    SuccessMessageMixin,
    CustomerServiceMixinView,
    FormView,
):
    template_name = 'customer_service/request_correction.html'
    form_class = CSCorrectionRequestForm
    success_url = '/cs/'
    success_message = 'Request for correction invoice sent!'

    def dispatch(self, request, *args, **kwargs):
        invoice = Invoice.objects.get(pk=self.kwargs['pk'])
        if invoice.pending_correction_requests.exists():
            messages.warning(request, 'Pending request already exists')
            return redirect(
                reverse('cs_user_overview', args=[invoice.order.owner_id]),
            )
        kwargs['instance'] = invoice
        return super().dispatch(request, *args, **kwargs)

    def get_initial(self):
        return {'invoice': self.kwargs['pk']}

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if 'instance' in kwargs:
            invoice = kwargs['invoice']
        else:
            invoice = Invoice.objects.get(pk=self.kwargs['pk'])
        if invoice.status == InvoiceStatus.CORRECTING and invoice.corrected_invoice:
            context['invoice'] = invoice.corrected_invoice
            context['correction'] = invoice
        else:
            context['invoice'] = invoice
        return context

    def form_valid(self, form):
        correction_request = form.save(commit=False)
        correction_request.issuer = self.request.user
        correction_request.status = CSCorrectionRequestStatus.STATUS_NEW
        correction_request.save()

        strategy = get_correction_request_strategy(correction_request)
        strategy.prepare_correction_request()
        data = self.prepare_form_data(form.cleaned_data)
        self.log_cs_data(
            self.request,
            'request_correction',
            correction_request._meta.label,
            correction_request.pk,
            data,
            str(self.form_class),
        )
        return super().form_valid(form)


class RequestCorrectionAddressView(
    RequestCorrectionView,
    SuccessMessageMixin,
    CustomerServiceMixinView,
    FormView,
):
    template_name = 'customer_service/request_correction_address.html'
    form_class = CSCorrectionAddressRequestForm
    success_url = '/cs/'
    success_message = 'Request for correction invoice sent!'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        invoice = Invoice.objects.get(pk=self.kwargs['pk'])
        if invoice.status == InvoiceStatus.CORRECTING and invoice.corrected_invoice:
            context['invoice'] = invoice.corrected_invoice
            context['correction'] = invoice
        else:
            context['invoice'] = invoice
        context['form2'] = CSCorrectionAddressDataRequestForm(
            initial=invoice.order.__dict__,
        )
        return context

    def post(self, request, *args, **kwargs):
        form2 = CSCorrectionAddressDataRequestForm(request.POST)
        form = self.get_form()
        if form.is_valid() and form2.is_valid():
            return self.form_valid(form, form2)
        return self.form_invalid(form, form2)

    def form_valid(self, form, form2):
        item = form.save(commit=False)
        item.type_cs = CSCorrectionRequestType.TYPE_ADDRESS.value
        item.issuer = self.request.user
        item.status = CSCorrectionRequestStatus.STATUS_NEW

        address = form2.save()
        item.cs_correction_request = address
        item.save()
        item.correction_amount_gross = 0
        item.save()
        return super().form_valid(form)


class RequestCorrectionInvoiceItemView(
    RequestCorrectionView,
    SuccessMessageMixin,
    CustomerServiceMixinView,
    FormView,
):
    template_name = 'customer_service/request_correction_invoice_items.html'
    form_class = CSCorrectionInvoiceItemsRequestForm
    success_url = '/cs/'
    success_message = 'Request for correction invoice sent!'

    def post(self, request, *args, **kwargs):
        """
        Handles POST requests, instantiating a form instance with the passed
        POST variables and then checked for validity.
        """
        invoice = Invoice.objects.get(pk=self.kwargs['pk'])
        form = CSCorrectionInvoiceItemsRequestForm(
            request.POST,
            inv=invoice,
            initial={'invoice': invoice.pk},
        )
        if form.is_valid():
            return self.form_valid(form)
        else:
            return self.form_invalid(form)

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        invoice = Invoice.objects.get(pk=self.kwargs['pk'])
        if invoice.status == InvoiceStatus.CORRECTING and invoice.corrected_invoice:
            context['invoice'] = invoice.corrected_invoice
            context['correction'] = invoice
        else:
            context['invoice'] = invoice
        if not getattr(kwargs.get('form', None), 'errors', None):
            context['form'] = CSCorrectionInvoiceItemsRequestForm(
                inv=invoice,
                initial={'invoice': invoice.pk},
            )
        return context

    def form_valid(self, form):
        item = form.save(commit=False)
        item.type_cs = CSCorrectionRequestType.TYPE_ITEMS
        item.issuer = self.request.user
        item.status = CSCorrectionRequestStatus.STATUS_NEW
        item.save()
        form.save_m2m()
        amount = 0
        for element in item.deleted_invoice_items.all():
            amount += element.gross_price
        item.correction_amount_gross = amount
        item.save()
        return super().form_valid(form)


class NeutralizeInvoiceView(
    SuccessMessageMixin,
    CustomerServiceMixinView,
    FormView,
):
    template_name = 'customer_service/neutralize_invoice.html'
    form_class = CSNeutralizeInvoiceForm
    success_url = '/cs/'
    success_message = 'Request for neutralize invoice sent!'

    def dispatch(self, request, *args, **kwargs):
        invoice = Invoice.objects.get(pk=self.kwargs['pk'])
        if invoice.pending_correction_requests.exists():
            messages.warning(request, 'Pending request already exists')
            return redirect(
                reverse('cs_user_overview', args=[invoice.order.owner_id]),
            )
        if invoice.get_total_net() == 0:
            messages.warning(
                request,
                'Unable to neutralize zero value invoice.',
            )
            return redirect(
                reverse('cs_user_overview', args=[invoice.order.owner_id]),
            )
        kwargs['instance'] = invoice
        return super().dispatch(request, *args, **kwargs)

    def get_initial(self):
        return {'invoice': self.kwargs['pk']}

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        if 'instance' in kwargs:
            invoice = kwargs['invoice']
        else:
            invoice = Invoice.objects.get(pk=self.kwargs['pk'])

        if invoice.status == InvoiceStatus.CORRECTING and invoice.corrected_invoice:
            context['invoice'] = invoice.corrected_invoice
            context['correction'] = invoice
        else:
            context['invoice'] = invoice
        return context

    def form_valid(self, form):
        correction_context = form.data['correction_context']
        invoice = Invoice.objects.get(pk=self.kwargs['pk'])
        create_neutralize_invoice_correction_request(
            invoice,
            self.request.user,
            correction_context,
            tag=int(form.data['tag']),
        )
        return super().form_valid(form)
