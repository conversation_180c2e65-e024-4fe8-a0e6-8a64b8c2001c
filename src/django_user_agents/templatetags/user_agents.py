from django import template

from abtests.models import ABTest
from regions.cached_region import get_region_data_from_request

from ..utils import get_and_set_user_agent

register = template.Library()


@register.filter()
def is_mobile(request):
    return get_and_set_user_agent(request).is_mobile or is_fb(request)


@register.filter()
def is_pc(request):
    return get_and_set_user_agent(request).is_pc


@register.filter()
def is_tablet(request):
    return (
        get_and_set_user_agent(request).is_tablet
        or 'ipad' in str(get_and_set_user_agent(request)).lower()
    )


@register.filter()
def is_bot(request):
    return get_and_set_user_agent(request).is_bot


@register.filter()
def is_touch_capable(request):
    return get_and_set_user_agent(request).is_touch_capable


@register.filter()
def is_ios(request):
    return get_and_set_user_agent(request).os.family == 'iOS'


@register.filter()
def is_android(request):
    return get_and_set_user_agent(request).os.family == 'Android'


@register.filter()
def is_ios_11(request):
    parsed_ua = get_and_set_user_agent(request)
    return parsed_ua.os.family == 'iOS' and parsed_ua.os.version[0] <= 11


@register.filter()
def is_safari_11(request):
    parsed_ua = get_and_set_user_agent(request)
    return parsed_ua.browser.family == 'Safari' and parsed_ua.browser.version[0] <= 11


@register.filter()
def is_ie11(request):
    browser = get_and_set_user_agent(request).browser
    its_ie11 = False
    if (
        browser.family == 'IE'
        and len(browser.version) == 2
        and browser.version[0] <= 11
    ):
        its_ie11 = True
    return its_ie11


@register.filter()
def is_ie(request):
    browser = get_and_set_user_agent(request).browser
    its_ie = False
    if browser.family == 'IE' or browser.family == 'EDGE':
        its_ie = True
    return its_ie


@register.filter()
def is_chrome(request):
    browser = get_and_set_user_agent(request).browser
    its_chrome = False
    if browser.family == 'Chrome':
        its_chrome = True
    return its_chrome


@register.filter()
def is_webp_support(request):
    browser = get_and_set_user_agent(request).browser
    is_supported = False
    if (
        browser.family == 'Chrome'
        or browser.family == 'Firefox'
        or browser.family == 'EDGE'
        or browser.family == 'Opera'
    ):
        is_supported = True
    return is_supported


@register.filter()
def is_fb(request):
    ua_string = request.META.get('HTTP_USER_AGENT', '').lower()
    return 'fban' in ua_string or 'fbav' in ua_string or 'fb_iab' in ua_string


@register.filter()
def not_decent_browser(request):
    browser = get_and_set_user_agent(request).browser
    parsed_ua = get_and_set_user_agent(request)

    # so, decent browser for us is edge (all versions),
    # safari (11<), chrome (65+), firefox (40+)
    if browser.family == 'EDGE':
        return False
    if parsed_ua.browser.family == 'Safari' and (
        len(parsed_ua.browser.version) > 0 and parsed_ua.browser.version[0] > 11
    ):
        return False
    if parsed_ua.browser.family == 'Chrome' and (
        len(parsed_ua.browser.version) > 0 and parsed_ua.browser.version[0] > 66
    ):
        return False
    if parsed_ua.browser.family == 'Firefox' and (
        len(parsed_ua.browser.version) > 0 and parsed_ua.browser.version[0] > 55
    ):
        return False

    return True


@register.filter()
def is_ab_test(request, ab_codename=None):
    if ab_codename is None:
        return False

    if is_active_feature_flag(request, ab_codename):
        return True

    if request.COOKIES.get(ab_codename, None) == 'ok' or (
        hasattr(request, 'session') and request.session.get(ab_codename, None)
    ):
        if getattr(request, 'cached_abtests', None) is not None:
            return (
                len(
                    [
                        abtest
                        for abtest in request.cached_abtests
                        if abtest.codename == ab_codename
                    ]
                )
                > 0
            )
        else:
            return any(
                test.codename == ab_codename
                for test in ABTest.objects.get_active_tests_cached_list(
                    get_region_data_from_request(request)
                )
            )
    else:
        return False


def is_active_feature_flag(request, ab_codename):
    ab_test_split_rate = request.feature_flag_rates.get(ab_codename)
    if not ab_test_split_rate:
        return False
    if ab_test_split_rate == 100:
        return True
    if request.COOKIES.get(ab_codename, None) == 'ok' and ab_test_split_rate != 0:
        return True
    return False


@register.filter()
def is_feature_flag(request, ab_codename=None):
    if ab_codename is None:
        return False

    return any(
        test.codename == ab_codename and test.feature_flag
        for test in ABTest.objects.get_tests_cached()
    )
