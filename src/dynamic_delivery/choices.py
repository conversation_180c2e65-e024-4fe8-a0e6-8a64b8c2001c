from django.db.models import IntegerChoices

from custom.utils.delivery import TimeHelper


class FeatureChoice(IntegerChoices):
    NO_FEATURE = 1
    WITH_DOORS = 2
    WITH_DRAWERS = 3


class ShipInRangeChoice(IntegerChoices):
    WEEKS_2_3 = 1, '2-3 weeks'
    WEEKS_3_4 = 2, '3-4 weeks'
    WEEKS_4_5 = 3, '4-5 weeks'
    WEEKS_5_6 = 4, '5-6 weeks'
    WEEKS_6_7 = 5, '6-7 weeks'
    WEEKS_7_8 = 6, '7-8 weeks'
    WEEKS_8_9 = 7, '8-9 weeks'
    WEEKS_9_10 = 8, '9-10 weeks'
    WEEKS_10_11 = 9, '10-11 weeks'
    WEEKS_11_12 = 10, '11-12 weeks'
    WEEKS_12_13 = 11, '12-13 weeks'
    WEEKS_13_14 = 12, '13-14 weeks'
    WEEKS_14_15 = 13, '14-15 weeks'
    WEEKS_15_16 = 14, '15-16 weeks'
    WEEKS_16_17 = 15, '16-17 weeks'
    WEEKS_17_18 = 16, '17-18 weeks'
    WEEKS_18_19 = 17, '18-19 weeks'
    WEEKS_19_20 = 18, '19-20 weeks'
    WEEKS_20_21 = 19, '20-21 weeks'
    WEEKS_21_22 = 20, '21-22 weeks'
    WEEKS_22_23 = 21, '22-23 weeks'
    WEEKS_23_24 = 22, '23-24 weeks'
    WEEKS_24_25 = 23, '24-25 weeks'

    @classmethod
    def get_range_base_on_week_occupancy(cls, occupancy: float):
        ranges = {
            (0, 3): cls.WEEKS_2_3,
            (3, 4): cls.WEEKS_3_4,
            (4, 5): cls.WEEKS_4_5,
            (5, 6): cls.WEEKS_5_6,
            (6, 7): cls.WEEKS_6_7,
            (7, 8): cls.WEEKS_7_8,
            (8, 9): cls.WEEKS_8_9,
            (9, 10): cls.WEEKS_9_10,
            (10, 11): cls.WEEKS_10_11,
            (11, 12): cls.WEEKS_11_12,
            (12, 13): cls.WEEKS_12_13,
            (13, 14): cls.WEEKS_13_14,
            (14, 15): cls.WEEKS_14_15,
            (15, 16): cls.WEEKS_15_16,
            (16, 17): cls.WEEKS_16_17,
            (17, 18): cls.WEEKS_17_18,
            (18, 19): cls.WEEKS_18_19,
            (19, 20): cls.WEEKS_19_20,
            (20, 21): cls.WEEKS_20_21,
            (21, 22): cls.WEEKS_21_22,
            (22, 23): cls.WEEKS_22_23,
            (23, 24): cls.WEEKS_23_24,
            (24, 25): cls.WEEKS_24_25,
        }
        for week_range, value in ranges.items():
            if week_range[0] <= occupancy < week_range[1]:
                return cls(value)
        return cls.get_max_range()

    @classmethod
    def get_max_range(cls) -> 'ShipInRangeChoice':
        return cls(max(cls.values))

    def get_time_helper(self):
        days = {
            self.WEEKS_2_3: 3 * 7,
            self.WEEKS_3_4: 4 * 7,
            self.WEEKS_4_5: 5 * 7,
            self.WEEKS_5_6: 6 * 7,
            self.WEEKS_6_7: 7 * 7,
            self.WEEKS_7_8: 8 * 7,
            self.WEEKS_8_9: 9 * 7,
            self.WEEKS_9_10: 10 * 7,
            self.WEEKS_10_11: 11 * 7,
            self.WEEKS_11_12: 12 * 7,
            self.WEEKS_12_13: 13 * 7,
            self.WEEKS_13_14: 14 * 7,
            self.WEEKS_14_15: 15 * 7,
            self.WEEKS_15_16: 16 * 7,
            self.WEEKS_16_17: 17 * 7,
            self.WEEKS_17_18: 18 * 7,
            self.WEEKS_18_19: 19 * 7,
            self.WEEKS_19_20: 20 * 7,
            self.WEEKS_20_21: 21 * 7,
            self.WEEKS_21_22: 22 * 7,
            self.WEEKS_22_23: 23 * 7,
            self.WEEKS_23_24: 24 * 7,
            self.WEEKS_24_25: 25 * 7,
        }
        return TimeHelper(days[self])

    def get_weeks_range(self):
        ranges = {
            self.WEEKS_2_3: (2, 3),
            self.WEEKS_3_4: (3, 4),
            self.WEEKS_4_5: (4, 5),
            self.WEEKS_5_6: (5, 6),
            self.WEEKS_6_7: (6, 7),
            self.WEEKS_7_8: (7, 8),
            self.WEEKS_8_9: (8, 9),
            self.WEEKS_9_10: (9, 10),
            self.WEEKS_10_11: (10, 11),
            self.WEEKS_11_12: (11, 12),
            self.WEEKS_12_13: (12, 13),
            self.WEEKS_13_14: (13, 14),
            self.WEEKS_14_15: (14, 15),
            self.WEEKS_15_16: (15, 16),
            self.WEEKS_16_17: (16, 17),
            self.WEEKS_17_18: (17, 18),
            self.WEEKS_18_19: (18, 19),
            self.WEEKS_19_20: (19, 20),
            self.WEEKS_20_21: (20, 21),
            self.WEEKS_21_22: (21, 22),
            self.WEEKS_22_23: (22, 23),
            self.WEEKS_23_24: (23, 24),
            self.WEEKS_24_25: (24, 25),
        }
        week_range = ranges.get(self)
        return {
            'min': week_range[0],
            'max': week_range[1],
        }

    @property
    def max(self):
        weeks_range = self.get_weeks_range()
        return weeks_range['max']

    @property
    def min(self):
        weeks_range = self.get_weeks_range()
        return weeks_range['min']

    @property
    def max_days(self):
        return self.max * 7

    def add_weeks(self, weeks_to_add: int):
        max_ship_in_range = ShipInRangeChoice.get_max_range()
        if max_ship_in_range.value >= self.value + weeks_to_add:
            return ShipInRangeChoice(self + weeks_to_add)
        return max_ship_in_range

    def subtrack_weeks(self, weeks_to_subtract: int):
        if self.value - weeks_to_subtract > 0:
            return ShipInRangeChoice(self - weeks_to_subtract)
        return ShipInRangeChoice.WEEKS_2_3
