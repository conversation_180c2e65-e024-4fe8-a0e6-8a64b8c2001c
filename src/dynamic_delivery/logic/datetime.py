import datetime


def date_from(year, week_number, weekday=5, as_datetime=False):
    date = datetime.datetime.strptime(
        # format: ISO_YEAR WEEK_NUMBER ISO_WEEK_DAY
        '{0:04d} {1:02d} {2}'.format(year, week_number, weekday),
        '%G %V %u',
    )
    if not as_datetime:
        date = date.date()
    # fix for Python's ``strptime`` formatting, because it treats 53 week
    # of 52 weeks' year as first week of next year
    if week_number == 53:
        if (year, week_number, weekday) != date.isocalendar():
            raise ValueError(
                'Year {0} does not contain week number {1}'.format(
                    year,
                    week_number,
                ),
            )
    return date
