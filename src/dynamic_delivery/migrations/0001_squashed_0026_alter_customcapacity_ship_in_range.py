# Generated by Django 4.1.9 on 2023-10-27 16:34

import datetime

import django.db.models.deletion

from django.conf import settings
from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    replaces = [
        ('dynamic_delivery', '0001_initial'),
        ('dynamic_delivery', '0002_typicalproductioncapacity_manufactor'),
        ('dynamic_delivery', '0003_add_capacity_for_new_type01_colors'),
        ('dynamic_delivery', '0004_add_productinplanning'),
        ('dynamic_delivery', '0005_add_type03_shelf_type'),
        ('dynamic_delivery', '0006_django31_jsonfield'),
        ('dynamic_delivery', '0007_add_mat_black_color'),
        ('dynamic_delivery', '0008_add_blue_burgundy_cotton_colors_type02'),
        ('dynamic_delivery', '0009_add_material_9'),
        ('dynamic_delivery', '0010_change_blue_to_sky_blue'),
        ('dynamic_delivery', '0011_change_mat_black_to_matte_black'),
        ('dynamic_delivery', '0012_change_pink_to_dusty_pink'),
        ('dynamic_delivery', '0013_historicalproductplanningdata'),
        ('dynamic_delivery', '0014_add_new_colors_capacity_type02'),
        ('dynamic_delivery', '0015_add_new_typ03_colors'),
        ('dynamic_delivery', '0016_add_capacity_for_lilac_and_forest_green_colors'),
        ('dynamic_delivery', '0017_delete_by_cascade'),
        ('dynamic_delivery', '0018_shelf_type_13'),
        ('dynamic_delivery', '0019_add_type_13'),
        ('dynamic_delivery', '0020_manufactorcapacity_shelfcapacity_weekcapacity'),
        ('dynamic_delivery', '0021_customcapacity'),
        ('dynamic_delivery', '0022_alter_customcapacity_unique_together'),
        ('dynamic_delivery', '0023_alter_customcapacity_ship_in_range'),
        ('dynamic_delivery', '0024_remove_old_dynamic__delivery'),
        ('dynamic_delivery', '0025_customcapacity_all_colors'),
        ('dynamic_delivery', '0026_alter_customcapacity_ship_in_range'),
    ]

    initial = True

    dependencies = [
        ('producers', '0001_squashed_0039_product_has_plus_feature'),
        (
            'producers',
            '0041_remove_priority_complaint_for_products_squashed_0091_alter_manufactor_invoice_company_name_and_more',  # noqa E501
        ),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='WeekCapacity',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('capacity', models.IntegerField(default=0)),
                ('margin_of_error', models.FloatField(default=0.05)),
                ('backlog', models.IntegerField(default=0)),
                ('valid_from', models.DateTimeField(default=datetime.datetime.now)),
                ('sent_to_production', models.IntegerField(default=0)),
            ],
            options={
                'verbose_name_plural': 'Week Capacities',
            },
        ),
        migrations.CreateModel(
            name='ShelfCapacity',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('capacity', models.IntegerField(default=0)),
                ('margin_of_error', models.FloatField(default=0.05)),
                ('backlog', models.IntegerField(default=0)),
                ('valid_from', models.DateTimeField(default=datetime.datetime.now)),
                (
                    'shelf_type',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, 'TYPE01'),
                            (1, 'TYPE02'),
                            (2, 'VENEER TYPE01'),
                            (3, 'TYPE03'),
                            (4, 'TYPE13'),
                        ]
                    ),
                ),
                (
                    'base',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='dynamic_delivery.weekcapacity',
                    ),
                ),
            ],
            options={
                'verbose_name_plural': 'Shelves Capacities',
            },
        ),
        migrations.CreateModel(
            name='ManufactorCapacity',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('capacity', models.IntegerField(default=0)),
                ('margin_of_error', models.FloatField(default=0.05)),
                ('backlog', models.IntegerField(default=0)),
                ('valid_from', models.DateTimeField(default=datetime.datetime.now)),
                (
                    'base',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='dynamic_delivery.weekcapacity',
                    ),
                ),
                (
                    'manufactor',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='producers.manufactor',
                    ),
                ),
            ],
            options={
                'verbose_name_plural': 'Manufactors Capacities',
            },
        ),
        migrations.CreateModel(
            name='CustomCapacity',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'shelf_type',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, 'TYPE01'),
                            (1, 'TYPE02'),
                            (2, 'VENEER TYPE01'),
                            (3, 'TYPE03'),
                            (4, 'TYPE13'),
                        ]
                    ),
                ),
                ('is_desk', models.BooleanField(default=False)),
                ('color', models.PositiveSmallIntegerField(null=True)),
                (
                    'feature',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, 'No Feature'),
                            (2, 'With Doors'),
                            (3, 'With Drawers'),
                        ],
                        default=1,
                    ),
                ),
                (
                    'ship_in_range',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, '2-3 weeks'),
                            (2, '3-4 weeks'),
                            (3, '4-5 weeks'),
                            (4, '5-6 weeks'),
                            (5, '6-7 weeks'),
                            (6, '7-8 weeks'),
                            (7, '8-9 weeks'),
                            (8, '9-10 weeks'),
                            (9, '10-11 weeks'),
                            (10, '11-12 weeks'),
                            (11, '12-13 weeks'),
                            (12, '13-14 weeks'),
                            (13, '14-15 weeks'),
                        ]
                    ),
                ),
                ('active', models.BooleanField(default=True)),
                ('all_colors', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name_plural': 'Custom Capacities',
                'unique_together': {('shelf_type', 'is_desk', 'color', 'feature')},
            },
        ),
    ]
