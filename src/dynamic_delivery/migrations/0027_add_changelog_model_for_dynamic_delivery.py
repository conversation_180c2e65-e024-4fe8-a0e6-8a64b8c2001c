# Generated by Django 4.1.9 on 2023-11-15 14:29

import django.db.models.deletion

from django.conf import settings
from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('dynamic_delivery', '0001_squashed_0026_alter_customcapacity_ship_in_range'),
    ]

    operations = [
        migrations.CreateModel(
            name='ChangeLog',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('model_name', models.Char<PERSON>ield(max_length=255)),
                ('changes', models.J<PERSON>NField()),
                (
                    'user',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
    ]
