# Generated by Django 4.1.9 on 2024-02-20 13:15

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('dynamic_delivery', '0027_add_changelog_model_for_dynamic_delivery'),
    ]

    operations = [
        migrations.AlterField(
            model_name='customcapacity',
            name='shelf_type',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (0, 'TYPE01'),
                    (1, 'TYPE02'),
                    (2, 'VENEER TYPE01'),
                    (3, 'TYPE03'),
                    (4, 'TYPE13'),
                    (5, 'VENEER TYPE13'),
                ]
            ),
        ),
        migrations.AlterField(
            model_name='shelfcapacity',
            name='shelf_type',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (0, 'TYPE01'),
                    (1, 'TYPE02'),
                    (2, 'VENEER TYPE01'),
                    (3, 'TYPE03'),
                    (4, 'TYPE13'),
                    (5, 'VENEER TYPE13'),
                ]
            ),
        ),
    ]
