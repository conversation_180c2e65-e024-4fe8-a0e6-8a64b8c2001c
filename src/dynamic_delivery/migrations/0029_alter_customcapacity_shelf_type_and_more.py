# Generated by Django 4.1.9 on 2024-05-14 14:09

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('dynamic_delivery', '0028_alter_customcapacity_shelf_type_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='customcapacity',
            name='shelf_type',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (0, 'TYPE01'),
                    (1, 'TYPE02'),
                    (2, 'VENEER TYPE01'),
                    (3, 'TYPE03'),
                    (4, 'TYPE13'),
                    (5, 'VENEER TYPE13'),
                    (6, 'TYPE23'),
                    (7, 'TYPE24'),
                    (8, 'TYPE25'),
                ]
            ),
        ),
        migrations.AlterField(
            model_name='shelfcapacity',
            name='shelf_type',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (0, 'TYPE01'),
                    (1, 'TYPE02'),
                    (2, 'VENEER TYPE01'),
                    (3, 'TYPE03'),
                    (4, 'TYPE13'),
                    (5, 'VENEER TYPE13'),
                    (6, 'TYPE23'),
                    (7, 'TYPE24'),
                    (8, 'TYPE25'),
                ]
            ),
        ),
    ]
