# Generated by Django 4.1.13 on 2024-09-12 09:33

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('dynamic_delivery', '0029_alter_customcapacity_shelf_type_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='customcapacity',
            name='ship_in_range',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (1, '2-3 weeks'),
                    (2, '3-4 weeks'),
                    (3, '4-5 weeks'),
                    (4, '5-6 weeks'),
                    (5, '6-7 weeks'),
                    (6, '7-8 weeks'),
                    (7, '8-9 weeks'),
                    (8, '9-10 weeks'),
                    (9, '10-11 weeks'),
                    (10, '11-12 weeks'),
                    (11, '12-13 weeks'),
                    (12, '13-14 weeks'),
                    (13, '14-15 weeks'),
                    (14, '15-16 weeks'),
                    (15, '16-17 weeks'),
                    (16, '17-18 weeks'),
                    (17, '18-19 weeks'),
                ]
            ),
        ),
    ]
