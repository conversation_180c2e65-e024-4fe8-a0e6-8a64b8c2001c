# Generated by Django 4.1.13 on 2024-10-29 10:54

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('dynamic_delivery', '0031_alter_customcapacity_ship_in_range'),
    ]

    operations = [
        migrations.CreateModel(
            name='ProductionPauseWeek',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('week', models.IntegerField()),
                ('year', models.IntegerField()),
                (
                    'base',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='dynamic_delivery.weekcapacity',
                    ),
                ),
            ],
        ),
    ]
