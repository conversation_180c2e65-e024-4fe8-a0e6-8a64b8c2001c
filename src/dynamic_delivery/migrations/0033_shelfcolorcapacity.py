# Generated by Django 4.1.13 on 2024-11-14 10:17

import datetime

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)

import dynamic_delivery.utils


class Migration(migrations.Migration):

    dependencies = [
        ('dynamic_delivery', '0032_productionpauseweek'),
    ]

    operations = [
        migrations.CreateModel(
            name='ShelfColorCapacity',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('capacity', models.IntegerField(default=0)),
                ('margin_of_error', models.FloatField(default=0.05)),
                ('backlog', models.IntegerField(default=0)),
                ('valid_from', models.DateTimeField(default=datetime.datetime.now)),
                (
                    'shelf_type',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, 'TYPE01'),
                            (1, 'TYPE02'),
                            (2, 'VENEER TYPE01'),
                            (3, 'TYPE03'),
                            (4, 'TYPE13'),
                            (5, 'VENEER TYPE13'),
                            (6, 'TYPE23'),
                            (7, 'TYPE24'),
                            (8, 'TYPE25'),
                        ]
                    ),
                ),
                ('color', models.PositiveSmallIntegerField(default=0)),
                (
                    'base',
                    models.ForeignKey(
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='dynamic_delivery.weekcapacity',
                    ),
                ),
            ],
            options={
                'verbose_name_plural': 'Shelves Colors Capacities',
                'unique_together': {('shelf_type', 'color')},
            },
            bases=(dynamic_delivery.utils.ShipInRangeAutoLogChangeMixin, models.Model),
        ),
    ]
