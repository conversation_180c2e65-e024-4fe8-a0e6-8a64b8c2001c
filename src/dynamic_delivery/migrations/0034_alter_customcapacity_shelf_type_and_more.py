# Generated by Django 4.1.13 on 2024-12-02 14:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('dynamic_delivery', '0033_shelfcolorcapacity'),
    ]

    operations = [
        migrations.AlterField(
            model_name='customcapacity',
            name='shelf_type',
            field=models.PositiveSmallIntegerField(choices=[(0, 'TYPE01'), (1, 'TYPE02'), (2, 'VENEER TYPE01'), (3, 'TYPE03'), (4, 'TYPE13'), (5, 'VENEER TYPE13'), (6, 'TYPE23'), (7, 'TYPE24'), (8, 'TYPE25'), (10, 'SOFA TYPE01')]),
        ),
        migrations.AlterField(
            model_name='shelfcapacity',
            name='shelf_type',
            field=models.PositiveSmallIntegerField(choices=[(0, 'TYPE01'), (1, 'TYPE02'), (2, 'VENEER TYPE01'), (3, 'TYPE03'), (4, 'TYPE13'), (5, 'VENEER TYPE13'), (6, 'TYPE23'), (7, 'TYPE24'), (8, 'TYPE25'), (10, 'SOFA TYPE01')]),
        ),
        migrations.AlterField(
            model_name='shelfcolorcapacity',
            name='shelf_type',
            field=models.PositiveSmallIntegerField(choices=[(0, 'TYPE01'), (1, 'TYPE02'), (2, 'VENEER TYPE01'), (3, 'TYPE03'), (4, 'TYPE13'), (5, 'VENEER TYPE13'), (6, 'TYPE23'), (7, 'TYPE24'), (8, 'TYPE25'), (10, 'SOFA TYPE01')]),
        ),
    ]
