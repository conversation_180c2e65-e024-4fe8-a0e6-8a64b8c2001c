# Generated by Django 4.1.13 on 2025-01-21 15:07

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('dynamic_delivery', '0034_alter_customcapacity_shelf_type_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='ShelfColorRange',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'shelf_type',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, 'TYPE01'),
                            (1, 'TYPE02'),
                            (2, 'VENEER TYPE01'),
                            (3, 'TYPE03'),
                            (4, 'TYPE13'),
                            (5, 'VENEER TYPE13'),
                            (6, 'TYPE23'),
                            (7, 'TYPE24'),
                            (8, 'TYPE25'),
                            (10, 'SOFA TYPE01'),
                        ]
                    ),
                ),
                ('color', models.PositiveSmallIntegerField(default=0)),
                (
                    'min_ship_in_range',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, '2-3 weeks'),
                            (2, '3-4 weeks'),
                            (3, '4-5 weeks'),
                            (4, '5-6 weeks'),
                            (5, '6-7 weeks'),
                            (6, '7-8 weeks'),
                            (7, '8-9 weeks'),
                            (8, '9-10 weeks'),
                            (9, '10-11 weeks'),
                            (10, '11-12 weeks'),
                            (11, '12-13 weeks'),
                            (12, '13-14 weeks'),
                            (13, '14-15 weeks'),
                            (14, '15-16 weeks'),
                            (15, '16-17 weeks'),
                            (16, '17-18 weeks'),
                            (17, '18-19 weeks'),
                            (18, '19-20 weeks'),
                            (19, '20-21 weeks'),
                            (20, '21-22 weeks'),
                            (21, '22-23 weeks'),
                            (22, '23-24 weeks'),
                            (23, '24-25 weeks'),
                        ],
                        default=1,
                    ),
                ),
                (
                    'max_ship_in_range',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, '2-3 weeks'),
                            (2, '3-4 weeks'),
                            (3, '4-5 weeks'),
                            (4, '5-6 weeks'),
                            (5, '6-7 weeks'),
                            (6, '7-8 weeks'),
                            (7, '8-9 weeks'),
                            (8, '9-10 weeks'),
                            (9, '10-11 weeks'),
                            (10, '11-12 weeks'),
                            (11, '12-13 weeks'),
                            (12, '13-14 weeks'),
                            (13, '14-15 weeks'),
                            (14, '15-16 weeks'),
                            (15, '16-17 weeks'),
                            (16, '17-18 weeks'),
                            (17, '18-19 weeks'),
                            (18, '19-20 weeks'),
                            (19, '20-21 weeks'),
                            (20, '21-22 weeks'),
                            (21, '22-23 weeks'),
                            (22, '23-24 weeks'),
                            (23, '24-25 weeks'),
                        ],
                        default=23,
                    ),
                ),
            ],
            options={
                'verbose_name_plural': 'Shelf Colors Ranges',
                'unique_together': {('shelf_type', 'color')},
            },
        ),
    ]
