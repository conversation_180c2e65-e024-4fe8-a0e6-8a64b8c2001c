# Generated by Django 4.1.13 on 2025-03-07 11:18

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('dynamic_delivery', '0035_shelfcolorrange'),
    ]

    operations = [
        migrations.CreateModel(
            name='ShelfColorCategorySubtract',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'shelf_type',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (0, 'TYPE01'),
                            (1, 'TYPE02'),
                            (2, 'VENEER TYPE01'),
                            (3, 'TYPE03'),
                            (4, 'TYPE13'),
                            (5, 'VENEER TYPE13'),
                            (6, 'TYPE23'),
                            (7, 'TYPE24'),
                            (8, 'TYPE25'),
                            (10, 'SOFA TYPE01'),
                        ]
                    ),
                ),
                (
                    'furniture_category',
                    models.CharField(
                        choices=[
                            ('vinyl_storage', 'Vinyl Storage'),
                            ('vinylstorage', 'Vinyl Storage Fe'),
                            ('chest', 'Chest'),
                            ('shoerack', 'Shoerack'),
                            ('tvstand', 'Tv Stand'),
                            ('sideboard', 'Sideboard'),
                            ('bookcase', 'Bookcase'),
                            ('wallstorage', 'Wall Storage'),
                            ('wardrobe', 'Wardrobe'),
                            ('bedside_table', 'Bedside Table'),
                            ('desk', 'Desk'),
                        ],
                        max_length=255,
                    ),
                ),
                ('color', models.PositiveSmallIntegerField(default=0)),
                ('weeks_to_subtract', models.PositiveIntegerField(default=0)),
            ],
            options={
                'unique_together': {('shelf_type', 'furniture_category', 'color')},
            },
        ),
    ]
