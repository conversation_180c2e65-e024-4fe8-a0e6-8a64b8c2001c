import datetime

from typing import (
    TYPE_CHECKING,
    Type,
)

from django.contrib.auth.models import User
from django.db import models
from django.db.models import Q

from custom import enums
from gallery.enums import FurnitureCategory

from .choices import (
    FeatureChoice,
    ShipInRangeChoice,
)
from .managers import CustomCapacityManager
from .utils import ShipInRangeAutoLogChangeMixin

if TYPE_CHECKING:
    from .services import ProductBacklogCounter


class EntityCapacity(models.Model):
    capacity = models.IntegerField(default=0)
    margin_of_error = models.FloatField(default=0.05)
    backlog = models.IntegerField(default=0)
    valid_from = models.DateTimeField(default=datetime.datetime.now)

    class Meta:
        abstract = True

    def refresh_products_no(self, counter: 'ProductBacklogCounter'):
        pass

    @property
    def ship_in_range(self) -> ShipInRangeChoice:
        return ShipInRangeChoice.get_range_base_on_week_occupancy(self.week_occupancy)

    @property
    def week_occupancy(self):
        if self.backlog and self.capacity:
            return round(self.backlog / (self.capacity * (1 - self.margin_of_error)), 2)
        return 0


class WeekCapacity(ShipInRangeAutoLogChangeMixin, EntityCapacity):
    sent_to_production = models.IntegerField(default=0)

    class Meta:
        verbose_name_plural = 'Week Capacities'

    @property
    def share_in_production(self):
        if self.backlog:
            return round(self.sent_to_production / self.backlog, 2)
        return 0

    def refresh_products_no(self, counter: 'ProductBacklogCounter'):
        self.backlog = counter.backlog()
        self.sent_to_production = counter.in_production()
        self.save(
            update_fields=['backlog', 'sent_to_production'], is_refresh_products_no=True
        )


class ManufactorCapacity(ShipInRangeAutoLogChangeMixin, EntityCapacity):
    base = models.ForeignKey(WeekCapacity, null=True, on_delete=models.SET_NULL)
    manufactor = models.ForeignKey('producers.Manufactor', on_delete=models.CASCADE)

    class Meta:
        verbose_name_plural = 'Manufactors Capacities'

    @property
    def share_in_production(self):
        if self.base and self.base.sent_to_production:
            return round(self.backlog / self.base.sent_to_production, 2)
        return 0

    def refresh_products_no(self, counter: 'ProductBacklogCounter'):
        self.backlog = counter.manufactor_backlog(self.manufactor)
        self.save(update_fields=['backlog'], is_refresh_products_no=True)


class ShelfCapacity(ShipInRangeAutoLogChangeMixin, EntityCapacity):
    base = models.ForeignKey(WeekCapacity, null=True, on_delete=models.SET_NULL)
    shelf_type = models.PositiveSmallIntegerField(choices=enums.ShelfType.choices())

    class Meta:
        verbose_name_plural = 'Shelves Capacities'

    @property
    def share_in_all(self):
        if self.base and self.base.backlog:
            return round(self.backlog / self.base.backlog, 2)
        return 0

    def refresh_products_no(self, counter: 'ProductBacklogCounter'):
        self.backlog = counter.shelf_type_backlog(enums.ShelfType(self.shelf_type))
        self.save(update_fields=['backlog'], is_refresh_products_no=True)


class ShelfColorCapacity(ShipInRangeAutoLogChangeMixin, EntityCapacity):
    base = models.ForeignKey(WeekCapacity, null=True, on_delete=models.SET_NULL)
    shelf_type = models.PositiveSmallIntegerField(choices=enums.ShelfType.choices())
    color = models.PositiveSmallIntegerField(default=0)

    class Meta:
        verbose_name_plural = 'Shelves Colors Capacities'
        unique_together = ('shelf_type', 'color')

    @property
    def shelf_type_color(self):
        shelf_type = enums.ShelfType(self.shelf_type)
        return shelf_type.colors(self.color)

    def get_color_display(self):
        if self.shelf_type_color is None:
            return None
        return self.shelf_type_color.name

    @property
    def share_in_all(self):
        if self.base and self.base.backlog:
            return round(self.backlog / self.base.backlog, 2)
        return 0

    def refresh_products_no(self, counter: 'ProductBacklogCounter'):
        self.backlog = counter.shelf_type_color_backlog(
            enums.ShelfType(self.shelf_type), self.shelf_type_color
        )
        self.save(update_fields=['backlog'], is_refresh_products_no=True)


class CustomCapacity(models.Model):

    shelf_type = models.PositiveSmallIntegerField(choices=enums.ShelfType.choices())
    is_desk = models.BooleanField(default=False)
    color = models.PositiveSmallIntegerField(null=True)
    all_colors = models.BooleanField(default=False)
    feature = models.PositiveSmallIntegerField(
        choices=FeatureChoice.choices, default=FeatureChoice.NO_FEATURE.value
    )
    ship_in_range = models.PositiveSmallIntegerField(choices=ShipInRangeChoice.choices)
    active = models.BooleanField(default=True)

    objects = CustomCapacityManager()

    class Meta:
        verbose_name_plural = 'Custom Capacities'
        unique_together = ('shelf_type', 'is_desk', 'color', 'feature')

    @property
    def shelf_type_color(self):
        if self.color is None:
            return None
        shelf_type = enums.ShelfType(self.shelf_type)
        return shelf_type.colors(self.color)

    def get_color_display(self):
        if self.shelf_type_color is None:
            return None
        return self.shelf_type_color.name


class ChangeLog(models.Model):
    user = models.ForeignKey(User, null=True, on_delete=models.CASCADE)
    created_at = models.DateTimeField(auto_now_add=True)
    model_name = models.CharField(max_length=255)
    changes = models.JSONField()

    def __str__(self):
        return f'ChangeLog for {self.model_name} by {self.user} at {self.created_at}'


class ProductionPauseWeekManager(models.Manager):
    def get_active_weeks_off(self):
        today = datetime.date.today()
        return self.filter(
            Q(year=today.isocalendar().year, week__gte=today.isocalendar().week)
            | Q(year__gt=today.isocalendar().year)
        )


class ProductionPauseWeek(models.Model):
    base = models.ForeignKey(WeekCapacity, null=True, on_delete=models.SET_NULL)
    week = models.IntegerField()
    year = models.IntegerField()

    objects = ProductionPauseWeekManager()

    @property
    def weeks_left_to_pause(self) -> int:
        if not self.pk:
            return -1
        today = datetime.date.today()
        if self.year == today.isocalendar().year:
            return self.week - today.isocalendar().week
        if self.year > today.isocalendar().year:
            return self.week + 52 - today.isocalendar().week
        return -1


class ShelfColorRange(models.Model):
    shelf_type = models.PositiveSmallIntegerField(choices=enums.ShelfType.choices())
    color = models.PositiveSmallIntegerField(default=0)
    min_ship_in_range = models.PositiveSmallIntegerField(
        choices=ShipInRangeChoice.choices,
        default=ShipInRangeChoice.WEEKS_2_3,
    )
    max_ship_in_range = models.PositiveSmallIntegerField(
        choices=ShipInRangeChoice.choices,
        default=ShipInRangeChoice.WEEKS_24_25,
    )

    class Meta:
        verbose_name_plural = 'Shelf Colors Ranges'
        unique_together = ('shelf_type', 'color')

    def get_ship_in_range(self, ship_in_range: ShipInRangeChoice) -> ShipInRangeChoice:
        if ship_in_range.value < self.min_ship_in_range:
            return ShipInRangeChoice(self.min_ship_in_range)
        if ship_in_range.value > self.max_ship_in_range:
            return ShipInRangeChoice(self.max_ship_in_range)
        return ship_in_range

    @property
    def shelf_type_color(self) -> enums.ColorEnum:
        shelf_type = enums.ShelfType(self.shelf_type)
        return shelf_type.colors(self.color)

    def get_color_display(self) -> str | None:
        if self.shelf_type_color is None:
            return None
        return self.shelf_type_color.name


class ShelfColorCategorySubtract(models.Model):
    shelf_type = models.PositiveSmallIntegerField(choices=enums.ShelfType.choices())
    furniture_category = models.CharField(
        choices=FurnitureCategory.choices,
        max_length=255,
    )
    color = models.PositiveSmallIntegerField(default=0)
    weeks_to_subtract = models.PositiveIntegerField(default=0)

    @property
    def shelf_type_color(self) -> Type[enums.ColorEnum]:
        shelf_type = enums.ShelfType(self.shelf_type)
        return shelf_type.colors(self.color)

    def get_color_display(self) -> str | None:
        if self.shelf_type_color is None:
            return None
        return self.shelf_type_color.name

    class Meta:
        unique_together = ('shelf_type', 'furniture_category', 'color')
