from django.utils.translation import gettext_lazy as _

from rest_framework import serializers


class DeliveryTextSerializer(serializers.Serializer):
    text = serializers.SerializerMethodField(read_only=True)
    text_with_broken_copy = serializers.SerializerMethodField(read_only=True)

    def get_text(self, obj=None):
        if self.context.get('use_extended_delivery', False):
            return _('extended_delivery_dynamic_text_with_br')
        return _('delivery_dynamic_text_with_br')

    def get_text_with_broken_copy(self, obj=None):
        if self.context.get('use_extended_delivery', False):
            return _('extended_delivery_dynamic_text_with_tick')
        return _('delivery_dynamic_text_with_tick')


class ProductionTimeSerializer(serializers.Serializer):
    min = serializers.IntegerField(read_only=True)
    max = serializers.IntegerField(read_only=True)
    translations = serializers.SerializerMethodField(read_only=True)

    def get_translations(self, obj=None):
        return DeliveryTextSerializer({}, context=self.context).data

    def get_translations_filled(self) -> dict[str, str]:
        min, max = self.data['min'], self.data['max']
        if min == 0 and max == 0:
            return {'text': '', 'text_with_broken_copy': ''}

        text = self.data['translations']['text']
        text_with_broken_copy = self.data['translations']['text_with_broken_copy']
        return {
            'text': text.format(min, max),
            'text_with_broken_copy': text_with_broken_copy.format(min, max),
        }
