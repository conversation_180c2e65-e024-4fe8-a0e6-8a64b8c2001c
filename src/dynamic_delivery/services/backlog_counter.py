from typing import (
    Iterable,
    Union,
)

from django.db.models import QuerySet

from custom.enums import (
    ColorEnum,
    ShelfType,
)
from dynamic_delivery.models import (
    EntityCapacity,
    ManufactorCapacity,
    ShelfCapacity,
    ShelfColorCapacity,
    WeekCapacity,
)
from orders.enums import OrderType
from producers.choices import (
    ProductPriority,
    ProductStatus,
)
from producers.models import (
    Manufactor,
    Product,
)


class ProductBacklogCounter:

    BACKLOG_STATUSES = [
        ProductStatus.ASSIGNED_TO_PRODUCTION,
        ProductStatus.IN_PRODUCTION,
        ProductStatus.NEW,
    ]
    IN_PRODUCTION_STATUSES = [
        ProductStatus.ASSIGNED_TO_PRODUCTION,
        ProductStatus.IN_PRODUCTION,
    ]

    def __init__(self):
        self.backlog_query = (
            Product.objects.filter(status__in=self.BACKLOG_STATUSES)
            .exclude(order__order_type=OrderType.COMPLAINT)
            .exclude(priority__in=ProductPriority.on_hold_and_postponed_priorities())
        )
        self.in_production_query = (
            Product.objects.filter(status__in=self.IN_PRODUCTION_STATUSES)
            .exclude(order__order_type=OrderType.COMPLAINT)
            .exclude(priority__in=ProductPriority.on_hold_and_postponed_priorities())
        )

    def backlog(self) -> int:
        return self.backlog_query.count()

    def shelf_type_backlog(self, shelf_type: ShelfType) -> int:
        return self.backlog_query.filter(
            cached_shelf_type=shelf_type.production_code
        ).count()

    def shelf_type_color_backlog(self, shelf_type: ShelfType, color: ColorEnum) -> int:
        return self.backlog_query.filter(
            cached_shelf_type=shelf_type.production_code, cached_material=color.value
        ).count()

    def in_production(self) -> int:
        return self.in_production_query.count()

    def manufactor_backlog(self, manufactor: Manufactor) -> int:
        return self.in_production_query.filter(manufactor=manufactor).count()


class BacklogUpdater:
    def __init__(self):
        self.counter = ProductBacklogCounter()

    def update(self):
        self.week_update()
        self.shelf_capacities_update()
        self.manufactor_capacities_update()
        self.shelf_color_capacities_update()

    def week_update(self):
        query = WeekCapacity.objects.all()
        self.refresh_backlog(query)

    def shelf_capacities_update(self):
        query = ShelfCapacity.objects.all()
        self.refresh_backlog(query)

    def shelf_color_capacities_update(self):
        query = ShelfColorCapacity.objects.all()
        self.refresh_backlog(query)

    def manufactor_capacities_update(self):
        query = ManufactorCapacity.objects.all()
        self.refresh_backlog(query)

    def refresh_backlog(self, query: Union[QuerySet, Iterable[EntityCapacity]]):
        for entity in query:
            entity.refresh_products_no(self.counter)
