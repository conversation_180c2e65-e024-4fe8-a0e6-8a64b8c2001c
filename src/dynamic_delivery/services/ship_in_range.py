from dataclasses import dataclass
from datetime import datetime
from typing import (
    Final,
    Optional,
)

from custom.enums import (
    ColorEnum,
    ShelfType,
)
from dynamic_delivery.choices import (
    FeatureChoice,
    ShipInRangeChoice,
)
from dynamic_delivery.models import (
    CustomCapacity,
    ManufactorCapacity,
    ProductionPauseWeek,
    ShelfCapacity,
    ShelfColorCapacity,
    ShelfColorCategorySubtract,
    ShelfColorRange,
    WeekCapacity,
)
from gallery.enums import FurnitureCategory

DEFAULT_SHIP_IN = ShipInRangeChoice.WEEKS_3_4
DELIVERY_WEEKS_TOTAL: Final[int] = 3


@dataclass
class ShipInSummary:
    no_feature: dict
    doors: dict
    drawers: dict
    sideboard: dict


def extend_ship_in_range(ship_in_range: ShipInRangeChoice) -> ShipInRangeChoice:
    try:
        return ShipInRangeChoice(ship_in_range + DELIVERY_WEEKS_TOTAL)
    except ValueError:
        return ShipInRangeChoice.get_max_range()


class ShelfCapacityRangeStrategy:
    def __init__(self, shelf_type) -> None:
        self.shelf_type = shelf_type

    def _get_range(self) -> Optional[ShipInRangeChoice]:
        shelf_capacity = (
            ShelfCapacity.objects.filter(
                valid_from__lte=datetime.now(),
                shelf_type=self.shelf_type.value,
            )
            .order_by('valid_from')
            .last()
        )
        return shelf_capacity.ship_in_range if shelf_capacity else None


class ManufactorCapacityRangeStrategy:
    def __init__(self, manufactor) -> None:
        self.manufactor = manufactor

    def _get_range(self) -> Optional[ShipInRangeChoice]:
        shelf_capacity = (
            ManufactorCapacity.objects.filter(
                valid_from__lte=datetime.now(),
                manufactor=self.manufactor,
            )
            .order_by('valid_from')
            .last()
        )
        return shelf_capacity.ship_in_range if shelf_capacity else None

    def ship_in_range(self) -> Optional[ShipInRangeChoice]:
        ship_in_range = self._get_range()
        return ship_in_range if ship_in_range else DEFAULT_SHIP_IN


def get_entity_capacity_range(capacity_strategy) -> ShipInRangeChoice:
    try:
        summary_range = WeekCapacity.objects.get().ship_in_range
    except WeekCapacity.DoesNotExist:
        summary_range = DEFAULT_SHIP_IN
    shelf_type_range = capacity_strategy._get_range()
    if shelf_type_range:
        return max([summary_range, shelf_type_range])
    return summary_range


class ShipInRangeMatrixService:
    """
    ShipInRangeMatrixService is a base class for service that generate ship-in range
    matrix.
    Matrix represents all possible configuration off shelf - colors, doors, drawers
    Matrix is saved to _results
    To get values of ship in range:
    1. Calculate basic ship in range for shelf type (capacity_range) based on strategy
        - ShelfCapacityRangeStrategy - based on how many shelves can be produced in a
        week by all manufactor
        - ManufactorCapacityRangeStrategy - based on how many shelves can be produced in
        a week by given manufactor
    2. Generate matrix (with colors, doors, drawers) with values = capacity_range
    3. Check if there are any custom capacities for given shelf type, colors, doors,
       drawers and set if there are bigger than basic capacity_range
    4. Check if shelf type and color exists ShelfColorCapacity object if yes set
       it range
    4. Check if in time before shipping manufactor does have any weeks off and extend
       ship in range for this time
    Matrix is calculated to optimise performance - less db queries
    """

    def __init__(
        self,
        shelf_type: ShelfType,
        capacity_strategy=None,
        furniture_category: FurnitureCategory = None,
        *args,
        **kwargs,
    ):
        self.shelf_type = shelf_type
        self.furniture_category = furniture_category
        self.capacity_range = get_entity_capacity_range(
            capacity_strategy or ShelfCapacityRangeStrategy(shelf_type)
        )
        self._results = self._generate_ship_in_matrix()
        self._update_results_with_custom()
        self._set_shelf_color_capacity_to_results()
        self._add_production_pause_weeks_to_results()
        self._set_shelf_color_range_to_results()
        if self.furniture_category:
            self._subtract_shelf_type_category()

    def _subtract_shelf_type_category(self):
        pass

    def _update_results_with_custom(self):
        pass

    def _add_production_pause_weeks_to_results(self):
        pass

    def _set_shelf_color_capacity_to_results(self):
        pass

    def _set_shelf_color_range_to_results(self):
        pass

    @classmethod
    def _get_weeks_left_to_production_pause_list(cls) -> list:
        pause_weeks = ProductionPauseWeek.objects.get_active_weeks_off()
        return [pause_week.weeks_left_to_pause for pause_week in pause_weeks]

    @classmethod
    def _add_production_pause_week_to_ship_in_range(
        cls, ship_in_range: ShipInRangeChoice, weeks_left_to_pause_list: list[int]
    ) -> ShipInRangeChoice:
        for weeks_left_to_pause in weeks_left_to_pause_list:
            if 0 <= weeks_left_to_pause <= ship_in_range.max:
                ship_in_range = ship_in_range.add_weeks(weeks_to_add=1)
        return ship_in_range

    def get_ship_in_range_for_shelf(
        self, color: ColorEnum, has_doors: bool = False, has_drawers: bool = False
    ) -> ShipInRangeChoice:
        pass

    def get_ship_in_range_for_many_colors(
        self,
        colors: list[ColorEnum],
        has_doors: bool = False,
        has_drawers: bool = False,
    ):
        return max(
            [
                self.get_ship_in_range_for_shelf(
                    color, has_doors=has_doors, has_drawers=has_drawers
                )
                for color in colors
            ]
        )

    def get_matrix_as_list(self) -> list[list[dict]]:
        pass

    def _generate_ship_in_matrix(self) -> ShipInSummary:
        pass


class JettyShipInRangeMatrixService(ShipInRangeMatrixService):
    def __init__(
        self,
        shelf_type: ShelfType,
        capacity_strategy=None,
        furniture_category: FurnitureCategory = None,
        is_desk: bool = False,
    ):
        self.is_desk = is_desk
        super().__init__(
            shelf_type, capacity_strategy, furniture_category=furniture_category
        )

    def _add_production_pause_weeks_to_results(self):
        weeks_left_to_pause_list = self._get_weeks_left_to_production_pause_list()
        if not weeks_left_to_pause_list:
            return
        for color in self._results.no_feature.keys():
            self._results.no_feature[
                color
            ] = self._add_production_pause_week_to_ship_in_range(
                self._results.no_feature[color], weeks_left_to_pause_list
            )
            self._results.doors[
                color
            ] = self._add_production_pause_week_to_ship_in_range(
                self._results.doors[color], weeks_left_to_pause_list
            )
            self._results.drawers[
                color
            ] = self._add_production_pause_week_to_ship_in_range(
                self._results.drawers[color], weeks_left_to_pause_list
            )
            self._results.sideboard[
                color
            ] = self._add_production_pause_week_to_ship_in_range(
                self._results.sideboard[color], weeks_left_to_pause_list
            )

    def _subtract_shelf_type_category(self):
        shelf_type_category_to_subtrack = ShelfColorCategorySubtract.objects.filter(
            shelf_type=self.shelf_type.value, furniture_category=self.furniture_category
        )
        if not shelf_type_category_to_subtrack:
            return
        for shelf_type_category in shelf_type_category_to_subtrack:
            self._results.no_feature[
                shelf_type_category.color
            ] = self._results.no_feature[shelf_type_category.color].subtrack_weeks(
                shelf_type_category.weeks_to_subtract
            )
            self._results.doors[shelf_type_category.color] = self._results.doors[
                shelf_type_category.color
            ].subtrack_weeks(shelf_type_category.weeks_to_subtract)
            self._results.drawers[shelf_type_category.color] = self._results.drawers[
                shelf_type_category.color
            ].subtrack_weeks(shelf_type_category.weeks_to_subtract)
            self._results.sideboard[
                shelf_type_category.color
            ] = self._results.sideboard[shelf_type_category.color].subtrack_weeks(
                shelf_type_category.weeks_to_subtract
            )

    def _set_shelf_color_capacity_to_results(self):
        shelf_color_capacities = ShelfColorCapacity.objects.filter(
            shelf_type=self.shelf_type.value,
            valid_from__lte=datetime.now(),
        )
        for shelf_color_capacity in shelf_color_capacities:
            self._results.no_feature[
                shelf_color_capacity.color
            ] = shelf_color_capacity.ship_in_range
            self._results.doors[
                shelf_color_capacity.color
            ] = shelf_color_capacity.ship_in_range
            self._results.drawers[
                shelf_color_capacity.color
            ] = shelf_color_capacity.ship_in_range
            self._results.sideboard[
                shelf_color_capacity.color
            ] = shelf_color_capacity.ship_in_range

    def _set_shelf_color_range_to_results(self):
        shelf_color_ranges = ShelfColorRange.objects.filter(
            shelf_type=self.shelf_type.value,
        )
        for shelf_color_range in shelf_color_ranges:
            self._results.no_feature[
                shelf_color_range.color
            ] = shelf_color_range.get_ship_in_range(
                self._results.no_feature[shelf_color_range.color]
            )
            self._results.doors[
                shelf_color_range.color
            ] = shelf_color_range.get_ship_in_range(
                self._results.doors[shelf_color_range.color]
            )
            self._results.drawers[
                shelf_color_range.color
            ] = shelf_color_range.get_ship_in_range(
                self._results.drawers[shelf_color_range.color]
            )
            self._results.sideboard[
                shelf_color_range.color
            ] = shelf_color_range.get_ship_in_range(
                self._results.sideboard[shelf_color_range.color]
            )

    def get_ship_in_range_for_shelf(
        self,
        color: ColorEnum,
        has_doors: bool = False,
        has_drawers: bool = False,
    ) -> ShipInRangeChoice:
        candidates = []
        features = self._get_features(has_doors, has_drawers)
        for feature in features:
            if feature == FeatureChoice.NO_FEATURE:
                candidates.append(
                    self._results.no_feature.get(color, self.capacity_range)
                )
            elif feature == FeatureChoice.WITH_DOORS:
                candidates.append(self._results.doors.get(color, self.capacity_range))
            elif feature == FeatureChoice.WITH_DRAWERS:
                candidates.append(self._results.drawers.get(color, self.capacity_range))
        return max(candidates)

    def get_matrix_as_list(self) -> list[list[dict]]:
        return self._to_list_format()

    def _generate_ship_in_matrix(self) -> ShipInSummary:
        return self._generate_capacity_ship_in_matrix()

    def _update_results_with_custom(self):
        if self.is_desk:
            self._update_results_with_custom_desk()
        else:
            self._update_results_with_custom_shelf()

    @classmethod
    def _get_features(cls, has_doors: bool, has_drawers: bool) -> list[FeatureChoice]:
        features = [FeatureChoice.NO_FEATURE]
        if has_doors:
            features.append(FeatureChoice.WITH_DOORS)
        if has_drawers:
            features.append(FeatureChoice.WITH_DRAWERS)
        return features

    def _to_list_format(self) -> list[list[dict]]:
        max_color_value = max(color.value for color in self.shelf_type.colors)
        result_list = [[], [], [], []]
        results = self._results
        for color in range(max_color_value + 1):
            result_list[0].append(
                results.no_feature.get(color, self.capacity_range).get_weeks_range()
            )
            result_list[1].append(
                results.doors.get(color, self.capacity_range).get_weeks_range()
            )
            result_list[2].append(
                results.drawers.get(color, self.capacity_range).get_weeks_range()
            )
            result_list[3].append(
                results.sideboard.get(color, self.capacity_range).get_weeks_range()
            )
        return result_list

    def _generate_capacity_ship_in_matrix(self) -> ShipInSummary:
        colors_dict = {
            color.value: self.capacity_range for color in self.shelf_type.colors
        }
        return ShipInSummary(
            no_feature=colors_dict.copy(),
            doors=colors_dict.copy(),
            drawers=colors_dict.copy(),
            sideboard=colors_dict.copy(),
        )

    def _update_results_with_custom_shelf(self):
        self._update_results_with_custom_shelf_all_colors()
        custom_capacities = CustomCapacity.objects.actives().filter(
            is_desk=False, shelf_type=self.shelf_type.value, all_colors=False
        )
        for custom in custom_capacities:
            self._update_feature_value(custom, custom.color)

    def _update_results_with_custom_shelf_all_colors(self):
        custom_capacities = CustomCapacity.objects.actives().filter(
            is_desk=False, shelf_type=self.shelf_type.value, all_colors=True
        )
        for custom in custom_capacities:
            for color in self.shelf_type.colors:
                self._update_feature_value(custom, color)

    def _update_feature_value(self, custom, color):
        if custom.feature in [FeatureChoice.WITH_DOORS, FeatureChoice.NO_FEATURE]:
            self._results.doors[color] = ShipInRangeChoice(custom.ship_in_range)
        if custom.feature in [FeatureChoice.WITH_DRAWERS, FeatureChoice.NO_FEATURE]:
            self._results.drawers[color] = ShipInRangeChoice(custom.ship_in_range)
        if custom.feature == FeatureChoice.NO_FEATURE:
            self._results.no_feature[color] = ShipInRangeChoice(custom.ship_in_range)
            self._results.sideboard[color] = ShipInRangeChoice(custom.ship_in_range)

    def _update_desk_value(self, custom, color):
        self._results.doors[color] = ShipInRangeChoice(custom.ship_in_range)
        self._results.drawers[color] = ShipInRangeChoice(custom.ship_in_range)
        self._results.no_feature[color] = ShipInRangeChoice(custom.ship_in_range)
        self._results.sideboard[color] = ShipInRangeChoice(custom.ship_in_range)

    def _update_results_with_custom_desk(self):
        self._update_results_with_custom_desk_all_colors()
        custom_capacities = (
            CustomCapacity.objects.actives()
            .filter(is_desk=True, shelf_type=self.shelf_type.value, all_colors=False)
            .order_by('shelf_type', '-ship_in_range')
            .distinct('shelf_type')
        )

        for custom in custom_capacities:
            self._update_desk_value(custom, custom.color)

    def _update_results_with_custom_desk_all_colors(self):
        custom_capacities = (
            CustomCapacity.objects.actives()
            .filter(is_desk=True, shelf_type=self.shelf_type.value, all_colors=True)
            .order_by('shelf_type', '-ship_in_range')
            .distinct('shelf_type')
        )
        for custom in custom_capacities:
            for color in self.shelf_type.colors:
                self._update_desk_value(custom, color)


class FurnitureWithoutFeatureShipInRangeMatrixService(ShipInRangeMatrixService):
    def __init__(
        self,
        shelf_type: ShelfType,
        capacity_strategy=None,
        furniture_category: FurnitureCategory = None,
        **kwargs,
    ):
        super().__init__(
            shelf_type=shelf_type,
            capacity_strategy=capacity_strategy,
            furniture_category=furniture_category,
        )

    def get_ship_in_range_for_shelf(
        self,
        color: ColorEnum,
        **kwargs,
    ) -> ShipInRangeChoice:
        return self._results.get(color.value)

    def get_matrix_as_list(self) -> list[list[dict]]:
        return [
            [
                ship_in_range.get_weeks_range()
                for ship_in_range in self._results.values()
            ]
        ]

    def _set_shelf_color_capacity_to_results(self):
        shelf_color_capacity = ShelfColorCapacity.objects.filter(
            shelf_type=self.shelf_type.value,
            valid_from__lte=datetime.now(),
        )
        for shelf_color_capacity in shelf_color_capacity:
            self._results[
                shelf_color_capacity.color
            ] = shelf_color_capacity.ship_in_range

    def _set_shelf_color_range_to_results(self):
        shelf_color_capacities = ShelfColorRange.objects.filter(
            shelf_type=self.shelf_type.value,
        )
        for shelf_color_range in shelf_color_capacities:
            self._results[
                shelf_color_range.color
            ] = shelf_color_range.get_ship_in_range(
                self._results[shelf_color_range.color]
            )

    def _add_production_pause_weeks_to_results(self):
        weeks_left_to_pause_list = self._get_weeks_left_to_production_pause_list()
        if not weeks_left_to_pause_list:
            return
        for color in self._results.keys():
            self._results[color] = self._add_production_pause_week_to_ship_in_range(
                self._results[color],
                weeks_left_to_pause_list,
            )

    def _subtract_shelf_type_category(self):
        shelf_type_category_to_subtrack = ShelfColorCategorySubtract.objects.filter(
            shelf_type=self.shelf_type.value, furniture_category=self.furniture_category
        )
        if not shelf_type_category_to_subtrack:
            return
        for shelf_type_category in shelf_type_category_to_subtrack:
            self._results[shelf_type_category.color] = self._results[
                shelf_type_category.color
            ].subtrack_weeks(shelf_type_category.weeks_to_subtract)

    def _update_results_with_custom(self):
        self._update_results_with_custom_shelf_all_colors()
        custom_capacities = CustomCapacity.objects.actives().filter(
            is_desk=False, shelf_type=self.shelf_type.value, all_colors=False
        )
        for custom in custom_capacities:
            self._results[custom.color] = ShipInRangeChoice(custom.ship_in_range)

    def _update_results_with_custom_shelf_all_colors(self):
        all_colors = (
            CustomCapacity.objects.actives()
            .filter(is_desk=False, shelf_type=self.shelf_type.value, all_colors=True)
            .first()
        )
        if all_colors:
            for color in self.shelf_type.colors.values():
                self._results[color] = ShipInRangeChoice(all_colors.ship_in_range)

    def _generate_ship_in_matrix(self) -> dict[ShipInRangeChoice]:
        capacity_by_color = {}
        for color in self.shelf_type.colors.values():
            capacity_by_color[color] = self.capacity_range
        return capacity_by_color


class WattyShipInRangeMatrixService(FurnitureWithoutFeatureShipInRangeMatrixService):
    pass


class SottyShipInRangeMatrixService(FurnitureWithoutFeatureShipInRangeMatrixService):
    pass
