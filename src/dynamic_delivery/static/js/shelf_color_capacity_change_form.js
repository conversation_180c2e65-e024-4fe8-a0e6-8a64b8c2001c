window.addEventListener('load', addEventForCapacity);
function addEventForCapacity() {
  const capacityInput = document.getElementById('id_capacity');
  const marginInput = document.getElementById('id_margin_of_error');
  const backlogValue = document.getElementsByClassName('form-row field-backlog')[0].childNodes[1].childNodes[3].textContent;
  const weekOccupancyEl = document.getElementsByClassName('form-row field-week_occupancy')[0].childNodes[1].childNodes[3];
  capacityInput.addEventListener('input', e => updateWeekOccupancy(capacityInput, marginInput, backlogValue, weekOccupancyEl));
  marginInput.addEventListener('input', e => updateWeekOccupancy(capacityInput, marginInput, backlogValue, weekOccupancyEl));
}

function updateWeekOccupancy(
  capacityInput, marginErrorInput, backlogValue, weekOccupancyEl
){
  const capacity = capacityInput.value;
  const marginError = marginErrorInput.value;
  const value = Number(backlogValue) / (capacity * (1 - marginError));
  weekOccupancyEl.textContent = value.toFixed(2);
}
