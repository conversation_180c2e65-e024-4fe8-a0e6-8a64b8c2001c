window.addEventListener('load', function () {
  const selectShelfType = document.getElementById('id_shelf_type');
  selectShelfType.addEventListener('change', (event) => {
    changeColorOption(event)
  });
  const selectedType = selectShelfType.options[selectShelfType.selectedIndex].value;
  setColorOption(selectedType);
});

function clearColorOptions(colorSelect) {
  while(colorSelect.firstChild) {
    colorSelect.removeChild(colorSelect.lastChild);
  }
}

function addOption(selectParent, value, name) {
  let option = document.createElement('option');
  option.value = value;
  option.innerHTML = name;
  selectParent.appendChild(option);
}


function setColorOption(shelfType){
  const colorSelect = document.getElementById('id_color');
  const selectedColor = colorSelect.options[colorSelect.selectedIndex];
  const shelfTypesToColors =  JSON.parse(
    document.getElementById('shelf_types_to_colors').value
  );
  if (shelfType !== null && shelfType !== '') {
    clearColorOptions(colorSelect);
    shelfTypesToColors[shelfType].forEach(option => {
      addOption(colorSelect, option[0], option[1]);
    })
  }
  if (selectedColor !== null) {
    colorSelect.value = selectedColor.value;
  }
  else {
    colorSelect.value = 0;
  }
}

function changeColorOption(event) {
  setColorOption(event.target.value);
}
