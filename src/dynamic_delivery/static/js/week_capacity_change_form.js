window.addEventListener('load', function() {
  addEventForAllCapacity();
  addEventForShelvesCapacities();
  addEventForManufactorCapacities();
});

function addEventForAllCapacity() {
  const capacityInput = document.getElementById('id_capacity');
  const marginInput = document.getElementById('id_margin_of_error');
  const backlogValue = document.getElementsByClassName('form-row field-backlog')[0].childNodes[1].childNodes[3].textContent;
  const weekOccupancyEl = document.getElementsByClassName('form-row field-week_occupancy')[0].childNodes[1].childNodes[3];
  capacityInput.addEventListener('input', e => updateWeekOccupancy(capacityInput, marginInput, backlogValue, weekOccupancyEl));
  marginInput.addEventListener('input', e => updateWeekOccupancy(capacityInput, marginInput, backlogValue, weekOccupancyEl));
}

function updateWeekOccupancy(
  capacityInput, marginErrorInput, backlogValue, weekOccupancyEl
){
  const capacity = capacityInput.value;
  const marginError = marginErrorInput.value;
  const value = Number(backlogValue) / (capacity * (1 - marginError));
  weekOccupancyEl.textContent = value.toFixed(2);
}

function addEventForShelvesCapacities() {
  addEventForCapacityByType('shelfcapacity');
}

function addEventForManufactorCapacities() {
  addEventForCapacityByType('manufactorcapacity');
}

function addEventForCapacityByType(type) {
  const shelvesRows = document.querySelectorAll('.form-row.dynamic-' + type + '_set');
  shelvesRows.forEach((el, i) => {
    const capacityInput = document.getElementById(`id_${type}_set-${i}-capacity`);
    const marginInput = document.getElementById(`id_${type}_set-${i}-margin_of_error`);
    const backlogValue = el.getElementsByClassName('field-backlog')[0].childNodes[1].innerText;
    const weekOccupancyEl = el.getElementsByClassName('field-week_occupancy')[0].childNodes[1];
    capacityInput.addEventListener('input', e => updateWeekOccupancy(capacityInput, marginInput, backlogValue, weekOccupancyEl));
    marginInput.addEventListener('input', e => updateWeekOccupancy(capacityInput, marginInput, backlogValue, weekOccupancyEl));
  })
}
