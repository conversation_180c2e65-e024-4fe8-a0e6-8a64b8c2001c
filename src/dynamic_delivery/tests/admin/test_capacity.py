from django.shortcuts import reverse

import pytest


@pytest.mark.django_db
def test_week_capacity_change_list_view(admin_client, week_capacity):
    url = reverse('admin:dynamic_delivery_weekcapacity_changelist')
    response = admin_client.get(url)
    assert response.status_code == 200


@pytest.mark.django_db
def test_week_capacity_change_form_view(
    admin_client,
    week_capacity,
    shelf_capacity_factory,
    manufactor_capacity_factory,
):
    shelf_capacity_factory(base=week_capacity)
    manufactor_capacity_factory(base=week_capacity)
    url = reverse(
        'admin:dynamic_delivery_weekcapacity_change',
        kwargs={'object_id': week_capacity.id},
    )
    response = admin_client.get(url)
    assert response.status_code == 200


@pytest.mark.django_db
def test_custom_capacity_change_list_view(admin_client, custom_capacity):
    url = reverse('admin:dynamic_delivery_customcapacity_changelist')
    response = admin_client.get(url)
    assert response.status_code == 200


@pytest.mark.django_db
def test_custom_capacity_change_form_view(admin_client, custom_capacity):
    url = reverse(
        'admin:dynamic_delivery_customcapacity_change',
        kwargs={'object_id': custom_capacity.id},
    )
    response = admin_client.get(url)
    assert response.status_code == 200
