import factory

from custom.enums import ShelfType

from ..choices import ShipInRangeChoice


class WeekCapacityFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = 'dynamic_delivery.WeekCapacity'


class ShelfCapacityFactory(factory.django.DjangoModelFactory):
    base = factory.SubFactory(WeekCapacityFactory)
    shelf_type = ShelfType.TYPE01.value

    class Meta:
        model = 'dynamic_delivery.ShelfCapacity'


class ShelfColorCapacityFactory(factory.django.DjangoModelFactory):
    base = factory.SubFactory(WeekCapacityFactory)
    shelf_type = ShelfType.TYPE01.value

    class Meta:
        model = 'dynamic_delivery.ShelfColorCapacity'


class ManufactorCapacityFactory(factory.django.DjangoModelFactory):
    base = factory.SubFactory(WeekCapacityFactory)
    manufactor = factory.SubFactory('producers.tests.factories.ManufactorFactory')

    class Meta:
        model = 'dynamic_delivery.ManufactorCapacity'


class CustomCapacityFactory(factory.django.DjangoModelFactory):
    shelf_type = ShelfType.TYPE01.value
    ship_in_range = ShipInRangeChoice.WEEKS_2_3.value

    class Meta:
        model = 'dynamic_delivery.CustomCapacity'


class ProductionPauseWeekFactory(factory.django.DjangoModelFactory):
    base = factory.SubFactory(WeekCapacityFactory)

    class Meta:
        model = 'dynamic_delivery.ProductionPauseWeek'


class ShelfColorRangeFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = 'dynamic_delivery.ShelfColorRange'


class ShelfColorCategorySubtractFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = 'dynamic_delivery.ShelfColorCategorySubtract'
