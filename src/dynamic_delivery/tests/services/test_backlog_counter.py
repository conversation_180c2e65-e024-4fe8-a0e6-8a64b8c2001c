import pytest

from custom.enums import ShelfType
from dynamic_delivery.services import BacklogUpdater
from producers.choices import ProductStatus

ASSIGN_PRODUCTS_NO = 6
NEW_PRODUCTS_NO = 4
DELIVERED_PRODUCTS_NO = 1

SHELF_TYPE_T1 = ShelfType.TYPE01


@pytest.fixture
def dtr_manufactor(manufactor_factory):
    return manufactor_factory(name='DTR')


@pytest.fixture
def setup_products(product_factory, dtr_manufactor):
    product_factory.create_batch(
        ASSIGN_PRODUCTS_NO,
        status=ProductStatus.ASSIGNED_TO_PRODUCTION,
        manufactor=dtr_manufactor,
        cached_shelf_type=SHELF_TYPE_T1.production_code,
    )
    product_factory.create_batch(
        NEW_PRODUCTS_NO,
        status=ProductStatus.NEW,
        cached_shelf_type=SHELF_TYPE_T1.production_code,
    )
    product_factory.create_batch(
        DELIVERED_PRODUCTS_NO,
        status=ProductStatus.DELIVERED_TO_CUSTOMER,
        manufactor=dtr_manufactor,
        cached_shelf_type=SHELF_TYPE_T1.production_code,
    )


@pytest.mark.django_db
def test_week_capacity_counter(week_capacity, setup_products):
    updater = BacklogUpdater()
    updater.week_update()
    week_capacity.refresh_from_db()
    assert week_capacity.backlog == NEW_PRODUCTS_NO + ASSIGN_PRODUCTS_NO
    assert week_capacity.sent_to_production == ASSIGN_PRODUCTS_NO


@pytest.mark.django_db
def test_shelf_capacity_counter(shelf_capacity_factory, setup_products):
    shelf_capacity = shelf_capacity_factory(shelf_type=SHELF_TYPE_T1.value)
    updater = BacklogUpdater()
    updater.shelf_capacities_update()
    shelf_capacity.refresh_from_db()
    assert shelf_capacity.backlog == NEW_PRODUCTS_NO + ASSIGN_PRODUCTS_NO


@pytest.mark.django_db
def test_manufactor_capacity_counter(
    manufactor_capacity_factory, setup_products, dtr_manufactor
):
    manufactor_capacity = manufactor_capacity_factory(manufactor=dtr_manufactor)
    updater = BacklogUpdater()
    updater.manufactor_capacities_update()
    manufactor_capacity.refresh_from_db()
    assert manufactor_capacity.backlog == ASSIGN_PRODUCTS_NO
