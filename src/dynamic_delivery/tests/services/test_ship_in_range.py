from datetime import datetime

import pytest

from freezegun import freeze_time

from custom.enums import (
    ShelfType,
    Type01Color,
    Type13Color,
)
from dynamic_delivery.choices import (
    FeatureChoice,
    ShipInRangeChoice,
)
from dynamic_delivery.services.ship_in_range import (
    JettyShipInRangeMatrixService,
    WattyShipInRangeMatrixService,
)
from gallery.enums import FurnitureCategory


@pytest.mark.django_db
def test_ship_in_range_for_desk(custom_capacity_factory, week_capacity_factory):
    week_capacity_factory(capacity=100, backlog=2.5 * 100)
    expected = ShipInRangeChoice.WEEKS_4_5.value
    custom_capacity_factory(
        shelf_type=ShelfType.TYPE01,
        is_desk=True,
        color=Type01Color.WHITE,
        ship_in_range=ShipInRangeChoice.WEEKS_4_5.value,
        active=True,
    )
    service = JettyShipInRangeMatrixService(shelf_type=ShelfType.TYPE01, is_desk=True)
    ship_in_range = service.get_ship_in_range_for_shelf(color=Type01Color.WHITE)
    assert ship_in_range == expected


@pytest.mark.django_db
def test_ship_in_range_for_custom_shelf(custom_capacity_factory, week_capacity_factory):
    expected = ShipInRangeChoice.WEEKS_8_9.value
    week_capacity_factory(capacity=100, backlog=2.5 * 100)
    custom_capacity_factory(
        shelf_type=ShelfType.TYPE01,
        color=Type01Color.WHITE,
        feature=FeatureChoice.NO_FEATURE,
        is_desk=False,
        ship_in_range=expected,
        active=True,
    )
    custom_capacity_factory(
        shelf_type=ShelfType.TYPE01,
        color=Type01Color.GRAY,
        feature=FeatureChoice.NO_FEATURE,
        is_desk=False,
        ship_in_range=ShipInRangeChoice.WEEKS_4_5.value,
        active=True,
    )
    custom_capacity_factory(
        shelf_type=ShelfType.TYPE01,
        color=Type01Color.WHITE,
        feature=FeatureChoice.WITH_DOORS,
        is_desk=False,
        ship_in_range=ShipInRangeChoice.WEEKS_6_7.value,
        active=True,
    )
    service = JettyShipInRangeMatrixService(shelf_type=ShelfType.TYPE01)
    ship_in_range = service.get_ship_in_range_for_shelf(color=Type01Color.WHITE)
    assert ship_in_range == expected


@pytest.mark.django_db
def test_ship_in_range_for_entity_capacity(week_capacity_factory):
    expected = ShipInRangeChoice.WEEKS_5_6
    capacity = 100
    week_capacity_factory(capacity=capacity, backlog=5.5 * capacity)
    service = JettyShipInRangeMatrixService(shelf_type=ShelfType.TYPE01)
    ship_in_range = service.get_ship_in_range_for_shelf(color=Type01Color.WHITE)
    assert ship_in_range == expected


@pytest.mark.django_db
def test_ship_in_range_for_shelf_capacity(
    week_capacity_factory, shelf_capacity_factory
):
    expected = ShipInRangeChoice.WEEKS_4_5
    week_capacity = week_capacity_factory(capacity=100, backlog=2.5 * 100)
    shelf_capacity_factory(
        base=week_capacity,
        capacity=50,
        backlog=4.5 * 50,
    )
    service = JettyShipInRangeMatrixService(shelf_type=ShelfType.TYPE01)
    ship_in_range = service.get_ship_in_range_for_shelf(color=Type01Color.WHITE)
    assert ship_in_range == expected


@pytest.mark.django_db
def test_production_pause_weeks_jetty(
    week_capacity_factory, shelf_capacity_factory, production_pause_week_factory
):
    week_capacity = week_capacity_factory(capacity=100, backlog=2.5 * 100)
    production_pause_week_factory(
        base=week_capacity, year=2021, week=50
    )  # should be added
    production_pause_week_factory(
        base=week_capacity, year=2022, week=1
    )  # should be added
    production_pause_week_factory(
        base=week_capacity, year=2022, week=9
    )  # should be ignored

    with freeze_time('2021-12-1'):
        shelf_capacity_factory(
            base=week_capacity,
            capacity=50,
            backlog=4.5 * 50,
            valid_from='2021-11-1',
        )
        service = JettyShipInRangeMatrixService(shelf_type=ShelfType.TYPE01)
        ship_in_range = service.get_ship_in_range_for_shelf(color=Type01Color.WHITE)

    expected = ShipInRangeChoice.WEEKS_6_7
    assert ship_in_range == expected


@pytest.mark.django_db
def test_delivery_time_matrix_for_watty(
    week_capacity_factory, shelf_capacity_factory, custom_capacity_factory
):
    week_capacity = week_capacity_factory(capacity=100, backlog=2 * 100)
    shelf_capacity_factory(
        base=week_capacity, shelf_type=ShelfType.TYPE03, capacity=10, backlog=5.5 * 10
    )
    custom_capacity_factory(
        shelf_type=ShelfType.TYPE03,
        color=ShelfType.TYPE03.colors.values()[-1],
        ship_in_range=ShipInRangeChoice.WEEKS_7_8,
        active=True,
    )
    expected = [
        [
            {'min': 5, 'max': 6},
            {'min': 5, 'max': 6},
            {'min': 5, 'max': 6},
            {'min': 5, 'max': 6},
            {'min': 5, 'max': 6},
            {'min': 5, 'max': 6},
            {'min': 5, 'max': 6},
            {'min': 5, 'max': 6},
            {'min': 5, 'max': 6},
            {'min': 5, 'max': 6},
            {'min': 5, 'max': 6},
            {'min': 5, 'max': 6},
            {'min': 5, 'max': 6},
            {'min': 5, 'max': 6},
            {'min': 5, 'max': 6},
            {'min': 5, 'max': 6},
            {'min': 5, 'max': 6},
            {'min': 5, 'max': 6},
            {'min': 5, 'max': 6},
            {'min': 5, 'max': 6},
            {'min': 7, 'max': 8},
        ]
    ]
    service = WattyShipInRangeMatrixService(shelf_type=ShelfType.TYPE03)
    matrix = service.get_matrix_as_list()
    assert matrix == expected


@pytest.mark.django_db
def test_delivery_time_for_watty(
    week_capacity_factory, shelf_capacity_factory, custom_capacity_factory
):
    week_capacity = week_capacity_factory(capacity=90, backlog=2 * 100)
    shelf_capacity_factory(
        base=week_capacity, shelf_type=ShelfType.TYPE03, capacity=10, backlog=5.5 * 10
    )
    custom_capacity_factory(
        shelf_type=ShelfType.TYPE03,
        color=ShelfType.TYPE03.colors.BEIGE_WHITE,
        ship_in_range=ShipInRangeChoice.WEEKS_7_8,
        active=True,
    )
    expected = ShipInRangeChoice.WEEKS_7_8
    service = WattyShipInRangeMatrixService(shelf_type=ShelfType.TYPE03)
    ship_in_range = service.get_ship_in_range_for_shelf(
        ShelfType.TYPE03.colors.BEIGE_WHITE
    )
    assert ship_in_range == expected


@pytest.mark.django_db
def test_production_pause_weeks_watty(
    week_capacity_factory,
    shelf_capacity_factory,
    custom_capacity_factory,
    production_pause_week_factory,
):
    week_capacity = week_capacity_factory(capacity=90, backlog=2 * 100)
    shelf_capacity_factory(
        base=week_capacity, shelf_type=ShelfType.TYPE03, capacity=10, backlog=5.5 * 10
    )
    custom_capacity_factory(
        shelf_type=ShelfType.TYPE03,
        color=ShelfType.TYPE03.colors.BEIGE_WHITE,
        ship_in_range=ShipInRangeChoice.WEEKS_7_8,
        active=True,
    )
    production_pause_week_factory(
        base=week_capacity, year=2021, week=50
    )  # should be ignored
    production_pause_week_factory(
        base=week_capacity, year=2022, week=1
    )  # should be added
    production_pause_week_factory(
        base=week_capacity, year=2022, week=9
    )  # should be added
    production_pause_week_factory(
        base=week_capacity, year=2022, week=11
    )  # should be ignored

    with freeze_time('2022-01-01'):
        service = WattyShipInRangeMatrixService(shelf_type=ShelfType.TYPE03)
        ship_in_range = service.get_ship_in_range_for_shelf(
            ShelfType.TYPE03.colors.BEIGE_WHITE
        )

    expected = ShipInRangeChoice.WEEKS_9_10
    assert ship_in_range == expected


@pytest.mark.django_db
def test_ship_in_range_for_shelf_color_capacity(
    week_capacity_factory, shelf_capacity_factory, shelf_color_capacity_factory
):
    expected = ShipInRangeChoice.WEEKS_11_12
    week_capacity = week_capacity_factory(capacity=100, backlog=2.5 * 100)
    shelf_capacity_factory(
        base=week_capacity,
        capacity=50,
        backlog=4.5 * 50,
    )
    shelf_color_capacity_factory(
        base=week_capacity,
        shelf_type=ShelfType.TYPE01,
        color=Type01Color.WHITE,
        capacity=20,
        backlog=10.5 * 20,
    )
    service = JettyShipInRangeMatrixService(shelf_type=ShelfType.TYPE01)
    ship_in_range = service.get_ship_in_range_for_shelf(color=Type01Color.WHITE)
    assert ship_in_range == expected


@pytest.mark.django_db
def test_ship_in_range_for_shelf_type_category_subtract_jetty(
    week_capacity_factory,
    shelf_capacity_factory,
    shelf_color_capacity_factory,
    shelf_color_category_subtract_factory,
):
    expected_without_subtract = ShipInRangeChoice.WEEKS_11_12
    expected_with_subtract = ShipInRangeChoice.WEEKS_8_9
    week_capacity = week_capacity_factory(capacity=100, backlog=2.5 * 100)
    shelf_capacity_factory(
        base=week_capacity,
        capacity=50,
        backlog=4.5 * 50,
    )
    shelf_color_capacity_factory(
        base=week_capacity,
        shelf_type=ShelfType.TYPE01,
        color=Type01Color.WHITE,
        capacity=20,
        backlog=10.5 * 20,
    )
    shelf_color_category_subtract_factory(
        shelf_type=ShelfType.TYPE01,
        furniture_category=FurnitureCategory.BOOKCASE.value,
        color=Type01Color.WHITE,
        weeks_to_subtract=3,
    )
    service = JettyShipInRangeMatrixService(shelf_type=ShelfType.TYPE01)
    ship_in_range = service.get_ship_in_range_for_shelf(color=Type01Color.WHITE)
    assert ship_in_range == expected_without_subtract
    service = JettyShipInRangeMatrixService(
        shelf_type=ShelfType.TYPE01,
        furniture_category=FurnitureCategory.BOOKCASE,
    )
    ship_in_range = service.get_ship_in_range_for_shelf(color=Type01Color.WHITE)
    assert ship_in_range == expected_with_subtract


@pytest.mark.django_db
def test_ship_in_range_for_shelf_type_category_subtract_watty(
    week_capacity_factory,
    shelf_capacity_factory,
    shelf_color_capacity_factory,
    shelf_color_category_subtract_factory,
):
    expected_without_subtract = ShipInRangeChoice.WEEKS_11_12
    expected_with_subtract = ShipInRangeChoice.WEEKS_8_9
    week_capacity = week_capacity_factory(capacity=100, backlog=2.5 * 100)
    shelf_capacity_factory(
        base=week_capacity,
        capacity=50,
        backlog=4.5 * 50,
    )
    shelf_color_capacity_factory(
        base=week_capacity,
        shelf_type=ShelfType.TYPE13,
        color=Type13Color.WHITE,
        capacity=20,
        backlog=10.5 * 20,
    )
    shelf_color_category_subtract_factory(
        shelf_type=ShelfType.TYPE13,
        furniture_category=FurnitureCategory.BOOKCASE.value,
        color=Type13Color.WHITE,
        weeks_to_subtract=3,
    )
    service = WattyShipInRangeMatrixService(shelf_type=ShelfType.TYPE13)
    ship_in_range = service.get_ship_in_range_for_shelf(color=Type13Color.WHITE)
    assert ship_in_range == expected_without_subtract
    service = WattyShipInRangeMatrixService(
        shelf_type=ShelfType.TYPE13,
        furniture_category=FurnitureCategory.BOOKCASE,
    )
    ship_in_range = service.get_ship_in_range_for_shelf(color=Type13Color.WHITE)
    assert ship_in_range == expected_with_subtract


@pytest.mark.django_db
def test_ship_in_range_for_shelf_color_capacity_min(
    week_capacity_factory,
    shelf_capacity_factory,
    shelf_color_capacity_factory,
    shelf_color_range_factory,
):
    expected = ShipInRangeChoice.WEEKS_15_16
    week_capacity = week_capacity_factory(capacity=100, backlog=2.5 * 100)
    shelf_capacity_factory(
        base=week_capacity,
        capacity=50,
        backlog=4.5 * 50,
    )
    shelf_color_capacity_factory(
        base=week_capacity,
        shelf_type=ShelfType.TYPE01,
        color=Type01Color.WHITE,
        capacity=20,
        backlog=10.5 * 20,
    )
    shelf_color_range_factory(
        shelf_type=ShelfType.TYPE01,
        color=Type01Color.WHITE,
        min_ship_in_range=expected,
    )

    service = JettyShipInRangeMatrixService(shelf_type=ShelfType.TYPE01)
    ship_in_range = service.get_ship_in_range_for_shelf(color=Type01Color.WHITE)
    assert ship_in_range == expected


@pytest.mark.django_db
def test_ship_in_range_for_shelf_color_capacity_max(
    week_capacity_factory,
    shelf_capacity_factory,
    shelf_color_capacity_factory,
    shelf_color_range_factory,
):
    expected = ShipInRangeChoice.WEEKS_8_9
    week_capacity = week_capacity_factory(capacity=100, backlog=2.5 * 100)
    shelf_capacity_factory(
        base=week_capacity,
        capacity=50,
        backlog=4.5 * 50,
    )
    shelf_color_capacity_factory(
        base=week_capacity,
        shelf_type=ShelfType.TYPE01,
        color=Type01Color.WHITE,
        capacity=20,
        backlog=10.5 * 20,
    )
    shelf_color_range_factory(
        shelf_type=ShelfType.TYPE01,
        color=Type01Color.WHITE,
        max_ship_in_range=expected,
    )

    service = JettyShipInRangeMatrixService(shelf_type=ShelfType.TYPE01)
    ship_in_range = service.get_ship_in_range_for_shelf(color=Type01Color.WHITE)
    assert ship_in_range == expected


@freeze_time('2022-01-01')
def test_all_conditions(
    week_capacity_factory,
    shelf_capacity_factory,
    shelf_color_capacity_factory,
    custom_capacity_factory,
    production_pause_week_factory,
):
    week_capacity = week_capacity_factory(capacity=90, backlog=2 * 100)
    shelf_capacity_factory(
        base=week_capacity,
        shelf_type=ShelfType.TYPE03,
        capacity=10,
        backlog=5.5 * 10,
        valid_from=datetime.now(),
    )
    shelf_color_capacity_factory(
        base=week_capacity,
        shelf_type=ShelfType.TYPE03,
        color=ShelfType.TYPE03.colors.BEIGE_WHITE,
        margin_of_error=0,
        capacity=10,
        backlog=9.5 * 10,
        valid_from=datetime.now(),
    )
    custom_capacity_factory(
        shelf_type=ShelfType.TYPE03,
        color=ShelfType.TYPE03.colors.BEIGE_WHITE,
        ship_in_range=ShipInRangeChoice.WEEKS_7_8,
        active=True,
    )
    production_pause_week_factory(
        base=week_capacity, year=2021, week=50
    )  # should be ignored
    production_pause_week_factory(
        base=week_capacity, year=2022, week=1
    )  # should be added
    service = WattyShipInRangeMatrixService(shelf_type=ShelfType.TYPE03)
    ship_in_range = service.get_ship_in_range_for_shelf(
        ShelfType.TYPE03.colors.BEIGE_WHITE
    )

    # should be shelf color capacity - WEEKS_9_10 plus 1 pause week
    expected = ShipInRangeChoice.WEEKS_10_11
    assert ship_in_range == expected
