from http.cookies import <PERSON><PERSON><PERSON><PERSON>
from unittest.mock import patch

from django.urls import reverse
from django.utils.translation import gettext_lazy as _

import pytest

from rest_framework import status

from abtests.constants import EXTENDED_DELIVERY_TEST_NAME
from custom.enums import ShelfType
from dynamic_delivery.choices import ShipInRangeChoice
from dynamic_delivery.services.ship_in_range import JettyShipInRangeMatrixService


@pytest.mark.django_db
class TestProductionTimeView:
    @pytest.mark.parametrize('extended_delivery_test', ['nok', 'ok'])
    @patch.object(
        JettyShipInRangeMatrixService,
        'get_ship_in_range_for_shelf',
        return_value=ShipInRangeChoice.WEEKS_4_5,
    )
    def test_should_get_production_time(
        self, _mock, api_client, extended_delivery_test
    ):
        # Assign
        cookie = {EXTENDED_DELIVERY_TEST_NAME: extended_delivery_test}
        if extended_delivery_test == 'ok':
            text = _('extended_delivery_dynamic_text_with_br')
            text_with_broken_copy = _('extended_delivery_dynamic_text_with_tick')
            min_delivery_time = 7
            max_delivery_time = 8
        else:
            text = _('delivery_dynamic_text_with_br')
            text_with_broken_copy = _('delivery_dynamic_text_with_tick')
            min_delivery_time = 4
            max_delivery_time = 5
        api_client.cookies = SimpleCookie(cookie)
        base_url = reverse('production-time')
        first_color = 0
        shelf_type = ShelfType.TYPE01
        url = f'{base_url}?shelf_type={shelf_type}&materials={first_color}'

        # Act
        response = api_client.get(url)

        # Assert
        response_json = response.json()
        assert response.status_code == status.HTTP_200_OK
        assert response_json['min'] == min_delivery_time
        assert response_json['max'] == max_delivery_time
        translations = response_json['translations']
        assert translations['text'] == text
        assert translations['text_with_broken_copy'] == text_with_broken_copy
