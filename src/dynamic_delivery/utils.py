import json
import logging

from datetime import datetime

from django.conf import settings
from django.contrib.auth.models import User


class DateTimeEncoder(json.JSONEncoder):
    def default(self, obj):
        if isinstance(obj, datetime):
            return obj.isoformat()
        return json.JSONEncoder.default(self, obj)


logger = logging.getLogger('cstm')

CAPACITY_INFO_FIELDS = ['shelf_type', 'color', 'manufactor', 'is_desk']


class ShipInRangeAutoLogChangeMixin:
    def save(self, *args, **kwargs):
        try:
            is_new_instance = self._state.adding
            if is_new_instance:
                return super().save(*args, **kwargs)

            is_refresh_products_no = kwargs.pop('is_refresh_products_no', False)
            old_instance = type(self).objects.get(pk=self.pk)
            old_ship_in_range = getattr(old_instance, 'ship_in_range', None)
            super().save(*args, **kwargs)

            self.refresh_from_db()
            new_ship_in_range = getattr(self, 'ship_in_range', None)

            if is_refresh_products_no and old_ship_in_range != new_ship_in_range:
                diff = {
                    self._meta.model_name: {
                        'ship_in_range': {
                            'old': old_ship_in_range,
                            'new': new_ship_in_range,
                        },
                    }
                }

                self.add_fields_if_exist(diff, CAPACITY_INFO_FIELDS)

                from dynamic_delivery.models import ChangeLog

                ChangeLog.objects.create(
                    user=User.objects.get_by_natural_key(
                        settings.CELERY_SUPERUSER_USERNAME,
                    ),
                    model_name=self._meta.model_name,
                    changes=json.dumps(diff, cls=DateTimeEncoder),
                )
        except Exception:
            logger.exception(
                f'Could not save ChangeLog for model:'
                f' [{self._meta.model_name} id: {self.pk}'
            )
            super().save(*args, **kwargs)

    def add_fields_if_exist(self, diff, field_names):
        model_name = self._meta.model_name

        for field_name in field_names:
            field_value = getattr(self, field_name, None)
            if field_value is not None:
                display_method_name = f'get_{field_name}_display'
                if hasattr(self, display_method_name):
                    field_display = getattr(self, display_method_name)()
                    if model_name not in diff:
                        diff[model_name] = {}
                    diff[model_name][field_name] = {
                        'old': field_display,
                        'new': field_display,
                    }
