from rest_framework import views
from rest_framework.permissions import AllowAny
from rest_framework.request import Request
from rest_framework.response import Response

from abtests.constants import EXTENDED_DELIVERY_TEST_NAME
from custom.enums import ShelfType
from custom.views import is_ab_test_enabled
from dynamic_delivery.serializers import ProductionTimeSerializer
from dynamic_delivery.services.ship_in_range import (
    JettyShipInRangeMatrixService,
    SottyShipInRangeMatrixService,
    WattyShipInRangeMatrixService,
    extend_ship_in_range,
)
from gallery.enums import FurnitureCategory


class ProductionTimeView(views.APIView):
    permission_classes = (AllowAny,)

    def get(self, request, *args, **kwargs):
        use_extended_delivery = is_ab_test_enabled(
            request=request, codename=EXTENDED_DELIVERY_TEST_NAME
        )
        delivery_time_range = self.get_delivery_time_range(
            request=request, use_extended_delivery=use_extended_delivery
        )
        data = ProductionTimeSerializer(
            delivery_time_range,
            context={'use_extended_delivery': use_extended_delivery},
        ).data
        return Response(data)

    @staticmethod
    def service_class(
        shelf_type: ShelfType, features: list, furniture_category: str
    ) -> JettyShipInRangeMatrixService | WattyShipInRangeMatrixService:
        if shelf_type in ShelfType.get_jetty_shelf_types():
            service_class = JettyShipInRangeMatrixService
        elif shelf_type in ShelfType.get_watty_shelf_types():
            service_class = WattyShipInRangeMatrixService
        else:
            service_class = SottyShipInRangeMatrixService
        furniture_category = (
            FurnitureCategory(furniture_category.lower())
            if furniture_category.lower() in FurnitureCategory.values
            else None
        )
        return service_class(
            shelf_type=shelf_type,
            is_desk='desk' in features,
            furniture_category=furniture_category,
        )

    def get_delivery_time_range(
        self, request: Request, use_extended_delivery: bool
    ) -> dict[str, int]:
        shelf_type = ShelfType(int(request.GET.get('shelf_type')))
        features = request.GET.get('features', '').split(',')
        furniture_category = request.GET.get('furniture_category', '')
        # support for legacy 'material' parameter
        materials = request.GET.get('materials', '') or request.GET.get('material', '')
        colors = [
            shelf_type.colors(int(material))
            for material in materials.split(',')
            if material
        ]
        service = self.service_class(
            shelf_type=shelf_type,
            features=features,
            furniture_category=furniture_category,
        )
        delivery_time_range = service.get_ship_in_range_for_many_colors(
            colors=colors,
            has_doors='doors' in features,
            has_drawers='drawers' in features,
        )
        if use_extended_delivery:
            delivery_time_range = extend_ship_in_range(
                ship_in_range=delivery_time_range
            )
        return delivery_time_range.get_weeks_range()
