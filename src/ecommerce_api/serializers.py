import datetime

from django.conf import settings
from django.contrib.auth.models import User
from django.utils.functional import cached_property

from rest_framework import serializers

from custom.context_processors.settings_cp import (
    get_user_email_hash,
    get_user_email_hash_no_hmac,
    get_user_hash,
)
from custom.enums import (
    Furniture,
    LanguageEnum,
)
from custom.tasks import export_survey_data_to_bigquery
from ecommerce_api.services.survey import (
    dumps_checkout_survey_data,
    dumps_configurator_survey_data,
)
from frontend_cms.utils import is_waiting_list_token_activated
from gallery.models import SampleBox
from mailing.constants import NEWSLETTER_VOUCHER_VALUES
from orders.models import Order
from promotions.serializers import (
    CartRibbonSerializer,
    CountdownConfigSerializer,
    RibbonSerializer,
)
from regions.utils import is_t03_available
from reviews.models import ReviewScore
from user_profile.choices import UserType
from waiting_list.models import WaitingListSetup


class BaseSerializerContext:
    @property
    def request(self):
        return self.context.get('request')

    @property
    def region(self):
        return self.context.get('region')

    @property
    def language(self):
        return self.context.get('language')


class GlobalVariablesSerializer(BaseSerializerContext, serializers.Serializer):
    global_session_id = serializers.SerializerMethodField()
    user_id = serializers.SerializerMethodField()
    user_type = serializers.SerializerMethodField()
    user_hash_id = serializers.SerializerMethodField()
    user_hash_email = serializers.SerializerMethodField()
    user_hash_email_no_hmac = serializers.SerializerMethodField()
    is_signed_in = serializers.SerializerMethodField()
    cart_items_count = serializers.SerializerMethodField()
    has_t03 = serializers.SerializerMethodField()
    library_items_count = serializers.SerializerMethodField()
    t03_available = serializers.SerializerMethodField()
    assembly_available = serializers.SerializerMethodField()
    region_code = serializers.SerializerMethodField()
    region_name = serializers.SerializerMethodField()
    currency_code = serializers.SerializerMethodField()
    country_locale = serializers.SerializerMethodField()
    promo_is_active = serializers.SerializerMethodField()
    newsletter_voucher_value = serializers.SerializerMethodField()
    reviews_average_score = serializers.SerializerMethodField()
    reviews_count = serializers.SerializerMethodField()
    # Fields added for BI with data as compressed as possible
    ab_ids = serializers.SerializerMethodField()
    feature_flags_ids = serializers.SerializerMethodField()
    ab_tests_list = serializers.SerializerMethodField()
    feature_flags_list = serializers.SerializerMethodField()
    cart_ribbon = CartRibbonSerializer(source='*')
    ribbon = RibbonSerializer(source='*')
    countdown = CountdownConfigSerializer(allow_null=True)
    is_sales_enabled = serializers.SerializerMethodField()
    waiting_list_token_active = serializers.SerializerMethodField()
    waiting_list_token_expired = serializers.SerializerMethodField()
    sample_price = serializers.SerializerMethodField()
    order_id = serializers.SerializerMethodField()
    cart_id = serializers.SerializerMethodField()
    user_language = serializers.SerializerMethodField()

    def get_global_session_id(self, _):
        return self.request.COOKIES.get(
            'global_session_id',
            self.request.session.session_key,
        )

    @property
    def profile_user_type(self):
        if not self.request.user.is_anonymous:
            return self.request.user.profile.user_type

    @property
    def is_customer(self):
        return self.profile_user_type == UserType.CUSTOMER

    @property
    def cart(self):
        return self.context.get('cart')

    def get_order_id(self, _):
        if self.request.user.is_anonymous:
            return ''

        return self.cart.order_id if self.cart else ''

    def get_cart_id(self, _):
        if self.request.user.is_anonymous:
            return ''

        return self.cart.id if self.cart else ''

    def get_user_type(self, obj):
        return self.profile_user_type

    def get_user_hash_id(self, obj):
        return get_user_hash(self.request)

    def get_user_hash_email(self, obj):
        return get_user_email_hash(self.request)

    def get_user_hash_email_no_hmac(self, obj):
        return get_user_email_hash_no_hmac(self.request)

    def get_region_name(self, obj):
        return self.region.name

    def get_region_code(self, obj):
        return self.region.country_code

    def get_is_signed_in(self, obj):
        return self.request.user.is_authenticated and self.is_customer

    def get_user_id(self, obj):
        return self.request.user.id

    def get_cart_items_count(self, obj):
        return len(self._cart_items)

    def get_has_t03(self, obj):
        cart = self.context.get('cart')
        if not cart:
            return False

        for cart_item in self._cart_items:
            item = cart_item.sellable_item
            if isinstance(item, SampleBox) and item.box_variant.is_type_03_variant:
                return True
            if item.is_t03_wardrobe:
                return True

        return False

    def get_t03_available(self, obj):
        return is_t03_available(self.request.user, self.region)

    def get_library_items_count(self, obj):
        try:
            return self.request.user.profile.get_library_item_number()
        except AttributeError:
            return 0

    def get_ab_tests_list(self, obj):
        ab_tests = self.context.get('ab_tests')
        return [
            [f'{test.codename}|{test.rate_split}', active]
            for test, active in ab_tests.items()
        ]

    def get_feature_flags_list(self, obj):
        feature_flags = self.context.get('feature_flags')
        return [test.codename for test in feature_flags]

    def get_ab_ids(self, obj):
        ab_tests = self.context.get('ab_tests')
        ab_ids = [f'{test.id}:{int(active)}' for test, active in ab_tests.items()]
        return ','.join(ab_ids)

    def get_feature_flags_ids(self, obj):
        feature_flags = self.context.get('feature_flags')
        feature_flags_ids = [str(test.id) for test in feature_flags]
        return ','.join(feature_flags_ids)

    @staticmethod
    def get_promo_is_active(obj):
        return bool(obj)

    def get_newsletter_voucher_value(self, obj):
        voucher_data = NEWSLETTER_VOUCHER_VALUES.get(self.region.currency_code)
        if voucher_data:
            return self.region.get_format_price(voucher_data.value)

        return None

    def get_currency_code(self, obj):
        return self.region.currency_code

    def get_country_locale(self, obj):
        return self.region.country_locale

    def get_reviews_average_score(self, obj):
        reviews_score, _ = self._general_review_score
        return reviews_score

    def get_reviews_count(self, obj):
        _, reviews_count = self._general_review_score
        return reviews_count

    @staticmethod
    def get_is_sales_enabled(obj):
        return WaitingListSetup.is_sales_enabled()

    def get_waiting_list_token_active(self, obj):
        return is_waiting_list_token_activated(self.request)

    def get_waiting_list_token_expired(self, obj):
        waiting_list_token_exp_date = is_waiting_list_token_activated(self.request)
        return (
            waiting_list_token_exp_date < datetime.datetime.now().timestamp()
            if waiting_list_token_exp_date
            else None
        )

    def get_sample_price(self, obj):
        return SampleBox().get_regionalized_price(region=self.region)

    def get_assembly_available(self, obj):
        return self.region.name in settings.ASSEMBLY_REGION_KEYS

    @cached_property
    def _cart_items(self):
        cart = self.context.get('cart')
        return cart.items.all() if cart else []

    @cached_property
    def _general_review_score(self):
        return ReviewScore.get_general_review_score()

    def get_user_language(self, _):
        if self.request.user.is_anonymous:
            return LanguageEnum(self.request.session['cached_language'])

        return self.request.user.profile.language


class ColorSerializer(serializers.Serializer):
    material_id = serializers.IntegerField()
    name = serializers.CharField()


class ColorListSerializer(serializers.Serializer):
    def to_representation(self, instance):
        return {
            instance.value: ColorSerializer(
                [
                    {'material_id': color[0], 'name': color[1]}
                    for color in instance.colors.choices()
                ],
                many=True,
            ).data
        }


class SurveySerializer(serializers.Serializer):
    score = serializers.IntegerField(min_value=1, max_value=5)
    feedback = serializers.CharField(allow_blank=True)
    lang = serializers.CharField()

    def create_task(self, user: User, **kwargs) -> None:
        raise NotImplementedError

    def _dumps_data(self, user: User, data: dict, **kwargs) -> str:
        raise NotImplementedError


class CheckoutSurveySerializer(SurveySerializer):
    order_id = serializers.IntegerField()

    def validate_order_id(self, value):
        request = self.context.get('request')
        is_owner = Order.objects.filter(id=value, owner_id=request.user.id).exists()
        if not is_owner:
            raise serializers.ValidationError(
                'Order does not exist or does not belong to user'
            )

        return value

    def create_task(self, user: User, **kwargs) -> None:
        if (
            not settings.BIGQUERY_SURVEY_SPACE
            or not settings.BIGQUERY_CHECKOUT_SURVEY_TABLE
        ):
            return

        serialized_data = dumps_checkout_survey_data(
            user=user, data=self.validated_data, device_type=self._get_device_type()
        )
        export_survey_data_to_bigquery.delay(
            space=settings.BIGQUERY_SURVEY_SPACE,
            table=settings.BIGQUERY_CHECKOUT_SURVEY_TABLE,
            serialized_data=serialized_data,
        )

    def _get_device_type(self) -> str:
        request = self.context.get('request')
        if request.user_agent.is_mobile or request.user_agent.is_tablet:
            return 'mobile'
        return 'web'


class ConfiguratorSurveySerializer(SurveySerializer):
    item_id = serializers.IntegerField()
    item_content_type = serializers.ChoiceField(
        choices=Furniture.furniture_types_choices()
    )

    def validate(self, attrs):
        model = Furniture(attrs['item_content_type']).model
        if not model.objects.filter(id=attrs['item_id']).exists():
            msg = (
                f'{attrs["item_content_type"]}[id={attrs["item_id"]}] '
                'item does not exist'
            )
            raise serializers.ValidationError(msg)
        return super().validate(attrs)

    def create_task(self, user: User, **kwargs) -> None:
        if not settings.BIGQUERY_SURVEY_SPACE or not settings.BIGQUERY_CES_SURVEY_TABLE:
            return

        serialized_data = dumps_configurator_survey_data(
            user=user, data=self.validated_data, ab_tests=self._get_ab_tests()
        )
        export_survey_data_to_bigquery.delay(
            space=settings.BIGQUERY_SURVEY_SPACE,
            table=settings.BIGQUERY_CES_SURVEY_TABLE,
            serialized_data=serialized_data,
        )

    def _get_ab_tests(self) -> dict:
        request = self.context.get('request')
        return {
            ab_test.codename: request.session[ab_test]
            for ab_test in request.cached_actual_tests
            if ab_test in request.session
        }
