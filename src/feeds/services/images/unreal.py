import io

from pathlib import Path

from django.contrib.contenttypes.models import ContentType
from django.core.files.base import ContentFile
from django.db.models import Q

from PIL import Image

from feeds.image_configs import ImageConfigOption
from feeds.models import (
    Feed,
    FeedImage,
    FeedItem,
)
from render_tasks.choices import RenderTaskStatuses
from render_tasks.models import UnrealRenderTask
from render_tasks.serializers import get_serialized_furniture_data_for_unreal


def create_unreal_render_tasks_for_feed(feed: Feed) -> None:
    categories = feed.categories.filter(
        image_config__in=ImageConfigOption.unreal_configs()
    )
    for category in categories:
        feed_items = FeedItem.objects.filter(category=category).prefetch_related(
            'images'
        )
        for item in feed_items:
            if not any(
                image.config == category.image_config for image in item.images.all()
            ):
                generate_unreal_render_task_for_feed_item(
                    config=ImageConfigOption(category.image_config),
                    feed_item=item,
                )


def generate_unreal_render_task_for_feed_item(
    config: ImageConfigOption,
    feed_item: FeedItem,
) -> None:
    interior = ImageConfigOption(config).unreal_interior
    defaults = {
        'furniture_content_type': feed_item.content_type,
        'furniture_id': feed_item.object_id,
        'initiator_content_type': ContentType.objects.get_for_model(FeedItem),
        'initiator_id': feed_item.id,
        'image_config': {
            'config_id': config,
            'interior': interior.for_feed(),
        },
    }
    if config in [
        ImageConfigOption.UNREAL_FRONT_STUDIO,
        ImageConfigOption.UNREAL_LEFT30_STUDIO,
    ]:
        defaults['image_config']['camera'] = config.camera_setting

    already_exists = UnrealRenderTask.objects.filter(
        ~Q(status=RenderTaskStatuses.FAILED),
        **defaults,
    ).exists()
    if already_exists:
        return
    UnrealRenderTask.objects.create(
        furniture_json=get_serialized_furniture_data_for_unreal(feed_item.furniture),
        **defaults,
    )


def add_image_to_items_and_delete_old_images(
    feed_image: FeedImage,
    render_task: UnrealRenderTask,
    image_config: ImageConfigOption | None = None,
) -> None:
    config = render_task.image_config.get('config_id', image_config)
    similar_feed_items = FeedItem.objects.filter(
        category__image_config=config,
        content_type=render_task.furniture_content_type,
        object_id=render_task.furniture_id,
    )
    FeedImage.objects.filter(config=config, items__in=similar_feed_items).delete()
    feed_image.items.add(*similar_feed_items)


def save_unreal_image(
    render_task: 'UnrealRenderTask', image_config: ImageConfigOption | None = None
) -> None:
    config = render_task.image_config.get('config_id', image_config)
    feed_image = FeedImage(
        config=config,
        additional_data={
            'scene': render_task.additional_data.get('scene', {}).get(
                'interior', 'NO DATA'
            )
        },
    )

    image_name = Path(render_task.image.name)
    new_image_name = str(image_name.with_suffix('.webp'))

    with Image.open(render_task.image) as img:
        img = img.convert('RGB')  # convert to RGB before saving as webp
        output = io.BytesIO()
        img.save(output, format='WEBP')
        output.seek(0)
        content_file = ContentFile(output.read())
        feed_image.image.save(new_image_name, content_file)

        feed_image.save()
    add_image_to_items_and_delete_old_images(
        feed_image, render_task, image_config=image_config
    )
