!function(t,e){"object"==typeof exports&&"undefined"!=typeof module?e(exports):"function"==typeof define&&define.amd?define(["exports"],e):e(t.THREE={})}(this,function(t){function e(){}function i(t,e){this.x=t||0,this.y=e||0}function n(){this.elements=[1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1],0<arguments.length&&void 0}function r(t,e,i,n){this._x=t||0,this._y=e||0,this._z=i||0,this._w=void 0!==n?n:1}function a(t,e,i){this.x=t||0,this.y=e||0,this.z=i||0}function o(){this.elements=[1,0,0,0,1,0,0,0,1],0<arguments.length&&void 0}function s(t,e,n,r,a,h,c,l,u,p){Object.defineProperty(this,"id",{value:ea++}),this.uuid=$r.generateUUID(),this.name="",this.image=void 0!==t?t:s.DEFAULT_IMAGE,this.mipmaps=[],this.mapping=void 0!==e?e:s.DEFAULT_MAPPING,this.wrapS=void 0!==n?n:1001,this.wrapT=void 0!==r?r:1001,this.magFilter=void 0!==a?a:1006,this.minFilter=void 0!==h?h:1008,this.anisotropy=void 0!==u?u:1,this.format=void 0!==c?c:1023,this.type=void 0!==l?l:1009,this.offset=new i(0,0),this.repeat=new i(1,1),this.center=new i(0,0),this.rotation=0,this.matrixAutoUpdate=!0,this.matrix=new o,this.generateMipmaps=!0,this.premultiplyAlpha=!1,this.flipY=!0,this.unpackAlignment=4,this.encoding=void 0!==p?p:3e3,this.version=0,this.onUpdate=null}function h(t,e,i,n){this.x=t||0,this.y=e||0,this.z=i||0,this.w=void 0!==n?n:1}function c(t,e,i){this.width=t,this.height=e,this.scissor=new h(0,0,t,e),this.scissorTest=!1,this.viewport=new h(0,0,t,e),i=i||{},void 0===i.minFilter&&(i.minFilter=1006),this.texture=new s((void 0),(void 0),i.wrapS,i.wrapT,i.magFilter,i.minFilter,i.format,i.type,i.anisotropy,i.encoding),this.texture.generateMipmaps=void 0===i.generateMipmaps||i.generateMipmaps,this.depthBuffer=void 0===i.depthBuffer||i.depthBuffer,this.stencilBuffer=void 0===i.stencilBuffer||i.stencilBuffer,this.depthTexture=void 0!==i.depthTexture?i.depthTexture:null}function l(t,e,i){c.call(this,t,e,i),this.activeMipMapLevel=this.activeCubeFace=0}function u(t,e,i,n,r,a,o,h,c,l,u,p){s.call(this,null,a,o,h,c,l,n,r,u,p),this.image={data:t,width:e,height:i},this.magFilter=void 0!==c?c:1003,this.minFilter=void 0!==l?l:1003,this.flipY=this.generateMipmaps=!1,this.unpackAlignment=1}function p(t,e){this.min=void 0!==t?t:new a(1/0,1/0,1/0),this.max=void 0!==e?e:new a((-(1/0)),(-(1/0)),(-(1/0)))}function d(t,e){this.center=void 0!==t?t:new a,this.radius=void 0!==e?e:0}function f(t,e){this.normal=void 0!==t?t:new a(1,0,0),this.constant=void 0!==e?e:0}function m(t,e,i,n,r,a){this.planes=[void 0!==t?t:new f,void 0!==e?e:new f,void 0!==i?i:new f,void 0!==n?n:new f,void 0!==r?r:new f,void 0!==a?a:new f]}function g(t,e,i){return void 0===e&&void 0===i?this.set(t):this.setRGB(t,e,i)}function v(){function t(r,a){!1!==i&&(n(r,a),e.requestAnimationFrame(t))}var e=null,i=!1,n=null;return{start:function(){!0!==i&&null!==n&&(e.requestAnimationFrame(t),i=!0)},stop:function(){i=!1},setAnimationLoop:function(t){n=t},setContext:function(t){e=t}}}function y(t){function e(e,i){var n=e.array,r=e.dynamic?t.DYNAMIC_DRAW:t.STATIC_DRAW,a=t.createBuffer();return t.bindBuffer(i,a),t.bufferData(i,n,r),e.onUploadCallback(),i=t.FLOAT,n instanceof Float32Array?i=t.FLOAT:n instanceof Float64Array?void 0:n instanceof Uint16Array?i=t.UNSIGNED_SHORT:n instanceof Int16Array?i=t.SHORT:n instanceof Uint32Array?i=t.UNSIGNED_INT:n instanceof Int32Array?i=t.INT:n instanceof Int8Array?i=t.BYTE:n instanceof Uint8Array&&(i=t.UNSIGNED_BYTE),{buffer:a,type:i,bytesPerElement:n.BYTES_PER_ELEMENT,version:e.version}}var i=new WeakMap;return{get:function(t){return t.isInterleavedBufferAttribute&&(t=t.data),i.get(t)},remove:function(e){e.isInterleavedBufferAttribute&&(e=e.data);var n=i.get(e);n&&(t.deleteBuffer(n.buffer),i["delete"](e))},update:function(n,r){n.isInterleavedBufferAttribute&&(n=n.data);var a=i.get(n);if(void 0===a)i.set(n,e(n,r));else if(a.version<n.version){var o=n,s=o.array,h=o.updateRange;t.bindBuffer(r,a.buffer),!1===o.dynamic?t.bufferData(r,s,t.STATIC_DRAW):-1===h.count?t.bufferSubData(r,0,s):0===h.count?void 0:(t.bufferSubData(r,h.offset*s.BYTES_PER_ELEMENT,s.subarray(h.offset,h.offset+h.count)),h.count=-1),a.version=n.version}}}}function x(t,e,i,n){this._x=t||0,this._y=e||0,this._z=i||0,this._order=n||x.DefaultOrder}function _(){this.mask=1}function b(){Object.defineProperty(this,"id",{value:sa++}),this.uuid=$r.generateUUID(),this.name="",this.type="Object3D",this.parent=null,this.children=[],this.up=b.DefaultUp.clone();var t=new a,e=new x,i=new r,s=new a(1,1,1);e.onChange(function(){i.setFromEuler(e,!1)}),i.onChange(function(){e.setFromQuaternion(i,void 0,!1)}),Object.defineProperties(this,{position:{enumerable:!0,value:t},rotation:{enumerable:!0,value:e},quaternion:{enumerable:!0,value:i},scale:{enumerable:!0,value:s},modelViewMatrix:{value:new n},normalMatrix:{value:new o}}),this.matrix=new n,this.matrixWorld=new n,this.matrixAutoUpdate=b.DefaultMatrixAutoUpdate,this.matrixWorldNeedsUpdate=!1,this.layers=new _,this.visible=!0,this.receiveShadow=this.castShadow=!1,this.frustumCulled=!0,this.renderOrder=0,this.userData={}}function M(){b.call(this),this.type="Camera",this.matrixWorldInverse=new n,this.projectionMatrix=new n,this.projectionMatrixInverse=new n}function w(t,e,i,n,r,a){M.call(this),this.type="OrthographicCamera",this.zoom=1,this.view=null,this.left=t,this.right=e,this.top=i,this.bottom=n,this.near=void 0!==r?r:.1,this.far=void 0!==a?a:2e3,this.updateProjectionMatrix()}function S(t,e,i,n,r,o){this.a=t,this.b=e,this.c=i,this.normal=n&&n.isVector3?n:new a,this.vertexNormals=Array.isArray(n)?n:[],this.color=r&&r.isColor?r:new g,this.vertexColors=Array.isArray(r)?r:[],this.materialIndex=void 0!==o?o:0}function A(){Object.defineProperty(this,"id",{value:ha+=2}),this.uuid=$r.generateUUID(),this.name="",this.type="Geometry",this.vertices=[],this.colors=[],this.faces=[],this.faceVertexUvs=[[]],this.morphTargets=[],this.morphNormals=[],this.skinWeights=[],this.skinIndices=[],this.lineDistances=[],this.boundingSphere=this.boundingBox=null,this.groupsNeedUpdate=this.lineDistancesNeedUpdate=this.colorsNeedUpdate=this.normalsNeedUpdate=this.uvsNeedUpdate=this.verticesNeedUpdate=this.elementsNeedUpdate=!1}function T(t,e,i){if(Array.isArray(t))throw new TypeError("THREE.BufferAttribute: array should be a Typed Array.");this.name="",this.array=t,this.itemSize=e,this.count=void 0!==t?t.length/e:0,this.normalized=!0===i,this.dynamic=!1,this.updateRange={offset:0,count:-1},this.version=0}function L(t,e,i){T.call(this,new Int8Array(t),e,i)}function E(t,e,i){T.call(this,new Uint8Array(t),e,i)}function C(t,e,i){T.call(this,new Uint8ClampedArray(t),e,i)}function P(t,e,i){T.call(this,new Int16Array(t),e,i)}function O(t,e,i){T.call(this,new Uint16Array(t),e,i)}function I(t,e,i){T.call(this,new Int32Array(t),e,i)}function N(t,e,i){T.call(this,new Uint32Array(t),e,i)}function R(t,e,i){T.call(this,new Float32Array(t),e,i)}function D(t,e,i){T.call(this,new Float64Array(t),e,i)}function U(){this.vertices=[],this.normals=[],this.colors=[],this.uvs=[],this.uvs2=[],this.groups=[],this.morphTargets={},this.skinWeights=[],this.skinIndices=[],this.boundingSphere=this.boundingBox=null,this.groupsNeedUpdate=this.uvsNeedUpdate=this.colorsNeedUpdate=this.normalsNeedUpdate=this.verticesNeedUpdate=!1}function B(t){if(0===t.length)return-(1/0);for(var e=t[0],i=1,n=t.length;i<n;++i)t[i]>e&&(e=t[i]);return e}function z(){Object.defineProperty(this,"id",{value:ca+=2}),this.uuid=$r.generateUUID(),this.name="",this.type="BufferGeometry",this.index=null,this.attributes={},this.morphAttributes={},this.groups=[],this.boundingSphere=this.boundingBox=null,this.drawRange={start:0,count:1/0},this.userData={}}function F(t,e,i,n,r,a){A.call(this),this.type="BoxGeometry",this.parameters={width:t,height:e,depth:i,widthSegments:n,heightSegments:r,depthSegments:a},this.fromBufferGeometry(new G(t,e,i,n,r,a)),this.mergeVertices()}function G(t,e,i,n,r,o){function s(t,e,i,n,r,o,s,m,g,v,y){var x=o/g,_=s/v,b=o/2,M=s/2,w=m/2;s=g+1;var S,A,T=v+1,L=o=0,E=new a;for(A=0;A<T;A++){var C=A*_-M;for(S=0;S<s;S++)E[t]=(S*x-b)*n,E[e]=C*r,E[i]=w,l.push(E.x,E.y,E.z),E[t]=0,E[e]=0,E[i]=0<m?1:-1,u.push(E.x,E.y,E.z),p.push(S/g),p.push(1-A/v),o+=1}for(A=0;A<v;A++)for(S=0;S<g;S++)t=d+S+s*(A+1),e=d+(S+1)+s*(A+1),i=d+(S+1)+s*A,c.push(d+S+s*A,t,i),c.push(t,e,i),L+=6;h.addGroup(f,L,y),f+=L,d+=o}z.call(this),this.type="BoxBufferGeometry",this.parameters={width:t,height:e,depth:i,widthSegments:n,heightSegments:r,depthSegments:o};var h=this;t=t||1,e=e||1,i=i||1,n=Math.floor(n)||1,r=Math.floor(r)||1,o=Math.floor(o)||1;var c=[],l=[],u=[],p=[],d=0,f=0;s("z","y","x",-1,-1,i,e,t,o,r,0),s("z","y","x",1,-1,i,e,-t,o,r,1),s("x","z","y",1,1,t,i,e,n,o,2),s("x","z","y",1,-1,t,i,-e,n,o,3),s("x","y","z",1,-1,t,e,i,n,r,4),s("x","y","z",-1,-1,t,e,-i,n,r,5),this.setIndex(c),this.addAttribute("position",new R(l,3)),this.addAttribute("normal",new R(u,3)),this.addAttribute("uv",new R(p,2))}function V(t,e,i,n){A.call(this),this.type="PlaneGeometry",this.parameters={width:t,height:e,widthSegments:i,heightSegments:n},this.fromBufferGeometry(new k(t,e,i,n)),this.mergeVertices()}function k(t,e,i,n){z.call(this),this.type="PlaneBufferGeometry",this.parameters={width:t,height:e,widthSegments:i,heightSegments:n},t=t||1,e=e||1;var r=t/2,a=e/2;i=Math.floor(i)||1,n=Math.floor(n)||1;var o=i+1,s=n+1,h=t/i,c=e/n,l=[],u=[],p=[],d=[];for(t=0;t<s;t++){var f=t*c-a;for(e=0;e<o;e++)u.push(e*h-r,-f,0),p.push(0,0,1),d.push(e/i),d.push(1-t/n)}for(t=0;t<n;t++)for(e=0;e<i;e++)r=e+o*(t+1),a=e+1+o*(t+1),s=e+1+o*t,l.push(e+o*t,r,s),l.push(r,a,s);this.setIndex(l),this.addAttribute("position",new R(u,3)),this.addAttribute("normal",new R(p,3)),this.addAttribute("uv",new R(d,2))}function j(){Object.defineProperty(this,"id",{value:la++}),this.uuid=$r.generateUUID(),this.name="",this.type="Material",this.lights=this.fog=!0,this.blending=1,this.side=0,this.flatShading=!1,this.vertexColors=0,this.opacity=1,this.transparent=!1,this.blendSrc=204,this.blendDst=205,this.blendEquation=100,this.blendEquationAlpha=this.blendDstAlpha=this.blendSrcAlpha=null,this.depthFunc=3,this.depthWrite=this.depthTest=!0,this.clippingPlanes=null,this.clipShadows=this.clipIntersection=!1,this.shadowSide=null,this.colorWrite=!0,this.precision=null,this.polygonOffset=!1,this.polygonOffsetUnits=this.polygonOffsetFactor=0,this.dithering=!1,this.alphaTest=0,this.premultipliedAlpha=!1,this.overdraw=0,this.visible=!0,this.userData={},this.needsUpdate=!0}function W(t){j.call(this),this.type="MeshBasicMaterial",this.color=new g(16777215),this.lightMap=this.map=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.envMap=this.alphaMap=this.specularMap=null,this.combine=0,this.reflectivity=1,this.refractionRatio=.98,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinejoin=this.wireframeLinecap="round",this.lights=this.morphTargets=this.skinning=!1,this.setValues(t)}function H(t){j.call(this),this.type="ShaderMaterial",this.defines={},this.uniforms={},this.vertexShader="void main() {\n\tgl_Position = projectionMatrix * modelViewMatrix * vec4( position, 1.0 );\n}",this.fragmentShader="void main() {\n\tgl_FragColor = vec4( 1.0, 0.0, 0.0, 1.0 );\n}",this.linewidth=1,this.wireframe=!1,this.wireframeLinewidth=1,this.morphNormals=this.morphTargets=this.skinning=this.clipping=this.lights=this.fog=!1,this.extensions={derivatives:!1,fragDepth:!1,drawBuffers:!1,shaderTextureLOD:!1},this.defaultAttributeValues={color:[1,1,1],uv:[0,0],uv2:[0,0]},this.index0AttributeName=void 0,this.uniformsNeedUpdate=!1,void 0!==t&&(void 0!==t.attributes&&void 0,this.setValues(t))}function X(t,e){this.origin=void 0!==t?t:new a,this.direction=void 0!==e?e:new a}function q(t,e,i){this.a=void 0!==t?t:new a,this.b=void 0!==e?e:new a,this.c=void 0!==i?i:new a}function Y(t,e){b.call(this),this.type="Mesh",this.geometry=void 0!==t?t:new z,this.material=void 0!==e?e:new W({color:16777215*Math.random()}),this.drawMode=0,this.updateMorphTargets()}function Z(t,e,i,n){function r(t,i){e.buffers.color.setClear(t.r,t.g,t.b,i,n)}var a,o,s,h=new g(0),c=0;return{getClearColor:function(){return h},setClearColor:function(t,e){h.set(t),c=void 0!==e?e:1,r(h,c)},getClearAlpha:function(){return c},setClearAlpha:function(t){c=t,r(h,c)},render:function(e,n,l,u){n=n.background,null===n?r(h,c):n&&n.isColor&&(r(n,1),u=!0),(t.autoClear||u)&&t.clear(t.autoClearColor,t.autoClearDepth,t.autoClearStencil),n&&n.isCubeTexture?(void 0===s&&(s=new Y(new G(1,1,1),new H({uniforms:oa.cube.uniforms,vertexShader:oa.cube.vertexShader,fragmentShader:oa.cube.fragmentShader,side:1,depthTest:!0,depthWrite:!1,fog:!1})),s.geometry.removeAttribute("normal"),s.geometry.removeAttribute("uv"),s.onBeforeRender=function(t,e,i){this.matrixWorld.copyPosition(i.matrixWorld)},i.update(s)),s.material.uniforms.tCube.value=n,e.push(s,s.geometry,s.material,0,null)):n&&n.isTexture&&(void 0===a&&(a=new w((-1),1,1,(-1),0,1),o=new Y(new k(2,2),new W({depthTest:!1,depthWrite:!1,fog:!1})),i.update(o)),o.material.map=n,t.renderBufferDirect(a,null,o.geometry,o.material,o,null))}}}function J(t,e,i,n){var r;this.setMode=function(t){r=t},this.render=function(e,n){t.drawArrays(r,e,n),i.update(n,r)},this.renderInstances=function(a,o,s){if(n.isWebGL2)var h=t;else if(h=e.get("ANGLE_instanced_arrays"),null===h)return;h[n.isWebGL2?"drawArraysInstanced":"drawArraysInstancedANGLE"](r,o,s,a.maxInstancedCount),i.update(s,r,a.maxInstancedCount)}}function Q(t,e,i){function n(e){if("highp"===e){if(0<t.getShaderPrecisionFormat(t.VERTEX_SHADER,t.HIGH_FLOAT).precision&&0<t.getShaderPrecisionFormat(t.FRAGMENT_SHADER,t.HIGH_FLOAT).precision)return"highp";e="mediump"}return"mediump"===e&&0<t.getShaderPrecisionFormat(t.VERTEX_SHADER,t.MEDIUM_FLOAT).precision&&0<t.getShaderPrecisionFormat(t.FRAGMENT_SHADER,t.MEDIUM_FLOAT).precision?"mediump":"lowp"}var r,a="undefined"!=typeof WebGL2RenderingContext&&t instanceof WebGL2RenderingContext,o=void 0!==i.precision?i.precision:"highp",s=n(o);s!==o&&(o=s),i=!0===i.logarithmicDepthBuffer,s=t.getParameter(t.MAX_TEXTURE_IMAGE_UNITS);var h=t.getParameter(t.MAX_VERTEX_TEXTURE_IMAGE_UNITS),c=t.getParameter(t.MAX_TEXTURE_SIZE),l=t.getParameter(t.MAX_CUBE_MAP_TEXTURE_SIZE),u=t.getParameter(t.MAX_VERTEX_ATTRIBS),p=t.getParameter(t.MAX_VERTEX_UNIFORM_VECTORS),d=t.getParameter(t.MAX_VARYING_VECTORS),f=t.getParameter(t.MAX_FRAGMENT_UNIFORM_VECTORS),m=0<h,g=a||!!e.get("OES_texture_float");return{isWebGL2:a,getMaxAnisotropy:function(){if(void 0!==r)return r;var i=e.get("EXT_texture_filter_anisotropic");return r=null!==i?t.getParameter(i.MAX_TEXTURE_MAX_ANISOTROPY_EXT):0},getMaxPrecision:n,precision:o,logarithmicDepthBuffer:i,maxTextures:s,maxVertexTextures:h,maxTextureSize:c,maxCubemapSize:l,maxAttributes:u,maxVertexUniforms:p,maxVaryings:d,maxFragmentUniforms:f,vertexTextures:m,floatFragmentTextures:g,floatVertexTextures:m&&g}}function K(){function t(){l.value!==n&&(l.value=n,l.needsUpdate=0<r),i.numPlanes=r,i.numIntersection=0}function e(t,e,n,r){var a=null!==t?t.length:0,o=null;if(0!==a){if(o=l.value,!0!==r||null===o)for(r=n+4*a,e=e.matrixWorldInverse,c.getNormalMatrix(e),(null===o||o.length<r)&&(o=new Float32Array(r)),r=0;r!==a;++r,n+=4)h.copy(t[r]).applyMatrix4(e,c),h.normal.toArray(o,n),o[n+3]=h.constant;l.value=o,l.needsUpdate=!0}return i.numPlanes=a,o}var i=this,n=null,r=0,a=!1,s=!1,h=new f,c=new o,l={value:null,needsUpdate:!1};this.uniform=l,this.numIntersection=this.numPlanes=0,this.init=function(t,i,o){var s=0!==t.length||i||0!==r||a;return a=i,n=e(t,o,0),r=t.length,s},this.beginShadows=function(){s=!0,e(null)},this.endShadows=function(){s=!1,t()},this.setState=function(i,o,h,c,u,p){if(!a||null===i||0===i.length||s&&!h)s?e(null):t();else{h=s?0:r;var d=4*h,f=u.clippingState||null;for(l.value=f,f=e(i,c,d,p),i=0;i!==d;++i)f[i]=n[i];u.clippingState=f,this.numIntersection=o?this.numPlanes:0,this.numPlanes+=h}}}function $(t){var e={};return{get:function(i){if(void 0!==e[i])return e[i];switch(i){case"WEBGL_depth_texture":var n=t.getExtension("WEBGL_depth_texture")||t.getExtension("MOZ_WEBGL_depth_texture")||t.getExtension("WEBKIT_WEBGL_depth_texture");break;case"EXT_texture_filter_anisotropic":n=t.getExtension("EXT_texture_filter_anisotropic")||t.getExtension("MOZ_EXT_texture_filter_anisotropic")||t.getExtension("WEBKIT_EXT_texture_filter_anisotropic");break;case"WEBGL_compressed_texture_s3tc":n=t.getExtension("WEBGL_compressed_texture_s3tc")||t.getExtension("MOZ_WEBGL_compressed_texture_s3tc")||t.getExtension("WEBKIT_WEBGL_compressed_texture_s3tc");break;case"WEBGL_compressed_texture_pvrtc":n=t.getExtension("WEBGL_compressed_texture_pvrtc")||t.getExtension("WEBKIT_WEBGL_compressed_texture_pvrtc");break;default:n=t.getExtension(i)}return e[i]=n}}}function tt(t,e,i){function n(t){var o=t.target;t=r[o.id],null!==t.index&&e.remove(t.index);for(var s in t.attributes)e.remove(t.attributes[s]);o.removeEventListener("dispose",n),delete r[o.id],(s=a[t.id])&&(e.remove(s),delete a[t.id]),i.memory.geometries--}var r={},a={};return{get:function(t,e){var a=r[e.id];return a?a:(e.addEventListener("dispose",n),e.isBufferGeometry?a=e:e.isGeometry&&(void 0===e._bufferGeometry&&(e._bufferGeometry=(new z).setFromObject(t)),a=e._bufferGeometry),r[e.id]=a,i.memory.geometries++,a)},update:function(i){var n=i.index,r=i.attributes;null!==n&&e.update(n,t.ELEMENT_ARRAY_BUFFER);for(var a in r)e.update(r[a],t.ARRAY_BUFFER);i=i.morphAttributes;for(a in i){n=i[a],r=0;for(var o=n.length;r<o;r++)e.update(n[r],t.ARRAY_BUFFER)}},getWireframeAttribute:function(i){var n=a[i.id];if(n)return n;n=[];var r=i.index,o=i.attributes;if(null!==r){r=r.array,o=0;for(var s=r.length;o<s;o+=3){var h=r[o+0],c=r[o+1],l=r[o+2];n.push(h,c,c,l,l,h)}}else for(r=o.position.array,o=0,s=r.length/3-1;o<s;o+=3)h=o+0,c=o+1,l=o+2,n.push(h,c,c,l,l,h);return n=new(65535<B(n)?N:O)(n,1),e.update(n,t.ELEMENT_ARRAY_BUFFER),a[i.id]=n}}}function et(t,e,i,n){var r,a,o;this.setMode=function(t){r=t},this.setIndex=function(t){a=t.type,o=t.bytesPerElement},this.render=function(e,n){t.drawElements(r,n,a,e*o),i.update(n,r)},this.renderInstances=function(s,h,c){if(n.isWebGL2)var l=t;else if(l=e.get("ANGLE_instanced_arrays"),null===l)return;l[n.isWebGL2?"drawElementsInstanced":"drawElementsInstancedANGLE"](r,c,a,h*o,s.maxInstancedCount),i.update(c,r,s.maxInstancedCount)}}function it(t){var e={frame:0,calls:0,triangles:0,points:0,lines:0};return{memory:{geometries:0,textures:0},render:e,programs:null,autoReset:!0,reset:function(){e.frame++,e.calls=0,e.triangles=0,e.points=0,e.lines=0},update:function(i,n,r){switch(r=r||1,e.calls++,n){case t.TRIANGLES:e.triangles+=i/3*r;break;case t.TRIANGLE_STRIP:case t.TRIANGLE_FAN:e.triangles+=r*(i-2);break;case t.LINES:e.lines+=i/2*r;break;case t.LINE_STRIP:e.lines+=r*(i-1);break;case t.LINE_LOOP:e.lines+=r*i;break;case t.POINTS:e.points+=r*i}}}}function nt(t,e){return Math.abs(e[1])-Math.abs(t[1])}function rt(t){var e={},i=new Float32Array(8);return{update:function(n,r,a,o){var s=n.morphTargetInfluences,h=s.length;if(n=e[r.id],void 0===n){n=[];for(var c=0;c<h;c++)n[c]=[c,0];e[r.id]=n}var l=a.morphTargets&&r.morphAttributes.position;for(a=a.morphNormals&&r.morphAttributes.normal,c=0;c<h;c++){var u=n[c];0!==u[1]&&(l&&r.removeAttribute("morphTarget"+c),a&&r.removeAttribute("morphNormal"+c))}for(c=0;c<h;c++)u=n[c],u[0]=c,u[1]=s[c];for(n.sort(nt),c=0;8>c;c++)(u=n[c])&&(s=u[0],h=u[1])?(l&&r.addAttribute("morphTarget"+c,l[s]),a&&r.addAttribute("morphNormal"+c,a[s]),i[c]=h):i[c]=0;o.getUniforms().setValue(t,"morphTargetInfluences",i)}}}function at(t,e){var i={};return{update:function(n){var r=e.render.frame,a=n.geometry,o=t.get(n,a);return i[o.id]!==r&&(a.isGeometry&&o.updateFromObject(n),t.update(o),i[o.id]=r),o},dispose:function(){i={}}}}function ot(t,e,i,n,r,a,o,h,c,l){t=void 0!==t?t:[],s.call(this,t,void 0!==e?e:301,i,n,r,a,o,h,c,l),this.flipY=!1}function st(t,e,i){var n=t[0];if(0>=n||0<n)return t;var r=e*i,a=da[r];if(void 0===a&&(a=new Float32Array(r),da[r]=a),0!==e)for(n.toArray(a,0),n=1,r=0;n!==e;++n)r+=i,t[n].toArray(a,r);return a}function ht(t,e){if(t.length!==e.length)return!1;for(var i=0,n=t.length;i<n;i++)if(t[i]!==e[i])return!1;return!0}function ct(t,e){for(var i=0,n=e.length;i<n;i++)t[i]=e[i]}function lt(t,e){var i=fa[e];void 0===i&&(i=new Int32Array(e),fa[e]=i);for(var n=0;n!==e;++n)i[n]=t.allocTextureUnit();return i}function ut(t,e){var i=this.cache;i[0]!==e&&(t.uniform1f(this.addr,e),i[0]=e)}function pt(t,e){var i=this.cache;i[0]!==e&&(t.uniform1i(this.addr,e),i[0]=e)}function dt(t,e){var i=this.cache;void 0!==e.x?i[0]===e.x&&i[1]===e.y||(t.uniform2f(this.addr,e.x,e.y),i[0]=e.x,i[1]=e.y):ht(i,e)||(t.uniform2fv(this.addr,e),ct(i,e))}function ft(t,e){var i=this.cache;void 0!==e.x?i[0]===e.x&&i[1]===e.y&&i[2]===e.z||(t.uniform3f(this.addr,e.x,e.y,e.z),i[0]=e.x,i[1]=e.y,i[2]=e.z):void 0!==e.r?i[0]===e.r&&i[1]===e.g&&i[2]===e.b||(t.uniform3f(this.addr,e.r,e.g,e.b),i[0]=e.r,i[1]=e.g,i[2]=e.b):ht(i,e)||(t.uniform3fv(this.addr,e),ct(i,e))}function mt(t,e){var i=this.cache;void 0!==e.x?i[0]===e.x&&i[1]===e.y&&i[2]===e.z&&i[3]===e.w||(t.uniform4f(this.addr,e.x,e.y,e.z,e.w),i[0]=e.x,i[1]=e.y,i[2]=e.z,i[3]=e.w):ht(i,e)||(t.uniform4fv(this.addr,e),ct(i,e))}function gt(t,e){var i=this.cache,n=e.elements;void 0===n?ht(i,e)||(t.uniformMatrix2fv(this.addr,!1,e),ct(i,e)):ht(i,n)||(va.set(n),t.uniformMatrix2fv(this.addr,!1,va),ct(i,n))}function vt(t,e){var i=this.cache,n=e.elements;void 0===n?ht(i,e)||(t.uniformMatrix3fv(this.addr,!1,e),ct(i,e)):ht(i,n)||(ga.set(n),t.uniformMatrix3fv(this.addr,!1,ga),ct(i,n))}function yt(t,e){var i=this.cache,n=e.elements;void 0===n?ht(i,e)||(t.uniformMatrix4fv(this.addr,!1,e),ct(i,e)):ht(i,n)||(ma.set(n),t.uniformMatrix4fv(this.addr,!1,ma),ct(i,n))}function xt(t,e,i){var n=this.cache,r=i.allocTextureUnit();n[0]!==r&&(t.uniform1i(this.addr,r),n[0]=r),i.setTexture2D(e||ua,r)}function _t(t,e,i){var n=this.cache,r=i.allocTextureUnit();n[0]!==r&&(t.uniform1i(this.addr,r),n[0]=r),i.setTextureCube(e||pa,r)}function bt(t,e){var i=this.cache;ht(i,e)||(t.uniform2iv(this.addr,e),ct(i,e))}function Mt(t,e){var i=this.cache;ht(i,e)||(t.uniform3iv(this.addr,e),ct(i,e))}function wt(t,e){var i=this.cache;ht(i,e)||(t.uniform4iv(this.addr,e),ct(i,e))}function St(t){switch(t){case 5126:return ut;case 35664:return dt;case 35665:return ft;case 35666:return mt;case 35674:return gt;case 35675:return vt;case 35676:return yt;case 35678:case 36198:return xt;case 35680:return _t;case 5124:case 35670:return pt;case 35667:case 35671:return bt;case 35668:case 35672:return Mt;case 35669:case 35673:return wt}}function At(t,e){var i=this.cache;ht(i,e)||(t.uniform1fv(this.addr,e),ct(i,e))}function Tt(t,e){var i=this.cache;ht(i,e)||(t.uniform1iv(this.addr,e),ct(i,e))}function Lt(t,e){var i=this.cache;e=st(e,this.size,2),ht(i,e)||(t.uniform2fv(this.addr,e),this.updateCache(e))}function Et(t,e){var i=this.cache;e=st(e,this.size,3),ht(i,e)||(t.uniform3fv(this.addr,e),this.updateCache(e))}function Ct(t,e){var i=this.cache;e=st(e,this.size,4),ht(i,e)||(t.uniform4fv(this.addr,e),this.updateCache(e))}function Pt(t,e){var i=this.cache;e=st(e,this.size,4),ht(i,e)||(t.uniformMatrix2fv(this.addr,!1,e),this.updateCache(e))}function Ot(t,e){var i=this.cache;e=st(e,this.size,9),ht(i,e)||(t.uniformMatrix3fv(this.addr,!1,e),this.updateCache(e))}function It(t,e){var i=this.cache;e=st(e,this.size,16),ht(i,e)||(t.uniformMatrix4fv(this.addr,!1,e),this.updateCache(e))}function Nt(t,e,i){var n=this.cache,r=e.length,a=lt(i,r);for(!1===ht(n,a)&&(t.uniform1iv(this.addr,a),ct(n,a)),t=0;t!==r;++t)i.setTexture2D(e[t]||ua,a[t])}function Rt(t,e,i){var n=this.cache,r=e.length,a=lt(i,r);for(!1===ht(n,a)&&(t.uniform1iv(this.addr,a),ct(n,a)),t=0;t!==r;++t)i.setTextureCube(e[t]||pa,a[t])}function Dt(t){switch(t){case 5126:return At;case 35664:return Lt;case 35665:return Et;case 35666:return Ct;case 35674:return Pt;case 35675:return Ot;case 35676:return It;case 35678:return Nt;case 35680:return Rt;case 5124:case 35670:return Tt;case 35667:case 35671:return bt;case 35668:case 35672:return Mt;case 35669:case 35673:return wt}}function Ut(t,e,i){this.id=t,this.addr=i,this.cache=[],this.setValue=St(e.type)}function Bt(t,e,i){this.id=t,this.addr=i,this.cache=[],this.size=e.size,this.setValue=Dt(e.type)}function zt(t){this.id=t,this.seq=[],this.map={}}function Ft(t,e,i){this.seq=[],this.map={},this.renderer=i,i=t.getProgramParameter(e,t.ACTIVE_UNIFORMS);for(var n=0;n<i;++n){var r=t.getActiveUniform(e,n),a=t.getUniformLocation(e,r.name),o=this,s=r.name,h=s.length;for(ya.lastIndex=0;;){var c=ya.exec(s),l=ya.lastIndex,u=c[1],p=c[3];if("]"===c[2]&&(u|=0),void 0===p||"["===p&&l+2===h){s=o,r=void 0===p?new Ut(u,r,a):new Bt(u,r,a),s.seq.push(r),s.map[r.id]=r;break}p=o.map[u],void 0===p&&(p=new zt(u),u=o,o=p,u.seq.push(o),u.map[o.id]=o),o=p}}}function Gt(t,e,i){var n=t.createShader(e);return t.shaderSource(n,i),t.compileShader(n),!1===t.getShaderParameter(n,t.COMPILE_STATUS)&&void 0,""!==t.getShaderInfoLog(n)&&void 0,n}function Vt(t){switch(t){case 3e3:return["Linear","( value )"];case 3001:return["sRGB","( value )"];case 3002:return["RGBE","( value )"];case 3004:return["RGBM","( value, 7.0 )"];case 3005:return["RGBM","( value, 16.0 )"];case 3006:return["RGBD","( value, 256.0 )"];case 3007:return["Gamma","( value, float( GAMMA_FACTOR ) )"];default:throw Error("unsupported encoding: "+t)}}function kt(t,e){return e=Vt(e),"vec4 "+t+"( vec4 value ) { return "+e[0]+"ToLinear"+e[1]+"; }"}function jt(t,e){return e=Vt(e),"vec4 "+t+"( vec4 value ) { return LinearTo"+e[0]+e[1]+"; }"}function Wt(t,e){switch(e){case 1:e="Linear";break;case 2:e="Reinhard";break;case 3:e="Uncharted2";break;case 4:e="OptimizedCineon";break;default:throw Error("unsupported toneMapping: "+e)}return"vec3 "+t+"( vec3 color ) { return "+e+"ToneMapping( color ); }"}function Ht(t,e,i){return t=t||{},[t.derivatives||e.envMapCubeUV||e.bumpMap||e.normalMap&&!e.objectSpaceNormalMap||e.flatShading?"#extension GL_OES_standard_derivatives : enable":"",(t.fragDepth||e.logarithmicDepthBuffer)&&i.get("EXT_frag_depth")?"#extension GL_EXT_frag_depth : enable":"",t.drawBuffers&&i.get("WEBGL_draw_buffers")?"#extension GL_EXT_draw_buffers : require":"",(t.shaderTextureLOD||e.envMap)&&i.get("EXT_shader_texture_lod")?"#extension GL_EXT_shader_texture_lod : enable":""].filter(qt).join("\n")}function Xt(t){var e,i=[];for(e in t){var n=t[e];!1!==n&&i.push("#define "+e+" "+n)}return i.join("\n")}function qt(t){return""!==t}function Yt(t,e){return t.replace(/NUM_DIR_LIGHTS/g,e.numDirLights).replace(/NUM_SPOT_LIGHTS/g,e.numSpotLights).replace(/NUM_RECT_AREA_LIGHTS/g,e.numRectAreaLights).replace(/NUM_POINT_LIGHTS/g,e.numPointLights).replace(/NUM_HEMI_LIGHTS/g,e.numHemiLights)}function Zt(t,e){return t.replace(/NUM_CLIPPING_PLANES/g,e.numClippingPlanes).replace(/UNION_CLIPPING_PLANES/g,e.numClippingPlanes-e.numClipIntersection)}function Jt(t){return t.replace(/^[ \t]*#include +<([\w\d.\/]+)>/gm,function(t,e){if(t=ia[e],void 0===t)throw Error("Can not resolve #include <"+e+">");return Jt(t)})}function Qt(t){return t.replace(/#pragma unroll_loop[\s]+?for \( int i = (\d+); i < (\d+); i \+\+ \) \{([\s\S]+?)(?=\})\}/g,function(t,e,i,n){for(t="",e=parseInt(e);e<parseInt(i);e++)t+=n.replace(/\[ i \]/g,"[ "+e+" ]");return t})}function Kt(t,e,i,n,r,a,o){var s=t.context,h=n.defines,c=r.vertexShader,l=r.fragmentShader,u="SHADOWMAP_TYPE_BASIC";1===a.shadowMapType?u="SHADOWMAP_TYPE_PCF":2===a.shadowMapType&&(u="SHADOWMAP_TYPE_PCF_SOFT");var p="ENVMAP_TYPE_CUBE",d="ENVMAP_MODE_REFLECTION",f="ENVMAP_BLENDING_MULTIPLY";if(a.envMap){switch(n.envMap.mapping){case 301:case 302:p="ENVMAP_TYPE_CUBE";break;case 306:case 307:p="ENVMAP_TYPE_CUBE_UV";break;case 303:case 304:p="ENVMAP_TYPE_EQUIREC";break;case 305:p="ENVMAP_TYPE_SPHERE"}switch(n.envMap.mapping){case 302:case 304:d="ENVMAP_MODE_REFRACTION"}switch(n.combine){case 0:f="ENVMAP_BLENDING_MULTIPLY";break;case 1:f="ENVMAP_BLENDING_MIX";break;case 2:f="ENVMAP_BLENDING_ADD"}}var m=0<t.gammaFactor?t.gammaFactor:1,g=o.isWebGL2?"":Ht(n.extensions,a,e),v=Xt(h),y=s.createProgram();n.isRawShaderMaterial?(h=[v].filter(qt).join("\n"),0<h.length&&(h+="\n"),e=[g,v].filter(qt).join("\n"),0<e.length&&(e+="\n")):(h=["precision "+a.precision+" float;","precision "+a.precision+" int;","#define SHADER_NAME "+r.name,v,a.supportsVertexTextures?"#define VERTEX_TEXTURES":"","#define GAMMA_FACTOR "+m,"#define MAX_BONES "+a.maxBones,a.useFog&&a.fog?"#define USE_FOG":"",a.useFog&&a.fogExp?"#define FOG_EXP2":"",a.map?"#define USE_MAP":"",a.envMap?"#define USE_ENVMAP":"",a.envMap?"#define "+d:"",a.lightMap?"#define USE_LIGHTMAP":"",a.aoMap?"#define USE_AOMAP":"",a.emissiveMap?"#define USE_EMISSIVEMAP":"",a.bumpMap?"#define USE_BUMPMAP":"",a.normalMap?"#define USE_NORMALMAP":"",a.normalMap&&a.objectSpaceNormalMap?"#define OBJECTSPACE_NORMALMAP":"",a.displacementMap&&a.supportsVertexTextures?"#define USE_DISPLACEMENTMAP":"",a.specularMap?"#define USE_SPECULARMAP":"",a.roughnessMap?"#define USE_ROUGHNESSMAP":"",a.metalnessMap?"#define USE_METALNESSMAP":"",a.alphaMap?"#define USE_ALPHAMAP":"",a.vertexColors?"#define USE_COLOR":"",a.flatShading?"#define FLAT_SHADED":"",a.skinning?"#define USE_SKINNING":"",a.useVertexTexture?"#define BONE_TEXTURE":"",a.morphTargets?"#define USE_MORPHTARGETS":"",a.morphNormals&&!1===a.flatShading?"#define USE_MORPHNORMALS":"",a.doubleSided?"#define DOUBLE_SIDED":"",a.flipSided?"#define FLIP_SIDED":"",a.shadowMapEnabled?"#define USE_SHADOWMAP":"",a.shadowMapEnabled?"#define "+u:"",a.sizeAttenuation?"#define USE_SIZEATTENUATION":"",a.logarithmicDepthBuffer?"#define USE_LOGDEPTHBUF":"",a.logarithmicDepthBuffer&&(o.isWebGL2||e.get("EXT_frag_depth"))?"#define USE_LOGDEPTHBUF_EXT":"","uniform mat4 modelMatrix;","uniform mat4 modelViewMatrix;","uniform mat4 projectionMatrix;","uniform mat4 viewMatrix;","uniform mat3 normalMatrix;","uniform vec3 cameraPosition;","attribute vec3 position;","attribute vec3 normal;","attribute vec2 uv;","#ifdef USE_COLOR","\tattribute vec3 color;","#endif","#ifdef USE_MORPHTARGETS","\tattribute vec3 morphTarget0;","\tattribute vec3 morphTarget1;","\tattribute vec3 morphTarget2;","\tattribute vec3 morphTarget3;","\t#ifdef USE_MORPHNORMALS","\t\tattribute vec3 morphNormal0;","\t\tattribute vec3 morphNormal1;","\t\tattribute vec3 morphNormal2;","\t\tattribute vec3 morphNormal3;","\t#else","\t\tattribute vec3 morphTarget4;","\t\tattribute vec3 morphTarget5;","\t\tattribute vec3 morphTarget6;","\t\tattribute vec3 morphTarget7;","\t#endif","#endif","#ifdef USE_SKINNING","\tattribute vec4 skinIndex;","\tattribute vec4 skinWeight;","#endif","\n"].filter(qt).join("\n"),e=[g,"precision "+a.precision+" float;","precision "+a.precision+" int;","#define SHADER_NAME "+r.name,v,a.alphaTest?"#define ALPHATEST "+a.alphaTest+(a.alphaTest%1?"":".0"):"","#define GAMMA_FACTOR "+m,a.useFog&&a.fog?"#define USE_FOG":"",a.useFog&&a.fogExp?"#define FOG_EXP2":"",a.map?"#define USE_MAP":"",a.envMap?"#define USE_ENVMAP":"",a.envMap?"#define "+p:"",a.envMap?"#define "+d:"",a.envMap?"#define "+f:"",a.lightMap?"#define USE_LIGHTMAP":"",a.aoMap?"#define USE_AOMAP":"",a.emissiveMap?"#define USE_EMISSIVEMAP":"",a.bumpMap?"#define USE_BUMPMAP":"",a.normalMap?"#define USE_NORMALMAP":"",a.normalMap&&a.objectSpaceNormalMap?"#define OBJECTSPACE_NORMALMAP":"",a.specularMap?"#define USE_SPECULARMAP":"",a.roughnessMap?"#define USE_ROUGHNESSMAP":"",a.metalnessMap?"#define USE_METALNESSMAP":"",a.alphaMap?"#define USE_ALPHAMAP":"",a.vertexColors?"#define USE_COLOR":"",a.gradientMap?"#define USE_GRADIENTMAP":"",a.flatShading?"#define FLAT_SHADED":"",a.doubleSided?"#define DOUBLE_SIDED":"",a.flipSided?"#define FLIP_SIDED":"",a.shadowMapEnabled?"#define USE_SHADOWMAP":"",a.shadowMapEnabled?"#define "+u:"",a.premultipliedAlpha?"#define PREMULTIPLIED_ALPHA":"",a.physicallyCorrectLights?"#define PHYSICALLY_CORRECT_LIGHTS":"",a.logarithmicDepthBuffer?"#define USE_LOGDEPTHBUF":"",a.logarithmicDepthBuffer&&(o.isWebGL2||e.get("EXT_frag_depth"))?"#define USE_LOGDEPTHBUF_EXT":"",a.envMap&&(o.isWebGL2||e.get("EXT_shader_texture_lod"))?"#define TEXTURE_LOD_EXT":"","uniform mat4 viewMatrix;","uniform vec3 cameraPosition;",0!==a.toneMapping?"#define TONE_MAPPING":"",0!==a.toneMapping?ia.tonemapping_pars_fragment:"",0!==a.toneMapping?Wt("toneMapping",a.toneMapping):"",a.dithering?"#define DITHERING":"",a.outputEncoding||a.mapEncoding||a.envMapEncoding||a.emissiveMapEncoding?ia.encodings_pars_fragment:"",a.mapEncoding?kt("mapTexelToLinear",a.mapEncoding):"",a.envMapEncoding?kt("envMapTexelToLinear",a.envMapEncoding):"",a.emissiveMapEncoding?kt("emissiveMapTexelToLinear",a.emissiveMapEncoding):"",a.outputEncoding?jt("linearToOutputTexel",a.outputEncoding):"",a.depthPacking?"#define DEPTH_PACKING "+n.depthPacking:"","\n"].filter(qt).join("\n")),
c=Jt(c),c=Yt(c,a),c=Zt(c,a),l=Jt(l),l=Yt(l,a),l=Zt(l,a),c=Qt(c),l=Qt(l),o.isWebGL2&&!n.isRawShaderMaterial&&(o=!1,u=/^\s*#version\s+300\s+es\s*\n/,n.isShaderMaterial&&null!==c.match(u)&&null!==l.match(u)&&(o=!0,c=c.replace(u,""),l=l.replace(u,"")),h="#version 300 es\n\n#define attribute in\n#define varying out\n#define texture2D texture\n"+h,e=["#version 300 es\n\n#define varying in",o?"":"out highp vec4 pc_fragColor;",o?"":"#define gl_FragColor pc_fragColor","#define gl_FragDepthEXT gl_FragDepth\n#define texture2D texture\n#define textureCube texture\n#define texture2DProj textureProj\n#define texture2DLodEXT textureLod\n#define texture2DProjLodEXT textureProjLod\n#define textureCubeLodEXT textureLod\n#define texture2DGradEXT textureGrad\n#define texture2DProjGradEXT textureProjGrad\n#define textureCubeGradEXT textureGrad"].join("\n")+"\n"+e),l=e+l,c=Gt(s,s.VERTEX_SHADER,h+c),l=Gt(s,s.FRAGMENT_SHADER,l),s.attachShader(y,c),s.attachShader(y,l),void 0!==n.index0AttributeName?s.bindAttribLocation(y,0,n.index0AttributeName):!0===a.morphTargets&&s.bindAttribLocation(y,0,"position"),s.linkProgram(y),a=s.getProgramInfoLog(y).trim(),o=s.getShaderInfoLog(c).trim(),u=s.getShaderInfoLog(l).trim(),d=p=!0,!1===s.getProgramParameter(y,s.LINK_STATUS)?void(p=!1):""!==a||""!==o&&""!==u||(d=!1),d&&(this.diagnostics={runnable:p,material:n,programLog:a,vertexShader:{log:o,prefix:h},fragmentShader:{log:u,prefix:e}}),s.deleteShader(c),s.deleteShader(l);var x;this.getUniforms=function(){return void 0===x&&(x=new Ft(s,y,t)),x};var _;return this.getAttributes=function(){if(void 0===_){for(var t={},e=s.getProgramParameter(y,s.ACTIVE_ATTRIBUTES),i=0;i<e;i++){var n=s.getActiveAttrib(y,i).name;t[n]=s.getAttribLocation(y,n)}_=t}return _},this.destroy=function(){s.deleteProgram(y),this.program=void 0},Object.defineProperties(this,{uniforms:{get:function(){return this.getUniforms()}},attributes:{get:function(){return this.getAttributes()}}}),this.name=r.name,this.id=xa++,this.code=i,this.usedTimes=1,this.program=y,this.vertexShader=c,this.fragmentShader=l,this}function $t(t,e,i){function n(t,e){if(t)t.isTexture?i=t.encoding:t.isWebGLRenderTarget&&(i=t.texture.encoding);else var i=3e3;return 3e3===i&&e&&(i=3007),i}var r=[],a={MeshDepthMaterial:"depth",MeshDistanceMaterial:"distanceRGBA",MeshNormalMaterial:"normal",MeshBasicMaterial:"basic",MeshLambertMaterial:"lambert",MeshPhongMaterial:"phong",MeshToonMaterial:"phong",MeshStandardMaterial:"physical",MeshPhysicalMaterial:"physical",LineBasicMaterial:"basic",LineDashedMaterial:"dashed",PointsMaterial:"points",ShadowMaterial:"shadow",SpriteMaterial:"sprite"},o="precision supportsVertexTextures map mapEncoding envMap envMapMode envMapEncoding lightMap aoMap emissiveMap emissiveMapEncoding bumpMap normalMap objectSpaceNormalMap displacementMap specularMap roughnessMap metalnessMap gradientMap alphaMap combine vertexColors fog useFog fogExp flatShading sizeAttenuation logarithmicDepthBuffer skinning maxBones useVertexTexture morphTargets morphNormals maxMorphTargets maxMorphNormals premultipliedAlpha numDirLights numPointLights numSpotLights numHemiLights numRectAreaLights shadowMapEnabled shadowMapType toneMapping physicallyCorrectLights alphaTest doubleSided flipSided numClippingPlanes numClipIntersection depthPacking dithering".split(" ");this.getParameters=function(e,r,o,s,h,c,l){var u=a[e.type];if(l.isSkinnedMesh){var p=l.skeleton.bones;if(i.floatVertexTextures)p=1024;else{var d=Math.min(Math.floor((i.maxVertexUniforms-20)/4),p.length);p=d<p.length?0:d}}else p=0;d=i.precision,null!==e.precision&&(d=i.getMaxPrecision(e.precision),d!==e.precision&&void 0);var f=t.getRenderTarget();return{shaderID:u,precision:d,supportsVertexTextures:i.vertexTextures,outputEncoding:n(f?f.texture:null,t.gammaOutput),map:!!e.map,mapEncoding:n(e.map,t.gammaInput),envMap:!!e.envMap,envMapMode:e.envMap&&e.envMap.mapping,envMapEncoding:n(e.envMap,t.gammaInput),envMapCubeUV:!!e.envMap&&(306===e.envMap.mapping||307===e.envMap.mapping),lightMap:!!e.lightMap,aoMap:!!e.aoMap,emissiveMap:!!e.emissiveMap,emissiveMapEncoding:n(e.emissiveMap,t.gammaInput),bumpMap:!!e.bumpMap,normalMap:!!e.normalMap,objectSpaceNormalMap:1===e.normalMapType,displacementMap:!!e.displacementMap,roughnessMap:!!e.roughnessMap,metalnessMap:!!e.metalnessMap,specularMap:!!e.specularMap,alphaMap:!!e.alphaMap,gradientMap:!!e.gradientMap,combine:e.combine,vertexColors:e.vertexColors,fog:!!s,useFog:e.fog,fogExp:s&&s.isFogExp2,flatShading:e.flatShading,sizeAttenuation:e.sizeAttenuation,logarithmicDepthBuffer:i.logarithmicDepthBuffer,skinning:e.skinning&&0<p,maxBones:p,useVertexTexture:i.floatVertexTextures,morphTargets:e.morphTargets,morphNormals:e.morphNormals,maxMorphTargets:t.maxMorphTargets,maxMorphNormals:t.maxMorphNormals,numDirLights:r.directional.length,numPointLights:r.point.length,numSpotLights:r.spot.length,numRectAreaLights:r.rectArea.length,numHemiLights:r.hemi.length,numClippingPlanes:h,numClipIntersection:c,dithering:e.dithering,shadowMapEnabled:t.shadowMap.enabled&&l.receiveShadow&&0<o.length,shadowMapType:t.shadowMap.type,toneMapping:t.toneMapping,physicallyCorrectLights:t.physicallyCorrectLights,premultipliedAlpha:e.premultipliedAlpha,alphaTest:e.alphaTest,doubleSided:2===e.side,flipSided:1===e.side,depthPacking:void 0!==e.depthPacking&&e.depthPacking}},this.getProgramCode=function(e,i){var n=[];if(i.shaderID?n.push(i.shaderID):(n.push(e.fragmentShader),n.push(e.vertexShader)),void 0!==e.defines)for(var r in e.defines)n.push(r),n.push(e.defines[r]);for(r=0;r<o.length;r++)n.push(i[o[r]]);return n.push(e.onBeforeCompile.toString()),n.push(t.gammaOutput),n.join()},this.acquireProgram=function(n,a,o,s){for(var h,c=0,l=r.length;c<l;c++){var u=r[c];if(u.code===s){h=u,++h.usedTimes;break}}return void 0===h&&(h=new Kt(t,e,s,n,a,o,i),r.push(h)),h},this.releaseProgram=function(t){if(0===--t.usedTimes){var e=r.indexOf(t);r[e]=r[r.length-1],r.pop(),t.destroy()}},this.programs=r}function te(){var t=new WeakMap;return{get:function(e){var i=t.get(e);return void 0===i&&(i={},t.set(e,i)),i},remove:function(e){t["delete"](e)},update:function(e,i,n){t.get(e)[i]=n},dispose:function(){t=new WeakMap}}}function ee(t,e){return t.renderOrder!==e.renderOrder?t.renderOrder-e.renderOrder:t.program&&e.program&&t.program!==e.program?t.program.id-e.program.id:t.material.id!==e.material.id?t.material.id-e.material.id:t.z!==e.z?t.z-e.z:t.id-e.id}function ie(t,e){return t.renderOrder!==e.renderOrder?t.renderOrder-e.renderOrder:t.z!==e.z?e.z-t.z:t.id-e.id}function ne(){var t=[],e=0,i=[],n=[];return{opaque:i,transparent:n,init:function(){e=0,i.length=0,n.length=0},push:function(r,a,o,s,h){var c=t[e];void 0===c?(c={id:r.id,object:r,geometry:a,material:o,program:o.program,renderOrder:r.renderOrder,z:s,group:h},t[e]=c):(c.id=r.id,c.object=r,c.geometry=a,c.material=o,c.program=o.program,c.renderOrder=r.renderOrder,c.z=s,c.group=h),(!0===o.transparent?n:i).push(c),e++},sort:function(){1<i.length&&i.sort(ee),1<n.length&&n.sort(ie)}}}function re(){var t={};return{get:function(e,i){return e=e.id+","+i.id,i=t[e],void 0===i&&(i=new ne,t[e]=i),i},dispose:function(){t={}}}}function ae(){var t={};return{get:function(e){if(void 0!==t[e.id])return t[e.id];switch(e.type){case"DirectionalLight":var n={direction:new a,color:new g,shadow:!1,shadowBias:0,shadowRadius:1,shadowMapSize:new i};break;case"SpotLight":n={position:new a,direction:new a,color:new g,distance:0,coneCos:0,penumbraCos:0,decay:0,shadow:!1,shadowBias:0,shadowRadius:1,shadowMapSize:new i};break;case"PointLight":n={position:new a,color:new g,distance:0,decay:0,shadow:!1,shadowBias:0,shadowRadius:1,shadowMapSize:new i,shadowCameraNear:1,shadowCameraFar:1e3};break;case"HemisphereLight":n={direction:new a,skyColor:new g,groundColor:new g};break;case"RectAreaLight":n={color:new g,position:new a,halfWidth:new a,halfHeight:new a}}return t[e.id]=n}}}function oe(){var t=new ae,e={id:_a++,hash:{stateID:-1,directionalLength:-1,pointLength:-1,spotLength:-1,rectAreaLength:-1,hemiLength:-1,shadowsLength:-1},ambient:[0,0,0],directional:[],directionalShadowMap:[],directionalShadowMatrix:[],spot:[],spotShadowMap:[],spotShadowMatrix:[],rectArea:[],point:[],pointShadowMap:[],pointShadowMatrix:[],hemi:[]},i=new a,r=new n,o=new n;return{setup:function(n,a,s){var h=0,c=0,l=0,u=0,p=0,d=0,f=0,m=0;s=s.matrixWorldInverse;for(var g=0,v=n.length;g<v;g++){var y=n[g],x=y.color,_=y.intensity,b=y.distance,M=y.shadow&&y.shadow.map?y.shadow.map.texture:null;if(y.isAmbientLight)h+=x.r*_,c+=x.g*_,l+=x.b*_;else if(y.isDirectionalLight){var w=t.get(y);w.color.copy(y.color).multiplyScalar(y.intensity),w.direction.setFromMatrixPosition(y.matrixWorld),i.setFromMatrixPosition(y.target.matrixWorld),w.direction.sub(i),w.direction.transformDirection(s),(w.shadow=y.castShadow)&&(x=y.shadow,w.shadowBias=x.bias,w.shadowRadius=x.radius,w.shadowMapSize=x.mapSize),e.directionalShadowMap[u]=M,e.directionalShadowMatrix[u]=y.shadow.matrix,e.directional[u]=w,u++}else y.isSpotLight?(w=t.get(y),w.position.setFromMatrixPosition(y.matrixWorld),w.position.applyMatrix4(s),w.color.copy(x).multiplyScalar(_),w.distance=b,w.direction.setFromMatrixPosition(y.matrixWorld),i.setFromMatrixPosition(y.target.matrixWorld),w.direction.sub(i),w.direction.transformDirection(s),w.coneCos=Math.cos(y.angle),w.penumbraCos=Math.cos(y.angle*(1-y.penumbra)),w.decay=0===y.distance?0:y.decay,(w.shadow=y.castShadow)&&(x=y.shadow,w.shadowBias=x.bias,w.shadowRadius=x.radius,w.shadowMapSize=x.mapSize),e.spotShadowMap[d]=M,e.spotShadowMatrix[d]=y.shadow.matrix,e.spot[d]=w,d++):y.isRectAreaLight?(w=t.get(y),w.color.copy(x).multiplyScalar(_),w.position.setFromMatrixPosition(y.matrixWorld),w.position.applyMatrix4(s),o.identity(),r.copy(y.matrixWorld),r.premultiply(s),o.extractRotation(r),w.halfWidth.set(.5*y.width,0,0),w.halfHeight.set(0,.5*y.height,0),w.halfWidth.applyMatrix4(o),w.halfHeight.applyMatrix4(o),e.rectArea[f]=w,f++):y.isPointLight?(w=t.get(y),w.position.setFromMatrixPosition(y.matrixWorld),w.position.applyMatrix4(s),w.color.copy(y.color).multiplyScalar(y.intensity),w.distance=y.distance,w.decay=0===y.distance?0:y.decay,(w.shadow=y.castShadow)&&(x=y.shadow,w.shadowBias=x.bias,w.shadowRadius=x.radius,w.shadowMapSize=x.mapSize,w.shadowCameraNear=x.camera.near,w.shadowCameraFar=x.camera.far),e.pointShadowMap[p]=M,e.pointShadowMatrix[p]=y.shadow.matrix,e.point[p]=w,p++):y.isHemisphereLight&&(w=t.get(y),w.direction.setFromMatrixPosition(y.matrixWorld),w.direction.transformDirection(s),w.direction.normalize(),w.skyColor.copy(y.color).multiplyScalar(_),w.groundColor.copy(y.groundColor).multiplyScalar(_),e.hemi[m]=w,m++)}e.ambient[0]=h,e.ambient[1]=c,e.ambient[2]=l,e.directional.length=u,e.spot.length=d,e.rectArea.length=f,e.point.length=p,e.hemi.length=m,e.hash.stateID=e.id,e.hash.directionalLength=u,e.hash.pointLength=p,e.hash.spotLength=d,e.hash.rectAreaLength=f,e.hash.hemiLength=m,e.hash.shadowsLength=a.length},state:e}}function se(){var t=new oe,e=[],i=[];return{init:function(){e.length=0,i.length=0},state:{lightsArray:e,shadowsArray:i,lights:t},setupLights:function(n){t.setup(e,i,n)},pushLight:function(t){e.push(t)},pushShadow:function(t){i.push(t)}}}function he(){var t={};return{get:function(e,i){if(void 0===t[e.id]){var n=new se;t[e.id]={},t[e.id][i.id]=n}else void 0===t[e.id][i.id]?(n=new se,t[e.id][i.id]=n):n=t[e.id][i.id];return n},dispose:function(){t={}}}}function ce(t){j.call(this),this.type="MeshDepthMaterial",this.depthPacking=3200,this.morphTargets=this.skinning=!1,this.displacementMap=this.alphaMap=this.map=null,this.displacementScale=1,this.displacementBias=0,this.wireframe=!1,this.wireframeLinewidth=1,this.lights=this.fog=!1,this.setValues(t)}function le(t){j.call(this),this.type="MeshDistanceMaterial",this.referencePosition=new a,this.nearDistance=1,this.farDistance=1e3,this.morphTargets=this.skinning=!1,this.displacementMap=this.alphaMap=this.map=null,this.displacementScale=1,this.displacementBias=0,this.lights=this.fog=!1,this.setValues(t)}function ue(t,e,r){function o(e,i,n,r,a,o){var s=e.geometry,h=v,c=e.customDepthMaterial;return n&&(h=y,c=e.customDistanceMaterial),c?h=c:(c=!1,i.morphTargets&&(s&&s.isBufferGeometry?c=s.morphAttributes&&s.morphAttributes.position&&0<s.morphAttributes.position.length:s&&s.isGeometry&&(c=s.morphTargets&&0<s.morphTargets.length)),e.isSkinnedMesh&&!1===i.skinning&&void 0,e=e.isSkinnedMesh&&i.skinning,s=0,c&&(s|=1),e&&(s|=2),h=h[s]),t.localClippingEnabled&&!0===i.clipShadows&&0!==i.clippingPlanes.length&&(s=h.uuid,c=i.uuid,e=x[s],void 0===e&&(e={},x[s]=e),s=e[c],void 0===s&&(s=h.clone(),e[c]=s),h=s),h.visible=i.visible,h.wireframe=i.wireframe,h.side=null!=i.shadowSide?i.shadowSide:_[i.side],h.clipShadows=i.clipShadows,h.clippingPlanes=i.clippingPlanes,h.clipIntersection=i.clipIntersection,h.wireframeLinewidth=i.wireframeLinewidth,h.linewidth=i.linewidth,n&&h.isMeshDistanceMaterial&&(h.referencePosition.copy(r),h.nearDistance=a,h.farDistance=o),h}function s(i,n,r,a){if(!1!==i.visible){if(i.layers.test(n.layers)&&(i.isMesh||i.isLine||i.isPoints)&&i.castShadow&&(!i.frustumCulled||l.intersectsObject(i))){i.modelViewMatrix.multiplyMatrices(r.matrixWorldInverse,i.matrixWorld);var h=e.update(i),c=i.material;if(Array.isArray(c))for(var u=h.groups,p=0,d=u.length;p<d;p++){var f=u[p],m=c[f.materialIndex];m&&m.visible&&(m=o(i,m,a,g,r.near,r.far),t.renderBufferDirect(r,null,h,m,i,f))}else c.visible&&(m=o(i,c,a,g,r.near,r.far),t.renderBufferDirect(r,null,h,m,i,null))}for(i=i.children,h=0,c=i.length;h<c;h++)s(i[h],n,r,a)}}var l=new m,u=new n,p=new i,d=new i(r,r),f=new a,g=new a,v=Array(4),y=Array(4),x={},_={0:1,1:0,2:2},b=[new a(1,0,0),new a((-1),0,0),new a(0,0,1),new a(0,0,(-1)),new a(0,1,0),new a(0,(-1),0)],M=[new a(0,1,0),new a(0,1,0),new a(0,1,0),new a(0,1,0),new a(0,0,1),new a(0,0,(-1))],w=[new h,new h,new h,new h,new h,new h];for(r=0;4!==r;++r){var S=0!==(1&r),A=0!==(2&r),T=new ce({depthPacking:3201,morphTargets:S,skinning:A});v[r]=T,S=new le({morphTargets:S,skinning:A}),y[r]=S}var L=this;this.enabled=!1,this.autoUpdate=!0,this.needsUpdate=!1,this.type=1,this.render=function(e,i,n){if(!1!==L.enabled&&(!1!==L.autoUpdate||!1!==L.needsUpdate)&&0!==e.length){var r=t.state;r.disable(t.context.BLEND),r.buffers.color.setClear(1,1,1,1),r.buffers.depth.setTest(!0),r.setScissorTest(!1);for(var a,o=0,h=e.length;o<h;o++){var m=e[o];a=m.shadow;var v=m&&m.isPointLight;if(void 0===a);else{var y=a.camera;if(p.copy(a.mapSize),p.min(d),v){var x=p.x,_=p.y;w[0].set(2*x,_,x,_),w[1].set(0,_,x,_),w[2].set(3*x,_,x,_),w[3].set(x,_,x,_),w[4].set(3*x,0,x,_),w[5].set(x,0,x,_),p.x*=4,p.y*=2}for(null===a.map&&(a.map=new c(p.x,p.y,{minFilter:1003,magFilter:1003,format:1023}),a.map.texture.name=m.name+".shadowMap",y.updateProjectionMatrix()),a.isSpotLightShadow&&a.update(m),x=a.map,_=a.matrix,g.setFromMatrixPosition(m.matrixWorld),y.position.copy(g),v?(a=6,_.makeTranslation(-g.x,-g.y,-g.z)):(a=1,f.setFromMatrixPosition(m.target.matrixWorld),y.lookAt(f),y.updateMatrixWorld(),_.set(.5,0,0,.5,0,.5,0,.5,0,0,.5,.5,0,0,0,1),_.multiply(y.projectionMatrix),_.multiply(y.matrixWorldInverse)),t.setRenderTarget(x),t.clear(),m=0;m<a;m++)v&&(f.copy(y.position),f.add(b[m]),y.up.copy(M[m]),y.lookAt(f),y.updateMatrixWorld(),r.viewport(w[m])),u.multiplyMatrices(y.projectionMatrix,y.matrixWorldInverse),l.setFromMatrix(u),s(i,n,y,v)}}L.needsUpdate=!1}}}function pe(t,e,i,n){function r(e,i,n){var r=new Uint8Array(4),a=t.createTexture();for(t.bindTexture(e,a),t.texParameteri(e,t.TEXTURE_MIN_FILTER,t.NEAREST),t.texParameteri(e,t.TEXTURE_MAG_FILTER,t.NEAREST),e=0;e<n;e++)t.texImage2D(i+e,0,t.RGBA,1,1,0,t.RGBA,t.UNSIGNED_BYTE,r);return a}function a(i,r){y[i]=1,0===x[i]&&(t.enableVertexAttribArray(i),x[i]=1),_[i]!==r&&((n.isWebGL2?t:e.get("ANGLE_instanced_arrays"))[n.isWebGL2?"vertexAttribDivisor":"vertexAttribDivisorANGLE"](i,r),_[i]=r)}function o(e){!0!==b[e]&&(t.enable(e),b[e]=!0)}function s(e){!1!==b[e]&&(t.disable(e),b[e]=!1)}function c(e,n,r,a,h,c,l,u){if(0===e)S&&(s(t.BLEND),S=!1);else if(S||(o(t.BLEND),S=!0),5!==e){if(e!==A||u!==I){if(100===T&&100===C||(t.blendEquation(t.FUNC_ADD),C=T=100),u)switch(e){case 1:t.blendFuncSeparate(t.ONE,t.ONE_MINUS_SRC_ALPHA,t.ONE,t.ONE_MINUS_SRC_ALPHA);break;case 2:t.blendFunc(t.ONE,t.ONE);break;case 3:t.blendFuncSeparate(t.ZERO,t.ZERO,t.ONE_MINUS_SRC_COLOR,t.ONE_MINUS_SRC_ALPHA);break;case 4:t.blendFuncSeparate(t.ZERO,t.SRC_COLOR,t.ZERO,t.SRC_ALPHA)}else switch(e){case 1:t.blendFuncSeparate(t.SRC_ALPHA,t.ONE_MINUS_SRC_ALPHA,t.ONE,t.ONE_MINUS_SRC_ALPHA);break;case 2:t.blendFunc(t.SRC_ALPHA,t.ONE);break;case 3:t.blendFunc(t.ZERO,t.ONE_MINUS_SRC_COLOR);break;case 4:t.blendFunc(t.ZERO,t.SRC_COLOR)}O=P=E=L=null,A=e,I=u}}else h=h||n,c=c||r,l=l||a,n===T&&h===C||(t.blendEquationSeparate(i.convert(n),i.convert(h)),T=n,C=h),r===L&&a===E&&c===P&&l===O||(t.blendFuncSeparate(i.convert(r),i.convert(a),i.convert(c),i.convert(l)),L=r,E=a,P=c,O=l),A=e,I=null}function l(e){N!==e&&(e?t.frontFace(t.CW):t.frontFace(t.CCW),N=e)}function u(e){0!==e?(o(t.CULL_FACE),e!==R&&(1===e?t.cullFace(t.BACK):2===e?t.cullFace(t.FRONT):t.cullFace(t.FRONT_AND_BACK))):s(t.CULL_FACE),R=e}function p(e,i,n){e?(o(t.POLYGON_OFFSET_FILL),(U!==i||B!==n)&&(t.polygonOffset(i,n),U=i,B=n)):s(t.POLYGON_OFFSET_FILL)}function d(e){void 0===e&&(e=t.TEXTURE0+z-1),G!==e&&(t.activeTexture(e),G=e)}var f=new function(){var e=!1,i=new h,n=null,r=new h(0,0,0,0);return{setMask:function(i){n===i||e||(t.colorMask(i,i,i,i),n=i)},setLocked:function(t){e=t},setClear:function(e,n,a,o,s){!0===s&&(e*=o,n*=o,a*=o),i.set(e,n,a,o),!1===r.equals(i)&&(t.clearColor(e,n,a,o),r.copy(i))},reset:function(){e=!1,n=null,r.set(-1,0,0,0)}}},m=new function(){var e=!1,i=null,n=null,r=null;return{setTest:function(e){e?o(t.DEPTH_TEST):s(t.DEPTH_TEST)},setMask:function(n){i===n||e||(t.depthMask(n),i=n)},setFunc:function(e){if(n!==e){if(e)switch(e){case 0:t.depthFunc(t.NEVER);break;case 1:t.depthFunc(t.ALWAYS);break;case 2:t.depthFunc(t.LESS);break;case 3:t.depthFunc(t.LEQUAL);break;case 4:t.depthFunc(t.EQUAL);break;case 5:t.depthFunc(t.GEQUAL);break;case 6:t.depthFunc(t.GREATER);break;case 7:t.depthFunc(t.NOTEQUAL);break;default:t.depthFunc(t.LEQUAL)}else t.depthFunc(t.LEQUAL);n=e}},setLocked:function(t){e=t},setClear:function(e){r!==e&&(t.clearDepth(e),r=e)},reset:function(){e=!1,r=n=i=null}}},g=new function(){var e=!1,i=null,n=null,r=null,a=null,h=null,c=null,l=null,u=null;return{setTest:function(e){e?o(t.STENCIL_TEST):s(t.STENCIL_TEST)},setMask:function(n){i===n||e||(t.stencilMask(n),i=n)},setFunc:function(e,i,o){n===e&&r===i&&a===o||(t.stencilFunc(e,i,o),n=e,r=i,a=o)},setOp:function(e,i,n){h===e&&c===i&&l===n||(t.stencilOp(e,i,n),h=e,c=i,l=n)},setLocked:function(t){e=t},setClear:function(e){u!==e&&(t.clearStencil(e),u=e)},reset:function(){e=!1,u=l=c=h=a=r=n=i=null}}},v=t.getParameter(t.MAX_VERTEX_ATTRIBS),y=new Uint8Array(v),x=new Uint8Array(v),_=new Uint8Array(v),b={},M=null,w=null,S=null,A=null,T=null,L=null,E=null,C=null,P=null,O=null,I=!1,N=null,R=null,D=null,U=null,B=null,z=t.getParameter(t.MAX_COMBINED_TEXTURE_IMAGE_UNITS),F=!1;v=0,v=t.getParameter(t.VERSION),-1!==v.indexOf("WebGL")?(v=parseFloat(/^WebGL ([0-9])/.exec(v)[1]),F=1<=v):-1!==v.indexOf("OpenGL ES")&&(v=parseFloat(/^OpenGL ES ([0-9])/.exec(v)[1]),F=2<=v);var G=null,V={},k=new h,j=new h,W={};return W[t.TEXTURE_2D]=r(t.TEXTURE_2D,t.TEXTURE_2D,1),W[t.TEXTURE_CUBE_MAP]=r(t.TEXTURE_CUBE_MAP,t.TEXTURE_CUBE_MAP_POSITIVE_X,6),f.setClear(0,0,0,1),m.setClear(1),g.setClear(0),o(t.DEPTH_TEST),m.setFunc(3),l(!1),u(1),o(t.CULL_FACE),c(0),{buffers:{color:f,depth:m,stencil:g},initAttributes:function(){for(var t=0,e=y.length;t<e;t++)y[t]=0},enableAttribute:function(t){a(t,0)},enableAttributeAndDivisor:a,disableUnusedAttributes:function(){for(var e=0,i=x.length;e!==i;++e)x[e]!==y[e]&&(t.disableVertexAttribArray(e),x[e]=0)},enable:o,disable:s,getCompressedTextureFormats:function(){if(null===M&&(M=[],e.get("WEBGL_compressed_texture_pvrtc")||e.get("WEBGL_compressed_texture_s3tc")||e.get("WEBGL_compressed_texture_etc1")||e.get("WEBGL_compressed_texture_astc")))for(var i=t.getParameter(t.COMPRESSED_TEXTURE_FORMATS),n=0;n<i.length;n++)M.push(i[n]);return M},useProgram:function(e){return w!==e&&(t.useProgram(e),w=e,!0)},setBlending:c,setMaterial:function(e,i){2===e.side?s(t.CULL_FACE):o(t.CULL_FACE);var n=1===e.side;i&&(n=!n),l(n),1===e.blending&&!1===e.transparent?c(0):c(e.blending,e.blendEquation,e.blendSrc,e.blendDst,e.blendEquationAlpha,e.blendSrcAlpha,e.blendDstAlpha,e.premultipliedAlpha),m.setFunc(e.depthFunc),m.setTest(e.depthTest),m.setMask(e.depthWrite),f.setMask(e.colorWrite),p(e.polygonOffset,e.polygonOffsetFactor,e.polygonOffsetUnits)},setFlipSided:l,setCullFace:u,setLineWidth:function(e){e!==D&&(F&&t.lineWidth(e),D=e)},setPolygonOffset:p,setScissorTest:function(e){e?o(t.SCISSOR_TEST):s(t.SCISSOR_TEST)},activeTexture:d,bindTexture:function(e,i){null===G&&d();var n=V[G];void 0===n&&(n={type:void 0,texture:void 0},V[G]=n),n.type===e&&n.texture===i||(t.bindTexture(e,i||W[e]),n.type=e,n.texture=i)},compressedTexImage2D:function(){try{t.compressedTexImage2D.apply(t,arguments)}catch(e){}},texImage2D:function(){try{t.texImage2D.apply(t,arguments)}catch(e){}},scissor:function(e){!1===k.equals(e)&&(t.scissor(e.x,e.y,e.z,e.w),k.copy(e))},viewport:function(e){!1===j.equals(e)&&(t.viewport(e.x,e.y,e.z,e.w),j.copy(e))},reset:function(){for(var e=0;e<x.length;e++)1===x[e]&&(t.disableVertexAttribArray(e),x[e]=0);b={},G=M=null,V={},R=N=A=w=null,f.reset(),m.reset(),g.reset()}}}function de(t,e,i,n,r,a,o){function s(t,e){if(t.width>e||t.height>e){if("data"in t)return;e/=Math.max(t.width,t.height);var i=document.createElementNS("http://www.w3.org/1999/xhtml","canvas");return i.width=Math.floor(t.width*e),i.height=Math.floor(t.height*e),i.getContext("2d").drawImage(t,0,0,t.width,t.height,0,0,i.width,i.height),i}return t}function h(t){return $r.isPowerOfTwo(t.width)&&$r.isPowerOfTwo(t.height)}function c(t,e){return t.generateMipmaps&&e&&1003!==t.minFilter&&1006!==t.minFilter}function l(e,i,r,a){t.generateMipmap(e),n.get(i).__maxMipLevel=Math.log(Math.max(r,a))*Math.LOG2E}function u(e,i){if(!r.isWebGL2)return e;if(e===t.RGB){if(i===t.FLOAT)return t.RGB32F;if(i===t.HALF_FLOAT)return t.RGB16F;if(i===t.UNSIGNED_BYTE)return t.RGB8}if(e===t.RGBA){if(i===t.FLOAT)return t.RGBA32F;if(i===t.HALF_FLOAT)return t.RGBA16F;if(i===t.UNSIGNED_BYTE)return t.RGBA8}return e}function p(e){return 1003===e||1004===e||1005===e?t.NEAREST:t.LINEAR}function d(e){e=e.target,e.removeEventListener("dispose",d);t:{var i=n.get(e);if(e.image&&i.__image__webglTextureCube)t.deleteTexture(i.__image__webglTextureCube);else{if(void 0===i.__webglInit)break t;t.deleteTexture(i.__webglTexture)}n.remove(e)}e.isVideoTexture&&delete _[e.id],o.memory.textures--}function f(e){e=e.target,e.removeEventListener("dispose",f);var i=n.get(e),r=n.get(e.texture);if(e){if(void 0!==r.__webglTexture&&t.deleteTexture(r.__webglTexture),e.depthTexture&&e.depthTexture.dispose(),e.isWebGLRenderTargetCube)for(r=0;6>r;r++)t.deleteFramebuffer(i.__webglFramebuffer[r]),i.__webglDepthbuffer&&t.deleteRenderbuffer(i.__webglDepthbuffer[r]);else t.deleteFramebuffer(i.__webglFramebuffer),i.__webglDepthbuffer&&t.deleteRenderbuffer(i.__webglDepthbuffer);n.remove(e.texture),n.remove(e)}o.memory.textures--}function m(e,p){var f=n.get(e);if(e.isVideoTexture){var m=e.id,v=o.render.frame;_[m]!==v&&(_[m]=v,e.update())}if(0<e.version&&f.__version!==e.version)if(m=e.image,void 0===m);else if(!1!==m.complete){void 0===f.__webglInit&&(f.__webglInit=!0,e.addEventListener("dispose",d),f.__webglTexture=t.createTexture(),o.memory.textures++),i.activeTexture(t.TEXTURE0+p),i.bindTexture(t.TEXTURE_2D,f.__webglTexture),t.pixelStorei(t.UNPACK_FLIP_Y_WEBGL,e.flipY),t.pixelStorei(t.UNPACK_PREMULTIPLY_ALPHA_WEBGL,e.premultiplyAlpha),t.pixelStorei(t.UNPACK_ALIGNMENT,e.unpackAlignment),p=s(e.image,r.maxTextureSize),(r.isWebGL2?0:1001!==e.wrapS||1001!==e.wrapT||1003!==e.minFilter&&1006!==e.minFilter)&&!1===h(p)&&(p instanceof HTMLImageElement||p instanceof HTMLCanvasElement||p instanceof ImageBitmap)&&(void 0===x&&(x=document.createElementNS("http://www.w3.org/1999/xhtml","canvas")),x.width=$r.floorPowerOfTwo(p.width),x.height=$r.floorPowerOfTwo(p.height),x.getContext("2d").drawImage(p,0,0,x.width,x.height),p=x),m=h(p),v=a.convert(e.format);var y=a.convert(e.type),b=u(v,y);g(t.TEXTURE_2D,e,m);var M=e.mipmaps;if(e.isDepthTexture){if(b=t.DEPTH_COMPONENT,1015===e.type){if(!r.isWebGL2)throw Error("Float Depth Texture only supported in WebGL2.0");b=t.DEPTH_COMPONENT32F}else r.isWebGL2&&(b=t.DEPTH_COMPONENT16);1026===e.format&&b===t.DEPTH_COMPONENT&&1012!==e.type&&1014!==e.type&&(e.type=1012,y=a.convert(e.type)),1027===e.format&&(b=t.DEPTH_STENCIL,1020!==e.type&&(e.type=1020,y=a.convert(e.type))),i.texImage2D(t.TEXTURE_2D,0,b,p.width,p.height,0,v,y,null)}else if(e.isDataTexture)if(0<M.length&&m){for(var w=0,S=M.length;w<S;w++){var A=M[w];i.texImage2D(t.TEXTURE_2D,w,b,A.width,A.height,0,v,y,A.data)}e.generateMipmaps=!1,f.__maxMipLevel=M.length-1}else i.texImage2D(t.TEXTURE_2D,0,b,p.width,p.height,0,v,y,p.data),f.__maxMipLevel=0;else if(e.isCompressedTexture){for(w=0,S=M.length;w<S;w++)A=M[w],1023!==e.format&&1022!==e.format?-1<i.getCompressedTextureFormats().indexOf(v)?i.compressedTexImage2D(t.TEXTURE_2D,w,b,A.width,A.height,0,A.data):void 0:i.texImage2D(t.TEXTURE_2D,w,b,A.width,A.height,0,v,y,A.data);f.__maxMipLevel=M.length-1}else if(0<M.length&&m){for(w=0,S=M.length;w<S;w++)A=M[w],i.texImage2D(t.TEXTURE_2D,w,b,v,y,A);e.generateMipmaps=!1,f.__maxMipLevel=M.length-1}else i.texImage2D(t.TEXTURE_2D,0,b,v,y,p),f.__maxMipLevel=0;return c(e,m)&&l(t.TEXTURE_2D,e,p.width,p.height),f.__version=e.version,void(e.onUpdate&&e.onUpdate(e))}i.activeTexture(t.TEXTURE0+p),i.bindTexture(t.TEXTURE_2D,f.__webglTexture)}function g(i,o,s){s?(t.texParameteri(i,t.TEXTURE_WRAP_S,a.convert(o.wrapS)),t.texParameteri(i,t.TEXTURE_WRAP_T,a.convert(o.wrapT)),t.texParameteri(i,t.TEXTURE_MAG_FILTER,a.convert(o.magFilter)),t.texParameteri(i,t.TEXTURE_MIN_FILTER,a.convert(o.minFilter))):(t.texParameteri(i,t.TEXTURE_WRAP_S,t.CLAMP_TO_EDGE),t.texParameteri(i,t.TEXTURE_WRAP_T,t.CLAMP_TO_EDGE),1001===o.wrapS&&1001===o.wrapT||void 0,t.texParameteri(i,t.TEXTURE_MAG_FILTER,p(o.magFilter)),t.texParameteri(i,t.TEXTURE_MIN_FILTER,p(o.minFilter)),1003!==o.minFilter&&1006!==o.minFilter&&void 0),!(s=e.get("EXT_texture_filter_anisotropic"))||1015===o.type&&null===e.get("OES_texture_float_linear")||1016===o.type&&null===(r.isWebGL2||e.get("OES_texture_half_float_linear"))||!(1<o.anisotropy||n.get(o).__currentAnisotropy)||(t.texParameterf(i,s.TEXTURE_MAX_ANISOTROPY_EXT,Math.min(o.anisotropy,r.getMaxAnisotropy())),n.get(o).__currentAnisotropy=o.anisotropy)}function v(e,r,o,s){var h=a.convert(r.texture.format),c=a.convert(r.texture.type),l=u(h,c);i.texImage2D(s,0,l,r.width,r.height,0,h,c,null),t.bindFramebuffer(t.FRAMEBUFFER,e),t.framebufferTexture2D(t.FRAMEBUFFER,o,s,n.get(r.texture).__webglTexture,0),t.bindFramebuffer(t.FRAMEBUFFER,null)}function y(e,i){t.bindRenderbuffer(t.RENDERBUFFER,e),i.depthBuffer&&!i.stencilBuffer?(t.renderbufferStorage(t.RENDERBUFFER,t.DEPTH_COMPONENT16,i.width,i.height),t.framebufferRenderbuffer(t.FRAMEBUFFER,t.DEPTH_ATTACHMENT,t.RENDERBUFFER,e)):i.depthBuffer&&i.stencilBuffer?(t.renderbufferStorage(t.RENDERBUFFER,t.DEPTH_STENCIL,i.width,i.height),t.framebufferRenderbuffer(t.FRAMEBUFFER,t.DEPTH_STENCIL_ATTACHMENT,t.RENDERBUFFER,e)):t.renderbufferStorage(t.RENDERBUFFER,t.RGBA4,i.width,i.height),t.bindRenderbuffer(t.RENDERBUFFER,null)}var x,_={};this.setTexture2D=m,this.setTextureCube=function(e,p){var f=n.get(e);if(6===e.image.length)if(0<e.version&&f.__version!==e.version){f.__image__webglTextureCube||(e.addEventListener("dispose",d),f.__image__webglTextureCube=t.createTexture(),o.memory.textures++),i.activeTexture(t.TEXTURE0+p),i.bindTexture(t.TEXTURE_CUBE_MAP,f.__image__webglTextureCube),t.pixelStorei(t.UNPACK_FLIP_Y_WEBGL,e.flipY),p=e&&e.isCompressedTexture;for(var m=e.image[0]&&e.image[0].isDataTexture,v=[],y=0;6>y;y++)v[y]=p||m?m?e.image[y].image:e.image[y]:s(e.image[y],r.maxCubemapSize);var x=v[0],_=h(x),b=a.convert(e.format),M=a.convert(e.type),w=u(b,M);for(g(t.TEXTURE_CUBE_MAP,e,_),y=0;6>y;y++)if(p)for(var S,A=v[y].mipmaps,T=0,L=A.length;T<L;T++)S=A[T],1023!==e.format&&1022!==e.format?-1<i.getCompressedTextureFormats().indexOf(b)?i.compressedTexImage2D(t.TEXTURE_CUBE_MAP_POSITIVE_X+y,T,w,S.width,S.height,0,S.data):void 0:i.texImage2D(t.TEXTURE_CUBE_MAP_POSITIVE_X+y,T,w,S.width,S.height,0,b,M,S.data);else m?i.texImage2D(t.TEXTURE_CUBE_MAP_POSITIVE_X+y,0,w,v[y].width,v[y].height,0,b,M,v[y].data):i.texImage2D(t.TEXTURE_CUBE_MAP_POSITIVE_X+y,0,w,b,M,v[y]);f.__maxMipLevel=p?A.length-1:0,c(e,_)&&l(t.TEXTURE_CUBE_MAP,e,x.width,x.height),f.__version=e.version,e.onUpdate&&e.onUpdate(e)}else i.activeTexture(t.TEXTURE0+p),i.bindTexture(t.TEXTURE_CUBE_MAP,f.__image__webglTextureCube)},this.setTextureCubeDynamic=function(e,r){i.activeTexture(t.TEXTURE0+r),i.bindTexture(t.TEXTURE_CUBE_MAP,n.get(e).__webglTexture)},this.setupRenderTarget=function(e){var r=n.get(e),a=n.get(e.texture);e.addEventListener("dispose",f),a.__webglTexture=t.createTexture(),o.memory.textures++;var s=!0===e.isWebGLRenderTargetCube,u=h(e);if(s){r.__webglFramebuffer=[];for(var p=0;6>p;p++)r.__webglFramebuffer[p]=t.createFramebuffer()}else r.__webglFramebuffer=t.createFramebuffer();if(s){for(i.bindTexture(t.TEXTURE_CUBE_MAP,a.__webglTexture),g(t.TEXTURE_CUBE_MAP,e.texture,u),p=0;6>p;p++)v(r.__webglFramebuffer[p],e,t.COLOR_ATTACHMENT0,t.TEXTURE_CUBE_MAP_POSITIVE_X+p);c(e.texture,u)&&l(t.TEXTURE_CUBE_MAP,e.texture,e.width,e.height),i.bindTexture(t.TEXTURE_CUBE_MAP,null)}else i.bindTexture(t.TEXTURE_2D,a.__webglTexture),g(t.TEXTURE_2D,e.texture,u),v(r.__webglFramebuffer,e,t.COLOR_ATTACHMENT0,t.TEXTURE_2D),c(e.texture,u)&&l(t.TEXTURE_2D,e.texture,e.width,e.height),i.bindTexture(t.TEXTURE_2D,null);if(e.depthBuffer){if(r=n.get(e),a=!0===e.isWebGLRenderTargetCube,e.depthTexture){if(a)throw Error("target.depthTexture not supported in Cube render targets");if(e&&e.isWebGLRenderTargetCube)throw Error("Depth Texture with cube render targets is not supported");if(t.bindFramebuffer(t.FRAMEBUFFER,r.__webglFramebuffer),!e.depthTexture||!e.depthTexture.isDepthTexture)throw Error("renderTarget.depthTexture must be an instance of THREE.DepthTexture");if(n.get(e.depthTexture).__webglTexture&&e.depthTexture.image.width===e.width&&e.depthTexture.image.height===e.height||(e.depthTexture.image.width=e.width,e.depthTexture.image.height=e.height,e.depthTexture.needsUpdate=!0),m(e.depthTexture,0),r=n.get(e.depthTexture).__webglTexture,1026===e.depthTexture.format)t.framebufferTexture2D(t.FRAMEBUFFER,t.DEPTH_ATTACHMENT,t.TEXTURE_2D,r,0);else{if(1027!==e.depthTexture.format)throw Error("Unknown depthTexture format");t.framebufferTexture2D(t.FRAMEBUFFER,t.DEPTH_STENCIL_ATTACHMENT,t.TEXTURE_2D,r,0)}}else if(a)for(r.__webglDepthbuffer=[],a=0;6>a;a++)t.bindFramebuffer(t.FRAMEBUFFER,r.__webglFramebuffer[a]),r.__webglDepthbuffer[a]=t.createRenderbuffer(),y(r.__webglDepthbuffer[a],e);else t.bindFramebuffer(t.FRAMEBUFFER,r.__webglFramebuffer),r.__webglDepthbuffer=t.createRenderbuffer(),y(r.__webglDepthbuffer,e);t.bindFramebuffer(t.FRAMEBUFFER,null)}},this.updateRenderTargetMipmap=function(e){var r=e.texture,a=h(e);if(c(r,a)){a=e.isWebGLRenderTargetCube?t.TEXTURE_CUBE_MAP:t.TEXTURE_2D;var o=n.get(r).__webglTexture;i.bindTexture(a,o),l(a,r,e.width,e.height),i.bindTexture(a,null)}}}function fe(t,e,i){return{convert:function(n){if(1e3===n)return t.REPEAT;if(1001===n)return t.CLAMP_TO_EDGE;if(1002===n)return t.MIRRORED_REPEAT;if(1003===n)return t.NEAREST;if(1004===n)return t.NEAREST_MIPMAP_NEAREST;if(1005===n)return t.NEAREST_MIPMAP_LINEAR;if(1006===n)return t.LINEAR;if(1007===n)return t.LINEAR_MIPMAP_NEAREST;if(1008===n)return t.LINEAR_MIPMAP_LINEAR;if(1009===n)return t.UNSIGNED_BYTE;if(1017===n)return t.UNSIGNED_SHORT_4_4_4_4;if(1018===n)return t.UNSIGNED_SHORT_5_5_5_1;if(1019===n)return t.UNSIGNED_SHORT_5_6_5;
if(1010===n)return t.BYTE;if(1011===n)return t.SHORT;if(1012===n)return t.UNSIGNED_SHORT;if(1013===n)return t.INT;if(1014===n)return t.UNSIGNED_INT;if(1015===n)return t.FLOAT;if(1016===n){if(i.isWebGL2)return t.HALF_FLOAT;var r=e.get("OES_texture_half_float");if(null!==r)return r.HALF_FLOAT_OES}if(1021===n)return t.ALPHA;if(1022===n)return t.RGB;if(1023===n)return t.RGBA;if(1024===n)return t.LUMINANCE;if(1025===n)return t.LUMINANCE_ALPHA;if(1026===n)return t.DEPTH_COMPONENT;if(1027===n)return t.DEPTH_STENCIL;if(100===n)return t.FUNC_ADD;if(101===n)return t.FUNC_SUBTRACT;if(102===n)return t.FUNC_REVERSE_SUBTRACT;if(200===n)return t.ZERO;if(201===n)return t.ONE;if(202===n)return t.SRC_COLOR;if(203===n)return t.ONE_MINUS_SRC_COLOR;if(204===n)return t.SRC_ALPHA;if(205===n)return t.ONE_MINUS_SRC_ALPHA;if(206===n)return t.DST_ALPHA;if(207===n)return t.ONE_MINUS_DST_ALPHA;if(208===n)return t.DST_COLOR;if(209===n)return t.ONE_MINUS_DST_COLOR;if(210===n)return t.SRC_ALPHA_SATURATE;if((33776===n||33777===n||33778===n||33779===n)&&(r=e.get("WEBGL_compressed_texture_s3tc"),null!==r)){if(33776===n)return r.COMPRESSED_RGB_S3TC_DXT1_EXT;if(33777===n)return r.COMPRESSED_RGBA_S3TC_DXT1_EXT;if(33778===n)return r.COMPRESSED_RGBA_S3TC_DXT3_EXT;if(33779===n)return r.COMPRESSED_RGBA_S3TC_DXT5_EXT}if((35840===n||35841===n||35842===n||35843===n)&&(r=e.get("WEBGL_compressed_texture_pvrtc"),null!==r)){if(35840===n)return r.COMPRESSED_RGB_PVRTC_4BPPV1_IMG;if(35841===n)return r.COMPRESSED_RGB_PVRTC_2BPPV1_IMG;if(35842===n)return r.COMPRESSED_RGBA_PVRTC_4BPPV1_IMG;if(35843===n)return r.COMPRESSED_RGBA_PVRTC_2BPPV1_IMG}if(36196===n&&(r=e.get("WEBGL_compressed_texture_etc1"),null!==r))return r.COMPRESSED_RGB_ETC1_WEBGL;if((37808===n||37809===n||37810===n||37811===n||37812===n||37813===n||37814===n||37815===n||37816===n||37817===n||37818===n||37819===n||37820===n||37821===n)&&(r=e.get("WEBGL_compressed_texture_astc"),null!==r))return n;if(103===n||104===n){if(i.isWebGL2){if(103===n)return t.MIN;if(104===n)return t.MAX}if(r=e.get("EXT_blend_minmax"),null!==r){if(103===n)return r.MIN_EXT;if(104===n)return r.MAX_EXT}}if(1020===n){if(i.isWebGL2)return t.UNSIGNED_INT_24_8;if(r=e.get("WEBGL_depth_texture"),null!==r)return r.UNSIGNED_INT_24_8_WEBGL}return 0}}}function me(){b.call(this),this.type="Group"}function ge(t,e,i,n){M.call(this),this.type="PerspectiveCamera",this.fov=void 0!==t?t:50,this.zoom=1,this.near=void 0!==i?i:.1,this.far=void 0!==n?n:2e3,this.focus=10,this.aspect=void 0!==e?e:1,this.view=null,this.filmGauge=35,this.filmOffset=0,this.updateProjectionMatrix()}function ve(t){ge.call(this),this.cameras=t||[]}function ye(t){function e(){return null!==s&&!0===s.isPresenting}function i(){if(e()){var i=s.getEyeParameters("left"),n=i.renderWidth;i=i.renderHeight,w=t.getPixelRatio(),M=t.getSize(),t.setDrawingBufferSize(2*n,i,1),A.start()}else o.enabled&&t.setDrawingBufferSize(M.width,M.height,w),A.stop()}var o=this,s=null,c=null,l=null,u=[],p=new n,d=new n,f="stage";"undefined"!=typeof window&&"VRFrameData"in window&&(c=new window.VRFrameData,window.addEventListener("vrdisplaypresentchange",i,!1));var m=new n,g=new r,y=new a,x=new ge;x.bounds=new h(0,0,.5,1),x.layers.enable(1);var _=new ge;_.bounds=new h(.5,0,.5,1),_.layers.enable(2);var b=new ve([x,_]);b.layers.enable(1),b.layers.enable(2);var M,w,S=[];this.enabled=!1,this.getController=function(t){var e=u[t];return void 0===e&&(e=new me,e.matrixAutoUpdate=!1,e.visible=!1,u[t]=e),e},this.getDevice=function(){return s},this.setDevice=function(t){void 0!==t&&(s=t),A.setContext(t)},this.setFrameOfReferenceType=function(t){f=t},this.setPoseTarget=function(t){void 0!==t&&(l=t)},this.getCamera=function(t){var e="stage"===f?1.6:0;if(null===s)return t.position.set(0,e,0),t;if(s.depthNear=t.near,s.depthFar=t.far,s.getFrameData(c),"stage"===f){var i=s.stageParameters;i?p.fromArray(i.sittingToStandingTransform):p.makeTranslation(0,e,0)}if(e=c.pose,i=null!==l?l:t,i.matrix.copy(p),i.matrix.decompose(i.position,i.quaternion,i.scale),null!==e.orientation&&(g.fromArray(e.orientation),i.quaternion.multiply(g)),null!==e.position&&(g.setFromRotationMatrix(p),y.fromArray(e.position),y.applyQuaternion(g),i.position.add(y)),i.updateMatrixWorld(),!1===s.isPresenting)return t;x.near=t.near,_.near=t.near,x.far=t.far,_.far=t.far,b.matrixWorld.copy(t.matrixWorld),b.matrixWorldInverse.copy(t.matrixWorldInverse),x.matrixWorldInverse.fromArray(c.leftViewMatrix),_.matrixWorldInverse.fromArray(c.rightViewMatrix),d.getInverse(p),"stage"===f&&(x.matrixWorldInverse.multiply(d),_.matrixWorldInverse.multiply(d)),t=i.parent,null!==t&&(m.getInverse(t.matrixWorld),x.matrixWorldInverse.multiply(m),_.matrixWorldInverse.multiply(m)),x.matrixWorld.getInverse(x.matrixWorldInverse),_.matrixWorld.getInverse(_.matrixWorldInverse),x.projectionMatrix.fromArray(c.leftProjectionMatrix),_.projectionMatrix.fromArray(c.rightProjectionMatrix),b.projectionMatrix.copy(x.projectionMatrix),t=s.getLayers(),t.length&&(t=t[0],null!==t.leftBounds&&4===t.leftBounds.length&&x.bounds.fromArray(t.leftBounds),null!==t.rightBounds&&4===t.rightBounds.length&&_.bounds.fromArray(t.rightBounds));t:for(t=0;t<u.length;t++){e=u[t];e:{i=t;for(var n=navigator.getGamepads&&navigator.getGamepads(),r=0,a=0,o=n.length;r<o;r++){var h=n[r];if(h&&("Daydream Controller"===h.id||"Gear VR Controller"===h.id||"Oculus Go Controller"===h.id||"OpenVR Gamepad"===h.id||h.id.startsWith("Oculus Touch")||h.id.startsWith("Spatial Controller"))){if(a===i){i=h;break e}a++}}i=void 0}if(void 0!==i&&void 0!==i.pose){if(null===i.pose)break t;n=i.pose,!1===n.hasPosition&&e.position.set(.2,-.6,-.05),null!==n.position&&e.position.fromArray(n.position),null!==n.orientation&&e.quaternion.fromArray(n.orientation),e.matrix.compose(e.position,e.quaternion,e.scale),e.matrix.premultiply(p),e.matrix.decompose(e.position,e.quaternion,e.scale),e.matrixWorldNeedsUpdate=!0,e.visible=!0,n="Daydream Controller"===i.id?0:1,S[t]!==i.buttons[n].pressed&&(S[t]=i.buttons[n].pressed,!0===S[t]?e.dispatchEvent({type:"selectstart"}):(e.dispatchEvent({type:"selectend"}),e.dispatchEvent({type:"select"})))}else e.visible=!1}return b},this.getStandingMatrix=function(){return p},this.isPresenting=e;var A=new v;this.setAnimationLoop=function(t){A.setAnimationLoop(t)},this.submitFrame=function(){e()&&s.submitFrame()},this.dispose=function(){"undefined"!=typeof window&&window.removeEventListener("vrdisplaypresentchange",i)}}function xe(t){function e(){return null!==s&&null!==c}function i(t){var e=p[d.indexOf(t.inputSource)];e&&e.dispatchEvent({type:t.type})}function n(){t.setFramebuffer(null),x.stop()}function r(t,e){null===e?t.matrixWorld.copy(t.matrix):t.matrixWorld.multiplyMatrices(e.matrixWorld,t.matrix),t.matrixWorldInverse.getInverse(t.matrixWorld)}var a=t.context,o=null,s=null,c=null,l="stage",u=null,p=[],d=[],f=new ge;f.layers.enable(1),f.viewport=new h;var m=new ge;m.layers.enable(2),m.viewport=new h;var g=new ve([f,m]);g.layers.enable(1),g.layers.enable(2),this.enabled=!1,this.getController=function(t){var e=p[t];return void 0===e&&(e=new me,e.matrixAutoUpdate=!1,e.visible=!1,p[t]=e),e},this.getDevice=function(){return o},this.setDevice=function(t){void 0!==t&&(o=t),t instanceof XRDevice&&a.setCompatibleXRDevice(t)},this.setFrameOfReferenceType=function(t){l=t},this.setSession=function(e){s=e,null!==s&&(s.addEventListener("select",i),s.addEventListener("selectstart",i),s.addEventListener("selectend",i),s.addEventListener("end",n),s.baseLayer=new XRWebGLLayer(s,a),s.requestFrameOfReference(l).then(function(e){c=e,t.setFramebuffer(s.baseLayer.framebuffer),x.setContext(s),x.start()}),d=s.getInputSources(),s.addEventListener("inputsourceschange",function(){d=s.getInputSources()}))},this.getCamera=function(t){if(e()){var i=t.parent,n=g.cameras;r(g,i);for(var a=0;a<n.length;a++)r(n[a],i);for(t.matrixWorld.copy(g.matrixWorld),t=t.children,a=0,i=t.length;a<i;a++)t[a].updateMatrixWorld(!0);return g}return t},this.isPresenting=e;var y=null,x=new v;x.setAnimationLoop(function(t,e){if(u=e.getDevicePose(c),null!==u)for(var i=s.baseLayer,n=e.views,r=0;r<n.length;r++){var a=n[r],o=i.getViewport(a),h=u.getViewMatrix(a),l=g.cameras[r];l.matrix.fromArray(h).getInverse(l.matrix),l.projectionMatrix.fromArray(a.projectionMatrix),l.viewport.set(o.x,o.y,o.width,o.height),0===r&&(g.matrix.copy(l.matrix),g.projectionMatrix.copy(l.projectionMatrix))}for(r=0;r<p.length;r++)i=p[r],(n=d[r])&&(n=e.getInputPose(n,c),null!==n)?("targetRay"in n?i.matrix.elements=n.targetRay.transformMatrix:"pointerMatrix"in n&&(i.matrix.elements=n.pointerMatrix),i.matrix.decompose(i.position,i.rotation,i.scale),i.visible=!0):i.visible=!1;y&&y(t)}),this.setAnimationLoop=function(t){y=t},this.dispose=function(){},this.getStandingMatrix=function(){return new THREE.Matrix4},this.submitFrame=function(){}}function _e(t){function e(){yt=new $(gt),xt=new Q(gt,yt,t),xt.isWebGL2||(yt.get("WEBGL_depth_texture"),yt.get("OES_texture_float"),yt.get("OES_texture_half_float"),yt.get("OES_texture_half_float_linear"),yt.get("OES_standard_derivatives"),yt.get("OES_element_index_uint"),yt.get("ANGLE_instanced_arrays")),yt.get("OES_texture_float_linear"),Rt=new fe(gt,yt,xt),_t=new pe(gt,yt,Rt,xt),_t.scissor(H.copy(ht).multiplyScalar(ot)),_t.viewport(W.copy(st).multiplyScalar(ot)),bt=new it(gt),Mt=new te,wt=new de(gt,yt,_t,Mt,xt,Rt,bt),St=new y(gt),At=new tt(gt,St,bt),Tt=new at(At,bt),Ot=new rt(gt),Lt=new $t(R,yt,xt),Et=new re,Ct=new he,Pt=new Z(R,_t,Tt,C),It=new J(gt,yt,bt,xt),Nt=new et(gt,yt,bt,xt),bt.programs=Lt.programs,R.context=gt,R.capabilities=xt,R.extensions=yt,R.properties=Mt,R.renderLists=Et,R.state=_t,R.info=bt}function i(t){t.preventDefault(),D=!0}function r(){D=!1,e()}function o(t){t=t.target,t.removeEventListener("dispose",o),s(t),Mt.remove(t)}function s(t){var e=Mt.get(t).program;t.program=void 0,void 0!==e&&Lt.releaseProgram(e)}function c(t,e){t.render(function(t){R.renderBufferImmediate(t,e)})}function l(t,e,i){if(!1!==t.visible){if(t.layers.test(e.layers))if(t.isLight)N.pushLight(t),t.castShadow&&N.pushShadow(t);else if(t.isSprite){if(!t.frustumCulled||lt.intersectsSprite(t)){i&&mt.setFromMatrixPosition(t.matrixWorld).applyMatrix4(ft);var n=Tt.update(t),r=t.material;I.push(t,n,r,mt.z,null)}}else if(t.isImmediateRenderObject)i&&mt.setFromMatrixPosition(t.matrixWorld).applyMatrix4(ft),I.push(t,null,t.material,mt.z,null);else if((t.isMesh||t.isLine||t.isPoints)&&(t.isSkinnedMesh&&t.skeleton.update(),!t.frustumCulled||lt.intersectsObject(t)))if(i&&mt.setFromMatrixPosition(t.matrixWorld).applyMatrix4(ft),n=Tt.update(t),r=t.material,Array.isArray(r))for(var a=n.groups,o=0,s=a.length;o<s;o++){var h=a[o],c=r[h.materialIndex];c&&c.visible&&I.push(t,n,c,mt.z,h)}else r.visible&&I.push(t,n,r,mt.z,null);for(t=t.children,o=0,s=t.length;o<s;o++)l(t[o],e,i)}}function p(t,e,i,n){for(var r=0,a=t.length;r<a;r++){var o=t[r],s=o.object,h=o.geometry,c=void 0===n?o.material:n;if(o=o.group,i.isArrayCamera){j=i;for(var l=i.cameras,u=0,p=l.length;u<p;u++){var f=l[u];if(s.layers.test(f.layers)){if("viewport"in f)_t.viewport(W.copy(f.viewport));else{var m=f.bounds;_t.viewport(W.set(m.x*Y,m.y*nt,m.z*Y,m.w*nt).multiplyScalar(ot))}N.setupLights(f),d(s,e,f,h,c,o)}}}else j=null,d(s,e,i,h,c,o)}}function d(t,e,i,n,r,a){if(t.onBeforeRender(R,e,i,n,r,a),N=Ct.get(e,j||i),t.modelViewMatrix.multiplyMatrices(i.matrixWorldInverse,t.matrixWorld),t.normalMatrix.getNormalMatrix(t.modelViewMatrix),t.isImmediateRenderObject){_t.setMaterial(r);var o=g(i,e.fog,r,t);G=M=null,V=!1,c(t,o)}else R.renderBufferDirect(i,e.fog,n,r,t,a);t.onAfterRender(R,e,i,n,r,a),N=Ct.get(e,j||i)}function f(t,e,i){var n=Mt.get(t),r=N.state.lights,a=n.lightsHash,h=r.state.hash;i=Lt.getParameters(t,r.state,N.state.shadowsArray,e,ut.numPlanes,ut.numIntersection,i);var c=Lt.getProgramCode(t,i),l=n.program,u=!0;if(void 0===l)t.addEventListener("dispose",o);else if(l.code!==c)s(t);else{if(a.stateID!==h.stateID||a.directionalLength!==h.directionalLength||a.pointLength!==h.pointLength||a.spotLength!==h.spotLength||a.rectAreaLength!==h.rectAreaLength||a.hemiLength!==h.hemiLength||a.shadowsLength!==h.shadowsLength)a.stateID=h.stateID,a.directionalLength=h.directionalLength,a.pointLength=h.pointLength,a.spotLength=h.spotLength,a.rectAreaLength=h.rectAreaLength,a.hemiLength=h.hemiLength,a.shadowsLength=h.shadowsLength;else if(void 0!==i.shaderID)return;u=!1}if(u&&(i.shaderID?(c=oa[i.shaderID],n.shader={name:t.type,uniforms:na.clone(c.uniforms),vertexShader:c.vertexShader,fragmentShader:c.fragmentShader}):n.shader={name:t.type,uniforms:t.uniforms,vertexShader:t.vertexShader,fragmentShader:t.fragmentShader},t.onBeforeCompile(n.shader,R),c=Lt.getProgramCode(t,i),l=Lt.acquireProgram(t,n.shader,i,c),n.program=l,t.program=l),i=l.getAttributes(),t.morphTargets)for(c=t.numSupportedMorphTargets=0;c<R.maxMorphTargets;c++)0<=i["morphTarget"+c]&&t.numSupportedMorphTargets++;if(t.morphNormals)for(c=t.numSupportedMorphNormals=0;c<R.maxMorphNormals;c++)0<=i["morphNormal"+c]&&t.numSupportedMorphNormals++;i=n.shader.uniforms,(t.isShaderMaterial||t.isRawShaderMaterial)&&!0!==t.clipping||(n.numClippingPlanes=ut.numPlanes,n.numIntersection=ut.numIntersection,i.clippingPlanes=ut.uniform),n.fog=e,void 0===a&&(n.lightsHash=a={}),a.stateID=h.stateID,a.directionalLength=h.directionalLength,a.pointLength=h.pointLength,a.spotLength=h.spotLength,a.rectAreaLength=h.rectAreaLength,a.hemiLength=h.hemiLength,a.shadowsLength=h.shadowsLength,t.lights&&(i.ambientLightColor.value=r.state.ambient,i.directionalLights.value=r.state.directional,i.spotLights.value=r.state.spot,i.rectAreaLights.value=r.state.rectArea,i.pointLights.value=r.state.point,i.hemisphereLights.value=r.state.hemi,i.directionalShadowMap.value=r.state.directionalShadowMap,i.directionalShadowMatrix.value=r.state.directionalShadowMatrix,i.spotShadowMap.value=r.state.spotShadowMap,i.spotShadowMatrix.value=r.state.spotShadowMatrix,i.pointShadowMap.value=r.state.pointShadowMap,i.pointShadowMatrix.value=r.state.pointShadowMatrix),t=n.program.getUniforms(),t=Ft.seqWithValue(t.seq,i),n.uniformsList=t}function g(t,e,i,n){q=0;var r=Mt.get(i),a=r.lightsHash,o=N.state.lights.state.hash;pt&&(dt||t!==k)&&ut.setState(i.clippingPlanes,i.clipIntersection,i.clipShadows,t,r,t===k&&i.id===F),!1===i.needsUpdate&&(void 0===r.program?i.needsUpdate=!0:i.fog&&r.fog!==e?i.needsUpdate=!0:!i.lights||a.stateID===o.stateID&&a.directionalLength===o.directionalLength&&a.pointLength===o.pointLength&&a.spotLength===o.spotLength&&a.rectAreaLength===o.rectAreaLength&&a.hemiLength===o.hemiLength&&a.shadowsLength===o.shadowsLength?void 0===r.numClippingPlanes||r.numClippingPlanes===ut.numPlanes&&r.numIntersection===ut.numIntersection||(i.needsUpdate=!0):i.needsUpdate=!0),i.needsUpdate&&(f(i,e,n),i.needsUpdate=!1);var s=!1,h=!1,c=!1;a=r.program,o=a.getUniforms();var l=r.shader.uniforms;if(_t.useProgram(a.program)&&(c=h=s=!0),i.id!==F&&(F=i.id,h=!0),(s||k!==t)&&(o.setValue(gt,"projectionMatrix",t.projectionMatrix),xt.logarithmicDepthBuffer&&o.setValue(gt,"logDepthBufFC",2/(Math.log(t.far+1)/Math.LN2)),k!==t&&(k=t,c=h=!0),(i.isShaderMaterial||i.isMeshPhongMaterial||i.isMeshStandardMaterial||i.envMap)&&(s=o.map.cameraPosition,void 0!==s&&s.setValue(gt,mt.setFromMatrixPosition(t.matrixWorld))),(i.isMeshPhongMaterial||i.isMeshLambertMaterial||i.isMeshBasicMaterial||i.isMeshStandardMaterial||i.isShaderMaterial||i.skinning)&&o.setValue(gt,"viewMatrix",t.matrixWorldInverse)),i.skinning&&(o.setOptional(gt,n,"bindMatrix"),o.setOptional(gt,n,"bindMatrixInverse"),t=n.skeleton))if(s=t.bones,xt.floatVertexTextures){if(void 0===t.boneTexture){s=Math.sqrt(4*s.length),s=$r.ceilPowerOfTwo(s),s=Math.max(s,4);var p=new Float32Array(s*s*4);p.set(t.boneMatrices);var d=new u(p,s,s,1023,1015);d.needsUpdate=!0,t.boneMatrices=p,t.boneTexture=d,t.boneTextureSize=s}o.setValue(gt,"boneTexture",t.boneTexture),o.setValue(gt,"boneTextureSize",t.boneTextureSize)}else o.setOptional(gt,t,"boneMatrices");return h&&(o.setValue(gt,"toneMappingExposure",R.toneMappingExposure),o.setValue(gt,"toneMappingWhitePoint",R.toneMappingWhitePoint),i.lights&&(h=c,l.ambientLightColor.needsUpdate=h,l.directionalLights.needsUpdate=h,l.pointLights.needsUpdate=h,l.spotLights.needsUpdate=h,l.rectAreaLights.needsUpdate=h,l.hemisphereLights.needsUpdate=h),e&&i.fog&&(l.fogColor.value=e.color,e.isFog?(l.fogNear.value=e.near,l.fogFar.value=e.far):e.isFogExp2&&(l.fogDensity.value=e.density)),i.isMeshBasicMaterial?x(l,i):i.isMeshLambertMaterial?(x(l,i),i.emissiveMap&&(l.emissiveMap.value=i.emissiveMap)):i.isMeshPhongMaterial?(x(l,i),i.isMeshToonMaterial?(_(l,i),i.gradientMap&&(l.gradientMap.value=i.gradientMap)):_(l,i)):i.isMeshStandardMaterial?(x(l,i),i.isMeshPhysicalMaterial?(b(l,i),l.reflectivity.value=i.reflectivity,l.clearCoat.value=i.clearCoat,l.clearCoatRoughness.value=i.clearCoatRoughness):b(l,i)):i.isMeshDepthMaterial?(x(l,i),i.displacementMap&&(l.displacementMap.value=i.displacementMap,l.displacementScale.value=i.displacementScale,l.displacementBias.value=i.displacementBias)):i.isMeshDistanceMaterial?(x(l,i),i.displacementMap&&(l.displacementMap.value=i.displacementMap,l.displacementScale.value=i.displacementScale,l.displacementBias.value=i.displacementBias),l.referencePosition.value.copy(i.referencePosition),l.nearDistance.value=i.nearDistance,l.farDistance.value=i.farDistance):i.isMeshNormalMaterial?(x(l,i),i.bumpMap&&(l.bumpMap.value=i.bumpMap,l.bumpScale.value=i.bumpScale,1===i.side&&(l.bumpScale.value*=-1)),i.normalMap&&(l.normalMap.value=i.normalMap,l.normalScale.value.copy(i.normalScale),1===i.side&&l.normalScale.value.negate()),i.displacementMap&&(l.displacementMap.value=i.displacementMap,l.displacementScale.value=i.displacementScale,l.displacementBias.value=i.displacementBias)):i.isLineBasicMaterial?(l.diffuse.value=i.color,l.opacity.value=i.opacity,i.isLineDashedMaterial&&(l.dashSize.value=i.dashSize,l.totalSize.value=i.dashSize+i.gapSize,l.scale.value=i.scale)):i.isPointsMaterial?(l.diffuse.value=i.color,l.opacity.value=i.opacity,l.size.value=i.size*ot,l.scale.value=.5*nt,l.map.value=i.map,null!==i.map&&(!0===i.map.matrixAutoUpdate&&i.map.updateMatrix(),l.uvTransform.value.copy(i.map.matrix))):i.isSpriteMaterial?(l.diffuse.value=i.color,l.opacity.value=i.opacity,l.rotation.value=i.rotation,l.map.value=i.map,null!==i.map&&(!0===i.map.matrixAutoUpdate&&i.map.updateMatrix(),l.uvTransform.value.copy(i.map.matrix))):i.isShadowMaterial&&(l.color.value=i.color,l.opacity.value=i.opacity),void 0!==l.ltc_1&&(l.ltc_1.value=aa.LTC_1),void 0!==l.ltc_2&&(l.ltc_2.value=aa.LTC_2),Ft.upload(gt,r.uniformsList,l,R)),i.isShaderMaterial&&!0===i.uniformsNeedUpdate&&(Ft.upload(gt,r.uniformsList,l,R),i.uniformsNeedUpdate=!1),i.isSpriteMaterial&&o.setValue(gt,"center",n.center),o.setValue(gt,"modelViewMatrix",n.modelViewMatrix),o.setValue(gt,"normalMatrix",n.normalMatrix),o.setValue(gt,"modelMatrix",n.matrixWorld),a}function x(t,e){if(t.opacity.value=e.opacity,e.color&&(t.diffuse.value=e.color),e.emissive&&t.emissive.value.copy(e.emissive).multiplyScalar(e.emissiveIntensity),e.map&&(t.map.value=e.map),e.alphaMap&&(t.alphaMap.value=e.alphaMap),e.specularMap&&(t.specularMap.value=e.specularMap),e.envMap&&(t.envMap.value=e.envMap,t.flipEnvMap.value=e.envMap&&e.envMap.isCubeTexture?-1:1,t.reflectivity.value=e.reflectivity,t.refractionRatio.value=e.refractionRatio,t.maxMipLevel.value=Mt.get(e.envMap).__maxMipLevel),e.lightMap&&(t.lightMap.value=e.lightMap,t.lightMapIntensity.value=e.lightMapIntensity),e.aoMap&&(t.aoMap.value=e.aoMap,t.aoMapIntensity.value=e.aoMapIntensity),e.map)var i=e.map;else e.specularMap?i=e.specularMap:e.displacementMap?i=e.displacementMap:e.normalMap?i=e.normalMap:e.bumpMap?i=e.bumpMap:e.roughnessMap?i=e.roughnessMap:e.metalnessMap?i=e.metalnessMap:e.alphaMap?i=e.alphaMap:e.emissiveMap&&(i=e.emissiveMap);void 0!==i&&(i.isWebGLRenderTarget&&(i=i.texture),!0===i.matrixAutoUpdate&&i.updateMatrix(),t.uvTransform.value.copy(i.matrix))}function _(t,e){t.specular.value=e.specular,t.shininess.value=Math.max(e.shininess,1e-4),e.emissiveMap&&(t.emissiveMap.value=e.emissiveMap),e.bumpMap&&(t.bumpMap.value=e.bumpMap,t.bumpScale.value=e.bumpScale,1===e.side&&(t.bumpScale.value*=-1)),e.normalMap&&(t.normalMap.value=e.normalMap,t.normalScale.value.copy(e.normalScale),1===e.side&&t.normalScale.value.negate()),e.displacementMap&&(t.displacementMap.value=e.displacementMap,t.displacementScale.value=e.displacementScale,t.displacementBias.value=e.displacementBias)}function b(t,e){t.roughness.value=e.roughness,t.metalness.value=e.metalness,e.roughnessMap&&(t.roughnessMap.value=e.roughnessMap),e.metalnessMap&&(t.metalnessMap.value=e.metalnessMap),e.emissiveMap&&(t.emissiveMap.value=e.emissiveMap),e.bumpMap&&(t.bumpMap.value=e.bumpMap,t.bumpScale.value=e.bumpScale,1===e.side&&(t.bumpScale.value*=-1)),e.normalMap&&(t.normalMap.value=e.normalMap,t.normalScale.value.copy(e.normalScale),1===e.side&&t.normalScale.value.negate()),e.displacementMap&&(t.displacementMap.value=e.displacementMap,t.displacementScale.value=e.displacementScale,t.displacementBias.value=e.displacementBias),e.envMap&&(t.envMapIntensity.value=e.envMapIntensity)}var M;t=t||{};var w=void 0!==t.canvas?t.canvas:document.createElementNS("http://www.w3.org/1999/xhtml","canvas"),S=void 0!==t.context?t.context:null,A=void 0!==t.alpha&&t.alpha,T=void 0===t.depth||t.depth,L=void 0===t.stencil||t.stencil,E=void 0!==t.antialias&&t.antialias,C=void 0===t.premultipliedAlpha||t.premultipliedAlpha,P=void 0!==t.preserveDrawingBuffer&&t.preserveDrawingBuffer,O=void 0!==t.powerPreference?t.powerPreference:"default",I=null,N=null;this.domElement=w,this.context=null,this.sortObjects=this.autoClearStencil=this.autoClearDepth=this.autoClearColor=this.autoClear=!0,this.clippingPlanes=[],this.localClippingEnabled=!1,this.gammaFactor=2,this.physicallyCorrectLights=this.gammaOutput=this.gammaInput=!1,this.toneMappingWhitePoint=this.toneMappingExposure=this.toneMapping=1,this.maxMorphTargets=8,this.maxMorphNormals=4;var R=this,D=!1,U=null,B=null,z=null,F=-1,G=M=null,V=!1,k=null,j=null,W=new h,H=new h,X=null,q=0,Y=w.width,nt=w.height,ot=1,st=new h(0,0,Y,nt),ht=new h(0,0,Y,nt),ct=!1,lt=new m,ut=new K,pt=!1,dt=!1,ft=new n,mt=new a;try{A={alpha:A,depth:T,stencil:L,antialias:E,premultipliedAlpha:C,preserveDrawingBuffer:P,powerPreference:O},w.addEventListener("webglcontextlost",i,!1),w.addEventListener("webglcontextrestored",r,!1);var gt=S||w.getContext("webgl",A)||w.getContext("experimental-webgl",A);if(null===gt){if(null!==w.getContext("webgl"))throw Error("Error creating WebGL context with your selected attributes.");throw Error("Error creating WebGL context.")}void 0===gt.getShaderPrecisionFormat&&(gt.getShaderPrecisionFormat=function(){return{rangeMin:1,rangeMax:1,precision:1}})}catch(vt){}var yt,xt,_t,bt,Mt,wt,St,At,Tt,Lt,Et,Ct,Pt,Ot,It,Nt,Rt;e();var Dt="xr"in navigator?new xe(R):new ye(R);this.vr=Dt;var Ut=new ue(R,Tt,xt.maxTextureSize);this.shadowMap=Ut,this.getContext=function(){return gt},this.getContextAttributes=function(){return gt.getContextAttributes()},this.forceContextLoss=function(){var t=yt.get("WEBGL_lose_context");t&&t.loseContext()},this.forceContextRestore=function(){var t=yt.get("WEBGL_lose_context");t&&t.restoreContext()},this.getPixelRatio=function(){return ot},this.setPixelRatio=function(t){void 0!==t&&(ot=t,this.setSize(Y,nt,!1))},this.getSize=function(){return{width:Y,height:nt}},this.setSize=function(t,e,i){Dt.isPresenting()?void 0:(Y=t,nt=e,w.width=t*ot,w.height=e*ot,!1!==i&&(w.style.width=t+"px",w.style.height=e+"px"),this.setViewport(0,0,t,e))},this.getDrawingBufferSize=function(){return{width:Y*ot,height:nt*ot}},this.setDrawingBufferSize=function(t,e,i){Y=t,nt=e,ot=i,w.width=t*i,w.height=e*i,this.setViewport(0,0,t,e)},this.getCurrentViewport=function(){return W},this.setViewport=function(t,e,i,n){st.set(t,nt-e-n,i,n),_t.viewport(W.copy(st).multiplyScalar(ot))},this.setScissor=function(t,e,i,n){ht.set(t,nt-e-n,i,n),_t.scissor(H.copy(ht).multiplyScalar(ot))},this.setScissorTest=function(t){_t.setScissorTest(ct=t)},this.getClearColor=function(){return Pt.getClearColor()},this.setClearColor=function(){Pt.setClearColor.apply(Pt,arguments)},this.getClearAlpha=function(){return Pt.getClearAlpha()},this.setClearAlpha=function(){Pt.setClearAlpha.apply(Pt,arguments)},this.clear=function(t,e,i){var n=0;(void 0===t||t)&&(n|=gt.COLOR_BUFFER_BIT),(void 0===e||e)&&(n|=gt.DEPTH_BUFFER_BIT),(void 0===i||i)&&(n|=gt.STENCIL_BUFFER_BIT),gt.clear(n)},this.clearColor=function(){this.clear(!0,!1,!1)},this.clearDepth=function(){this.clear(!1,!0,!1)},this.clearStencil=function(){this.clear(!1,!1,!0)},this.clearTarget=function(t,e,i,n){this.setRenderTarget(t),this.clear(e,i,n)},this.dispose=function(){w.removeEventListener("webglcontextlost",i,!1),w.removeEventListener("webglcontextrestored",r,!1),Et.dispose(),Ct.dispose(),Mt.dispose(),Tt.dispose(),Dt.dispose(),zt.stop()},this.renderBufferImmediate=function(t,e){_t.initAttributes();var i=Mt.get(t);t.hasPositions&&!i.position&&(i.position=gt.createBuffer()),t.hasNormals&&!i.normal&&(i.normal=gt.createBuffer()),t.hasUvs&&!i.uv&&(i.uv=gt.createBuffer()),t.hasColors&&!i.color&&(i.color=gt.createBuffer()),e=e.getAttributes(),t.hasPositions&&(gt.bindBuffer(gt.ARRAY_BUFFER,i.position),gt.bufferData(gt.ARRAY_BUFFER,t.positionArray,gt.DYNAMIC_DRAW),_t.enableAttribute(e.position),gt.vertexAttribPointer(e.position,3,gt.FLOAT,!1,0,0)),t.hasNormals&&(gt.bindBuffer(gt.ARRAY_BUFFER,i.normal),gt.bufferData(gt.ARRAY_BUFFER,t.normalArray,gt.DYNAMIC_DRAW),_t.enableAttribute(e.normal),gt.vertexAttribPointer(e.normal,3,gt.FLOAT,!1,0,0)),t.hasUvs&&(gt.bindBuffer(gt.ARRAY_BUFFER,i.uv),gt.bufferData(gt.ARRAY_BUFFER,t.uvArray,gt.DYNAMIC_DRAW),_t.enableAttribute(e.uv),gt.vertexAttribPointer(e.uv,2,gt.FLOAT,!1,0,0)),t.hasColors&&(gt.bindBuffer(gt.ARRAY_BUFFER,i.color),gt.bufferData(gt.ARRAY_BUFFER,t.colorArray,gt.DYNAMIC_DRAW),_t.enableAttribute(e.color),gt.vertexAttribPointer(e.color,3,gt.FLOAT,!1,0,0)),_t.disableUnusedAttributes(),gt.drawArrays(gt.TRIANGLES,0,t.count),t.count=0},this.renderBufferDirect=function(t,e,i,n,r,a){var o=r.isMesh&&0>r.normalMatrix.determinant();_t.setMaterial(n,o);var s=g(t,e,n,r),h=!1;M===i.id&&G===s.id&&V===(!0===n.wireframe)||(M=i.id,G=s.id,V=!0===n.wireframe,h=!0),r.morphTargetInfluences&&(Ot.update(r,i,n,s),h=!0),o=i.index;var c=i.attributes.position;if(e=1,!0===n.wireframe&&(o=At.getWireframeAttribute(i),e=2),t=It,null!==o){var l=St.get(o);t=Nt,t.setIndex(l)}if(h){if(i&&i.isInstancedBufferGeometry&!xt.isWebGL2&&null===yt.get("ANGLE_instanced_arrays"));else{_t.initAttributes(),h=i.attributes,s=s.getAttributes();var u=n.defaultAttributeValues;for(w in s){var p=s[w];if(0<=p){var d=h[w];if(void 0!==d){var f=d.normalized,m=d.itemSize,v=St.get(d);if(void 0!==v){var y=v.buffer,x=v.type;if(v=v.bytesPerElement,d.isInterleavedBufferAttribute){var _=d.data,b=_.stride;d=d.offset,_&&_.isInstancedInterleavedBuffer?(_t.enableAttributeAndDivisor(p,_.meshPerAttribute),void 0===i.maxInstancedCount&&(i.maxInstancedCount=_.meshPerAttribute*_.count)):_t.enableAttribute(p),gt.bindBuffer(gt.ARRAY_BUFFER,y),gt.vertexAttribPointer(p,m,x,f,b*v,d*v)}else d.isInstancedBufferAttribute?(_t.enableAttributeAndDivisor(p,d.meshPerAttribute),void 0===i.maxInstancedCount&&(i.maxInstancedCount=d.meshPerAttribute*d.count)):_t.enableAttribute(p),gt.bindBuffer(gt.ARRAY_BUFFER,y),gt.vertexAttribPointer(p,m,x,f,0,0)}}else if(void 0!==u&&(f=u[w],void 0!==f))switch(f.length){case 2:gt.vertexAttrib2fv(p,f);break;case 3:gt.vertexAttrib3fv(p,f);break;case 4:gt.vertexAttrib4fv(p,f);break;default:gt.vertexAttrib1fv(p,f)}}}_t.disableUnusedAttributes()}null!==o&&gt.bindBuffer(gt.ELEMENT_ARRAY_BUFFER,l.buffer)}l=1/0,null!==o?l=o.count:void 0!==c&&(l=c.count),o=i.drawRange.start*e,c=null!==a?a.start*e:0;var w=Math.max(o,c);if(a=Math.max(0,Math.min(l,o+i.drawRange.count*e,c+(null!==a?a.count*e:1/0))-1-w+1),0!==a){if(r.isMesh)if(!0===n.wireframe)_t.setLineWidth(n.wireframeLinewidth*(null===B?ot:1)),t.setMode(gt.LINES);else switch(r.drawMode){case 0:t.setMode(gt.TRIANGLES);break;case 1:t.setMode(gt.TRIANGLE_STRIP);break;case 2:t.setMode(gt.TRIANGLE_FAN)}else r.isLine?(n=n.linewidth,void 0===n&&(n=1),_t.setLineWidth(n*(null===B?ot:1)),r.isLineSegments?t.setMode(gt.LINES):r.isLineLoop?t.setMode(gt.LINE_LOOP):t.setMode(gt.LINE_STRIP)):r.isPoints?t.setMode(gt.POINTS):r.isSprite&&t.setMode(gt.TRIANGLES);i&&i.isInstancedBufferGeometry?0<i.maxInstancedCount&&t.renderInstances(i,w,a):t.render(w,a)}},this.compile=function(t,e){N=Ct.get(t,e),N.init(),t.traverse(function(t){t.isLight&&(N.pushLight(t),t.castShadow&&N.pushShadow(t))}),N.setupLights(e),t.traverse(function(e){if(e.material)if(Array.isArray(e.material))for(var i=0;i<e.material.length;i++)f(e.material[i],t.fog,e);else f(e.material,t.fog,e)})};var Bt=null,zt=new v;zt.setAnimationLoop(function(t){Dt.isPresenting()||Bt&&Bt(t)}),"undefined"!=typeof window&&zt.setContext(window),this.setAnimationLoop=function(t){Bt=t,Dt.setAnimationLoop(t),zt.start()},this.render=function(t,e,i,n){if(e&&e.isCamera){if(!D){G=M=null,V=!1,F=-1,k=null,!0===t.autoUpdate&&t.updateMatrixWorld(),null===e.parent&&e.updateMatrixWorld(),Dt.enabled&&(e=Dt.getCamera(e)),N=Ct.get(t,e),N.init(),t.onBeforeRender(R,t,e,i),ft.multiplyMatrices(e.projectionMatrix,e.matrixWorldInverse),lt.setFromMatrix(ft),dt=this.localClippingEnabled,pt=ut.init(this.clippingPlanes,dt,e),I=Et.get(t,e),I.init(),l(t,e,R.sortObjects),!0===R.sortObjects&&I.sort(),pt&&ut.beginShadows(),Ut.render(N.state.shadowsArray,t,e),N.setupLights(e),pt&&ut.endShadows(),this.info.autoReset&&this.info.reset(),void 0===i&&(i=null),this.setRenderTarget(i),Pt.render(I,t,e,n),n=I.opaque;var r=I.transparent;if(t.overrideMaterial){var a=t.overrideMaterial;n.length&&p(n,t,e,a),r.length&&p(r,t,e,a)}else n.length&&p(n,t,e),r.length&&p(r,t,e);i&&wt.updateRenderTargetMipmap(i),_t.buffers.depth.setTest(!0),_t.buffers.depth.setMask(!0),_t.buffers.color.setMask(!0),_t.setPolygonOffset(!1),t.onAfterRender(R,t,e),Dt.enabled&&Dt.submitFrame(),N=I=null}}else;},this.allocTextureUnit=function(){var t=q;return t>=xt.maxTextures&&void 0,q+=1,t},this.setTexture2D=function(){var t=!1;return function(e,i){e&&e.isWebGLRenderTarget&&(t||(t=!0),e=e.texture),wt.setTexture2D(e,i)}}(),this.setTexture=function(){var t=!1;return function(e,i){t||(t=!0),wt.setTexture2D(e,i)}}(),this.setTextureCube=function(){var t=!1;return function(e,i){e&&e.isWebGLRenderTargetCube&&(t||(t=!0),e=e.texture),e&&e.isCubeTexture||Array.isArray(e.image)&&6===e.image.length?wt.setTextureCube(e,i):wt.setTextureCubeDynamic(e,i)}}(),this.setFramebuffer=function(t){U=t},this.getRenderTarget=function(){return B},this.setRenderTarget=function(t){(B=t)&&void 0===Mt.get(t).__webglFramebuffer&&wt.setupRenderTarget(t);var e=U,i=!1;t?(e=Mt.get(t).__webglFramebuffer,t.isWebGLRenderTargetCube&&(e=e[t.activeCubeFace],i=!0),W.copy(t.viewport),H.copy(t.scissor),X=t.scissorTest):(W.copy(st).multiplyScalar(ot),H.copy(ht).multiplyScalar(ot),X=ct),z!==e&&(gt.bindFramebuffer(gt.FRAMEBUFFER,e),z=e),_t.viewport(W),_t.scissor(H),_t.setScissorTest(X),i&&(i=Mt.get(t.texture),gt.framebufferTexture2D(gt.FRAMEBUFFER,gt.COLOR_ATTACHMENT0,gt.TEXTURE_CUBE_MAP_POSITIVE_X+t.activeCubeFace,i.__webglTexture,t.activeMipMapLevel))},this.readRenderTargetPixels=function(t,e,i,n,r,a){if(t&&t.isWebGLRenderTarget){var o=Mt.get(t).__webglFramebuffer;if(o){var s=!1;o!==z&&(gt.bindFramebuffer(gt.FRAMEBUFFER,o),s=!0);try{var h=t.texture,c=h.format,l=h.type;1023!==c&&Rt.convert(c)!==gt.getParameter(gt.IMPLEMENTATION_COLOR_READ_FORMAT)?void 0:(1009===l||Rt.convert(l)===gt.getParameter(gt.IMPLEMENTATION_COLOR_READ_TYPE)||1015===l&&(xt.isWebGL2||yt.get("OES_texture_float")||yt.get("WEBGL_color_buffer_float"))||1016===l&&(xt.isWebGL2?yt.get("EXT_color_buffer_float"):yt.get("EXT_color_buffer_half_float")))&&gt.checkFramebufferStatus(gt.FRAMEBUFFER)===gt.FRAMEBUFFER_COMPLETE?0<=e&&e<=t.width-n&&0<=i&&i<=t.height-r&&gt.readPixels(e,i,n,r,Rt.convert(c),Rt.convert(l),a):void 0}finally{s&&gt.bindFramebuffer(gt.FRAMEBUFFER,z);
}}}},this.copyFramebufferToTexture=function(t,e,i){var n=e.image.width,r=e.image.height,a=Rt.convert(e.format);this.setTexture2D(e,0),gt.copyTexImage2D(gt.TEXTURE_2D,i||0,a,t.x,t.y,n,r,0)},this.copyTextureToTexture=function(t,e,i,n){var r=e.image.width,a=e.image.height,o=Rt.convert(i.format),s=Rt.convert(i.type);this.setTexture2D(i,0),e.isDataTexture?gt.texSubImage2D(gt.TEXTURE_2D,n||0,t.x,t.y,r,a,o,s,e.image.data):gt.texSubImage2D(gt.TEXTURE_2D,n||0,t.x,t.y,o,s,e.image)}}function be(t,e){this.name="",this.color=new g(t),this.density=void 0!==e?e:25e-5}function Me(t,e,i){this.name="",this.color=new g(t),this.near=void 0!==e?e:1,this.far=void 0!==i?i:1e3}function we(){b.call(this),this.type="Scene",this.overrideMaterial=this.fog=this.background=null,this.autoUpdate=!0}function Se(t,e){this.array=t,this.stride=e,this.count=void 0!==t?t.length/e:0,this.dynamic=!1,this.updateRange={offset:0,count:-1},this.version=0}function Ae(t,e,i,n){this.data=t,this.itemSize=e,this.offset=i,this.normalized=!0===n}function Te(t){j.call(this),this.type="SpriteMaterial",this.color=new g(16777215),this.map=null,this.rotation=0,this.sizeAttenuation=!0,this.lights=!1,this.transparent=!0,this.setValues(t)}function Le(t){if(b.call(this),this.type="Sprite",void 0===ba){ba=new z;var e=new Float32Array([-.5,-.5,0,0,0,.5,-.5,0,1,0,.5,.5,0,1,1,-.5,.5,0,0,1]);e=new Se(e,5),ba.setIndex([0,1,2,0,2,3]),ba.addAttribute("position",new Ae(e,3,0,(!1))),ba.addAttribute("uv",new Ae(e,2,3,(!1)))}this.geometry=ba,this.material=void 0!==t?t:new Te,this.center=new i(.5,.5)}function Ee(){b.call(this),this.type="LOD",Object.defineProperties(this,{levels:{enumerable:!0,value:[]}})}function Ce(t,e){if(t=t||[],this.bones=t.slice(0),this.boneMatrices=new Float32Array(16*this.bones.length),void 0===e)this.calculateInverses();else if(this.bones.length===e.length)this.boneInverses=e.slice(0);else for(this.boneInverses=[],t=0,e=this.bones.length;t<e;t++)this.boneInverses.push(new n)}function Pe(){b.call(this),this.type="Bone"}function Oe(t,e){Y.call(this,t,e),this.type="SkinnedMesh",this.bindMode="attached",this.bindMatrix=new n,this.bindMatrixInverse=new n,t=this.initBones(),t=new Ce(t),this.bind(t,this.matrixWorld),this.normalizeSkinWeights()}function Ie(t){j.call(this),this.type="LineBasicMaterial",this.color=new g(16777215),this.linewidth=1,this.linejoin=this.linecap="round",this.lights=!1,this.setValues(t)}function Ne(t,e,i){b.call(this),this.type="Line",this.geometry=void 0!==t?t:new z,this.material=void 0!==e?e:new Ie({color:16777215*Math.random()})}function Re(t,e){Ne.call(this,t,e),this.type="LineSegments"}function De(t,e){Ne.call(this,t,e),this.type="LineLoop"}function Ue(t){j.call(this),this.type="PointsMaterial",this.color=new g(16777215),this.map=null,this.size=1,this.sizeAttenuation=!0,this.lights=this.morphTargets=!1,this.setValues(t)}function Be(t,e){b.call(this),this.type="Points",this.geometry=void 0!==t?t:new z,this.material=void 0!==e?e:new Ue({color:16777215*Math.random()})}function ze(t,e,i,n,r,a,o,h,c){s.call(this,t,e,i,n,r,a,o,h,c),this.generateMipmaps=!1}function Fe(t,e,i,n,r,a,o,h,c,l,u,p){s.call(this,null,a,o,h,c,l,n,r,u,p),this.image={width:e,height:i},this.mipmaps=t,this.generateMipmaps=this.flipY=!1}function Ge(t,e,i,n,r,a,o,h,c){s.call(this,t,e,i,n,r,a,o,h,c),this.needsUpdate=!0}function Ve(t,e,i,n,r,a,o,h,c,l){if(l=void 0!==l?l:1026,1026!==l&&1027!==l)throw Error("DepthTexture format must be either THREE.DepthFormat or THREE.DepthStencilFormat");void 0===i&&1026===l&&(i=1012),void 0===i&&1027===l&&(i=1020),s.call(this,null,n,r,a,o,h,l,i,c),this.image={width:t,height:e},this.magFilter=void 0!==o?o:1003,this.minFilter=void 0!==h?h:1003,this.generateMipmaps=this.flipY=!1}function ke(t){z.call(this),this.type="WireframeGeometry";var e,i,n,r=[],o=[0,0],s={},h=["a","b","c"];if(t&&t.isGeometry){var c=t.faces,l=0;for(i=c.length;l<i;l++){var u=c[l];for(e=0;3>e;e++){var p=u[h[e]],d=u[h[(e+1)%3]];o[0]=Math.min(p,d),o[1]=Math.max(p,d),p=o[0]+","+o[1],void 0===s[p]&&(s[p]={index1:o[0],index2:o[1]})}}for(p in s)l=s[p],h=t.vertices[l.index1],r.push(h.x,h.y,h.z),h=t.vertices[l.index2],r.push(h.x,h.y,h.z)}else if(t&&t.isBufferGeometry)if(h=new a,null!==t.index){c=t.attributes.position,u=t.index;var f=t.groups;for(0===f.length&&(f=[{start:0,count:u.count,materialIndex:0}]),t=0,n=f.length;t<n;++t)for(l=f[t],e=l.start,i=l.count,l=e,i=e+i;l<i;l+=3)for(e=0;3>e;e++)p=u.getX(l+e),d=u.getX(l+(e+1)%3),o[0]=Math.min(p,d),o[1]=Math.max(p,d),p=o[0]+","+o[1],void 0===s[p]&&(s[p]={index1:o[0],index2:o[1]});for(p in s)l=s[p],h.fromBufferAttribute(c,l.index1),r.push(h.x,h.y,h.z),h.fromBufferAttribute(c,l.index2),r.push(h.x,h.y,h.z)}else for(c=t.attributes.position,l=0,i=c.count/3;l<i;l++)for(e=0;3>e;e++)s=3*l+e,h.fromBufferAttribute(c,s),r.push(h.x,h.y,h.z),s=3*l+(e+1)%3,h.fromBufferAttribute(c,s),r.push(h.x,h.y,h.z);this.addAttribute("position",new R(r,3))}function je(t,e,i){A.call(this),this.type="ParametricGeometry",this.parameters={func:t,slices:e,stacks:i},this.fromBufferGeometry(new We(t,e,i)),this.mergeVertices()}function We(t,e,i){z.call(this),this.type="ParametricBufferGeometry",this.parameters={func:t,slices:e,stacks:i};var n,r,o=[],s=[],h=[],c=[],l=new a,u=new a,p=new a,d=new a,f=new a;3>t.length&&void 0;var m=e+1;for(n=0;n<=i;n++){var g=n/i;for(r=0;r<=e;r++){var v=r/e;t(v,g,u),s.push(u.x,u.y,u.z),0<=v-1e-5?(t(v-1e-5,g,p),d.subVectors(u,p)):(t(v+1e-5,g,p),d.subVectors(p,u)),0<=g-1e-5?(t(v,g-1e-5,p),f.subVectors(u,p)):(t(v,g+1e-5,p),f.subVectors(p,u)),l.crossVectors(d,f).normalize(),h.push(l.x,l.y,l.z),c.push(v,g)}}for(n=0;n<i;n++)for(r=0;r<e;r++)t=n*m+r+1,l=(n+1)*m+r+1,u=(n+1)*m+r,o.push(n*m+r,t,u),o.push(t,l,u);this.setIndex(o),this.addAttribute("position",new R(s,3)),this.addAttribute("normal",new R(h,3)),this.addAttribute("uv",new R(c,2))}function He(t,e,i,n){A.call(this),this.type="PolyhedronGeometry",this.parameters={vertices:t,indices:e,radius:i,detail:n},this.fromBufferGeometry(new Xe(t,e,i,n)),this.mergeVertices()}function Xe(t,e,n,r){function o(t){c.push(t.x,t.y,t.z)}function s(e,i){e*=3,i.x=t[e+0],i.y=t[e+1],i.z=t[e+2]}function h(t,e,i,n){0>n&&1===t.x&&(l[e]=t.x-1),0===i.x&&0===i.z&&(l[e]=n/2/Math.PI+.5)}z.call(this),this.type="PolyhedronBufferGeometry",this.parameters={vertices:t,indices:e,radius:n,detail:r},n=n||1,r=r||0;var c=[],l=[];!function(t){for(var i=new a,n=new a,r=new a,h=0;h<e.length;h+=3){s(e[h+0],i),s(e[h+1],n),s(e[h+2],r);var c,l,u=i,p=n,d=r,f=Math.pow(2,t),m=[];for(l=0;l<=f;l++){m[l]=[];var g=u.clone().lerp(d,l/f),v=p.clone().lerp(d,l/f),y=f-l;for(c=0;c<=y;c++)m[l][c]=0===c&&l===f?g:g.clone().lerp(v,c/y)}for(l=0;l<f;l++)for(c=0;c<2*(f-l)-1;c++)u=Math.floor(c/2),0===c%2?(o(m[l][u+1]),o(m[l+1][u]),o(m[l][u])):(o(m[l][u+1]),o(m[l+1][u+1]),o(m[l+1][u]))}}(r),function(t){for(var e=new a,i=0;i<c.length;i+=3)e.x=c[i+0],e.y=c[i+1],e.z=c[i+2],e.normalize().multiplyScalar(t),c[i+0]=e.x,c[i+1]=e.y,c[i+2]=e.z}(n),function(){for(var t=new a,e=0;e<c.length;e+=3)t.x=c[e+0],t.y=c[e+1],t.z=c[e+2],l.push(Math.atan2(t.z,-t.x)/2/Math.PI+.5,1-(Math.atan2(-t.y,Math.sqrt(t.x*t.x+t.z*t.z))/Math.PI+.5));t=new a,e=new a;for(var n=new a,r=new a,o=new i,s=new i,u=new i,p=0,d=0;p<c.length;p+=9,d+=6){t.set(c[p+0],c[p+1],c[p+2]),e.set(c[p+3],c[p+4],c[p+5]),n.set(c[p+6],c[p+7],c[p+8]),o.set(l[d+0],l[d+1]),s.set(l[d+2],l[d+3]),u.set(l[d+4],l[d+5]),r.copy(t).add(e).add(n).divideScalar(3);var f=Math.atan2(r.z,-r.x);h(o,d+0,t,f),h(s,d+2,e,f),h(u,d+4,n,f)}for(t=0;t<l.length;t+=6)e=l[t+0],n=l[t+2],r=l[t+4],o=Math.min(e,n,r),.9<Math.max(e,n,r)&&.1>o&&(.2>e&&(l[t+0]+=1),.2>n&&(l[t+2]+=1),.2>r&&(l[t+4]+=1))}(),this.addAttribute("position",new R(c,3)),this.addAttribute("normal",new R(c.slice(),3)),this.addAttribute("uv",new R(l,2)),0===r?this.computeVertexNormals():this.normalizeNormals()}function qe(t,e){A.call(this),this.type="TetrahedronGeometry",this.parameters={radius:t,detail:e},this.fromBufferGeometry(new Ye(t,e)),this.mergeVertices()}function Ye(t,e){Xe.call(this,[1,1,1,-1,-1,1,-1,1,-1,1,-1,-1],[2,1,0,0,3,2,1,3,0,2,3,1],t,e),this.type="TetrahedronBufferGeometry",this.parameters={radius:t,detail:e}}function Ze(t,e){A.call(this),this.type="OctahedronGeometry",this.parameters={radius:t,detail:e},this.fromBufferGeometry(new Je(t,e)),this.mergeVertices()}function Je(t,e){Xe.call(this,[1,0,0,-1,0,0,0,1,0,0,-1,0,0,0,1,0,0,-1],[0,2,4,0,4,3,0,3,5,0,5,2,1,2,5,1,5,3,1,3,4,1,4,2],t,e),this.type="OctahedronBufferGeometry",this.parameters={radius:t,detail:e}}function Qe(t,e){A.call(this),this.type="IcosahedronGeometry",this.parameters={radius:t,detail:e},this.fromBufferGeometry(new Ke(t,e)),this.mergeVertices()}function Ke(t,e){var i=(1+Math.sqrt(5))/2;Xe.call(this,[-1,i,0,1,i,0,-1,-i,0,1,-i,0,0,-1,i,0,1,i,0,-1,-i,0,1,-i,i,0,-1,i,0,1,-i,0,-1,-i,0,1],[0,11,5,0,5,1,0,1,7,0,7,10,0,10,11,1,5,9,5,11,4,11,10,2,10,7,6,7,1,8,3,9,4,3,4,2,3,2,6,3,6,8,3,8,9,4,9,5,2,4,11,6,2,10,8,6,7,9,8,1],t,e),this.type="IcosahedronBufferGeometry",this.parameters={radius:t,detail:e}}function $e(t,e){A.call(this),this.type="DodecahedronGeometry",this.parameters={radius:t,detail:e},this.fromBufferGeometry(new ti(t,e)),this.mergeVertices()}function ti(t,e){var i=(1+Math.sqrt(5))/2,n=1/i;Xe.call(this,[-1,-1,-1,-1,-1,1,-1,1,-1,-1,1,1,1,-1,-1,1,-1,1,1,1,-1,1,1,1,0,-n,-i,0,-n,i,0,n,-i,0,n,i,-n,-i,0,-n,i,0,n,-i,0,n,i,0,-i,0,-n,i,0,-n,-i,0,n,i,0,n],[3,11,7,3,7,15,3,15,13,7,19,17,7,17,6,7,6,15,17,4,8,17,8,10,17,10,6,8,0,16,8,16,2,8,2,10,0,12,1,0,1,18,0,18,16,6,10,2,6,2,13,6,13,15,2,16,18,2,18,3,2,3,13,18,1,9,18,9,11,18,11,3,4,14,12,4,12,0,4,0,8,11,9,5,11,5,19,11,19,7,19,5,14,19,14,4,19,4,17,1,12,14,1,14,5,1,5,9],t,e),this.type="DodecahedronBufferGeometry",this.parameters={radius:t,detail:e}}function ei(t,e,i,n,r,a){A.call(this),this.type="TubeGeometry",this.parameters={path:t,tubularSegments:e,radius:i,radialSegments:n,closed:r},t=new ii(t,e,i,n,r),this.tangents=t.tangents,this.normals=t.normals,this.binormals=t.binormals,this.fromBufferGeometry(t),this.mergeVertices()}function ii(t,e,n,r,o){function s(i){f=t.getPointAt(i/e,f);var a=h.normals[i];for(i=h.binormals[i],l=0;l<=r;l++){var o=l/r*Math.PI*2,s=Math.sin(o);o=-Math.cos(o),p.x=o*a.x+s*i.x,p.y=o*a.y+s*i.y,p.z=o*a.z+s*i.z,p.normalize(),g.push(p.x,p.y,p.z),u.x=f.x+n*p.x,u.y=f.y+n*p.y,u.z=f.z+n*p.z,m.push(u.x,u.y,u.z)}}z.call(this),this.type="TubeBufferGeometry",this.parameters={path:t,tubularSegments:e,radius:n,radialSegments:r,closed:o},e=e||64,n=n||1,r=r||8,o=o||!1;var h=t.computeFrenetFrames(e,o);this.tangents=h.tangents,this.normals=h.normals,this.binormals=h.binormals;var c,l,u=new a,p=new a,d=new i,f=new a,m=[],g=[],v=[],y=[];for(c=0;c<e;c++)s(c);for(s(!1===o?e:0),c=0;c<=e;c++)for(l=0;l<=r;l++)d.x=c/e,d.y=l/r,v.push(d.x,d.y);!function(){for(l=1;l<=e;l++)for(c=1;c<=r;c++){var t=(r+1)*l+(c-1),i=(r+1)*l+c,n=(r+1)*(l-1)+c;y.push((r+1)*(l-1)+(c-1),t,n),y.push(t,i,n)}}(),this.setIndex(y),this.addAttribute("position",new R(m,3)),this.addAttribute("normal",new R(g,3)),this.addAttribute("uv",new R(v,2))}function ni(t,e,i,n,r,a,o){A.call(this),this.type="TorusKnotGeometry",this.parameters={radius:t,tube:e,tubularSegments:i,radialSegments:n,p:r,q:a},this.fromBufferGeometry(new ri(t,e,i,n,r,a)),this.mergeVertices()}function ri(t,e,i,n,r,o){function s(t,e,i,n,r){var a=Math.sin(t);e=i/e*t,i=Math.cos(e),r.x=n*(2+i)*.5*Math.cos(t),r.y=n*(2+i)*a*.5,r.z=n*Math.sin(e)*.5}z.call(this),this.type="TorusKnotBufferGeometry",this.parameters={radius:t,tube:e,tubularSegments:i,radialSegments:n,p:r,q:o},t=t||1,e=e||.4,i=Math.floor(i)||64,n=Math.floor(n)||8,r=r||2,o=o||3;var h,c=[],l=[],u=[],p=[],d=new a,f=new a,m=new a,g=new a,v=new a,y=new a,x=new a;for(h=0;h<=i;++h){var _=h/i*r*Math.PI*2;for(s(_,r,o,t,m),s(_+.01,r,o,t,g),y.subVectors(g,m),x.addVectors(g,m),v.crossVectors(y,x),x.crossVectors(v,y),v.normalize(),x.normalize(),_=0;_<=n;++_){var b=_/n*Math.PI*2,M=-e*Math.cos(b);b=e*Math.sin(b),d.x=m.x+(M*x.x+b*v.x),d.y=m.y+(M*x.y+b*v.y),d.z=m.z+(M*x.z+b*v.z),l.push(d.x,d.y,d.z),f.subVectors(d,m).normalize(),u.push(f.x,f.y,f.z),p.push(h/i),p.push(_/n)}}for(_=1;_<=i;_++)for(h=1;h<=n;h++)t=(n+1)*_+(h-1),e=(n+1)*_+h,r=(n+1)*(_-1)+h,c.push((n+1)*(_-1)+(h-1),t,r),c.push(t,e,r);this.setIndex(c),this.addAttribute("position",new R(l,3)),this.addAttribute("normal",new R(u,3)),this.addAttribute("uv",new R(p,2))}function ai(t,e,i,n,r){A.call(this),this.type="TorusGeometry",this.parameters={radius:t,tube:e,radialSegments:i,tubularSegments:n,arc:r},this.fromBufferGeometry(new oi(t,e,i,n,r)),this.mergeVertices()}function oi(t,e,i,n,r){z.call(this),this.type="TorusBufferGeometry",this.parameters={radius:t,tube:e,radialSegments:i,tubularSegments:n,arc:r},t=t||1,e=e||.4,i=Math.floor(i)||8,n=Math.floor(n)||6,r=r||2*Math.PI;var o,s,h=[],c=[],l=[],u=[],p=new a,d=new a,f=new a;for(o=0;o<=i;o++)for(s=0;s<=n;s++){var m=s/n*r,g=o/i*Math.PI*2;d.x=(t+e*Math.cos(g))*Math.cos(m),d.y=(t+e*Math.cos(g))*Math.sin(m),d.z=e*Math.sin(g),c.push(d.x,d.y,d.z),p.x=t*Math.cos(m),p.y=t*Math.sin(m),f.subVectors(d,p).normalize(),l.push(f.x,f.y,f.z),u.push(s/n),u.push(o/i)}for(o=1;o<=i;o++)for(s=1;s<=n;s++)t=(n+1)*(o-1)+s-1,e=(n+1)*(o-1)+s,r=(n+1)*o+s,h.push((n+1)*o+s-1,t,r),h.push(t,e,r);this.setIndex(h),this.addAttribute("position",new R(c,3)),this.addAttribute("normal",new R(l,3)),this.addAttribute("uv",new R(u,2))}function si(t,e,i,n,r){for(var a,o=0,s=e,h=i-n;s<i;s+=n)o+=(t[h]-t[s])*(t[s+1]+t[h+1]),h=s;if(r===0<o)for(r=e;r<i;r+=n)a=_i(r,t[r],t[r+1],a);else for(r=i-n;r>=e;r-=n)a=_i(r,t[r],t[r+1],a);return a&&gi(a,a.next)&&(bi(a),a=a.next),a}function hi(t,e){if(!t)return t;e||(e=t);do{var i=!1;if(t.steiner||!gi(t,t.next)&&0!==mi(t.prev,t,t.next))t=t.next;else{if(bi(t),t=e=t.prev,t===t.next)break;i=!0}}while(i||t!==e);return e}function ci(t,e,i,n,r,a,o){if(t){if(!o&&a){var s=t,h=s;do null===h.z&&(h.z=pi(h.x,h.y,n,r,a)),h.prevZ=h.prev,h=h.nextZ=h.next;while(h!==s);h.prevZ.nextZ=null,h.prevZ=null,s=h;var c,l,u,p,d=1;do{h=s;var f=s=null;for(l=0;h;){l++;var m=h;for(c=u=0;c<d&&(u++,m=m.nextZ);c++);for(p=d;0<u||0<p&&m;)0!==u&&(0===p||!m||h.z<=m.z)?(c=h,h=h.nextZ,u--):(c=m,m=m.nextZ,p--),f?f.nextZ=c:s=c,c.prevZ=f,f=c;h=m}f.nextZ=null,d*=2}while(1<l)}for(s=t;t.prev!==t.next;){if(h=t.prev,m=t.next,a)t:{f=t,p=n;var g=r,v=a;if(l=f.prev,u=f,d=f.next,0<=mi(l,u,d))f=!1;else{var y=l.x>u.x?l.x>d.x?l.x:d.x:u.x>d.x?u.x:d.x,x=l.y>u.y?l.y>d.y?l.y:d.y:u.y>d.y?u.y:d.y;for(c=pi(l.x<u.x?l.x<d.x?l.x:d.x:u.x<d.x?u.x:d.x,l.y<u.y?l.y<d.y?l.y:d.y:u.y<d.y?u.y:d.y,p,g,v),p=pi(y,x,p,g,v),g=f.nextZ;g&&g.z<=p;){if(g!==f.prev&&g!==f.next&&fi(l.x,l.y,u.x,u.y,d.x,d.y,g.x,g.y)&&0<=mi(g.prev,g,g.next)){f=!1;break t}g=g.nextZ}for(g=f.prevZ;g&&g.z>=c;){if(g!==f.prev&&g!==f.next&&fi(l.x,l.y,u.x,u.y,d.x,d.y,g.x,g.y)&&0<=mi(g.prev,g,g.next)){f=!1;break t}g=g.prevZ}f=!0}}else t:if(f=t,l=f.prev,u=f,d=f.next,0<=mi(l,u,d))f=!1;else{for(c=f.next.next;c!==f.prev;){if(fi(l.x,l.y,u.x,u.y,d.x,d.y,c.x,c.y)&&0<=mi(c.prev,c,c.next)){f=!1;break t}c=c.next}f=!0}if(f)e.push(h.i/i),e.push(t.i/i),e.push(m.i/i),bi(t),s=t=m.next;else if(t=m,t===s){if(o){if(1===o){o=e,s=i,h=t;do m=h.prev,f=h.next.next,!gi(m,f)&&vi(m,h,h.next,f)&&yi(m,f)&&yi(f,m)&&(o.push(m.i/s),o.push(h.i/s),o.push(f.i/s),bi(h),bi(h.next),h=t=f),h=h.next;while(h!==t);t=h,ci(t,e,i,n,r,a,2)}else if(2===o)t:{o=t;do{for(s=o.next.next;s!==o.prev;){if(h=o.i!==s.i){if(h=o,m=s,f=h.next.i!==m.i&&h.prev.i!==m.i){e:{f=h;do{if(f.i!==h.i&&f.next.i!==h.i&&f.i!==m.i&&f.next.i!==m.i&&vi(f,f.next,h,m)){f=!0;break e}f=f.next}while(f!==h);f=!1}f=!f}if(f=f&&yi(h,m)&&yi(m,h)){f=h,l=!1,u=(h.x+m.x)/2,m=(h.y+m.y)/2;do f.y>m!=f.next.y>m&&f.next.y!==f.y&&u<(f.next.x-f.x)*(m-f.y)/(f.next.y-f.y)+f.x&&(l=!l),f=f.next;while(f!==h);f=l}h=f}if(h){t=xi(o,s),o=hi(o,o.next),t=hi(t,t.next),ci(o,e,i,n,r,a),ci(t,e,i,n,r,a);break t}s=s.next}o=o.next}while(o!==t)}}else ci(hi(t),e,i,n,r,a,1);break}}}}function li(t,e){return t.x-e.x}function ui(t,e){var i=e,n=t.x,r=t.y,a=-(1/0);do{if(r<=i.y&&r>=i.next.y&&i.next.y!==i.y){var o=i.x+(r-i.y)*(i.next.x-i.x)/(i.next.y-i.y);if(o<=n&&o>a){if(a=o,o===n){if(r===i.y)return i;if(r===i.next.y)return i.next}var s=i.x<i.next.x?i:i.next}}i=i.next}while(i!==e);if(!s)return null;if(n===a)return s.prev;e=s,o=s.x;var h=s.y,c=1/0;for(i=s.next;i!==e;){if(n>=i.x&&i.x>=o&&n!==i.x&&fi(r<h?n:a,r,o,h,r<h?a:n,r,i.x,i.y)){var l=Math.abs(r-i.y)/(n-i.x);(l<c||l===c&&i.x>s.x)&&yi(i,t)&&(s=i,c=l)}i=i.next}return s}function pi(t,e,i,n,r){return t=32767*(t-i)*r,e=32767*(e-n)*r,t=16711935&(t|t<<8),t=252645135&(t|t<<4),t=858993459&(t|t<<2),e=16711935&(e|e<<8),e=252645135&(e|e<<4),e=858993459&(e|e<<2),1431655765&(t|t<<1)|(1431655765&(e|e<<1))<<1}function di(t){var e=t,i=t;do e.x<i.x&&(i=e),e=e.next;while(e!==t);return i}function fi(t,e,i,n,r,a,o,s){return 0<=(r-o)*(e-s)-(t-o)*(a-s)&&0<=(t-o)*(n-s)-(i-o)*(e-s)&&0<=(i-o)*(a-s)-(r-o)*(n-s)}function mi(t,e,i){return(e.y-t.y)*(i.x-e.x)-(e.x-t.x)*(i.y-e.y)}function gi(t,e){return t.x===e.x&&t.y===e.y}function vi(t,e,i,n){return!!(gi(t,e)&&gi(i,n)||gi(t,n)&&gi(i,e))||0<mi(t,e,i)!=0<mi(t,e,n)&&0<mi(i,n,t)!=0<mi(i,n,e)}function yi(t,e){return 0>mi(t.prev,t,t.next)?0<=mi(t,e,t.next)&&0<=mi(t,t.prev,e):0>mi(t,e,t.prev)||0>mi(t,t.next,e)}function xi(t,e){var i=new Mi(t.i,t.x,t.y),n=new Mi(e.i,e.x,e.y),r=t.next,a=e.prev;return t.next=e,e.prev=t,i.next=r,r.prev=i,n.next=i,i.prev=n,a.next=n,n.prev=a,n}function _i(t,e,i,n){return t=new Mi(t,e,i),n?(t.next=n.next,t.prev=n,n.next.prev=t,n.next=t):(t.prev=t,t.next=t),t}function bi(t){t.next.prev=t.prev,t.prev.next=t.next,t.prevZ&&(t.prevZ.nextZ=t.nextZ),t.nextZ&&(t.nextZ.prevZ=t.prevZ)}function Mi(t,e,i){this.i=t,this.x=e,this.y=i,this.nextZ=this.prevZ=this.z=this.next=this.prev=null,this.steiner=!1}function wi(t){var e=t.length;2<e&&t[e-1].equals(t[0])&&t.pop()}function Si(t,e){for(var i=0;i<e.length;i++)t.push(e[i].x),t.push(e[i].y)}function Ai(t,e){A.call(this),this.type="ExtrudeGeometry",this.parameters={shapes:t,options:e},this.fromBufferGeometry(new Ti(t,e)),this.mergeVertices()}function Ti(t,e){function n(t){function n(t,e,i){return e.clone().multiplyScalar(i).add(t)}function h(t,e,n){var r=t.x-e.x,a=t.y-e.y,o=n.x-t.x,s=n.y-t.y,h=r*r+a*a;if(Math.abs(r*s-a*o)>Number.EPSILON){var c=Math.sqrt(h),l=Math.sqrt(o*o+s*s);if(h=e.x-a/c,e=e.y+r/c,s=((n.x-s/l-h)*s-(n.y+o/l-e)*o)/(r*s-a*o),o=h+r*s-t.x,r=e+a*s-t.y,a=o*o+r*r,2>=a)return new i(o,r);a=Math.sqrt(a/2)}else t=!1,r>Number.EPSILON?o>Number.EPSILON&&(t=!0):r<-Number.EPSILON?o<-Number.EPSILON&&(t=!0):Math.sign(a)===Math.sign(s)&&(t=!0),t?(o=-a,a=Math.sqrt(h)):(o=r,r=a,a=Math.sqrt(h/2));return new i(o/a,r/a)}function c(t,e){for(G=t.length;0<=--G;){var i=G,n=G-1;0>n&&(n=t.length-1);var a,s=g+2*b;for(a=0;a<s;a++){var h=z*a,c=z*(a+1),l=e+n+h,u=e+n+c;c=e+i+c,p(e+i+h),p(l),p(c),p(l),p(u),p(c),h=o.length/3,h=w.generateSideWallUV(r,o,h-6,h-3,h-2,h-1),d(h[0]),d(h[1]),d(h[3]),d(h[1]),d(h[2]),d(h[3])}}}function l(t,e,i){f.push(t),f.push(e),f.push(i)}function u(t,e,i){p(t),p(e),p(i),t=o.length/3,t=w.generateTopUV(r,o,t-3,t-2,t-1),d(t[0]),d(t[1]),d(t[2])}function p(t){o.push(f[3*t]),o.push(f[3*t+1]),o.push(f[3*t+2])}function d(t){s.push(t.x),s.push(t.y)}var f=[],m=void 0!==e.curveSegments?e.curveSegments:12,g=void 0!==e.steps?e.steps:1,v=void 0!==e.depth?e.depth:100,y=void 0===e.bevelEnabled||e.bevelEnabled,x=void 0!==e.bevelThickness?e.bevelThickness:6,_=void 0!==e.bevelSize?e.bevelSize:x-2,b=void 0!==e.bevelSegments?e.bevelSegments:3,M=e.extrudePath,w=void 0!==e.UVGenerator?e.UVGenerator:Sa;void 0!==e.amount&&(v=e.amount);var S=!1;if(M){var A=M.getSpacedPoints(g);S=!0,y=!1;var T=M.computeFrenetFrames(g,!1),L=new a,E=new a,C=new a}y||(_=x=b=0);var P;m=t.extractPoints(m),t=m.shape;var O=m.holes;if(!wa.isClockWise(t)){t=t.reverse();var I=0;for(P=O.length;I<P;I++){var N=O[I];wa.isClockWise(N)&&(O[I]=N.reverse())}}var R=wa.triangulateShape(t,O),D=t;for(I=0,P=O.length;I<P;I++)N=O[I],t=t.concat(N);var U,B,z=t.length,F=R.length;m=[];var G=0,V=D.length,k=V-1;for(U=G+1;G<V;G++,k++,U++)k===V&&(k=0),U===V&&(U=0),m[G]=h(D[G],D[k],D[U]);M=[];var j=m.concat();for(I=0,P=O.length;I<P;I++){N=O[I];var W=[];for(G=0,V=N.length,k=V-1,U=G+1;G<V;G++,k++,U++)k===V&&(k=0),U===V&&(U=0),W[G]=h(N[G],N[k],N[U]);M.push(W),j=j.concat(W)}for(k=0;k<b;k++){V=k/b;var H=x*Math.cos(V*Math.PI/2);for(U=_*Math.sin(V*Math.PI/2),G=0,V=D.length;G<V;G++){var X=n(D[G],m[G],U);l(X.x,X.y,-H)}for(I=0,P=O.length;I<P;I++)for(N=O[I],W=M[I],G=0,V=N.length;G<V;G++)X=n(N[G],W[G],U),l(X.x,X.y,-H)}for(U=_,G=0;G<z;G++)X=y?n(t[G],j[G],U):t[G],S?(E.copy(T.normals[0]).multiplyScalar(X.x),L.copy(T.binormals[0]).multiplyScalar(X.y),C.copy(A[0]).add(E).add(L),l(C.x,C.y,C.z)):l(X.x,X.y,0);for(V=1;V<=g;V++)for(G=0;G<z;G++)X=y?n(t[G],j[G],U):t[G],S?(E.copy(T.normals[V]).multiplyScalar(X.x),L.copy(T.binormals[V]).multiplyScalar(X.y),C.copy(A[V]).add(E).add(L),l(C.x,C.y,C.z)):l(X.x,X.y,v/g*V);for(k=b-1;0<=k;k--){for(V=k/b,H=x*Math.cos(V*Math.PI/2),U=_*Math.sin(V*Math.PI/2),G=0,V=D.length;G<V;G++)X=n(D[G],m[G],U),l(X.x,X.y,v+H);for(I=0,P=O.length;I<P;I++)for(N=O[I],W=M[I],G=0,V=N.length;G<V;G++)X=n(N[G],W[G],U),S?l(X.x,X.y+A[g-1].y,A[g-1].x+H):l(X.x,X.y,v+H)}!function(){var t=o.length/3;if(y){var e=0*z;for(G=0;G<F;G++)B=R[G],u(B[2]+e,B[1]+e,B[0]+e);for(e=z*(g+2*b),G=0;G<F;G++)B=R[G],u(B[0]+e,B[1]+e,B[2]+e)}else{for(G=0;G<F;G++)B=R[G],u(B[2],B[1],B[0]);for(G=0;G<F;G++)B=R[G],u(B[0]+z*g,B[1]+z*g,B[2]+z*g)}r.addGroup(t,o.length/3-t,0)}(),function(){var t=o.length/3,e=0;for(c(D,e),e+=D.length,I=0,P=O.length;I<P;I++)N=O[I],c(N,e),e+=N.length;r.addGroup(t,o.length/3-t,1)}()}z.call(this),this.type="ExtrudeBufferGeometry",this.parameters={shapes:t,options:e},t=Array.isArray(t)?t:[t];for(var r=this,o=[],s=[],h=0,c=t.length;h<c;h++)n(t[h]);this.addAttribute("position",new R(o,3)),this.addAttribute("uv",new R(s,2)),this.computeVertexNormals()}function Li(t,e,i){if(i.shapes=[],Array.isArray(t))for(var n=0,r=t.length;n<r;n++)i.shapes.push(t[n].uuid);else i.shapes.push(t.uuid);return void 0!==e.extrudePath&&(i.options.extrudePath=e.extrudePath.toJSON()),i}function Ei(t,e){A.call(this),this.type="TextGeometry",this.parameters={text:t,parameters:e},this.fromBufferGeometry(new Ci(t,e)),this.mergeVertices()}function Ci(t,e){e=e||{};var i=e.font;return i&&i.isFont?(t=i.generateShapes(t,e.size),e.depth=void 0!==e.height?e.height:50,void 0===e.bevelThickness&&(e.bevelThickness=10),void 0===e.bevelSize&&(e.bevelSize=8),void 0===e.bevelEnabled&&(e.bevelEnabled=!1),Ti.call(this,t,e),void(this.type="TextBufferGeometry")):new A}function Pi(t,e,i,n,r,a,o){A.call(this),this.type="SphereGeometry",this.parameters={radius:t,widthSegments:e,heightSegments:i,phiStart:n,phiLength:r,thetaStart:a,thetaLength:o},this.fromBufferGeometry(new Oi(t,e,i,n,r,a,o)),this.mergeVertices()}function Oi(t,e,i,n,r,o,s){z.call(this),this.type="SphereBufferGeometry",this.parameters={radius:t,widthSegments:e,heightSegments:i,phiStart:n,phiLength:r,thetaStart:o,thetaLength:s},t=t||1,e=Math.max(3,Math.floor(e)||8),i=Math.max(2,Math.floor(i)||6),n=void 0!==n?n:0,r=void 0!==r?r:2*Math.PI,o=void 0!==o?o:0,s=void 0!==s?s:Math.PI;var h,c,l=o+s,u=0,p=[],d=new a,f=new a,m=[],g=[],v=[],y=[];for(c=0;c<=i;c++){var x=[],_=c/i;for(h=0;h<=e;h++){var b=h/e;d.x=-t*Math.cos(n+b*r)*Math.sin(o+_*s),d.y=t*Math.cos(o+_*s),d.z=t*Math.sin(n+b*r)*Math.sin(o+_*s),g.push(d.x,d.y,d.z),f.set(d.x,d.y,d.z).normalize(),v.push(f.x,f.y,f.z),y.push(b,1-_),x.push(u++)}p.push(x)}for(c=0;c<i;c++)for(h=0;h<e;h++)t=p[c][h+1],n=p[c][h],r=p[c+1][h],s=p[c+1][h+1],(0!==c||0<o)&&m.push(t,n,s),(c!==i-1||l<Math.PI)&&m.push(n,r,s);this.setIndex(m),this.addAttribute("position",new R(g,3)),this.addAttribute("normal",new R(v,3)),this.addAttribute("uv",new R(y,2))}function Ii(t,e,i,n,r,a){A.call(this),this.type="RingGeometry",this.parameters={innerRadius:t,outerRadius:e,thetaSegments:i,phiSegments:n,thetaStart:r,thetaLength:a},this.fromBufferGeometry(new Ni(t,e,i,n,r,a)),this.mergeVertices()}function Ni(t,e,n,r,o,s){z.call(this),this.type="RingBufferGeometry",this.parameters={innerRadius:t,outerRadius:e,thetaSegments:n,phiSegments:r,thetaStart:o,thetaLength:s},t=t||.5,e=e||1,o=void 0!==o?o:0,s=void 0!==s?s:2*Math.PI,n=void 0!==n?Math.max(3,n):8,r=void 0!==r?Math.max(1,r):1;var h,c,l=[],u=[],p=[],d=[],f=t,m=(e-t)/r,g=new a,v=new i;for(h=0;h<=r;h++){for(c=0;c<=n;c++)t=o+c/n*s,g.x=f*Math.cos(t),g.y=f*Math.sin(t),u.push(g.x,g.y,g.z),p.push(0,0,1),v.x=(g.x/e+1)/2,v.y=(g.y/e+1)/2,d.push(v.x,v.y);f+=m}for(h=0;h<r;h++)for(e=h*(n+1),c=0;c<n;c++)t=c+e,o=t+n+1,s=t+n+2,f=t+1,l.push(t,o,f),l.push(o,s,f);this.setIndex(l),this.addAttribute("position",new R(u,3)),this.addAttribute("normal",new R(p,3)),this.addAttribute("uv",new R(d,2))}function Ri(t,e,i,n){A.call(this),this.type="LatheGeometry",this.parameters={points:t,segments:e,phiStart:i,phiLength:n},this.fromBufferGeometry(new Di(t,e,i,n)),this.mergeVertices()}function Di(t,e,n,r){z.call(this),this.type="LatheBufferGeometry",this.parameters={points:t,segments:e,phiStart:n,phiLength:r},e=Math.floor(e)||12,n=n||0,r=r||2*Math.PI,r=$r.clamp(r,0,2*Math.PI);var o,s=[],h=[],c=[],l=1/e,u=new a,p=new i;for(o=0;o<=e;o++){var d=n+o*l*r,f=Math.sin(d),m=Math.cos(d);for(d=0;d<=t.length-1;d++)u.x=t[d].x*f,u.y=t[d].y,u.z=t[d].x*m,h.push(u.x,u.y,u.z),p.x=o/e,p.y=d/(t.length-1),c.push(p.x,p.y)}for(o=0;o<e;o++)for(d=0;d<t.length-1;d++)n=d+o*t.length,l=n+t.length,u=n+t.length+1,p=n+1,s.push(n,l,p),s.push(l,u,p);if(this.setIndex(s),this.addAttribute("position",new R(h,3)),this.addAttribute("uv",new R(c,2)),this.computeVertexNormals(),r===2*Math.PI)for(r=this.attributes.normal.array,s=new a,h=new a,c=new a,n=e*t.length*3,d=o=0;o<t.length;o++,d+=3)s.x=r[d+0],s.y=r[d+1],s.z=r[d+2],h.x=r[n+d+0],h.y=r[n+d+1],h.z=r[n+d+2],c.addVectors(s,h).normalize(),r[d+0]=r[n+d+0]=c.x,r[d+1]=r[n+d+1]=c.y,r[d+2]=r[n+d+2]=c.z}function Ui(t,e){A.call(this),this.type="ShapeGeometry","object"==typeof e&&(e=e.curveSegments),this.parameters={shapes:t,curveSegments:e},this.fromBufferGeometry(new Bi(t,e)),this.mergeVertices()}function Bi(t,e){function i(t){var i,s=r.length/3;t=t.extractPoints(e);var c=t.shape,l=t.holes;if(!1===wa.isClockWise(c))for(c=c.reverse(),t=0,i=l.length;t<i;t++){var u=l[t];!0===wa.isClockWise(u)&&(l[t]=u.reverse())}var p=wa.triangulateShape(c,l);for(t=0,i=l.length;t<i;t++)u=l[t],c=c.concat(u);for(t=0,i=c.length;t<i;t++)u=c[t],r.push(u.x,u.y,0),a.push(0,0,1),o.push(u.x,u.y);for(t=0,i=p.length;t<i;t++)c=p[t],n.push(c[0]+s,c[1]+s,c[2]+s),h+=3}z.call(this),this.type="ShapeBufferGeometry",this.parameters={shapes:t,curveSegments:e},e=e||12;var n=[],r=[],a=[],o=[],s=0,h=0;if(!1===Array.isArray(t))i(t);else for(var c=0;c<t.length;c++)i(t[c]),this.addGroup(s,h,c),s+=h,h=0;this.setIndex(n),this.addAttribute("position",new R(r,3)),this.addAttribute("normal",new R(a,3)),this.addAttribute("uv",new R(o,2))}function zi(t,e){if(e.shapes=[],Array.isArray(t))for(var i=0,n=t.length;i<n;i++)e.shapes.push(t[i].uuid);else e.shapes.push(t.uuid);return e}function Fi(t,e){z.call(this),this.type="EdgesGeometry",this.parameters={thresholdAngle:e};var i=[];e=Math.cos($r.DEG2RAD*(void 0!==e?e:1));var n=[0,0],r={},a=["a","b","c"];if(t.isBufferGeometry){var o=new A;o.fromBufferGeometry(t)}else o=t.clone();o.mergeVertices(),o.computeFaceNormals(),t=o.vertices,o=o.faces;for(var s=0,h=o.length;s<h;s++)for(var c=o[s],l=0;3>l;l++){var u=c[a[l]],p=c[a[(l+1)%3]];n[0]=Math.min(u,p),n[1]=Math.max(u,p),u=n[0]+","+n[1],void 0===r[u]?r[u]={index1:n[0],index2:n[1],face1:s,face2:void 0}:r[u].face2=s}for(u in r)n=r[u],(void 0===n.face2||o[n.face1].normal.dot(o[n.face2].normal)<=e)&&(a=t[n.index1],i.push(a.x,a.y,a.z),a=t[n.index2],i.push(a.x,a.y,a.z));this.addAttribute("position",new R(i,3))}function Gi(t,e,i,n,r,a,o,s){A.call(this),this.type="CylinderGeometry",this.parameters={radiusTop:t,radiusBottom:e,height:i,radialSegments:n,heightSegments:r,openEnded:a,thetaStart:o,thetaLength:s},this.fromBufferGeometry(new Vi(t,e,i,n,r,a,o,s)),this.mergeVertices()}function Vi(t,e,n,r,o,s,h,c){function l(n){var o,s=new i,l=new a,v=0,_=!0===n?t:e,b=!0===n?1:-1,M=g;for(o=1;o<=r;o++)d.push(0,y*b,0),f.push(0,b,0),m.push(.5,.5),g++;var w=g;for(o=0;o<=r;o++){var S=o/r*c+h,A=Math.cos(S);S=Math.sin(S),l.x=_*S,l.y=y*b,l.z=_*A,d.push(l.x,l.y,l.z),f.push(0,b,0),s.x=.5*A+.5,s.y=.5*S*b+.5,m.push(s.x,s.y),g++}for(o=0;o<r;o++)s=M+o,l=w+o,!0===n?p.push(l,l+1,s):p.push(l+1,l,s),v+=3;u.addGroup(x,v,!0===n?1:2),x+=v}z.call(this),this.type="CylinderBufferGeometry",this.parameters={radiusTop:t,radiusBottom:e,height:n,radialSegments:r,heightSegments:o,openEnded:s,thetaStart:h,thetaLength:c};var u=this;t=void 0!==t?t:1,e=void 0!==e?e:1,n=n||1,r=Math.floor(r)||8,o=Math.floor(o)||1,s=void 0!==s&&s,h=void 0!==h?h:0,c=void 0!==c?c:2*Math.PI;var p=[],d=[],f=[],m=[],g=0,v=[],y=n/2,x=0;!function(){var i,s,l=new a,_=new a,b=0,M=(e-t)/n;for(s=0;s<=o;s++){var w=[],S=s/o,A=S*(e-t)+t;for(i=0;i<=r;i++){var T=i/r,L=T*c+h,E=Math.sin(L);L=Math.cos(L),_.x=A*E,_.y=-S*n+y,_.z=A*L,d.push(_.x,_.y,_.z),l.set(E,M,L).normalize(),f.push(l.x,l.y,l.z),m.push(T,1-S),w.push(g++)}v.push(w)}for(i=0;i<r;i++)for(s=0;s<o;s++)l=v[s+1][i],_=v[s+1][i+1],M=v[s][i+1],p.push(v[s][i],l,M),p.push(l,_,M),b+=6;u.addGroup(x,b,0),x+=b}(),!1===s&&(0<t&&l(!0),0<e&&l(!1)),this.setIndex(p),this.addAttribute("position",new R(d,3)),this.addAttribute("normal",new R(f,3)),this.addAttribute("uv",new R(m,2))}function ki(t,e,i,n,r,a,o){Gi.call(this,0,t,e,i,n,r,a,o),this.type="ConeGeometry",this.parameters={radius:t,height:e,radialSegments:i,heightSegments:n,openEnded:r,thetaStart:a,thetaLength:o}}function ji(t,e,i,n,r,a,o){Vi.call(this,0,t,e,i,n,r,a,o),this.type="ConeBufferGeometry",this.parameters={radius:t,height:e,radialSegments:i,heightSegments:n,openEnded:r,thetaStart:a,thetaLength:o}}function Wi(t,e,i,n){A.call(this),this.type="CircleGeometry",this.parameters={radius:t,segments:e,thetaStart:i,thetaLength:n},this.fromBufferGeometry(new Hi(t,e,i,n)),this.mergeVertices()}function Hi(t,e,n,r){z.call(this),this.type="CircleBufferGeometry",this.parameters={radius:t,segments:e,thetaStart:n,thetaLength:r},t=t||1,e=void 0!==e?Math.max(3,e):8,n=void 0!==n?n:0,r=void 0!==r?r:2*Math.PI;var o,s=[],h=[],c=[],l=[],u=new a,p=new i;h.push(0,0,0),c.push(0,0,1),l.push(.5,.5);var d=0;for(o=3;d<=e;d++,o+=3){var f=n+d/e*r;u.x=t*Math.cos(f),u.y=t*Math.sin(f),h.push(u.x,u.y,u.z),c.push(0,0,1),p.x=(h[o]/t+1)/2,p.y=(h[o+1]/t+1)/2,l.push(p.x,p.y)}for(o=1;o<=e;o++)s.push(o,o+1,0);this.setIndex(s),this.addAttribute("position",new R(h,3)),this.addAttribute("normal",new R(c,3)),this.addAttribute("uv",new R(l,2))}function Xi(t){j.call(this),this.type="ShadowMaterial",this.color=new g(0),this.transparent=!0,this.setValues(t)}function qi(t){H.call(this,t),this.type="RawShaderMaterial"}function Yi(t){j.call(this),this.defines={STANDARD:""},this.type="MeshStandardMaterial",this.color=new g(16777215),this.metalness=this.roughness=.5,this.lightMap=this.map=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.emissive=new g(0),this.emissiveIntensity=1,this.bumpMap=this.emissiveMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=0,this.normalScale=new i(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.envMap=this.alphaMap=this.metalnessMap=this.roughnessMap=null,this.envMapIntensity=1,this.refractionRatio=.98,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinejoin=this.wireframeLinecap="round",this.morphNormals=this.morphTargets=this.skinning=!1,this.setValues(t)}function Zi(t){Yi.call(this),this.defines={PHYSICAL:""},this.type="MeshPhysicalMaterial",this.reflectivity=.5,this.clearCoatRoughness=this.clearCoat=0,this.setValues(t)}function Ji(t){j.call(this),this.type="MeshPhongMaterial",this.color=new g(16777215),this.specular=new g(1118481),this.shininess=30,this.lightMap=this.map=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.emissive=new g(0),this.emissiveIntensity=1,this.bumpMap=this.emissiveMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=0,this.normalScale=new i(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.envMap=this.alphaMap=this.specularMap=null,this.combine=0,
this.reflectivity=1,this.refractionRatio=.98,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinejoin=this.wireframeLinecap="round",this.morphNormals=this.morphTargets=this.skinning=!1,this.setValues(t)}function Qi(t){Ji.call(this),this.defines={TOON:""},this.type="MeshToonMaterial",this.gradientMap=null,this.setValues(t)}function Ki(t){j.call(this),this.type="MeshNormalMaterial",this.bumpMap=null,this.bumpScale=1,this.normalMap=null,this.normalMapType=0,this.normalScale=new i(1,1),this.displacementMap=null,this.displacementScale=1,this.displacementBias=0,this.wireframe=!1,this.wireframeLinewidth=1,this.morphNormals=this.morphTargets=this.skinning=this.lights=this.fog=!1,this.setValues(t)}function $i(t){j.call(this),this.type="MeshLambertMaterial",this.color=new g(16777215),this.lightMap=this.map=null,this.lightMapIntensity=1,this.aoMap=null,this.aoMapIntensity=1,this.emissive=new g(0),this.emissiveIntensity=1,this.envMap=this.alphaMap=this.specularMap=this.emissiveMap=null,this.combine=0,this.reflectivity=1,this.refractionRatio=.98,this.wireframe=!1,this.wireframeLinewidth=1,this.wireframeLinejoin=this.wireframeLinecap="round",this.morphNormals=this.morphTargets=this.skinning=!1,this.setValues(t)}function tn(t){Ie.call(this),this.type="LineDashedMaterial",this.scale=1,this.dashSize=3,this.gapSize=1,this.setValues(t)}function en(t,e,i){var n=this,r=!1,a=0,o=0,s=void 0;this.onStart=void 0,this.onLoad=t,this.onProgress=e,this.onError=i,this.itemStart=function(t){o++,!1===r&&void 0!==n.onStart&&n.onStart(t,a,o),r=!0},this.itemEnd=function(t){a++,void 0!==n.onProgress&&n.onProgress(t,a,o),a===o&&(r=!1,void 0!==n.onLoad)&&n.onLoad()},this.itemError=function(t){void 0!==n.onError&&n.onError(t)},this.resolveURL=function(t){return s?s(t):t},this.setURLModifier=function(t){return s=t,this}}function nn(t){this.manager=void 0!==t?t:Ea}function rn(t){this.manager=void 0!==t?t:Ea,this._parser=null}function an(t){this.manager=void 0!==t?t:Ea,this._parser=null}function on(t){this.manager=void 0!==t?t:Ea}function sn(t){this.manager=void 0!==t?t:Ea}function hn(t){this.manager=void 0!==t?t:Ea}function cn(){this.type="Curve",this.arcLengthDivisions=200}function ln(t,e,i,n,r,a,o,s){cn.call(this),this.type="EllipseCurve",this.aX=t||0,this.aY=e||0,this.xRadius=i||1,this.yRadius=n||1,this.aStartAngle=r||0,this.aEndAngle=a||2*Math.PI,this.aClockwise=o||!1,this.aRotation=s||0}function un(t,e,i,n,r,a){ln.call(this,t,e,i,i,n,r,a),this.type="ArcCurve"}function pn(){var t=0,e=0,i=0,n=0;return{initCatmullRom:function(r,a,o,s,h){r=h*(o-r),s=h*(s-a),t=a,e=r,i=-3*a+3*o-2*r-s,n=2*a-2*o+r+s},initNonuniformCatmullRom:function(r,a,o,s,h,c,l){r=((a-r)/h-(o-r)/(h+c)+(o-a)/c)*c,s=((o-a)/c-(s-a)/(c+l)+(s-o)/l)*c,t=a,e=r,i=-3*a+3*o-2*r-s,n=2*a-2*o+r+s},calc:function(r){var a=r*r;return t+e*r+i*a+n*a*r}}}function dn(t,e,i,n){cn.call(this),this.type="CatmullRomCurve3",this.points=t||[],this.closed=e||!1,this.curveType=i||"centripetal",this.tension=n||.5}function fn(t,e,i,n,r){e=.5*(n-e),r=.5*(r-i);var a=t*t;return(2*i-2*n+e+r)*t*a+(-3*i+3*n-2*e-r)*a+e*t+i}function mn(t,e,i,n){var r=1-t;return r*r*e+2*(1-t)*t*i+t*t*n}function gn(t,e,i,n,r){var a=1-t,o=1-t;return a*a*a*e+3*o*o*t*i+3*(1-t)*t*t*n+t*t*t*r}function vn(t,e,n,r){cn.call(this),this.type="CubicBezierCurve",this.v0=t||new i,this.v1=e||new i,this.v2=n||new i,this.v3=r||new i}function yn(t,e,i,n){cn.call(this),this.type="CubicBezierCurve3",this.v0=t||new a,this.v1=e||new a,this.v2=i||new a,this.v3=n||new a}function xn(t,e){cn.call(this),this.type="LineCurve",this.v1=t||new i,this.v2=e||new i}function _n(t,e){cn.call(this),this.type="LineCurve3",this.v1=t||new a,this.v2=e||new a}function bn(t,e,n){cn.call(this),this.type="QuadraticBezierCurve",this.v0=t||new i,this.v1=e||new i,this.v2=n||new i}function Mn(t,e,i){cn.call(this),this.type="QuadraticBezierCurve3",this.v0=t||new a,this.v1=e||new a,this.v2=i||new a}function wn(t){cn.call(this),this.type="SplineCurve",this.points=t||[]}function Sn(){cn.call(this),this.type="CurvePath",this.curves=[],this.autoClose=!1}function An(t){Sn.call(this),this.type="Path",this.currentPoint=new i,t&&this.setFromPoints(t)}function Tn(t){An.call(this,t),this.uuid=$r.generateUUID(),this.type="Shape",this.holes=[]}function Ln(t,e){b.call(this),this.type="Light",this.color=new g(t),this.intensity=void 0!==e?e:1,this.receiveShadow=void 0}function En(t,e,i){Ln.call(this,t,i),this.type="HemisphereLight",this.castShadow=void 0,this.position.copy(b.DefaultUp),this.updateMatrix(),this.groundColor=new g(e)}function Cn(t){this.camera=t,this.bias=0,this.radius=1,this.mapSize=new i(512,512),this.map=null,this.matrix=new n}function Pn(){Cn.call(this,new ge(50,1,.5,500))}function On(t,e,i,n,r,a){Ln.call(this,t,e),this.type="SpotLight",this.position.copy(b.DefaultUp),this.updateMatrix(),this.target=new b,Object.defineProperty(this,"power",{get:function(){return this.intensity*Math.PI},set:function(t){this.intensity=t/Math.PI}}),this.distance=void 0!==i?i:0,this.angle=void 0!==n?n:Math.PI/3,this.penumbra=void 0!==r?r:0,this.decay=void 0!==a?a:1,this.shadow=new Pn}function In(t,e,i,n){Ln.call(this,t,e),this.type="PointLight",Object.defineProperty(this,"power",{get:function(){return 4*this.intensity*Math.PI},set:function(t){this.intensity=t/(4*Math.PI)}}),this.distance=void 0!==i?i:0,this.decay=void 0!==n?n:1,this.shadow=new Cn(new ge(90,1,.5,500))}function Nn(){Cn.call(this,new w((-5),5,5,(-5),.5,500))}function Rn(t,e){Ln.call(this,t,e),this.type="DirectionalLight",this.position.copy(b.DefaultUp),this.updateMatrix(),this.target=new b,this.shadow=new Nn}function Dn(t,e){Ln.call(this,t,e),this.type="AmbientLight",this.castShadow=void 0}function Un(t,e,i,n){Ln.call(this,t,e),this.type="RectAreaLight",this.width=void 0!==i?i:10,this.height=void 0!==n?n:10}function Bn(t,e,i,n){this.parameterPositions=t,this._cachedIndex=0,this.resultBuffer=void 0!==n?n:new e.constructor(i),this.sampleValues=e,this.valueSize=i}function zn(t,e,i,n){Bn.call(this,t,e,i,n),this._offsetNext=this._weightNext=this._offsetPrev=this._weightPrev=-0}function Fn(t,e,i,n){Bn.call(this,t,e,i,n)}function Gn(t,e,i,n){Bn.call(this,t,e,i,n)}function Vn(t,e,i,n){if(void 0===t)throw Error("THREE.KeyframeTrack: track name is undefined");if(void 0===e||0===e.length)throw Error("THREE.KeyframeTrack: no keyframes in track named "+t);this.name=t,this.times=Da.convertArray(e,this.TimeBufferType),this.values=Da.convertArray(i,this.ValueBufferType),this.setInterpolation(n||this.DefaultInterpolation)}function kn(t,e,i){Vn.call(this,t,e,i)}function jn(t,e,i,n){Vn.call(this,t,e,i,n)}function Wn(t,e,i,n){Vn.call(this,t,e,i,n)}function Hn(t,e,i,n){Bn.call(this,t,e,i,n)}function Xn(t,e,i,n){Vn.call(this,t,e,i,n)}function qn(t,e,i,n){Vn.call(this,t,e,i,n)}function Yn(t,e,i,n){Vn.call(this,t,e,i,n)}function Zn(t,e,i){this.name=t,this.tracks=i,this.duration=void 0!==e?e:-1,this.uuid=$r.generateUUID(),0>this.duration&&this.resetDuration()}function Jn(t){switch(t.toLowerCase()){case"scalar":case"double":case"float":case"number":case"integer":return Wn;case"vector":case"vector2":case"vector3":case"vector4":return Yn;case"color":return jn;case"quaternion":return Xn;case"bool":case"boolean":return kn;case"string":return qn}throw Error("THREE.KeyframeTrack: Unsupported typeName: "+t)}function Qn(t){if(void 0===t.type)throw Error("THREE.KeyframeTrack: track type undefined, can not parse");var e=Jn(t.type);if(void 0===t.times){var i=[],n=[];Da.flattenJSON(t.keys,i,n,"value"),t.times=i,t.values=n}return void 0!==e.parse?e.parse(t):new e(t.name,t.times,t.values,t.interpolation)}function Kn(t){this.manager=void 0!==t?t:Ea,this.textures={}}function $n(t){this.manager=void 0!==t?t:Ea}function tr(){}function er(t){"boolean"==typeof t&&(t=void 0),this.manager=void 0!==t?t:Ea,this.withCredentials=!1}function ir(t){this.manager=void 0!==t?t:Ea,this.texturePath=""}function nr(t){"undefined"==typeof createImageBitmap&&void 0,"undefined"==typeof fetch&&void 0,this.manager=void 0!==t?t:Ea,this.options=void 0}function rr(){this.type="ShapePath",this.color=new g,this.subPaths=[],this.currentPath=null}function ar(t){this.type="Font",this.data=t}function or(t){this.manager=void 0!==t?t:Ea}function sr(t){this.manager=void 0!==t?t:Ea}function hr(){this.type="StereoCamera",this.aspect=1,this.eyeSep=.064,this.cameraL=new ge,this.cameraL.layers.enable(1),this.cameraL.matrixAutoUpdate=!1,this.cameraR=new ge,this.cameraR.layers.enable(2),this.cameraR.matrixAutoUpdate=!1}function cr(t,e,i){b.call(this),this.type="CubeCamera";var n=new ge(90,1,t,e);n.up.set(0,-1,0),n.lookAt(new a(1,0,0)),this.add(n);var r=new ge(90,1,t,e);r.up.set(0,-1,0),r.lookAt(new a((-1),0,0)),this.add(r);var o=new ge(90,1,t,e);o.up.set(0,0,1),o.lookAt(new a(0,1,0)),this.add(o);var s=new ge(90,1,t,e);s.up.set(0,0,-1),s.lookAt(new a(0,(-1),0)),this.add(s);var h=new ge(90,1,t,e);h.up.set(0,-1,0),h.lookAt(new a(0,0,1)),this.add(h);var c=new ge(90,1,t,e);c.up.set(0,-1,0),c.lookAt(new a(0,0,(-1))),this.add(c),this.renderTarget=new l(i,i,{format:1022,magFilter:1006,minFilter:1006}),this.renderTarget.texture.name="CubeCamera",this.update=function(t,e){null===this.parent&&this.updateMatrixWorld();var i=this.renderTarget,a=i.texture.generateMipmaps;i.texture.generateMipmaps=!1,i.activeCubeFace=0,t.render(e,n,i),i.activeCubeFace=1,t.render(e,r,i),i.activeCubeFace=2,t.render(e,o,i),i.activeCubeFace=3,t.render(e,s,i),i.activeCubeFace=4,t.render(e,h,i),i.texture.generateMipmaps=a,i.activeCubeFace=5,t.render(e,c,i),t.setRenderTarget(null)},this.clear=function(t,e,i,n){for(var r=this.renderTarget,a=0;6>a;a++)r.activeCubeFace=a,t.setRenderTarget(r),t.clear(e,i,n);t.setRenderTarget(null)}}function lr(){b.call(this),this.type="AudioListener",this.context=ka.getContext(),this.gain=this.context.createGain(),this.gain.connect(this.context.destination),this.filter=null}function ur(t){b.call(this),this.type="Audio",this.context=t.context,this.gain=this.context.createGain(),this.gain.connect(t.getInput()),this.autoplay=!1,this.buffer=null,this.loop=!1,this.offset=this.startTime=0,this.playbackRate=1,this.isPlaying=!1,this.hasPlaybackControl=!0,this.sourceType="empty",this.filters=[]}function pr(t){ur.call(this,t),this.panner=this.context.createPanner(),this.panner.connect(this.gain)}function dr(t,e){this.analyser=t.context.createAnalyser(),this.analyser.fftSize=void 0!==e?e:2048,this.data=new Uint8Array(this.analyser.frequencyBinCount),t.getOutput().connect(this.analyser)}function fr(t,e,i){switch(this.binding=t,this.valueSize=i,t=Float64Array,e){case"quaternion":e=this._slerp;break;case"string":case"bool":t=Array,e=this._select;break;default:e=this._lerp}this.buffer=new t(4*i),this._mixBufferRegion=e,this.referenceCount=this.useCount=this.cumulativeWeight=0}function mr(t,e,i){i=i||gr.parseTrackName(e),this._targetGroup=t,this._bindings=t.subscribe_(e,i)}function gr(t,e,i){this.path=e,this.parsedPath=i||gr.parseTrackName(e),this.node=gr.findNode(t,this.parsedPath.nodeName)||t,this.rootNode=t}function vr(){this.uuid=$r.generateUUID(),this._objects=Array.prototype.slice.call(arguments),this.nCachedObjects_=0;var t={};this._indicesByUUID=t;for(var e=0,i=arguments.length;e!==i;++e)t[arguments[e].uuid]=e;this._paths=[],this._parsedPaths=[],this._bindings=[],this._bindingsIndicesByPath={};var n=this;this.stats={objects:{get total(){return n._objects.length},get inUse(){return this.total-n.nCachedObjects_}},get bindingsPerObject(){return n._bindings.length}}}function yr(t,e,i){this._mixer=t,this._clip=e,this._localRoot=i||null,t=e.tracks,e=t.length,i=Array(e);for(var n={endingStart:2400,endingEnd:2400},r=0;r!==e;++r){var a=t[r].createInterpolant(null);i[r]=a,a.settings=n}this._interpolantSettings=n,this._interpolants=i,this._propertyBindings=Array(e),this._weightInterpolant=this._timeScaleInterpolant=this._byClipCacheIndex=this._cacheIndex=null,this.loop=2201,this._loopCount=-1,this._startTime=null,this.time=0,this._effectiveWeight=this.weight=this._effectiveTimeScale=this.timeScale=1,this.repetitions=1/0,this.paused=!1,this.enabled=!0,this.clampWhenFinished=!1,this.zeroSlopeAtEnd=this.zeroSlopeAtStart=!0}function xr(t){this._root=t,this._initMemoryManager(),this.time=this._accuIndex=0,this.timeScale=1}function _r(t,e){"string"==typeof t&&(t=e),this.value=t}function br(){z.call(this),this.type="InstancedBufferGeometry",this.maxInstancedCount=void 0}function Mr(t,e,i){Se.call(this,t,e),this.meshPerAttribute=i||1}function wr(t,e,i,n){"number"==typeof i&&(n=i,void(i=!1)),T.call(this,t,e,i),this.meshPerAttribute=n||1}function Sr(t,e,i,n){this.ray=new X(t,e),this.near=i||0,this.far=n||1/0,this.params={Mesh:{},Line:{},LOD:{},Points:{threshold:1},Sprite:{}},Object.defineProperties(this.params,{PointCloud:{get:function(){return this.Points}}})}function Ar(t,e){return t.distance-e.distance}function Tr(t,e,i,n){if(!1!==t.visible&&(t.raycast(e,i),!0===n)){t=t.children,n=0;for(var r=t.length;n<r;n++)Tr(t[n],e,i,!0)}}function Lr(t){this.autoStart=void 0===t||t,this.elapsedTime=this.oldTime=this.startTime=0,this.running=!1}function Er(t,e,i){return this.radius=void 0!==t?t:1,this.phi=void 0!==e?e:0,this.theta=void 0!==i?i:0,this}function Cr(t,e,i){return this.radius=void 0!==t?t:1,this.theta=void 0!==e?e:0,this.y=void 0!==i?i:0,this}function Pr(t,e){this.min=void 0!==t?t:new i(1/0,1/0),this.max=void 0!==e?e:new i((-(1/0)),(-(1/0)))}function Or(t,e){this.start=void 0!==t?t:new a,this.end=void 0!==e?e:new a}function Ir(t){b.call(this),this.material=t,this.render=function(){}}function Nr(t,e,i,n){this.object=t,this.size=void 0!==e?e:1,t=void 0!==i?i:16711680,n=void 0!==n?n:1,e=0,(i=this.object.geometry)&&i.isGeometry?e=3*i.faces.length:i&&i.isBufferGeometry&&(e=i.attributes.normal.count),i=new z,e=new R(6*e,3),i.addAttribute("position",e),Re.call(this,i,new Ie({color:t,linewidth:n})),this.matrixAutoUpdate=!1,this.update()}function Rr(t,e){b.call(this),this.light=t,this.light.updateMatrixWorld(),this.matrix=t.matrixWorld,this.matrixAutoUpdate=!1,this.color=e,t=new z,e=[0,0,0,0,0,1,0,0,0,1,0,1,0,0,0,-1,0,1,0,0,0,0,1,1,0,0,0,0,-1,1];for(var i=0,n=1;32>i;i++,n++){var r=i/32*Math.PI*2,a=n/32*Math.PI*2;e.push(Math.cos(r),Math.sin(r),1,Math.cos(a),Math.sin(a),1)}t.addAttribute("position",new R(e,3)),e=new Ie({fog:!1}),this.cone=new Re(t,e),this.add(this.cone),this.update()}function Dr(t){var e=[];t&&t.isBone&&e.push(t);for(var i=0;i<t.children.length;i++)e.push.apply(e,Dr(t.children[i]));return e}function Ur(t){for(var e=Dr(t),i=new z,n=[],r=[],a=new g(0,0,1),o=new g(0,1,0),s=0;s<e.length;s++){var h=e[s];h.parent&&h.parent.isBone&&(n.push(0,0,0),n.push(0,0,0),r.push(a.r,a.g,a.b),r.push(o.r,o.g,o.b))}i.addAttribute("position",new R(n,3)),i.addAttribute("color",new R(r,3)),n=new Ie({vertexColors:2,depthTest:!1,depthWrite:!1,transparent:!0}),Re.call(this,i,n),this.root=t,this.bones=e,this.matrix=t.matrixWorld,this.matrixAutoUpdate=!1}function Br(t,e,i){this.light=t,this.light.updateMatrixWorld(),this.color=i,t=new Oi(e,4,2),e=new W({wireframe:!0,fog:!1}),Y.call(this,t,e),this.matrix=this.light.matrixWorld,this.matrixAutoUpdate=!1,this.update()}function zr(t,e){b.call(this),this.light=t,this.light.updateMatrixWorld(),this.matrix=t.matrixWorld,this.matrixAutoUpdate=!1,this.color=e,t=new Ie({fog:!1}),e=new z,e.addAttribute("position",new T(new Float32Array(15),3)),this.line=new Ne(e,t),this.add(this.line),this.update()}function Fr(t,e,i){b.call(this),this.light=t,this.light.updateMatrixWorld(),this.matrix=t.matrixWorld,this.matrixAutoUpdate=!1,this.color=i,t=new Je(e),t.rotateY(.5*Math.PI),this.material=new W({wireframe:!0,fog:!1}),void 0===this.color&&(this.material.vertexColors=2),e=t.getAttribute("position"),e=new Float32Array(3*e.count),t.addAttribute("color",new T(e,3)),this.add(new Y(t,this.material)),this.update()}function Gr(t,e,i,n){t=t||10,e=e||10,i=new g(void 0!==i?i:4473924),n=new g(void 0!==n?n:8947848);var r=e/2,a=t/e,o=t/2;t=[];for(var s=[],h=0,c=0,l=-o;h<=e;h++,l+=a){t.push(-o,0,l,o,0,l),t.push(l,0,-o,l,0,o);var u=h===r?i:n;u.toArray(s,c),c+=3,u.toArray(s,c),c+=3,u.toArray(s,c),c+=3,u.toArray(s,c),c+=3}e=new z,e.addAttribute("position",new R(t,3)),e.addAttribute("color",new R(s,3)),i=new Ie({vertexColors:2}),Re.call(this,e,i)}function Vr(t,e,i,n,r,a){t=t||10,e=e||16,i=i||8,n=n||64,r=new g(void 0!==r?r:4473924),a=new g(void 0!==a?a:8947848);var o,s=[],h=[];for(o=0;o<=e;o++){var c=o/e*2*Math.PI,l=Math.sin(c)*t;c=Math.cos(c)*t,s.push(0,0,0),s.push(l,0,c);var u=1&o?r:a;h.push(u.r,u.g,u.b),h.push(u.r,u.g,u.b)}for(o=0;o<=i;o++){u=1&o?r:a;var p=t-t/i*o;for(e=0;e<n;e++)c=e/n*2*Math.PI,l=Math.sin(c)*p,c=Math.cos(c)*p,s.push(l,0,c),h.push(u.r,u.g,u.b),c=(e+1)/n*2*Math.PI,l=Math.sin(c)*p,c=Math.cos(c)*p,s.push(l,0,c),h.push(u.r,u.g,u.b)}t=new z,t.addAttribute("position",new R(s,3)),t.addAttribute("color",new R(h,3)),s=new Ie({vertexColors:2}),Re.call(this,t,s)}function kr(t,e,i,n){this.object=t,this.size=void 0!==e?e:1,t=void 0!==i?i:16776960,n=void 0!==n?n:1,e=0,(i=this.object.geometry)&&i.isGeometry?e=i.faces.length:void 0,i=new z,e=new R(6*e,3),i.addAttribute("position",e),Re.call(this,i,new Ie({color:t,linewidth:n})),this.matrixAutoUpdate=!1,this.update()}function jr(t,e,i){b.call(this),this.light=t,this.light.updateMatrixWorld(),this.matrix=t.matrixWorld,this.matrixAutoUpdate=!1,this.color=i,void 0===e&&(e=1),t=new z,t.addAttribute("position",new R([-e,e,0,e,e,0,e,-e,0,-e,-e,0,-e,e,0],3)),e=new Ie({fog:!1}),this.lightPlane=new Ne(t,e),this.add(this.lightPlane),t=new z,t.addAttribute("position",new R([0,0,0,0,0,1],3)),this.targetLine=new Ne(t,e),this.add(this.targetLine),this.update()}function Wr(t){function e(t,e,n){i(t,n),i(e,n)}function i(t,e){a.push(0,0,0),o.push(e.r,e.g,e.b),void 0===s[t]&&(s[t]=[]),s[t].push(a.length/3-1)}var n=new z,r=new Ie({color:16777215,vertexColors:1}),a=[],o=[],s={},h=new g(16755200),c=new g(16711680),l=new g(43775),u=new g(16777215),p=new g(3355443);e("n1","n2",h),e("n2","n4",h),e("n4","n3",h),e("n3","n1",h),e("f1","f2",h),e("f2","f4",h),e("f4","f3",h),e("f3","f1",h),e("n1","f1",h),e("n2","f2",h),e("n3","f3",h),e("n4","f4",h),e("p","n1",c),e("p","n2",c),e("p","n3",c),e("p","n4",c),e("u1","u2",l),e("u2","u3",l),e("u3","u1",l),e("c","t",u),e("p","c",p),e("cn1","cn2",p),e("cn3","cn4",p),e("cf1","cf2",p),e("cf3","cf4",p),n.addAttribute("position",new R(a,3)),n.addAttribute("color",new R(o,3)),Re.call(this,n,r),this.camera=t,this.camera.updateProjectionMatrix&&this.camera.updateProjectionMatrix(),this.matrix=t.matrixWorld,this.matrixAutoUpdate=!1,this.pointMap=s,this.update()}function Hr(t,e){this.object=t,void 0===e&&(e=16776960),t=new Uint16Array([0,1,1,2,2,3,3,0,4,5,5,6,6,7,7,4,0,4,1,5,2,6,3,7]);var i=new Float32Array(24),n=new z;n.setIndex(new T(t,1)),n.addAttribute("position",new T(i,3)),Re.call(this,n,new Ie({color:e})),this.matrixAutoUpdate=!1,this.update()}function Xr(t,e){this.type="Box3Helper",this.box=t,t=void 0!==e?e:16776960,e=new Uint16Array([0,1,1,2,2,3,3,0,4,5,5,6,6,7,7,4,0,4,1,5,2,6,3,7]);var i=new z;i.setIndex(new T(e,1)),i.addAttribute("position",new R([1,1,1,-1,1,1,-1,-1,1,1,-1,1,1,1,-1,-1,1,-1,-1,-1,-1,1,-1,-1],3)),Re.call(this,i,new Ie({color:t})),this.geometry.computeBoundingSphere()}function qr(t,e,i){this.type="PlaneHelper",this.plane=t,this.size=void 0===e?1:e,t=void 0!==i?i:16776960,e=new z,e.addAttribute("position",new R([1,-1,1,-1,1,1,-1,-1,1,1,1,1,-1,1,1,-1,-1,1,1,-1,1,1,1,1,0,0,1,0,0,0],3)),e.computeBoundingSphere(),Ne.call(this,e,new Ie({color:t})),e=new z,e.addAttribute("position",new R([1,1,1,-1,1,1,-1,-1,1,1,1,1,-1,-1,1,1,-1,1],3)),e.computeBoundingSphere(),this.add(new Y(e,new W({color:t,opacity:.2,transparent:!0,depthWrite:!1})))}function Yr(t,e,i,n,r,a){b.call(this),void 0===n&&(n=16776960),void 0===i&&(i=1),void 0===r&&(r=.2*i),void 0===a&&(a=.2*r),void 0===ja&&(ja=new z,ja.addAttribute("position",new R([0,0,0,0,1,0],3)),Wa=new Vi(0,.5,1,5,1),Wa.translate(0,-.5,0)),this.position.copy(e),this.line=new Ne(ja,new Ie({color:n})),this.line.matrixAutoUpdate=!1,this.add(this.line),this.cone=new Y(Wa,new W({color:n})),this.cone.matrixAutoUpdate=!1,this.add(this.cone),this.setDirection(t),this.setLength(i,r,a)}function Zr(t){t=t||1;var e=[0,0,0,t,0,0,0,0,0,0,t,0,0,0,0,0,0,t];t=new z,t.addAttribute("position",new R(e,3)),t.addAttribute("color",new R([1,0,0,1,.6,0,0,1,0,.6,1,0,0,0,1,0,.6,1],3)),e=new Ie({vertexColors:2}),Re.call(this,t,e)}function Jr(t){dn.call(this,t),this.type="catmullrom",this.closed=!0}function Qr(t){dn.call(this,t),this.type="catmullrom"}function Kr(t){dn.call(this,t),this.type="catmullrom"}void 0===Number.EPSILON&&(Number.EPSILON=Math.pow(2,-52)),void 0===Number.isInteger&&(Number.isInteger=function(t){return"number"==typeof t&&isFinite(t)&&Math.floor(t)===t}),void 0===Math.sign&&(Math.sign=function(t){return 0>t?-1:0<t?1:+t}),!1=="name"in Function.prototype&&Object.defineProperty(Function.prototype,"name",{get:function(){return this.toString().match(/^\s*function\s*([^\(\s]*)/)[1]}}),void 0===Object.assign&&function(){Object.assign=function(t){if(void 0===t||null===t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),i=1;i<arguments.length;i++){var n=arguments[i];if(void 0!==n&&null!==n)for(var r in n)Object.prototype.hasOwnProperty.call(n,r)&&(e[r]=n[r])}return e}}(),Object.assign(e.prototype,{addEventListener:function(t,e){void 0===this._listeners&&(this._listeners={});var i=this._listeners;void 0===i[t]&&(i[t]=[]),-1===i[t].indexOf(e)&&i[t].push(e)},hasEventListener:function(t,e){if(void 0===this._listeners)return!1;var i=this._listeners;return void 0!==i[t]&&-1!==i[t].indexOf(e)},removeEventListener:function(t,e){void 0!==this._listeners&&(t=this._listeners[t],void 0!==t&&(e=t.indexOf(e),-1!==e&&t.splice(e,1)))},dispatchEvent:function(t){if(void 0!==this._listeners){var e=this._listeners[t.type];if(void 0!==e){t.target=this,e=e.slice(0);for(var i=0,n=e.length;i<n;i++)e[i].call(this,t)}}}});var $r={DEG2RAD:Math.PI/180,RAD2DEG:180/Math.PI,generateUUID:function(){for(var t=[],e=0;256>e;e++)t[e]=(16>e?"0":"")+e.toString(16);return function(){var e=4294967295*Math.random()|0,i=4294967295*Math.random()|0,n=4294967295*Math.random()|0,r=4294967295*Math.random()|0;return(t[255&e]+t[e>>8&255]+t[e>>16&255]+t[e>>24&255]+"-"+t[255&i]+t[i>>8&255]+"-"+t[i>>16&15|64]+t[i>>24&255]+"-"+t[63&n|128]+t[n>>8&255]+"-"+t[n>>16&255]+t[n>>24&255]+t[255&r]+t[r>>8&255]+t[r>>16&255]+t[r>>24&255]).toUpperCase()}}(),clamp:function(t,e,i){return Math.max(e,Math.min(i,t))},euclideanModulo:function(t,e){return(t%e+e)%e},mapLinear:function(t,e,i,n,r){return n+(t-e)*(r-n)/(i-e)},lerp:function(t,e,i){return(1-i)*t+i*e},smoothstep:function(t,e,i){return t<=e?0:t>=i?1:(t=(t-e)/(i-e),t*t*(3-2*t))},smootherstep:function(t,e,i){return t<=e?0:t>=i?1:(t=(t-e)/(i-e),t*t*t*(t*(6*t-15)+10))},randInt:function(t,e){return t+Math.floor(Math.random()*(e-t+1))},randFloat:function(t,e){return t+Math.random()*(e-t)},randFloatSpread:function(t){return t*(.5-Math.random())},degToRad:function(t){return t*$r.DEG2RAD},radToDeg:function(t){return t*$r.RAD2DEG},isPowerOfTwo:function(t){return 0===(t&t-1)&&0!==t},ceilPowerOfTwo:function(t){return Math.pow(2,Math.ceil(Math.log(t)/Math.LN2))},floorPowerOfTwo:function(t){return Math.pow(2,Math.floor(Math.log(t)/Math.LN2))}};Object.defineProperties(i.prototype,{width:{get:function(){return this.x},set:function(t){this.x=t}},height:{get:function(){return this.y},set:function(t){this.y=t}}}),Object.assign(i.prototype,{isVector2:!0,set:function(t,e){return this.x=t,this.y=e,this},setScalar:function(t){return this.y=this.x=t,this},setX:function(t){return this.x=t,this},setY:function(t){return this.y=t,this},setComponent:function(t,e){switch(t){case 0:this.x=e;break;case 1:this.y=e;break;default:throw Error("index is out of range: "+t)}return this},getComponent:function(t){switch(t){case 0:return this.x;case 1:return this.y;default:throw Error("index is out of range: "+t)}},clone:function(){return new this.constructor(this.x,this.y)},copy:function(t){return this.x=t.x,this.y=t.y,this},add:function(t,e){return void 0!==e?this.addVectors(t,e):(this.x+=t.x,this.y+=t.y,this)},addScalar:function(t){return this.x+=t,this.y+=t,this},addVectors:function(t,e){return this.x=t.x+e.x,this.y=t.y+e.y,this},addScaledVector:function(t,e){return this.x+=t.x*e,this.y+=t.y*e,this},sub:function(t,e){return void 0!==e?this.subVectors(t,e):(this.x-=t.x,this.y-=t.y,this)},subScalar:function(t){return this.x-=t,this.y-=t,this},subVectors:function(t,e){return this.x=t.x-e.x,this.y=t.y-e.y,this},multiply:function(t){return this.x*=t.x,this.y*=t.y,this},multiplyScalar:function(t){return this.x*=t,this.y*=t,this},divide:function(t){return this.x/=t.x,this.y/=t.y,this},divideScalar:function(t){return this.multiplyScalar(1/t)},applyMatrix3:function(t){var e=this.x,i=this.y;return t=t.elements,this.x=t[0]*e+t[3]*i+t[6],this.y=t[1]*e+t[4]*i+t[7],this},min:function(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this},max:function(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this},clamp:function(t,e){return this.x=Math.max(t.x,Math.min(e.x,this.x)),this.y=Math.max(t.y,Math.min(e.y,this.y)),this},clampScalar:function(){var t=new i,e=new i;return function(i,n){return t.set(i,i),e.set(n,n),this.clamp(t,e)}}(),clampLength:function(t,e){var i=this.length();return this.divideScalar(i||1).multiplyScalar(Math.max(t,Math.min(e,i)))},floor:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this},ceil:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this},round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this},roundToZero:function(){return this.x=0>this.x?Math.ceil(this.x):Math.floor(this.x),this.y=0>this.y?Math.ceil(this.y):Math.floor(this.y),this},negate:function(){return this.x=-this.x,this.y=-this.y,this},dot:function(t){return this.x*t.x+this.y*t.y},cross:function(t){return this.x*t.y-this.y*t.x},lengthSq:function(){return this.x*this.x+this.y*this.y},length:function(){return Math.sqrt(this.x*this.x+this.y*this.y)},manhattanLength:function(){return Math.abs(this.x)+Math.abs(this.y)},normalize:function(){return this.divideScalar(this.length()||1)},angle:function(){var t=Math.atan2(this.y,this.x);return 0>t&&(t+=2*Math.PI),t},distanceTo:function(t){return Math.sqrt(this.distanceToSquared(t))},distanceToSquared:function(t){var e=this.x-t.x;return t=this.y-t.y,e*e+t*t},manhattanDistanceTo:function(t){return Math.abs(this.x-t.x)+Math.abs(this.y-t.y)},setLength:function(t){return this.normalize().multiplyScalar(t)},lerp:function(t,e){return this.x+=(t.x-this.x)*e,this.y+=(t.y-this.y)*e,this},lerpVectors:function(t,e,i){return this.subVectors(e,t).multiplyScalar(i).add(t)},equals:function(t){return t.x===this.x&&t.y===this.y},fromArray:function(t,e){return void 0===e&&(e=0),this.x=t[e],this.y=t[e+1],this},toArray:function(t,e){return void 0===t&&(t=[]),void 0===e&&(e=0),t[e]=this.x,t[e+1]=this.y,t},fromBufferAttribute:function(t,e,i){return this.x=t.getX(e),this.y=t.getY(e),this},rotateAround:function(t,e){var i=Math.cos(e);e=Math.sin(e);var n=this.x-t.x,r=this.y-t.y;return this.x=n*i-r*e+t.x,this.y=n*e+r*i+t.y,this}}),Object.assign(n.prototype,{isMatrix4:!0,set:function(t,e,i,n,r,a,o,s,h,c,l,u,p,d,f,m){var g=this.elements;return g[0]=t,g[4]=e,g[8]=i,g[12]=n,g[1]=r,g[5]=a,g[9]=o,g[13]=s,g[2]=h,g[6]=c,g[10]=l,g[14]=u,g[3]=p,g[7]=d,g[11]=f,g[15]=m,this},identity:function(){return this.set(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1),this},clone:function(){return(new n).fromArray(this.elements)},copy:function(t){var e=this.elements;return t=t.elements,e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],e[6]=t[6],e[7]=t[7],e[8]=t[8],e[9]=t[9],e[10]=t[10],e[11]=t[11],e[12]=t[12],e[13]=t[13],e[14]=t[14],e[15]=t[15],this},copyPosition:function(t){var e=this.elements;return t=t.elements,e[12]=t[12],e[13]=t[13],e[14]=t[14],this},extractBasis:function(t,e,i){return t.setFromMatrixColumn(this,0),e.setFromMatrixColumn(this,1),i.setFromMatrixColumn(this,2),this},makeBasis:function(t,e,i){return this.set(t.x,e.x,i.x,0,t.y,e.y,i.y,0,t.z,e.z,i.z,0,0,0,0,1),this},extractRotation:function(){var t=new a;return function(e){var i=this.elements,n=e.elements,r=1/t.setFromMatrixColumn(e,0).length(),a=1/t.setFromMatrixColumn(e,1).length();return e=1/t.setFromMatrixColumn(e,2).length(),i[0]=n[0]*r,i[1]=n[1]*r,i[2]=n[2]*r,i[3]=0,i[4]=n[4]*a,i[5]=n[5]*a,i[6]=n[6]*a,i[7]=0,i[8]=n[8]*e,i[9]=n[9]*e,i[10]=n[10]*e,i[11]=0,i[12]=0,i[13]=0,i[14]=0,i[15]=1,this}}(),makeRotationFromEuler:function(t){t&&t.isEuler||void 0;var e=this.elements,i=t.x,n=t.y,r=t.z,a=Math.cos(i);i=Math.sin(i);var o=Math.cos(n);n=Math.sin(n);var s=Math.cos(r);if(r=Math.sin(r),"XYZ"===t.order){t=a*s;var h=a*r,c=i*s,l=i*r;e[0]=o*s,e[4]=-o*r,e[8]=n,e[1]=h+c*n,e[5]=t-l*n,e[9]=-i*o,e[2]=l-t*n,e[6]=c+h*n,e[10]=a*o}else"YXZ"===t.order?(t=o*s,h=o*r,c=n*s,l=n*r,e[0]=t+l*i,e[4]=c*i-h,e[8]=a*n,e[1]=a*r,e[5]=a*s,e[9]=-i,e[2]=h*i-c,e[6]=l+t*i,e[10]=a*o):"ZXY"===t.order?(t=o*s,h=o*r,c=n*s,l=n*r,e[0]=t-l*i,e[4]=-a*r,e[8]=c+h*i,e[1]=h+c*i,e[5]=a*s,e[9]=l-t*i,e[2]=-a*n,e[6]=i,e[10]=a*o):"ZYX"===t.order?(t=a*s,h=a*r,c=i*s,l=i*r,e[0]=o*s,e[4]=c*n-h,e[8]=t*n+l,e[1]=o*r,e[5]=l*n+t,e[9]=h*n-c,e[2]=-n,e[6]=i*o,e[10]=a*o):"YZX"===t.order?(t=a*o,h=a*n,c=i*o,l=i*n,e[0]=o*s,e[4]=l-t*r,e[8]=c*r+h,e[1]=r,e[5]=a*s,e[9]=-i*s,e[2]=-n*s,e[6]=h*r+c,e[10]=t-l*r):"XZY"===t.order&&(t=a*o,h=a*n,c=i*o,l=i*n,e[0]=o*s,e[4]=-r,e[8]=n*s,e[1]=t*r+l,e[5]=a*s,e[9]=h*r-c,e[2]=c*r-h,e[6]=i*s,e[10]=l*r+t);return e[3]=0,e[7]=0,e[11]=0,e[12]=0,e[13]=0,e[14]=0,e[15]=1,this},makeRotationFromQuaternion:function(){var t=new a(0,0,0),e=new a(1,1,1);return function(i){return this.compose(t,i,e)}}(),lookAt:function(){var t=new a,e=new a,i=new a;return function(n,r,a){var o=this.elements;return i.subVectors(n,r),0===i.lengthSq()&&(i.z=1),i.normalize(),t.crossVectors(a,i),0===t.lengthSq()&&(1===Math.abs(a.z)?i.x+=1e-4:i.z+=1e-4,i.normalize(),t.crossVectors(a,i)),t.normalize(),e.crossVectors(i,t),o[0]=t.x,o[4]=e.x,o[8]=i.x,o[1]=t.y,o[5]=e.y,o[9]=i.y,o[2]=t.z,o[6]=e.z,o[10]=i.z,this}}(),multiply:function(t,e){return void 0!==e?this.multiplyMatrices(t,e):this.multiplyMatrices(this,t)},premultiply:function(t){return this.multiplyMatrices(t,this)},multiplyMatrices:function(t,e){var i=t.elements,n=e.elements;e=this.elements,t=i[0];var r=i[4],a=i[8],o=i[12],s=i[1],h=i[5],c=i[9],l=i[13],u=i[2],p=i[6],d=i[10],f=i[14],m=i[3],g=i[7],v=i[11];i=i[15];var y=n[0],x=n[4],_=n[8],b=n[12],M=n[1],w=n[5],S=n[9],A=n[13],T=n[2],L=n[6],E=n[10],C=n[14],P=n[3],O=n[7],I=n[11];return n=n[15],e[0]=t*y+r*M+a*T+o*P,e[4]=t*x+r*w+a*L+o*O,e[8]=t*_+r*S+a*E+o*I,e[12]=t*b+r*A+a*C+o*n,e[1]=s*y+h*M+c*T+l*P,e[5]=s*x+h*w+c*L+l*O,e[9]=s*_+h*S+c*E+l*I,e[13]=s*b+h*A+c*C+l*n,e[2]=u*y+p*M+d*T+f*P,e[6]=u*x+p*w+d*L+f*O,e[10]=u*_+p*S+d*E+f*I,e[14]=u*b+p*A+d*C+f*n,e[3]=m*y+g*M+v*T+i*P,e[7]=m*x+g*w+v*L+i*O,e[11]=m*_+g*S+v*E+i*I,e[15]=m*b+g*A+v*C+i*n,this},multiplyScalar:function(t){var e=this.elements;return e[0]*=t,e[4]*=t,e[8]*=t,e[12]*=t,e[1]*=t,e[5]*=t,e[9]*=t,e[13]*=t,e[2]*=t,e[6]*=t,e[10]*=t,e[14]*=t,e[3]*=t,e[7]*=t,e[11]*=t,e[15]*=t,this},applyToBufferAttribute:function(){var t=new a;return function(e){for(var i=0,n=e.count;i<n;i++)t.x=e.getX(i),t.y=e.getY(i),t.z=e.getZ(i),t.applyMatrix4(this),e.setXYZ(i,t.x,t.y,t.z);return e}}(),determinant:function(){var t=this.elements,e=t[0],i=t[4],n=t[8],r=t[12],a=t[1],o=t[5],s=t[9],h=t[13],c=t[2],l=t[6],u=t[10],p=t[14];return t[3]*(+r*s*l-n*h*l-r*o*u+i*h*u+n*o*p-i*s*p)+t[7]*(+e*s*p-e*h*u+r*a*u-n*a*p+n*h*c-r*s*c)+t[11]*(+e*h*l-e*o*p-r*a*l+i*a*p+r*o*c-i*h*c)+t[15]*(-n*o*c-e*s*l+e*o*u+n*a*l-i*a*u+i*s*c)},transpose:function(){var t=this.elements,e=t[1];return t[1]=t[4],t[4]=e,e=t[2],t[2]=t[8],t[8]=e,e=t[6],t[6]=t[9],t[9]=e,e=t[3],t[3]=t[12],t[12]=e,e=t[7],t[7]=t[13],t[13]=e,e=t[11],t[11]=t[14],t[14]=e,this},setPosition:function(t){var e=this.elements;return e[12]=t.x,e[13]=t.y,e[14]=t.z,this},getInverse:function(t,e){var i=this.elements,n=t.elements;t=n[0];var r=n[1],a=n[2],o=n[3],s=n[4],h=n[5],c=n[6],l=n[7],u=n[8],p=n[9],d=n[10],f=n[11],m=n[12],g=n[13],v=n[14];
n=n[15];var y=p*v*l-g*d*l+g*c*f-h*v*f-p*c*n+h*d*n,x=m*d*l-u*v*l-m*c*f+s*v*f+u*c*n-s*d*n,_=u*g*l-m*p*l+m*h*f-s*g*f-u*h*n+s*p*n,b=m*p*c-u*g*c-m*h*d+s*g*d+u*h*v-s*p*v,M=t*y+r*x+a*_+o*b;if(0===M){if(!0===e)throw Error("THREE.Matrix4: .getInverse() can't invert matrix, determinant is 0");return this.identity()}return e=1/M,i[0]=y*e,i[1]=(g*d*o-p*v*o-g*a*f+r*v*f+p*a*n-r*d*n)*e,i[2]=(h*v*o-g*c*o+g*a*l-r*v*l-h*a*n+r*c*n)*e,i[3]=(p*c*o-h*d*o-p*a*l+r*d*l+h*a*f-r*c*f)*e,i[4]=x*e,i[5]=(u*v*o-m*d*o+m*a*f-t*v*f-u*a*n+t*d*n)*e,i[6]=(m*c*o-s*v*o-m*a*l+t*v*l+s*a*n-t*c*n)*e,i[7]=(s*d*o-u*c*o+u*a*l-t*d*l-s*a*f+t*c*f)*e,i[8]=_*e,i[9]=(m*p*o-u*g*o-m*r*f+t*g*f+u*r*n-t*p*n)*e,i[10]=(s*g*o-m*h*o+m*r*l-t*g*l-s*r*n+t*h*n)*e,i[11]=(u*h*o-s*p*o-u*r*l+t*p*l+s*r*f-t*h*f)*e,i[12]=b*e,i[13]=(u*g*a-m*p*a+m*r*d-t*g*d-u*r*v+t*p*v)*e,i[14]=(m*h*a-s*g*a-m*r*c+t*g*c+s*r*v-t*h*v)*e,i[15]=(s*p*a-u*h*a+u*r*c-t*p*c-s*r*d+t*h*d)*e,this},scale:function(t){var e=this.elements,i=t.x,n=t.y;return t=t.z,e[0]*=i,e[4]*=n,e[8]*=t,e[1]*=i,e[5]*=n,e[9]*=t,e[2]*=i,e[6]*=n,e[10]*=t,e[3]*=i,e[7]*=n,e[11]*=t,this},getMaxScaleOnAxis:function(){var t=this.elements;return Math.sqrt(Math.max(t[0]*t[0]+t[1]*t[1]+t[2]*t[2],t[4]*t[4]+t[5]*t[5]+t[6]*t[6],t[8]*t[8]+t[9]*t[9]+t[10]*t[10]))},makeTranslation:function(t,e,i){return this.set(1,0,0,t,0,1,0,e,0,0,1,i,0,0,0,1),this},makeRotationX:function(t){var e=Math.cos(t);return t=Math.sin(t),this.set(1,0,0,0,0,e,-t,0,0,t,e,0,0,0,0,1),this},makeRotationY:function(t){var e=Math.cos(t);return t=Math.sin(t),this.set(e,0,t,0,0,1,0,0,-t,0,e,0,0,0,0,1),this},makeRotationZ:function(t){var e=Math.cos(t);return t=Math.sin(t),this.set(e,-t,0,0,t,e,0,0,0,0,1,0,0,0,0,1),this},makeRotationAxis:function(t,e){var i=Math.cos(e);e=Math.sin(e);var n=1-i,r=t.x,a=t.y;t=t.z;var o=n*r,s=n*a;return this.set(o*r+i,o*a-e*t,o*t+e*a,0,o*a+e*t,s*a+i,s*t-e*r,0,o*t-e*a,s*t+e*r,n*t*t+i,0,0,0,0,1),this},makeScale:function(t,e,i){return this.set(t,0,0,0,0,e,0,0,0,0,i,0,0,0,0,1),this},makeShear:function(t,e,i){return this.set(1,e,i,0,t,1,i,0,t,e,1,0,0,0,0,1),this},compose:function(t,e,i){var n=this.elements,r=e._x,a=e._y,o=e._z,s=e._w,h=r+r,c=a+a,l=o+o;e=r*h;var u=r*c;r*=l;var p=a*c;a*=l,o*=l,h*=s,c*=s,s*=l,l=i.x;var d=i.y;return i=i.z,n[0]=(1-(p+o))*l,n[1]=(u+s)*l,n[2]=(r-c)*l,n[3]=0,n[4]=(u-s)*d,n[5]=(1-(e+o))*d,n[6]=(a+h)*d,n[7]=0,n[8]=(r+c)*i,n[9]=(a-h)*i,n[10]=(1-(e+p))*i,n[11]=0,n[12]=t.x,n[13]=t.y,n[14]=t.z,n[15]=1,this},decompose:function(){var t=new a,e=new n;return function(i,n,r){var a=this.elements,o=t.set(a[0],a[1],a[2]).length(),s=t.set(a[4],a[5],a[6]).length(),h=t.set(a[8],a[9],a[10]).length();0>this.determinant()&&(o=-o),i.x=a[12],i.y=a[13],i.z=a[14],e.copy(this),i=1/o,a=1/s;var c=1/h;return e.elements[0]*=i,e.elements[1]*=i,e.elements[2]*=i,e.elements[4]*=a,e.elements[5]*=a,e.elements[6]*=a,e.elements[8]*=c,e.elements[9]*=c,e.elements[10]*=c,n.setFromRotationMatrix(e),r.x=o,r.y=s,r.z=h,this}}(),makePerspective:function(t,e,i,n,r,a){var o=this.elements;return o[0]=2*r/(e-t),o[4]=0,o[8]=(e+t)/(e-t),o[12]=0,o[1]=0,o[5]=2*r/(i-n),o[9]=(i+n)/(i-n),o[13]=0,o[2]=0,o[6]=0,o[10]=-(a+r)/(a-r),o[14]=-2*a*r/(a-r),o[3]=0,o[7]=0,o[11]=-1,o[15]=0,this},makeOrthographic:function(t,e,i,n,r,a){var o=this.elements,s=1/(e-t),h=1/(i-n),c=1/(a-r);return o[0]=2*s,o[4]=0,o[8]=0,o[12]=-((e+t)*s),o[1]=0,o[5]=2*h,o[9]=0,o[13]=-((i+n)*h),o[2]=0,o[6]=0,o[10]=-2*c,o[14]=-((a+r)*c),o[3]=0,o[7]=0,o[11]=0,o[15]=1,this},equals:function(t){var e=this.elements;t=t.elements;for(var i=0;16>i;i++)if(e[i]!==t[i])return!1;return!0},fromArray:function(t,e){void 0===e&&(e=0);for(var i=0;16>i;i++)this.elements[i]=t[i+e];return this},toArray:function(t,e){void 0===t&&(t=[]),void 0===e&&(e=0);var i=this.elements;return t[e]=i[0],t[e+1]=i[1],t[e+2]=i[2],t[e+3]=i[3],t[e+4]=i[4],t[e+5]=i[5],t[e+6]=i[6],t[e+7]=i[7],t[e+8]=i[8],t[e+9]=i[9],t[e+10]=i[10],t[e+11]=i[11],t[e+12]=i[12],t[e+13]=i[13],t[e+14]=i[14],t[e+15]=i[15],t}}),Object.assign(r,{slerp:function(t,e,i,n){return i.copy(t).slerp(e,n)},slerpFlat:function(t,e,i,n,r,a,o){var s=i[n+0],h=i[n+1],c=i[n+2];i=i[n+3],n=r[a+0];var l=r[a+1],u=r[a+2];if(r=r[a+3],i!==r||s!==n||h!==l||c!==u){a=1-o;var p=s*n+h*l+c*u+i*r,d=0<=p?1:-1,f=1-p*p;f>Number.EPSILON&&(f=Math.sqrt(f),p=Math.atan2(f,p*d),a=Math.sin(a*p)/f,o=Math.sin(o*p)/f),d*=o,s=s*a+n*d,h=h*a+l*d,c=c*a+u*d,i=i*a+r*d,a===1-o&&(o=1/Math.sqrt(s*s+h*h+c*c+i*i),s*=o,h*=o,c*=o,i*=o)}t[e]=s,t[e+1]=h,t[e+2]=c,t[e+3]=i}}),Object.defineProperties(r.prototype,{x:{get:function(){return this._x},set:function(t){this._x=t,this.onChangeCallback()}},y:{get:function(){return this._y},set:function(t){this._y=t,this.onChangeCallback()}},z:{get:function(){return this._z},set:function(t){this._z=t,this.onChangeCallback()}},w:{get:function(){return this._w},set:function(t){this._w=t,this.onChangeCallback()}}}),Object.assign(r.prototype,{set:function(t,e,i,n){return this._x=t,this._y=e,this._z=i,this._w=n,this.onChangeCallback(),this},clone:function(){return new this.constructor(this._x,this._y,this._z,this._w)},copy:function(t){return this._x=t.x,this._y=t.y,this._z=t.z,this._w=t.w,this.onChangeCallback(),this},setFromEuler:function(t,e){if(!t||!t.isEuler)throw Error("THREE.Quaternion: .setFromEuler() now expects an Euler rotation rather than a Vector3 and order.");var i=t._x,n=t._y,r=t._z;t=t.order;var a=Math.cos,o=Math.sin,s=a(i/2),h=a(n/2);return a=a(r/2),i=o(i/2),n=o(n/2),r=o(r/2),"XYZ"===t?(this._x=i*h*a+s*n*r,this._y=s*n*a-i*h*r,this._z=s*h*r+i*n*a,this._w=s*h*a-i*n*r):"YXZ"===t?(this._x=i*h*a+s*n*r,this._y=s*n*a-i*h*r,this._z=s*h*r-i*n*a,this._w=s*h*a+i*n*r):"ZXY"===t?(this._x=i*h*a-s*n*r,this._y=s*n*a+i*h*r,this._z=s*h*r+i*n*a,this._w=s*h*a-i*n*r):"ZYX"===t?(this._x=i*h*a-s*n*r,this._y=s*n*a+i*h*r,this._z=s*h*r-i*n*a,this._w=s*h*a+i*n*r):"YZX"===t?(this._x=i*h*a+s*n*r,this._y=s*n*a+i*h*r,this._z=s*h*r-i*n*a,this._w=s*h*a-i*n*r):"XZY"===t&&(this._x=i*h*a-s*n*r,this._y=s*n*a-i*h*r,this._z=s*h*r+i*n*a,this._w=s*h*a+i*n*r),!1!==e&&this.onChangeCallback(),this},setFromAxisAngle:function(t,e){e/=2;var i=Math.sin(e);return this._x=t.x*i,this._y=t.y*i,this._z=t.z*i,this._w=Math.cos(e),this.onChangeCallback(),this},setFromRotationMatrix:function(t){var e=t.elements,i=e[0];t=e[4];var n=e[8],r=e[1],a=e[5],o=e[9],s=e[2],h=e[6];e=e[10];var c=i+a+e;return 0<c?(i=.5/Math.sqrt(c+1),this._w=.25/i,this._x=(h-o)*i,this._y=(n-s)*i,this._z=(r-t)*i):i>a&&i>e?(i=2*Math.sqrt(1+i-a-e),this._w=(h-o)/i,this._x=.25*i,this._y=(t+r)/i,this._z=(n+s)/i):a>e?(i=2*Math.sqrt(1+a-i-e),this._w=(n-s)/i,this._x=(t+r)/i,this._y=.25*i,this._z=(o+h)/i):(i=2*Math.sqrt(1+e-i-a),this._w=(r-t)/i,this._x=(n+s)/i,this._y=(o+h)/i,this._z=.25*i),this.onChangeCallback(),this},setFromUnitVectors:function(){var t,e=new a;return function(i,n){return void 0===e&&(e=new a),t=i.dot(n)+1,1e-6>t?(t=0,Math.abs(i.x)>Math.abs(i.z)?e.set(-i.y,i.x,0):e.set(0,-i.z,i.y)):e.crossVectors(i,n),this._x=e.x,this._y=e.y,this._z=e.z,this._w=t,this.normalize()}}(),angleTo:function(t){return 2*Math.acos(Math.abs($r.clamp(this.dot(t),-1,1)))},rotateTowards:function(t,e){var i=this.angleTo(t);return 0===i?this:(this.slerp(t,Math.min(1,e/i)),this)},inverse:function(){return this.conjugate()},conjugate:function(){return this._x*=-1,this._y*=-1,this._z*=-1,this.onChangeCallback(),this},dot:function(t){return this._x*t._x+this._y*t._y+this._z*t._z+this._w*t._w},lengthSq:function(){return this._x*this._x+this._y*this._y+this._z*this._z+this._w*this._w},length:function(){return Math.sqrt(this._x*this._x+this._y*this._y+this._z*this._z+this._w*this._w)},normalize:function(){var t=this.length();return 0===t?(this._z=this._y=this._x=0,this._w=1):(t=1/t,this._x*=t,this._y*=t,this._z*=t,this._w*=t),this.onChangeCallback(),this},multiply:function(t,e){return void 0!==e?this.multiplyQuaternions(t,e):this.multiplyQuaternions(this,t)},premultiply:function(t){return this.multiplyQuaternions(t,this)},multiplyQuaternions:function(t,e){var i=t._x,n=t._y,r=t._z;t=t._w;var a=e._x,o=e._y,s=e._z;return e=e._w,this._x=i*e+t*a+n*s-r*o,this._y=n*e+t*o+r*a-i*s,this._z=r*e+t*s+i*o-n*a,this._w=t*e-i*a-n*o-r*s,this.onChangeCallback(),this},slerp:function(t,e){if(0===e)return this;if(1===e)return this.copy(t);var i=this._x,n=this._y,r=this._z,a=this._w,o=a*t._w+i*t._x+n*t._y+r*t._z;if(0>o?(this._w=-t._w,this._x=-t._x,this._y=-t._y,this._z=-t._z,o=-o):this.copy(t),1<=o)return this._w=a,this._x=i,this._y=n,this._z=r,this;if(t=1-o*o,t<=Number.EPSILON)return o=1-e,this._w=o*a+e*this._w,this._x=o*i+e*this._x,this._y=o*n+e*this._y,this._z=o*r+e*this._z,this.normalize();t=Math.sqrt(t);var s=Math.atan2(t,o);return o=Math.sin((1-e)*s)/t,e=Math.sin(e*s)/t,this._w=a*o+this._w*e,this._x=i*o+this._x*e,this._y=n*o+this._y*e,this._z=r*o+this._z*e,this.onChangeCallback(),this},equals:function(t){return t._x===this._x&&t._y===this._y&&t._z===this._z&&t._w===this._w},fromArray:function(t,e){return void 0===e&&(e=0),this._x=t[e],this._y=t[e+1],this._z=t[e+2],this._w=t[e+3],this.onChangeCallback(),this},toArray:function(t,e){return void 0===t&&(t=[]),void 0===e&&(e=0),t[e]=this._x,t[e+1]=this._y,t[e+2]=this._z,t[e+3]=this._w,t},onChange:function(t){return this.onChangeCallback=t,this},onChangeCallback:function(){}}),Object.assign(a.prototype,{isVector3:!0,set:function(t,e,i){return this.x=t,this.y=e,this.z=i,this},setScalar:function(t){return this.z=this.y=this.x=t,this},setX:function(t){return this.x=t,this},setY:function(t){return this.y=t,this},setZ:function(t){return this.z=t,this},setComponent:function(t,e){switch(t){case 0:this.x=e;break;case 1:this.y=e;break;case 2:this.z=e;break;default:throw Error("index is out of range: "+t)}return this},getComponent:function(t){switch(t){case 0:return this.x;case 1:return this.y;case 2:return this.z;default:throw Error("index is out of range: "+t)}},clone:function(){return new this.constructor(this.x,this.y,this.z)},copy:function(t){return this.x=t.x,this.y=t.y,this.z=t.z,this},add:function(t,e){return void 0!==e?this.addVectors(t,e):(this.x+=t.x,this.y+=t.y,this.z+=t.z,this)},addScalar:function(t){return this.x+=t,this.y+=t,this.z+=t,this},addVectors:function(t,e){return this.x=t.x+e.x,this.y=t.y+e.y,this.z=t.z+e.z,this},addScaledVector:function(t,e){return this.x+=t.x*e,this.y+=t.y*e,this.z+=t.z*e,this},sub:function(t,e){return void 0!==e?this.subVectors(t,e):(this.x-=t.x,this.y-=t.y,this.z-=t.z,this)},subScalar:function(t){return this.x-=t,this.y-=t,this.z-=t,this},subVectors:function(t,e){return this.x=t.x-e.x,this.y=t.y-e.y,this.z=t.z-e.z,this},multiply:function(t,e){return void 0!==e?this.multiplyVectors(t,e):(this.x*=t.x,this.y*=t.y,this.z*=t.z,this)},multiplyScalar:function(t){return this.x*=t,this.y*=t,this.z*=t,this},multiplyVectors:function(t,e){return this.x=t.x*e.x,this.y=t.y*e.y,this.z=t.z*e.z,this},applyEuler:function(){var t=new r;return function(e){return e&&e.isEuler||void 0,this.applyQuaternion(t.setFromEuler(e))}}(),applyAxisAngle:function(){var t=new r;return function(e,i){return this.applyQuaternion(t.setFromAxisAngle(e,i))}}(),applyMatrix3:function(t){var e=this.x,i=this.y,n=this.z;return t=t.elements,this.x=t[0]*e+t[3]*i+t[6]*n,this.y=t[1]*e+t[4]*i+t[7]*n,this.z=t[2]*e+t[5]*i+t[8]*n,this},applyMatrix4:function(t){var e=this.x,i=this.y,n=this.z;t=t.elements;var r=1/(t[3]*e+t[7]*i+t[11]*n+t[15]);return this.x=(t[0]*e+t[4]*i+t[8]*n+t[12])*r,this.y=(t[1]*e+t[5]*i+t[9]*n+t[13])*r,this.z=(t[2]*e+t[6]*i+t[10]*n+t[14])*r,this},applyQuaternion:function(t){var e=this.x,i=this.y,n=this.z,r=t.x,a=t.y,o=t.z;t=t.w;var s=t*e+a*n-o*i,h=t*i+o*e-r*n,c=t*n+r*i-a*e;return e=-r*e-a*i-o*n,this.x=s*t+e*-r+h*-o-c*-a,this.y=h*t+e*-a+c*-r-s*-o,this.z=c*t+e*-o+s*-a-h*-r,this},project:function(t){return this.applyMatrix4(t.matrixWorldInverse).applyMatrix4(t.projectionMatrix)},unproject:function(){var t=new n;return function(e){return this.applyMatrix4(t.getInverse(e.projectionMatrix)).applyMatrix4(e.matrixWorld)}}(),transformDirection:function(t){var e=this.x,i=this.y,n=this.z;return t=t.elements,this.x=t[0]*e+t[4]*i+t[8]*n,this.y=t[1]*e+t[5]*i+t[9]*n,this.z=t[2]*e+t[6]*i+t[10]*n,this.normalize()},divide:function(t){return this.x/=t.x,this.y/=t.y,this.z/=t.z,this},divideScalar:function(t){return this.multiplyScalar(1/t)},min:function(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this.z=Math.min(this.z,t.z),this},max:function(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this.z=Math.max(this.z,t.z),this},clamp:function(t,e){return this.x=Math.max(t.x,Math.min(e.x,this.x)),this.y=Math.max(t.y,Math.min(e.y,this.y)),this.z=Math.max(t.z,Math.min(e.z,this.z)),this},clampScalar:function(){var t=new a,e=new a;return function(i,n){return t.set(i,i,i),e.set(n,n,n),this.clamp(t,e)}}(),clampLength:function(t,e){var i=this.length();return this.divideScalar(i||1).multiplyScalar(Math.max(t,Math.min(e,i)))},floor:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this.z=Math.floor(this.z),this},ceil:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this.z=Math.ceil(this.z),this},round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this.z=Math.round(this.z),this},roundToZero:function(){return this.x=0>this.x?Math.ceil(this.x):Math.floor(this.x),this.y=0>this.y?Math.ceil(this.y):Math.floor(this.y),this.z=0>this.z?Math.ceil(this.z):Math.floor(this.z),this},negate:function(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this},dot:function(t){return this.x*t.x+this.y*t.y+this.z*t.z},lengthSq:function(){return this.x*this.x+this.y*this.y+this.z*this.z},length:function(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z)},manhattanLength:function(){return Math.abs(this.x)+Math.abs(this.y)+Math.abs(this.z)},normalize:function(){return this.divideScalar(this.length()||1)},setLength:function(t){return this.normalize().multiplyScalar(t)},lerp:function(t,e){return this.x+=(t.x-this.x)*e,this.y+=(t.y-this.y)*e,this.z+=(t.z-this.z)*e,this},lerpVectors:function(t,e,i){return this.subVectors(e,t).multiplyScalar(i).add(t)},cross:function(t,e){return void 0!==e?this.crossVectors(t,e):this.crossVectors(this,t)},crossVectors:function(t,e){var i=t.x,n=t.y;t=t.z;var r=e.x,a=e.y;return e=e.z,this.x=n*e-t*a,this.y=t*r-i*e,this.z=i*a-n*r,this},projectOnVector:function(t){var e=t.dot(this)/t.lengthSq();return this.copy(t).multiplyScalar(e)},projectOnPlane:function(){var t=new a;return function(e){return t.copy(this).projectOnVector(e),this.sub(t)}}(),reflect:function(){var t=new a;return function(e){return this.sub(t.copy(e).multiplyScalar(2*this.dot(e)))}}(),angleTo:function(t){return t=this.dot(t)/Math.sqrt(this.lengthSq()*t.lengthSq()),Math.acos($r.clamp(t,-1,1))},distanceTo:function(t){return Math.sqrt(this.distanceToSquared(t))},distanceToSquared:function(t){var e=this.x-t.x,i=this.y-t.y;return t=this.z-t.z,e*e+i*i+t*t},manhattanDistanceTo:function(t){return Math.abs(this.x-t.x)+Math.abs(this.y-t.y)+Math.abs(this.z-t.z)},setFromSpherical:function(t){return this.setFromSphericalCoords(t.radius,t.phi,t.theta)},setFromSphericalCoords:function(t,e,i){var n=Math.sin(e)*t;return this.x=n*Math.sin(i),this.y=Math.cos(e)*t,this.z=n*Math.cos(i),this},setFromCylindrical:function(t){return this.setFromCylindricalCoords(t.radius,t.theta,t.y)},setFromCylindricalCoords:function(t,e,i){return this.x=t*Math.sin(e),this.y=i,this.z=t*Math.cos(e),this},setFromMatrixPosition:function(t){return t=t.elements,this.x=t[12],this.y=t[13],this.z=t[14],this},setFromMatrixScale:function(t){var e=this.setFromMatrixColumn(t,0).length(),i=this.setFromMatrixColumn(t,1).length();return t=this.setFromMatrixColumn(t,2).length(),this.x=e,this.y=i,this.z=t,this},setFromMatrixColumn:function(t,e){return this.fromArray(t.elements,4*e)},equals:function(t){return t.x===this.x&&t.y===this.y&&t.z===this.z},fromArray:function(t,e){return void 0===e&&(e=0),this.x=t[e],this.y=t[e+1],this.z=t[e+2],this},toArray:function(t,e){return void 0===t&&(t=[]),void 0===e&&(e=0),t[e]=this.x,t[e+1]=this.y,t[e+2]=this.z,t},fromBufferAttribute:function(t,e,i){return this.x=t.getX(e),this.y=t.getY(e),this.z=t.getZ(e),this}}),Object.assign(o.prototype,{isMatrix3:!0,set:function(t,e,i,n,r,a,o,s,h){var c=this.elements;return c[0]=t,c[1]=n,c[2]=o,c[3]=e,c[4]=r,c[5]=s,c[6]=i,c[7]=a,c[8]=h,this},identity:function(){return this.set(1,0,0,0,1,0,0,0,1),this},clone:function(){return(new this.constructor).fromArray(this.elements)},copy:function(t){var e=this.elements;return t=t.elements,e[0]=t[0],e[1]=t[1],e[2]=t[2],e[3]=t[3],e[4]=t[4],e[5]=t[5],e[6]=t[6],e[7]=t[7],e[8]=t[8],this},setFromMatrix4:function(t){return t=t.elements,this.set(t[0],t[4],t[8],t[1],t[5],t[9],t[2],t[6],t[10]),this},applyToBufferAttribute:function(){var t=new a;return function(e){for(var i=0,n=e.count;i<n;i++)t.x=e.getX(i),t.y=e.getY(i),t.z=e.getZ(i),t.applyMatrix3(this),e.setXYZ(i,t.x,t.y,t.z);return e}}(),multiply:function(t){return this.multiplyMatrices(this,t)},premultiply:function(t){return this.multiplyMatrices(t,this)},multiplyMatrices:function(t,e){var i=t.elements,n=e.elements;e=this.elements,t=i[0];var r=i[3],a=i[6],o=i[1],s=i[4],h=i[7],c=i[2],l=i[5];i=i[8];var u=n[0],p=n[3],d=n[6],f=n[1],m=n[4],g=n[7],v=n[2],y=n[5];return n=n[8],e[0]=t*u+r*f+a*v,e[3]=t*p+r*m+a*y,e[6]=t*d+r*g+a*n,e[1]=o*u+s*f+h*v,e[4]=o*p+s*m+h*y,e[7]=o*d+s*g+h*n,e[2]=c*u+l*f+i*v,e[5]=c*p+l*m+i*y,e[8]=c*d+l*g+i*n,this},multiplyScalar:function(t){var e=this.elements;return e[0]*=t,e[3]*=t,e[6]*=t,e[1]*=t,e[4]*=t,e[7]*=t,e[2]*=t,e[5]*=t,e[8]*=t,this},determinant:function(){var t=this.elements,e=t[0],i=t[1],n=t[2],r=t[3],a=t[4],o=t[5],s=t[6],h=t[7];return t=t[8],e*a*t-e*o*h-i*r*t+i*o*s+n*r*h-n*a*s},getInverse:function(t,e){t&&t.isMatrix4&&void 0;var i=t.elements;t=this.elements;var n=i[0],r=i[1],a=i[2],o=i[3],s=i[4],h=i[5],c=i[6],l=i[7];i=i[8];var u=i*s-h*l,p=h*c-i*o,d=l*o-s*c,f=n*u+r*p+a*d;if(0===f){if(!0===e)throw Error("THREE.Matrix3: .getInverse() can't invert matrix, determinant is 0");return this.identity()}return e=1/f,t[0]=u*e,t[1]=(a*l-i*r)*e,t[2]=(h*r-a*s)*e,t[3]=p*e,t[4]=(i*n-a*c)*e,t[5]=(a*o-h*n)*e,t[6]=d*e,t[7]=(r*c-l*n)*e,t[8]=(s*n-r*o)*e,this},transpose:function(){var t=this.elements,e=t[1];return t[1]=t[3],t[3]=e,e=t[2],t[2]=t[6],t[6]=e,e=t[5],t[5]=t[7],t[7]=e,this},getNormalMatrix:function(t){return this.setFromMatrix4(t).getInverse(this).transpose()},transposeIntoArray:function(t){var e=this.elements;return t[0]=e[0],t[1]=e[3],t[2]=e[6],t[3]=e[1],t[4]=e[4],t[5]=e[7],t[6]=e[2],t[7]=e[5],t[8]=e[8],this},setUvTransform:function(t,e,i,n,r,a,o){var s=Math.cos(r);r=Math.sin(r),this.set(i*s,i*r,-i*(s*a+r*o)+a+t,-n*r,n*s,-n*(-r*a+s*o)+o+e,0,0,1)},scale:function(t,e){var i=this.elements;return i[0]*=t,i[3]*=t,i[6]*=t,i[1]*=e,i[4]*=e,i[7]*=e,this},rotate:function(t){var e=Math.cos(t);t=Math.sin(t);var i=this.elements,n=i[0],r=i[3],a=i[6],o=i[1],s=i[4],h=i[7];return i[0]=e*n+t*o,i[3]=e*r+t*s,i[6]=e*a+t*h,i[1]=-t*n+e*o,i[4]=-t*r+e*s,i[7]=-t*a+e*h,this},translate:function(t,e){var i=this.elements;return i[0]+=t*i[2],i[3]+=t*i[5],i[6]+=t*i[8],i[1]+=e*i[2],i[4]+=e*i[5],i[7]+=e*i[8],this},equals:function(t){var e=this.elements;t=t.elements;for(var i=0;9>i;i++)if(e[i]!==t[i])return!1;return!0},fromArray:function(t,e){void 0===e&&(e=0);for(var i=0;9>i;i++)this.elements[i]=t[i+e];return this},toArray:function(t,e){void 0===t&&(t=[]),void 0===e&&(e=0);var i=this.elements;return t[e]=i[0],t[e+1]=i[1],t[e+2]=i[2],t[e+3]=i[3],t[e+4]=i[4],t[e+5]=i[5],t[e+6]=i[6],t[e+7]=i[7],t[e+8]=i[8],t}});var ta={getDataURL:function(t){if(t instanceof HTMLCanvasElement)var e=t;else{e=document.createElementNS("http://www.w3.org/1999/xhtml","canvas"),e.width=t.width,e.height=t.height;var i=e.getContext("2d");t instanceof ImageData?i.putImageData(t,0,0):i.drawImage(t,0,0,t.width,t.height)}return 2048<e.width||2048<e.height?e.toDataURL("image/jpeg",.6):e.toDataURL("image/png")}},ea=0;s.DEFAULT_IMAGE=void 0,s.DEFAULT_MAPPING=300,s.prototype=Object.assign(Object.create(e.prototype),{constructor:s,isTexture:!0,updateMatrix:function(){this.matrix.setUvTransform(this.offset.x,this.offset.y,this.repeat.x,this.repeat.y,this.rotation,this.center.x,this.center.y)},clone:function(){return(new this.constructor).copy(this)},copy:function(t){return this.name=t.name,this.image=t.image,this.mipmaps=t.mipmaps.slice(0),this.mapping=t.mapping,this.wrapS=t.wrapS,this.wrapT=t.wrapT,this.magFilter=t.magFilter,this.minFilter=t.minFilter,this.anisotropy=t.anisotropy,this.format=t.format,this.type=t.type,this.offset.copy(t.offset),this.repeat.copy(t.repeat),this.center.copy(t.center),this.rotation=t.rotation,this.matrixAutoUpdate=t.matrixAutoUpdate,this.matrix.copy(t.matrix),this.generateMipmaps=t.generateMipmaps,this.premultiplyAlpha=t.premultiplyAlpha,this.flipY=t.flipY,this.unpackAlignment=t.unpackAlignment,this.encoding=t.encoding,this},toJSON:function(t){var e=void 0===t||"string"==typeof t;if(!e&&void 0!==t.textures[this.uuid])return t.textures[this.uuid];var i={metadata:{version:4.5,type:"Texture",generator:"Texture.toJSON"},uuid:this.uuid,name:this.name,mapping:this.mapping,repeat:[this.repeat.x,this.repeat.y],offset:[this.offset.x,this.offset.y],center:[this.center.x,this.center.y],rotation:this.rotation,wrap:[this.wrapS,this.wrapT],format:this.format,minFilter:this.minFilter,magFilter:this.magFilter,anisotropy:this.anisotropy,flipY:this.flipY};if(void 0!==this.image){var n=this.image;if(void 0===n.uuid&&(n.uuid=$r.generateUUID()),!e&&void 0===t.images[n.uuid]){if(Array.isArray(n))for(var r=[],a=0,o=n.length;a<o;a++)r.push(ta.getDataURL(n[a]));else r=ta.getDataURL(n);t.images[n.uuid]={uuid:n.uuid,url:r}}i.image=n.uuid}return e||(t.textures[this.uuid]=i),i},dispose:function(){this.dispatchEvent({type:"dispose"})},transformUv:function(t){if(300!==this.mapping)return t;if(t.applyMatrix3(this.matrix),0>t.x||1<t.x)switch(this.wrapS){case 1e3:t.x-=Math.floor(t.x);break;case 1001:t.x=0>t.x?0:1;break;case 1002:t.x=1===Math.abs(Math.floor(t.x)%2)?Math.ceil(t.x)-t.x:t.x-Math.floor(t.x)}if(0>t.y||1<t.y)switch(this.wrapT){case 1e3:t.y-=Math.floor(t.y);break;case 1001:t.y=0>t.y?0:1;break;case 1002:t.y=1===Math.abs(Math.floor(t.y)%2)?Math.ceil(t.y)-t.y:t.y-Math.floor(t.y)}return this.flipY&&(t.y=1-t.y),t}}),Object.defineProperty(s.prototype,"needsUpdate",{set:function(t){!0===t&&this.version++}}),Object.assign(h.prototype,{isVector4:!0,set:function(t,e,i,n){return this.x=t,this.y=e,this.z=i,this.w=n,this},setScalar:function(t){return this.w=this.z=this.y=this.x=t,this},setX:function(t){return this.x=t,this},setY:function(t){return this.y=t,this},setZ:function(t){return this.z=t,this},setW:function(t){return this.w=t,this},setComponent:function(t,e){switch(t){case 0:this.x=e;break;case 1:this.y=e;break;case 2:this.z=e;break;case 3:this.w=e;break;default:throw Error("index is out of range: "+t)}return this},getComponent:function(t){switch(t){case 0:return this.x;case 1:return this.y;case 2:return this.z;case 3:return this.w;default:throw Error("index is out of range: "+t)}},clone:function(){return new this.constructor(this.x,this.y,this.z,this.w)},copy:function(t){return this.x=t.x,this.y=t.y,this.z=t.z,this.w=void 0!==t.w?t.w:1,this},add:function(t,e){return void 0!==e?this.addVectors(t,e):(this.x+=t.x,this.y+=t.y,this.z+=t.z,this.w+=t.w,this)},addScalar:function(t){return this.x+=t,this.y+=t,this.z+=t,this.w+=t,this},addVectors:function(t,e){return this.x=t.x+e.x,this.y=t.y+e.y,this.z=t.z+e.z,this.w=t.w+e.w,this},addScaledVector:function(t,e){return this.x+=t.x*e,this.y+=t.y*e,this.z+=t.z*e,this.w+=t.w*e,this},sub:function(t,e){return void 0!==e?this.subVectors(t,e):(this.x-=t.x,this.y-=t.y,this.z-=t.z,this.w-=t.w,this)},subScalar:function(t){return this.x-=t,this.y-=t,this.z-=t,this.w-=t,this},subVectors:function(t,e){return this.x=t.x-e.x,this.y=t.y-e.y,this.z=t.z-e.z,this.w=t.w-e.w,this},multiplyScalar:function(t){return this.x*=t,this.y*=t,this.z*=t,this.w*=t,this},applyMatrix4:function(t){var e=this.x,i=this.y,n=this.z,r=this.w;return t=t.elements,this.x=t[0]*e+t[4]*i+t[8]*n+t[12]*r,this.y=t[1]*e+t[5]*i+t[9]*n+t[13]*r,this.z=t[2]*e+t[6]*i+t[10]*n+t[14]*r,this.w=t[3]*e+t[7]*i+t[11]*n+t[15]*r,this},divideScalar:function(t){return this.multiplyScalar(1/t)},setAxisAngleFromQuaternion:function(t){this.w=2*Math.acos(t.w);var e=Math.sqrt(1-t.w*t.w);return 1e-4>e?(this.x=1,this.z=this.y=0):(this.x=t.x/e,this.y=t.y/e,this.z=t.z/e),this},setAxisAngleFromRotationMatrix:function(t){t=t.elements;var e=t[0],i=t[4],n=t[8],r=t[1],a=t[5],o=t[9],s=t[2],h=t[6],c=t[10];return.01>Math.abs(i-r)&&.01>Math.abs(n-s)&&.01>Math.abs(o-h)?.1>Math.abs(i+r)&&.1>Math.abs(n+s)&&.1>Math.abs(o+h)&&.1>Math.abs(e+a+c-3)?(this.set(1,0,0,0),this):(t=Math.PI,e=(e+1)/2,a=(a+1)/2,c=(c+1)/2,i=(i+r)/4,n=(n+s)/4,o=(o+h)/4,e>a&&e>c?.01>e?(h=0,i=s=.*********):(h=Math.sqrt(e),s=i/h,i=n/h):a>c?.01>a?(h=.*********,s=0,i=.*********):(s=Math.sqrt(a),h=i/s,i=o/s):.01>c?(s=h=.*********,i=0):(i=Math.sqrt(c),h=n/i,s=o/i),this.set(h,s,i,t),this):(t=Math.sqrt((h-o)*(h-o)+(n-s)*(n-s)+(r-i)*(r-i)),.001>Math.abs(t)&&(t=1),this.x=(h-o)/t,this.y=(n-s)/t,this.z=(r-i)/t,this.w=Math.acos((e+a+c-1)/2),this)},min:function(t){return this.x=Math.min(this.x,t.x),this.y=Math.min(this.y,t.y),this.z=Math.min(this.z,t.z),this.w=Math.min(this.w,t.w),this},max:function(t){return this.x=Math.max(this.x,t.x),this.y=Math.max(this.y,t.y),this.z=Math.max(this.z,t.z),this.w=Math.max(this.w,t.w),this},clamp:function(t,e){return this.x=Math.max(t.x,Math.min(e.x,this.x)),this.y=Math.max(t.y,Math.min(e.y,this.y)),this.z=Math.max(t.z,Math.min(e.z,this.z)),this.w=Math.max(t.w,Math.min(e.w,this.w)),this},clampScalar:function(){var t,e;return function(i,n){return void 0===t&&(t=new h,e=new h),t.set(i,i,i,i),e.set(n,n,n,n),this.clamp(t,e)}}(),clampLength:function(t,e){var i=this.length();return this.divideScalar(i||1).multiplyScalar(Math.max(t,Math.min(e,i)))},floor:function(){return this.x=Math.floor(this.x),this.y=Math.floor(this.y),this.z=Math.floor(this.z),this.w=Math.floor(this.w),this},ceil:function(){return this.x=Math.ceil(this.x),this.y=Math.ceil(this.y),this.z=Math.ceil(this.z),this.w=Math.ceil(this.w),this},round:function(){return this.x=Math.round(this.x),this.y=Math.round(this.y),this.z=Math.round(this.z),this.w=Math.round(this.w),this},roundToZero:function(){return this.x=0>this.x?Math.ceil(this.x):Math.floor(this.x),this.y=0>this.y?Math.ceil(this.y):Math.floor(this.y),this.z=0>this.z?Math.ceil(this.z):Math.floor(this.z),this.w=0>this.w?Math.ceil(this.w):Math.floor(this.w),this},negate:function(){return this.x=-this.x,this.y=-this.y,this.z=-this.z,this.w=-this.w,this},dot:function(t){return this.x*t.x+this.y*t.y+this.z*t.z+this.w*t.w},lengthSq:function(){return this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w},length:function(){return Math.sqrt(this.x*this.x+this.y*this.y+this.z*this.z+this.w*this.w)},manhattanLength:function(){return Math.abs(this.x)+Math.abs(this.y)+Math.abs(this.z)+Math.abs(this.w)},normalize:function(){return this.divideScalar(this.length()||1)},setLength:function(t){return this.normalize().multiplyScalar(t)},lerp:function(t,e){return this.x+=(t.x-this.x)*e,this.y+=(t.y-this.y)*e,this.z+=(t.z-this.z)*e,this.w+=(t.w-this.w)*e,this},lerpVectors:function(t,e,i){return this.subVectors(e,t).multiplyScalar(i).add(t)},equals:function(t){return t.x===this.x&&t.y===this.y&&t.z===this.z&&t.w===this.w},fromArray:function(t,e){return void 0===e&&(e=0),this.x=t[e],this.y=t[e+1],this.z=t[e+2],this.w=t[e+3],this},toArray:function(t,e){return void 0===t&&(t=[]),void 0===e&&(e=0),t[e]=this.x,t[e+1]=this.y,t[e+2]=this.z,t[e+3]=this.w,t},fromBufferAttribute:function(t,e,i){return this.x=t.getX(e),this.y=t.getY(e),this.z=t.getZ(e),this.w=t.getW(e),this}}),c.prototype=Object.assign(Object.create(e.prototype),{constructor:c,isWebGLRenderTarget:!0,setSize:function(t,e){this.width===t&&this.height===e||(this.width=t,this.height=e,this.dispose()),this.viewport.set(0,0,t,e),this.scissor.set(0,0,t,e)},clone:function(){return(new this.constructor).copy(this)},copy:function(t){return this.width=t.width,this.height=t.height,this.viewport.copy(t.viewport),this.texture=t.texture.clone(),this.depthBuffer=t.depthBuffer,this.stencilBuffer=t.stencilBuffer,this.depthTexture=t.depthTexture,this},dispose:function(){this.dispatchEvent({type:"dispose"})}}),l.prototype=Object.create(c.prototype),l.prototype.constructor=l,l.prototype.isWebGLRenderTargetCube=!0,u.prototype=Object.create(s.prototype),u.prototype.constructor=u,u.prototype.isDataTexture=!0,Object.assign(p.prototype,{isBox3:!0,set:function(t,e){return this.min.copy(t),this.max.copy(e),this},setFromArray:function(t){for(var e=1/0,i=1/0,n=1/0,r=-(1/0),a=-(1/0),o=-(1/0),s=0,h=t.length;s<h;s+=3){var c=t[s],l=t[s+1],u=t[s+2];c<e&&(e=c),l<i&&(i=l),u<n&&(n=u),c>r&&(r=c),l>a&&(a=l),u>o&&(o=u)}return this.min.set(e,i,n),this.max.set(r,a,o),this},setFromBufferAttribute:function(t){for(var e=1/0,i=1/0,n=1/0,r=-(1/0),a=-(1/0),o=-(1/0),s=0,h=t.count;s<h;s++){var c=t.getX(s),l=t.getY(s),u=t.getZ(s);c<e&&(e=c),l<i&&(i=l),u<n&&(n=u),c>r&&(r=c),l>a&&(a=l),u>o&&(o=u)}return this.min.set(e,i,n),this.max.set(r,a,o),this},setFromPoints:function(t){this.makeEmpty();for(var e=0,i=t.length;e<i;e++)this.expandByPoint(t[e]);return this},setFromCenterAndSize:function(){var t=new a;return function(e,i){return i=t.copy(i).multiplyScalar(.5),this.min.copy(e).sub(i),this.max.copy(e).add(i),this}}(),setFromObject:function(t){return this.makeEmpty(),this.expandByObject(t)},clone:function(){return(new this.constructor).copy(this)},copy:function(t){return this.min.copy(t.min),this.max.copy(t.max),this},makeEmpty:function(){return this.min.x=this.min.y=this.min.z=1/0,this.max.x=this.max.y=this.max.z=-(1/0),this},isEmpty:function(){return this.max.x<this.min.x||this.max.y<this.min.y||this.max.z<this.min.z},getCenter:function(t){return void 0===t&&(t=new a),this.isEmpty()?t.set(0,0,0):t.addVectors(this.min,this.max).multiplyScalar(.5)},getSize:function(t){return void 0===t&&(t=new a),this.isEmpty()?t.set(0,0,0):t.subVectors(this.max,this.min)},expandByPoint:function(t){return this.min.min(t),this.max.max(t),this},expandByVector:function(t){return this.min.sub(t),this.max.add(t),this},expandByScalar:function(t){return this.min.addScalar(-t),this.max.addScalar(t),this},expandByObject:function(){function t(t){var a=t.geometry;if(void 0!==a)if(a.isGeometry)for(a=a.vertices,i=0,n=a.length;i<n;i++)r.copy(a[i]),r.applyMatrix4(t.matrixWorld),e.expandByPoint(r);else if(a.isBufferGeometry&&(a=a.attributes.position,void 0!==a))for(i=0,n=a.count;i<n;i++)r.fromBufferAttribute(a,i).applyMatrix4(t.matrixWorld),e.expandByPoint(r)}var e,i,n,r=new a;return function(i){return e=this,i.updateMatrixWorld(!0),i.traverse(t),this}}(),containsPoint:function(t){return!(t.x<this.min.x||t.x>this.max.x||t.y<this.min.y||t.y>this.max.y||t.z<this.min.z||t.z>this.max.z)},containsBox:function(t){return this.min.x<=t.min.x&&t.max.x<=this.max.x&&this.min.y<=t.min.y&&t.max.y<=this.max.y&&this.min.z<=t.min.z&&t.max.z<=this.max.z},getParameter:function(t,e){return void 0===e&&(e=new a),e.set((t.x-this.min.x)/(this.max.x-this.min.x),(t.y-this.min.y)/(this.max.y-this.min.y),(t.z-this.min.z)/(this.max.z-this.min.z))},intersectsBox:function(t){return!(t.max.x<this.min.x||t.min.x>this.max.x||t.max.y<this.min.y||t.min.y>this.max.y||t.max.z<this.min.z||t.min.z>this.max.z)},intersectsSphere:function(){var t=new a;return function(e){return this.clampPoint(e.center,t),t.distanceToSquared(e.center)<=e.radius*e.radius}}(),intersectsPlane:function(t){if(0<t.normal.x)var e=t.normal.x*this.min.x,i=t.normal.x*this.max.x;else e=t.normal.x*this.max.x,i=t.normal.x*this.min.x;return 0<t.normal.y?(e+=t.normal.y*this.min.y,i+=t.normal.y*this.max.y):(e+=t.normal.y*this.max.y,i+=t.normal.y*this.min.y),0<t.normal.z?(e+=t.normal.z*this.min.z,i+=t.normal.z*this.max.z):(e+=t.normal.z*this.max.z,i+=t.normal.z*this.min.z),e<=t.constant&&i>=t.constant},intersectsTriangle:function(){function t(t){var r,a=0;for(r=t.length-3;a<=r;a+=3){h.fromArray(t,a);var o=l.x*Math.abs(h.x)+l.y*Math.abs(h.y)+l.z*Math.abs(h.z),s=e.dot(h),c=i.dot(h),u=n.dot(h);
if(Math.max(-Math.max(s,c,u),Math.min(s,c,u))>o)return!1}return!0}var e=new a,i=new a,n=new a,r=new a,o=new a,s=new a,h=new a,c=new a,l=new a,u=new a;return function(a){return!this.isEmpty()&&(this.getCenter(c),l.subVectors(this.max,c),e.subVectors(a.a,c),i.subVectors(a.b,c),n.subVectors(a.c,c),r.subVectors(i,e),o.subVectors(n,i),s.subVectors(e,n),a=[0,-r.z,r.y,0,-o.z,o.y,0,-s.z,s.y,r.z,0,-r.x,o.z,0,-o.x,s.z,0,-s.x,-r.y,r.x,0,-o.y,o.x,0,-s.y,s.x,0],!!t(a)&&(a=[1,0,0,0,1,0,0,0,1],!!t(a)&&(u.crossVectors(r,o),a=[u.x,u.y,u.z],t(a))))}}(),clampPoint:function(t,e){return void 0===e&&(e=new a),e.copy(t).clamp(this.min,this.max)},distanceToPoint:function(){var t=new a;return function(e){return t.copy(e).clamp(this.min,this.max).sub(e).length()}}(),getBoundingSphere:function(){var t=new a;return function(e){return void 0===e&&(e=new d),this.getCenter(e.center),e.radius=.5*this.getSize(t).length(),e}}(),intersect:function(t){return this.min.max(t.min),this.max.min(t.max),this.isEmpty()&&this.makeEmpty(),this},union:function(t){return this.min.min(t.min),this.max.max(t.max),this},applyMatrix4:function(){var t=[new a,new a,new a,new a,new a,new a,new a,new a];return function(e){return this.isEmpty()?this:(t[0].set(this.min.x,this.min.y,this.min.z).applyMatrix4(e),t[1].set(this.min.x,this.min.y,this.max.z).applyMatrix4(e),t[2].set(this.min.x,this.max.y,this.min.z).applyMatrix4(e),t[3].set(this.min.x,this.max.y,this.max.z).applyMatrix4(e),t[4].set(this.max.x,this.min.y,this.min.z).applyMatrix4(e),t[5].set(this.max.x,this.min.y,this.max.z).applyMatrix4(e),t[6].set(this.max.x,this.max.y,this.min.z).applyMatrix4(e),t[7].set(this.max.x,this.max.y,this.max.z).applyMatrix4(e),this.setFromPoints(t),this)}}(),translate:function(t){return this.min.add(t),this.max.add(t),this},equals:function(t){return t.min.equals(this.min)&&t.max.equals(this.max)}}),Object.assign(d.prototype,{set:function(t,e){return this.center.copy(t),this.radius=e,this},setFromPoints:function(){var t=new p;return function(e,i){var n=this.center;void 0!==i?n.copy(i):t.setFromPoints(e).getCenter(n);for(var r=i=0,a=e.length;r<a;r++)i=Math.max(i,n.distanceToSquared(e[r]));return this.radius=Math.sqrt(i),this}}(),clone:function(){return(new this.constructor).copy(this)},copy:function(t){return this.center.copy(t.center),this.radius=t.radius,this},empty:function(){return 0>=this.radius},containsPoint:function(t){return t.distanceToSquared(this.center)<=this.radius*this.radius},distanceToPoint:function(t){return t.distanceTo(this.center)-this.radius},intersectsSphere:function(t){var e=this.radius+t.radius;return t.center.distanceToSquared(this.center)<=e*e},intersectsBox:function(t){return t.intersectsSphere(this)},intersectsPlane:function(t){return Math.abs(t.distanceToPoint(this.center))<=this.radius},clampPoint:function(t,e){var i=this.center.distanceToSquared(t);return void 0===e&&(e=new a),e.copy(t),i>this.radius*this.radius&&(e.sub(this.center).normalize(),e.multiplyScalar(this.radius).add(this.center)),e},getBoundingBox:function(t){return void 0===t&&(t=new p),t.set(this.center,this.center),t.expandByScalar(this.radius),t},applyMatrix4:function(t){return this.center.applyMatrix4(t),this.radius*=t.getMaxScaleOnAxis(),this},translate:function(t){return this.center.add(t),this},equals:function(t){return t.center.equals(this.center)&&t.radius===this.radius}}),Object.assign(f.prototype,{set:function(t,e){return this.normal.copy(t),this.constant=e,this},setComponents:function(t,e,i,n){return this.normal.set(t,e,i),this.constant=n,this},setFromNormalAndCoplanarPoint:function(t,e){return this.normal.copy(t),this.constant=-e.dot(this.normal),this},setFromCoplanarPoints:function(){var t=new a,e=new a;return function(i,n,r){return n=t.subVectors(r,n).cross(e.subVectors(i,n)).normalize(),this.setFromNormalAndCoplanarPoint(n,i),this}}(),clone:function(){return(new this.constructor).copy(this)},copy:function(t){return this.normal.copy(t.normal),this.constant=t.constant,this},normalize:function(){var t=1/this.normal.length();return this.normal.multiplyScalar(t),this.constant*=t,this},negate:function(){return this.constant*=-1,this.normal.negate(),this},distanceToPoint:function(t){return this.normal.dot(t)+this.constant},distanceToSphere:function(t){return this.distanceToPoint(t.center)-t.radius},projectPoint:function(t,e){return void 0===e&&(e=new a),e.copy(this.normal).multiplyScalar(-this.distanceToPoint(t)).add(t)},intersectLine:function(){var t=new a;return function(e,i){void 0===i&&(i=new a);var n=e.delta(t),r=this.normal.dot(n);if(0===r){if(0===this.distanceToPoint(e.start))return i.copy(e.start)}else if(r=-(e.start.dot(this.normal)+this.constant)/r,!(0>r||1<r))return i.copy(n).multiplyScalar(r).add(e.start)}}(),intersectsLine:function(t){var e=this.distanceToPoint(t.start);return t=this.distanceToPoint(t.end),0>e&&0<t||0>t&&0<e},intersectsBox:function(t){return t.intersectsPlane(this)},intersectsSphere:function(t){return t.intersectsPlane(this)},coplanarPoint:function(t){return void 0===t&&(t=new a),t.copy(this.normal).multiplyScalar(-this.constant)},applyMatrix4:function(){var t=new a,e=new o;return function(i,n){return n=n||e.getNormalMatrix(i),i=this.coplanarPoint(t).applyMatrix4(i),n=this.normal.applyMatrix3(n).normalize(),this.constant=-i.dot(n),this}}(),translate:function(t){return this.constant-=t.dot(this.normal),this},equals:function(t){return t.normal.equals(this.normal)&&t.constant===this.constant}}),Object.assign(m.prototype,{set:function(t,e,i,n,r,a){var o=this.planes;return o[0].copy(t),o[1].copy(e),o[2].copy(i),o[3].copy(n),o[4].copy(r),o[5].copy(a),this},clone:function(){return(new this.constructor).copy(this)},copy:function(t){for(var e=this.planes,i=0;6>i;i++)e[i].copy(t.planes[i]);return this},setFromMatrix:function(t){var e=this.planes,i=t.elements;t=i[0];var n=i[1],r=i[2],a=i[3],o=i[4],s=i[5],h=i[6],c=i[7],l=i[8],u=i[9],p=i[10],d=i[11],f=i[12],m=i[13],g=i[14];return i=i[15],e[0].setComponents(a-t,c-o,d-l,i-f).normalize(),e[1].setComponents(a+t,c+o,d+l,i+f).normalize(),e[2].setComponents(a+n,c+s,d+u,i+m).normalize(),e[3].setComponents(a-n,c-s,d-u,i-m).normalize(),e[4].setComponents(a-r,c-h,d-p,i-g).normalize(),e[5].setComponents(a+r,c+h,d+p,i+g).normalize(),this},intersectsObject:function(){var t=new d;return function(e){var i=e.geometry;return null===i.boundingSphere&&i.computeBoundingSphere(),t.copy(i.boundingSphere).applyMatrix4(e.matrixWorld),this.intersectsSphere(t)}}(),intersectsSprite:function(){var t=new d;return function(e){return t.center.set(0,0,0),t.radius=.*********1865476,t.applyMatrix4(e.matrixWorld),this.intersectsSphere(t)}}(),intersectsSphere:function(t){var e=this.planes,i=t.center;t=-t.radius;for(var n=0;6>n;n++)if(e[n].distanceToPoint(i)<t)return!1;return!0},intersectsBox:function(){var t=new a;return function(e){for(var i=this.planes,n=0;6>n;n++){var r=i[n];if(t.x=0<r.normal.x?e.max.x:e.min.x,t.y=0<r.normal.y?e.max.y:e.min.y,t.z=0<r.normal.z?e.max.z:e.min.z,0>r.distanceToPoint(t))return!1}return!0}}(),containsPoint:function(t){for(var e=this.planes,i=0;6>i;i++)if(0>e[i].distanceToPoint(t))return!1;return!0}});var ia={alphamap_fragment:"#ifdef USE_ALPHAMAP\n\tdiffuseColor.a *= texture2D( alphaMap, vUv ).g;\n#endif\n",alphamap_pars_fragment:"#ifdef USE_ALPHAMAP\n\tuniform sampler2D alphaMap;\n#endif\n",alphatest_fragment:"#ifdef ALPHATEST\n\tif ( diffuseColor.a < ALPHATEST ) discard;\n#endif\n",aomap_fragment:"#ifdef USE_AOMAP\n\tfloat ambientOcclusion = ( texture2D( aoMap, vUv2 ).r - 1.0 ) * aoMapIntensity + 1.0;\n\treflectedLight.indirectDiffuse *= ambientOcclusion;\n\t#if defined( USE_ENVMAP ) && defined( PHYSICAL )\n\t\tfloat dotNV = saturate( dot( geometry.normal, geometry.viewDir ) );\n\t\treflectedLight.indirectSpecular *= computeSpecularOcclusion( dotNV, ambientOcclusion, material.specularRoughness );\n\t#endif\n#endif\n",aomap_pars_fragment:"#ifdef USE_AOMAP\n\tuniform sampler2D aoMap;\n\tuniform float aoMapIntensity;\n#endif",begin_vertex:"\nvec3 transformed = vec3( position );\n",beginnormal_vertex:"\nvec3 objectNormal = vec3( normal );\n",bsdfs:"float punctualLightIntensityToIrradianceFactor( const in float lightDistance, const in float cutoffDistance, const in float decayExponent ) {\n\tif( decayExponent > 0.0 ) {\n#if defined ( PHYSICALLY_CORRECT_LIGHTS )\n\t\tfloat distanceFalloff = 1.0 / max( pow( lightDistance, decayExponent ), 0.01 );\n\t\tfloat maxDistanceCutoffFactor = pow2( saturate( 1.0 - pow4( lightDistance / cutoffDistance ) ) );\n\t\treturn distanceFalloff * maxDistanceCutoffFactor;\n#else\n\t\treturn pow( saturate( -lightDistance / cutoffDistance + 1.0 ), decayExponent );\n#endif\n\t}\n\treturn 1.0;\n}\nvec3 BRDF_Diffuse_Lambert( const in vec3 diffuseColor ) {\n\treturn RECIPROCAL_PI * diffuseColor;\n}\nvec3 F_Schlick( const in vec3 specularColor, const in float dotLH ) {\n\tfloat fresnel = exp2( ( -5.55473 * dotLH - 6.98316 ) * dotLH );\n\treturn ( 1.0 - specularColor ) * fresnel + specularColor;\n}\nfloat G_GGX_Smith( const in float alpha, const in float dotNL, const in float dotNV ) {\n\tfloat a2 = pow2( alpha );\n\tfloat gl = dotNL + sqrt( a2 + ( 1.0 - a2 ) * pow2( dotNL ) );\n\tfloat gv = dotNV + sqrt( a2 + ( 1.0 - a2 ) * pow2( dotNV ) );\n\treturn 1.0 / ( gl * gv );\n}\nfloat G_GGX_SmithCorrelated( const in float alpha, const in float dotNL, const in float dotNV ) {\n\tfloat a2 = pow2( alpha );\n\tfloat gv = dotNL * sqrt( a2 + ( 1.0 - a2 ) * pow2( dotNV ) );\n\tfloat gl = dotNV * sqrt( a2 + ( 1.0 - a2 ) * pow2( dotNL ) );\n\treturn 0.5 / max( gv + gl, EPSILON );\n}\nfloat D_GGX( const in float alpha, const in float dotNH ) {\n\tfloat a2 = pow2( alpha );\n\tfloat denom = pow2( dotNH ) * ( a2 - 1.0 ) + 1.0;\n\treturn RECIPROCAL_PI * a2 / pow2( denom );\n}\nvec3 BRDF_Specular_GGX( const in IncidentLight incidentLight, const in GeometricContext geometry, const in vec3 specularColor, const in float roughness ) {\n\tfloat alpha = pow2( roughness );\n\tvec3 halfDir = normalize( incidentLight.direction + geometry.viewDir );\n\tfloat dotNL = saturate( dot( geometry.normal, incidentLight.direction ) );\n\tfloat dotNV = saturate( dot( geometry.normal, geometry.viewDir ) );\n\tfloat dotNH = saturate( dot( geometry.normal, halfDir ) );\n\tfloat dotLH = saturate( dot( incidentLight.direction, halfDir ) );\n\tvec3 F = F_Schlick( specularColor, dotLH );\n\tfloat G = G_GGX_SmithCorrelated( alpha, dotNL, dotNV );\n\tfloat D = D_GGX( alpha, dotNH );\n\treturn F * ( G * D );\n}\nvec2 LTC_Uv( const in vec3 N, const in vec3 V, const in float roughness ) {\n\tconst float LUT_SIZE  = 64.0;\n\tconst float LUT_SCALE = ( LUT_SIZE - 1.0 ) / LUT_SIZE;\n\tconst float LUT_BIAS  = 0.5 / LUT_SIZE;\n\tfloat dotNV = saturate( dot( N, V ) );\n\tvec2 uv = vec2( roughness, sqrt( 1.0 - dotNV ) );\n\tuv = uv * LUT_SCALE + LUT_BIAS;\n\treturn uv;\n}\nfloat LTC_ClippedSphereFormFactor( const in vec3 f ) {\n\tfloat l = length( f );\n\treturn max( ( l * l + f.z ) / ( l + 1.0 ), 0.0 );\n}\nvec3 LTC_EdgeVectorFormFactor( const in vec3 v1, const in vec3 v2 ) {\n\tfloat x = dot( v1, v2 );\n\tfloat y = abs( x );\n\tfloat a = 0.8543985 + ( 0.4965155 + 0.0145206 * y ) * y;\n\tfloat b = 3.4175940 + ( 4.1616724 + y ) * y;\n\tfloat v = a / b;\n\tfloat theta_sintheta = ( x > 0.0 ) ? v : 0.5 * inversesqrt( max( 1.0 - x * x, 1e-7 ) ) - v;\n\treturn cross( v1, v2 ) * theta_sintheta;\n}\nvec3 LTC_Evaluate( const in vec3 N, const in vec3 V, const in vec3 P, const in mat3 mInv, const in vec3 rectCoords[ 4 ] ) {\n\tvec3 v1 = rectCoords[ 1 ] - rectCoords[ 0 ];\n\tvec3 v2 = rectCoords[ 3 ] - rectCoords[ 0 ];\n\tvec3 lightNormal = cross( v1, v2 );\n\tif( dot( lightNormal, P - rectCoords[ 0 ] ) < 0.0 ) return vec3( 0.0 );\n\tvec3 T1, T2;\n\tT1 = normalize( V - N * dot( V, N ) );\n\tT2 = - cross( N, T1 );\n\tmat3 mat = mInv * transposeMat3( mat3( T1, T2, N ) );\n\tvec3 coords[ 4 ];\n\tcoords[ 0 ] = mat * ( rectCoords[ 0 ] - P );\n\tcoords[ 1 ] = mat * ( rectCoords[ 1 ] - P );\n\tcoords[ 2 ] = mat * ( rectCoords[ 2 ] - P );\n\tcoords[ 3 ] = mat * ( rectCoords[ 3 ] - P );\n\tcoords[ 0 ] = normalize( coords[ 0 ] );\n\tcoords[ 1 ] = normalize( coords[ 1 ] );\n\tcoords[ 2 ] = normalize( coords[ 2 ] );\n\tcoords[ 3 ] = normalize( coords[ 3 ] );\n\tvec3 vectorFormFactor = vec3( 0.0 );\n\tvectorFormFactor += LTC_EdgeVectorFormFactor( coords[ 0 ], coords[ 1 ] );\n\tvectorFormFactor += LTC_EdgeVectorFormFactor( coords[ 1 ], coords[ 2 ] );\n\tvectorFormFactor += LTC_EdgeVectorFormFactor( coords[ 2 ], coords[ 3 ] );\n\tvectorFormFactor += LTC_EdgeVectorFormFactor( coords[ 3 ], coords[ 0 ] );\n\tfloat result = LTC_ClippedSphereFormFactor( vectorFormFactor );\n\treturn vec3( result );\n}\nvec3 BRDF_Specular_GGX_Environment( const in GeometricContext geometry, const in vec3 specularColor, const in float roughness ) {\n\tfloat dotNV = saturate( dot( geometry.normal, geometry.viewDir ) );\n\tconst vec4 c0 = vec4( - 1, - 0.0275, - 0.572, 0.022 );\n\tconst vec4 c1 = vec4( 1, 0.0425, 1.04, - 0.04 );\n\tvec4 r = roughness * c0 + c1;\n\tfloat a004 = min( r.x * r.x, exp2( - 9.28 * dotNV ) ) * r.x + r.y;\n\tvec2 AB = vec2( -1.04, 1.04 ) * a004 + r.zw;\n\treturn specularColor * AB.x + AB.y;\n}\nfloat G_BlinnPhong_Implicit( ) {\n\treturn 0.25;\n}\nfloat D_BlinnPhong( const in float shininess, const in float dotNH ) {\n\treturn RECIPROCAL_PI * ( shininess * 0.5 + 1.0 ) * pow( dotNH, shininess );\n}\nvec3 BRDF_Specular_BlinnPhong( const in IncidentLight incidentLight, const in GeometricContext geometry, const in vec3 specularColor, const in float shininess ) {\n\tvec3 halfDir = normalize( incidentLight.direction + geometry.viewDir );\n\tfloat dotNH = saturate( dot( geometry.normal, halfDir ) );\n\tfloat dotLH = saturate( dot( incidentLight.direction, halfDir ) );\n\tvec3 F = F_Schlick( specularColor, dotLH );\n\tfloat G = G_BlinnPhong_Implicit( );\n\tfloat D = D_BlinnPhong( shininess, dotNH );\n\treturn F * ( G * D );\n}\nfloat GGXRoughnessToBlinnExponent( const in float ggxRoughness ) {\n\treturn ( 2.0 / pow2( ggxRoughness + 0.0001 ) - 2.0 );\n}\nfloat BlinnExponentToGGXRoughness( const in float blinnExponent ) {\n\treturn sqrt( 2.0 / ( blinnExponent + 2.0 ) );\n}\n",bumpmap_pars_fragment:"#ifdef USE_BUMPMAP\n\tuniform sampler2D bumpMap;\n\tuniform float bumpScale;\n\tvec2 dHdxy_fwd() {\n\t\tvec2 dSTdx = dFdx( vUv );\n\t\tvec2 dSTdy = dFdy( vUv );\n\t\tfloat Hll = bumpScale * texture2D( bumpMap, vUv ).x;\n\t\tfloat dBx = bumpScale * texture2D( bumpMap, vUv + dSTdx ).x - Hll;\n\t\tfloat dBy = bumpScale * texture2D( bumpMap, vUv + dSTdy ).x - Hll;\n\t\treturn vec2( dBx, dBy );\n\t}\n\tvec3 perturbNormalArb( vec3 surf_pos, vec3 surf_norm, vec2 dHdxy ) {\n\t\tvec3 vSigmaX = vec3( dFdx( surf_pos.x ), dFdx( surf_pos.y ), dFdx( surf_pos.z ) );\n\t\tvec3 vSigmaY = vec3( dFdy( surf_pos.x ), dFdy( surf_pos.y ), dFdy( surf_pos.z ) );\n\t\tvec3 vN = surf_norm;\n\t\tvec3 R1 = cross( vSigmaY, vN );\n\t\tvec3 R2 = cross( vN, vSigmaX );\n\t\tfloat fDet = dot( vSigmaX, R1 );\n\t\tfDet *= ( float( gl_FrontFacing ) * 2.0 - 1.0 );\n\t\tvec3 vGrad = sign( fDet ) * ( dHdxy.x * R1 + dHdxy.y * R2 );\n\t\treturn normalize( abs( fDet ) * surf_norm - vGrad );\n\t}\n#endif\n",clipping_planes_fragment:"#if NUM_CLIPPING_PLANES > 0\n\tvec4 plane;\n\t#pragma unroll_loop\n\tfor ( int i = 0; i < UNION_CLIPPING_PLANES; i ++ ) {\n\t\tplane = clippingPlanes[ i ];\n\t\tif ( dot( vViewPosition, plane.xyz ) > plane.w ) discard;\n\t}\n\t#if UNION_CLIPPING_PLANES < NUM_CLIPPING_PLANES\n\t\tbool clipped = true;\n\t\t#pragma unroll_loop\n\t\tfor ( int i = UNION_CLIPPING_PLANES; i < NUM_CLIPPING_PLANES; i ++ ) {\n\t\t\tplane = clippingPlanes[ i ];\n\t\t\tclipped = ( dot( vViewPosition, plane.xyz ) > plane.w ) && clipped;\n\t\t}\n\t\tif ( clipped ) discard;\n\t#endif\n#endif\n",clipping_planes_pars_fragment:"#if NUM_CLIPPING_PLANES > 0\n\t#if ! defined( PHYSICAL ) && ! defined( PHONG )\n\t\tvarying vec3 vViewPosition;\n\t#endif\n\tuniform vec4 clippingPlanes[ NUM_CLIPPING_PLANES ];\n#endif\n",clipping_planes_pars_vertex:"#if NUM_CLIPPING_PLANES > 0 && ! defined( PHYSICAL ) && ! defined( PHONG )\n\tvarying vec3 vViewPosition;\n#endif\n",clipping_planes_vertex:"#if NUM_CLIPPING_PLANES > 0 && ! defined( PHYSICAL ) && ! defined( PHONG )\n\tvViewPosition = - mvPosition.xyz;\n#endif\n",color_fragment:"#ifdef USE_COLOR\n\tdiffuseColor.rgb *= vColor;\n#endif",color_pars_fragment:"#ifdef USE_COLOR\n\tvarying vec3 vColor;\n#endif\n",color_pars_vertex:"#ifdef USE_COLOR\n\tvarying vec3 vColor;\n#endif",color_vertex:"#ifdef USE_COLOR\n\tvColor.xyz = color.xyz;\n#endif",common:"#define PI 3.14159265359\n#define PI2 6.28318530718\n#define PI_HALF 1.5707963267949\n#define RECIPROCAL_PI 0.31830988618\n#define RECIPROCAL_PI2 0.15915494\n#define LOG2 1.442695\n#define EPSILON 1e-6\n#define saturate(a) clamp( a, 0.0, 1.0 )\n#define whiteCompliment(a) ( 1.0 - saturate( a ) )\nfloat pow2( const in float x ) { return x*x; }\nfloat pow3( const in float x ) { return x*x*x; }\nfloat pow4( const in float x ) { float x2 = x*x; return x2*x2; }\nfloat average( const in vec3 color ) { return dot( color, vec3( 0.3333 ) ); }\nhighp float rand( const in vec2 uv ) {\n\tconst highp float a = 12.9898, b = 78.233, c = 43758.5453;\n\thighp float dt = dot( uv.xy, vec2( a,b ) ), sn = mod( dt, PI );\n\treturn fract(sin(sn) * c);\n}\nstruct IncidentLight {\n\tvec3 color;\n\tvec3 direction;\n\tbool visible;\n};\nstruct ReflectedLight {\n\tvec3 directDiffuse;\n\tvec3 directSpecular;\n\tvec3 indirectDiffuse;\n\tvec3 indirectSpecular;\n};\nstruct GeometricContext {\n\tvec3 position;\n\tvec3 normal;\n\tvec3 viewDir;\n};\nvec3 transformDirection( in vec3 dir, in mat4 matrix ) {\n\treturn normalize( ( matrix * vec4( dir, 0.0 ) ).xyz );\n}\nvec3 inverseTransformDirection( in vec3 dir, in mat4 matrix ) {\n\treturn normalize( ( vec4( dir, 0.0 ) * matrix ).xyz );\n}\nvec3 projectOnPlane(in vec3 point, in vec3 pointOnPlane, in vec3 planeNormal ) {\n\tfloat distance = dot( planeNormal, point - pointOnPlane );\n\treturn - distance * planeNormal + point;\n}\nfloat sideOfPlane( in vec3 point, in vec3 pointOnPlane, in vec3 planeNormal ) {\n\treturn sign( dot( point - pointOnPlane, planeNormal ) );\n}\nvec3 linePlaneIntersect( in vec3 pointOnLine, in vec3 lineDirection, in vec3 pointOnPlane, in vec3 planeNormal ) {\n\treturn lineDirection * ( dot( planeNormal, pointOnPlane - pointOnLine ) / dot( planeNormal, lineDirection ) ) + pointOnLine;\n}\nmat3 transposeMat3( const in mat3 m ) {\n\tmat3 tmp;\n\ttmp[ 0 ] = vec3( m[ 0 ].x, m[ 1 ].x, m[ 2 ].x );\n\ttmp[ 1 ] = vec3( m[ 0 ].y, m[ 1 ].y, m[ 2 ].y );\n\ttmp[ 2 ] = vec3( m[ 0 ].z, m[ 1 ].z, m[ 2 ].z );\n\treturn tmp;\n}\nfloat linearToRelativeLuminance( const in vec3 color ) {\n\tvec3 weights = vec3( 0.2126, 0.7152, 0.0722 );\n\treturn dot( weights, color.rgb );\n}\n",cube_uv_reflection_fragment:"#ifdef ENVMAP_TYPE_CUBE_UV\n#define cubeUV_textureSize (1024.0)\nint getFaceFromDirection(vec3 direction) {\n\tvec3 absDirection = abs(direction);\n\tint face = -1;\n\tif( absDirection.x > absDirection.z ) {\n\t\tif(absDirection.x > absDirection.y )\n\t\t\tface = direction.x > 0.0 ? 0 : 3;\n\t\telse\n\t\t\tface = direction.y > 0.0 ? 1 : 4;\n\t}\n\telse {\n\t\tif(absDirection.z > absDirection.y )\n\t\t\tface = direction.z > 0.0 ? 2 : 5;\n\t\telse\n\t\t\tface = direction.y > 0.0 ? 1 : 4;\n\t}\n\treturn face;\n}\n#define cubeUV_maxLods1  (log2(cubeUV_textureSize*0.25) - 1.0)\n#define cubeUV_rangeClamp (exp2((6.0 - 1.0) * 2.0))\nvec2 MipLevelInfo( vec3 vec, float roughnessLevel, float roughness ) {\n\tfloat scale = exp2(cubeUV_maxLods1 - roughnessLevel);\n\tfloat dxRoughness = dFdx(roughness);\n\tfloat dyRoughness = dFdy(roughness);\n\tvec3 dx = dFdx( vec * scale * dxRoughness );\n\tvec3 dy = dFdy( vec * scale * dyRoughness );\n\tfloat d = max( dot( dx, dx ), dot( dy, dy ) );\n\td = clamp(d, 1.0, cubeUV_rangeClamp);\n\tfloat mipLevel = 0.5 * log2(d);\n\treturn vec2(floor(mipLevel), fract(mipLevel));\n}\n#define cubeUV_maxLods2 (log2(cubeUV_textureSize*0.25) - 2.0)\n#define cubeUV_rcpTextureSize (1.0 / cubeUV_textureSize)\nvec2 getCubeUV(vec3 direction, float roughnessLevel, float mipLevel) {\n\tmipLevel = roughnessLevel > cubeUV_maxLods2 - 3.0 ? 0.0 : mipLevel;\n\tfloat a = 16.0 * cubeUV_rcpTextureSize;\n\tvec2 exp2_packed = exp2( vec2( roughnessLevel, mipLevel ) );\n\tvec2 rcp_exp2_packed = vec2( 1.0 ) / exp2_packed;\n\tfloat powScale = exp2_packed.x * exp2_packed.y;\n\tfloat scale = rcp_exp2_packed.x * rcp_exp2_packed.y * 0.25;\n\tfloat mipOffset = 0.75*(1.0 - rcp_exp2_packed.y) * rcp_exp2_packed.x;\n\tbool bRes = mipLevel == 0.0;\n\tscale =  bRes && (scale < a) ? a : scale;\n\tvec3 r;\n\tvec2 offset;\n\tint face = getFaceFromDirection(direction);\n\tfloat rcpPowScale = 1.0 / powScale;\n\tif( face == 0) {\n\t\tr = vec3(direction.x, -direction.z, direction.y);\n\t\toffset = vec2(0.0+mipOffset,0.75 * rcpPowScale);\n\t\toffset.y = bRes && (offset.y < 2.0*a) ? a : offset.y;\n\t}\n\telse if( face == 1) {\n\t\tr = vec3(direction.y, direction.x, direction.z);\n\t\toffset = vec2(scale+mipOffset, 0.75 * rcpPowScale);\n\t\toffset.y = bRes && (offset.y < 2.0*a) ? a : offset.y;\n\t}\n\telse if( face == 2) {\n\t\tr = vec3(direction.z, direction.x, direction.y);\n\t\toffset = vec2(2.0*scale+mipOffset, 0.75 * rcpPowScale);\n\t\toffset.y = bRes && (offset.y < 2.0*a) ? a : offset.y;\n\t}\n\telse if( face == 3) {\n\t\tr = vec3(direction.x, direction.z, direction.y);\n\t\toffset = vec2(0.0+mipOffset,0.5 * rcpPowScale);\n\t\toffset.y = bRes && (offset.y < 2.0*a) ? 0.0 : offset.y;\n\t}\n\telse if( face == 4) {\n\t\tr = vec3(direction.y, direction.x, -direction.z);\n\t\toffset = vec2(scale+mipOffset, 0.5 * rcpPowScale);\n\t\toffset.y = bRes && (offset.y < 2.0*a) ? 0.0 : offset.y;\n\t}\n\telse {\n\t\tr = vec3(direction.z, -direction.x, direction.y);\n\t\toffset = vec2(2.0*scale+mipOffset, 0.5 * rcpPowScale);\n\t\toffset.y = bRes && (offset.y < 2.0*a) ? 0.0 : offset.y;\n\t}\n\tr = normalize(r);\n\tfloat texelOffset = 0.5 * cubeUV_rcpTextureSize;\n\tvec2 s = ( r.yz / abs( r.x ) + vec2( 1.0 ) ) * 0.5;\n\tvec2 base = offset + vec2( texelOffset );\n\treturn base + s * ( scale - 2.0 * texelOffset );\n}\n#define cubeUV_maxLods3 (log2(cubeUV_textureSize*0.25) - 3.0)\nvec4 textureCubeUV( sampler2D envMap, vec3 reflectedDirection, float roughness ) {\n\tfloat roughnessVal = roughness* cubeUV_maxLods3;\n\tfloat r1 = floor(roughnessVal);\n\tfloat r2 = r1 + 1.0;\n\tfloat t = fract(roughnessVal);\n\tvec2 mipInfo = MipLevelInfo(reflectedDirection, r1, roughness);\n\tfloat s = mipInfo.y;\n\tfloat level0 = mipInfo.x;\n\tfloat level1 = level0 + 1.0;\n\tlevel1 = level1 > 5.0 ? 5.0 : level1;\n\tlevel0 += min( floor( s + 0.5 ), 5.0 );\n\tvec2 uv_10 = getCubeUV(reflectedDirection, r1, level0);\n\tvec4 color10 = envMapTexelToLinear(texture2D(envMap, uv_10));\n\tvec2 uv_20 = getCubeUV(reflectedDirection, r2, level0);\n\tvec4 color20 = envMapTexelToLinear(texture2D(envMap, uv_20));\n\tvec4 result = mix(color10, color20, t);\n\treturn vec4(result.rgb, 1.0);\n}\n#endif\n",defaultnormal_vertex:"vec3 transformedNormal = normalMatrix * objectNormal;\n#ifdef FLIP_SIDED\n\ttransformedNormal = - transformedNormal;\n#endif\n",displacementmap_pars_vertex:"#ifdef USE_DISPLACEMENTMAP\n\tuniform sampler2D displacementMap;\n\tuniform float displacementScale;\n\tuniform float displacementBias;\n#endif\n",displacementmap_vertex:"#ifdef USE_DISPLACEMENTMAP\n\ttransformed += normalize( objectNormal ) * ( texture2D( displacementMap, uv ).x * displacementScale + displacementBias );\n#endif\n",emissivemap_fragment:"#ifdef USE_EMISSIVEMAP\n\tvec4 emissiveColor = texture2D( emissiveMap, vUv );\n\temissiveColor.rgb = emissiveMapTexelToLinear( emissiveColor ).rgb;\n\ttotalEmissiveRadiance *= emissiveColor.rgb;\n#endif\n",emissivemap_pars_fragment:"#ifdef USE_EMISSIVEMAP\n\tuniform sampler2D emissiveMap;\n#endif\n",encodings_fragment:"  gl_FragColor = linearToOutputTexel( gl_FragColor );\n",encodings_pars_fragment:"\nvec4 LinearToLinear( in vec4 value ) {\n\treturn value;\n}\nvec4 GammaToLinear( in vec4 value, in float gammaFactor ) {\n\treturn vec4( pow( value.rgb, vec3( gammaFactor ) ), value.a );\n}\nvec4 LinearToGamma( in vec4 value, in float gammaFactor ) {\n\treturn vec4( pow( value.rgb, vec3( 1.0 / gammaFactor ) ), value.a );\n}\nvec4 sRGBToLinear( in vec4 value ) {\n\treturn vec4( mix( pow( value.rgb * 0.9478672986 + vec3( 0.0521327014 ), vec3( 2.4 ) ), value.rgb * 0.0773993808, vec3( lessThanEqual( value.rgb, vec3( 0.04045 ) ) ) ), value.a );\n}\nvec4 LinearTosRGB( in vec4 value ) {\n\treturn vec4( mix( pow( value.rgb, vec3( 0.41666 ) ) * 1.055 - vec3( 0.055 ), value.rgb * 12.92, vec3( lessThanEqual( value.rgb, vec3( 0.0031308 ) ) ) ), value.a );\n}\nvec4 RGBEToLinear( in vec4 value ) {\n\treturn vec4( value.rgb * exp2( value.a * 255.0 - 128.0 ), 1.0 );\n}\nvec4 LinearToRGBE( in vec4 value ) {\n\tfloat maxComponent = max( max( value.r, value.g ), value.b );\n\tfloat fExp = clamp( ceil( log2( maxComponent ) ), -128.0, 127.0 );\n\treturn vec4( value.rgb / exp2( fExp ), ( fExp + 128.0 ) / 255.0 );\n}\nvec4 RGBMToLinear( in vec4 value, in float maxRange ) {\n\treturn vec4( value.rgb * value.a * maxRange, 1.0 );\n}\nvec4 LinearToRGBM( in vec4 value, in float maxRange ) {\n\tfloat maxRGB = max( value.r, max( value.g, value.b ) );\n\tfloat M = clamp( maxRGB / maxRange, 0.0, 1.0 );\n\tM = ceil( M * 255.0 ) / 255.0;\n\treturn vec4( value.rgb / ( M * maxRange ), M );\n}\nvec4 RGBDToLinear( in vec4 value, in float maxRange ) {\n\treturn vec4( value.rgb * ( ( maxRange / 255.0 ) / value.a ), 1.0 );\n}\nvec4 LinearToRGBD( in vec4 value, in float maxRange ) {\n\tfloat maxRGB = max( value.r, max( value.g, value.b ) );\n\tfloat D = max( maxRange / maxRGB, 1.0 );\n\tD = min( floor( D ) / 255.0, 1.0 );\n\treturn vec4( value.rgb * ( D * ( 255.0 / maxRange ) ), D );\n}\nconst mat3 cLogLuvM = mat3( 0.2209, 0.3390, 0.4184, 0.1138, 0.6780, 0.7319, 0.0102, 0.1130, 0.2969 );\nvec4 LinearToLogLuv( in vec4 value )  {\n\tvec3 Xp_Y_XYZp = value.rgb * cLogLuvM;\n\tXp_Y_XYZp = max( Xp_Y_XYZp, vec3( 1e-6, 1e-6, 1e-6 ) );\n\tvec4 vResult;\n\tvResult.xy = Xp_Y_XYZp.xy / Xp_Y_XYZp.z;\n\tfloat Le = 2.0 * log2(Xp_Y_XYZp.y) + 127.0;\n\tvResult.w = fract( Le );\n\tvResult.z = ( Le - ( floor( vResult.w * 255.0 ) ) / 255.0 ) / 255.0;\n\treturn vResult;\n}\nconst mat3 cLogLuvInverseM = mat3( 6.0014, -2.7008, -1.7996, -1.3320, 3.1029, -5.7721, 0.3008, -1.0882, 5.6268 );\nvec4 LogLuvToLinear( in vec4 value ) {\n\tfloat Le = value.z * 255.0 + value.w;\n\tvec3 Xp_Y_XYZp;\n\tXp_Y_XYZp.y = exp2( ( Le - 127.0 ) / 2.0 );\n\tXp_Y_XYZp.z = Xp_Y_XYZp.y / value.y;\n\tXp_Y_XYZp.x = value.x * Xp_Y_XYZp.z;\n\tvec3 vRGB = Xp_Y_XYZp.rgb * cLogLuvInverseM;\n\treturn vec4( max( vRGB, 0.0 ), 1.0 );\n}\n",envmap_fragment:"#ifdef USE_ENVMAP\n\t#if defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( PHONG )\n\t\tvec3 cameraToVertex = normalize( vWorldPosition - cameraPosition );\n\t\tvec3 worldNormal = inverseTransformDirection( normal, viewMatrix );\n\t\t#ifdef ENVMAP_MODE_REFLECTION\n\t\t\tvec3 reflectVec = reflect( cameraToVertex, worldNormal );\n\t\t#else\n\t\t\tvec3 reflectVec = refract( cameraToVertex, worldNormal, refractionRatio );\n\t\t#endif\n\t#else\n\t\tvec3 reflectVec = vReflect;\n\t#endif\n\t#ifdef ENVMAP_TYPE_CUBE\n\t\tvec4 envColor = textureCube( envMap, vec3( flipEnvMap * reflectVec.x, reflectVec.yz ) );\n\t#elif defined( ENVMAP_TYPE_EQUIREC )\n\t\tvec2 sampleUV;\n\t\treflectVec = normalize( reflectVec );\n\t\tsampleUV.y = asin( clamp( reflectVec.y, - 1.0, 1.0 ) ) * RECIPROCAL_PI + 0.5;\n\t\tsampleUV.x = atan( reflectVec.z, reflectVec.x ) * RECIPROCAL_PI2 + 0.5;\n\t\tvec4 envColor = texture2D( envMap, sampleUV );\n\t#elif defined( ENVMAP_TYPE_SPHERE )\n\t\treflectVec = normalize( reflectVec );\n\t\tvec3 reflectView = normalize( ( viewMatrix * vec4( reflectVec, 0.0 ) ).xyz + vec3( 0.0, 0.0, 1.0 ) );\n\t\tvec4 envColor = texture2D( envMap, reflectView.xy * 0.5 + 0.5 );\n\t#else\n\t\tvec4 envColor = vec4( 0.0 );\n\t#endif\n\tenvColor = envMapTexelToLinear( envColor );\n\t#ifdef ENVMAP_BLENDING_MULTIPLY\n\t\toutgoingLight = mix( outgoingLight, outgoingLight * envColor.xyz, specularStrength * reflectivity );\n\t#elif defined( ENVMAP_BLENDING_MIX )\n\t\toutgoingLight = mix( outgoingLight, envColor.xyz, specularStrength * reflectivity );\n\t#elif defined( ENVMAP_BLENDING_ADD )\n\t\toutgoingLight += envColor.xyz * specularStrength * reflectivity;\n\t#endif\n#endif\n",envmap_pars_fragment:"#if defined( USE_ENVMAP ) || defined( PHYSICAL )\n\tuniform float reflectivity;\n\tuniform float envMapIntensity;\n#endif\n#ifdef USE_ENVMAP\n\t#if ! defined( PHYSICAL ) && ( defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( PHONG ) )\n\t\tvarying vec3 vWorldPosition;\n\t#endif\n\t#ifdef ENVMAP_TYPE_CUBE\n\t\tuniform samplerCube envMap;\n\t#else\n\t\tuniform sampler2D envMap;\n\t#endif\n\tuniform float flipEnvMap;\n\tuniform int maxMipLevel;\n\t#if defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( PHONG ) || defined( PHYSICAL )\n\t\tuniform float refractionRatio;\n\t#else\n\t\tvarying vec3 vReflect;\n\t#endif\n#endif\n",envmap_pars_vertex:"#ifdef USE_ENVMAP\n\t#if defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( PHONG )\n\t\tvarying vec3 vWorldPosition;\n\t#else\n\t\tvarying vec3 vReflect;\n\t\tuniform float refractionRatio;\n\t#endif\n#endif\n",envmap_physical_pars_fragment:"#if defined( USE_ENVMAP ) && defined( PHYSICAL )\n\tvec3 getLightProbeIndirectIrradiance( const in GeometricContext geometry, const in int maxMIPLevel ) {\n\t\tvec3 worldNormal = inverseTransformDirection( geometry.normal, viewMatrix );\n\t\t#ifdef ENVMAP_TYPE_CUBE\n\t\t\tvec3 queryVec = vec3( flipEnvMap * worldNormal.x, worldNormal.yz );\n\t\t\t#ifdef TEXTURE_LOD_EXT\n\t\t\t\tvec4 envMapColor = textureCubeLodEXT( envMap, queryVec, float( maxMIPLevel ) );\n\t\t\t#else\n\t\t\t\tvec4 envMapColor = textureCube( envMap, queryVec, float( maxMIPLevel ) );\n\t\t\t#endif\n\t\t\tenvMapColor.rgb = envMapTexelToLinear( envMapColor ).rgb;\n\t\t#elif defined( ENVMAP_TYPE_CUBE_UV )\n\t\t\tvec3 queryVec = vec3( flipEnvMap * worldNormal.x, worldNormal.yz );\n\t\t\tvec4 envMapColor = textureCubeUV( envMap, queryVec, 1.0 );\n\t\t#else\n\t\t\tvec4 envMapColor = vec4( 0.0 );\n\t\t#endif\n\t\treturn PI * envMapColor.rgb * envMapIntensity;\n\t}\n\tfloat getSpecularMIPLevel( const in float blinnShininessExponent, const in int maxMIPLevel ) {\n\t\tfloat maxMIPLevelScalar = float( maxMIPLevel );\n\t\tfloat desiredMIPLevel = maxMIPLevelScalar + 0.79248 - 0.5 * log2( pow2( blinnShininessExponent ) + 1.0 );\n\t\treturn clamp( desiredMIPLevel, 0.0, maxMIPLevelScalar );\n\t}\n\tvec3 getLightProbeIndirectRadiance( const in GeometricContext geometry, const in float blinnShininessExponent, const in int maxMIPLevel ) {\n\t\t#ifdef ENVMAP_MODE_REFLECTION\n\t\t\tvec3 reflectVec = reflect( -geometry.viewDir, geometry.normal );\n\t\t#else\n\t\t\tvec3 reflectVec = refract( -geometry.viewDir, geometry.normal, refractionRatio );\n\t\t#endif\n\t\treflectVec = inverseTransformDirection( reflectVec, viewMatrix );\n\t\tfloat specularMIPLevel = getSpecularMIPLevel( blinnShininessExponent, maxMIPLevel );\n\t\t#ifdef ENVMAP_TYPE_CUBE\n\t\t\tvec3 queryReflectVec = vec3( flipEnvMap * reflectVec.x, reflectVec.yz );\n\t\t\t#ifdef TEXTURE_LOD_EXT\n\t\t\t\tvec4 envMapColor = textureCubeLodEXT( envMap, queryReflectVec, specularMIPLevel );\n\t\t\t#else\n\t\t\t\tvec4 envMapColor = textureCube( envMap, queryReflectVec, specularMIPLevel );\n\t\t\t#endif\n\t\t\tenvMapColor.rgb = envMapTexelToLinear( envMapColor ).rgb;\n\t\t#elif defined( ENVMAP_TYPE_CUBE_UV )\n\t\t\tvec3 queryReflectVec = vec3( flipEnvMap * reflectVec.x, reflectVec.yz );\n\t\t\tvec4 envMapColor = textureCubeUV( envMap, queryReflectVec, BlinnExponentToGGXRoughness(blinnShininessExponent ));\n\t\t#elif defined( ENVMAP_TYPE_EQUIREC )\n\t\t\tvec2 sampleUV;\n\t\t\tsampleUV.y = asin( clamp( reflectVec.y, - 1.0, 1.0 ) ) * RECIPROCAL_PI + 0.5;\n\t\t\tsampleUV.x = atan( reflectVec.z, reflectVec.x ) * RECIPROCAL_PI2 + 0.5;\n\t\t\t#ifdef TEXTURE_LOD_EXT\n\t\t\t\tvec4 envMapColor = texture2DLodEXT( envMap, sampleUV, specularMIPLevel );\n\t\t\t#else\n\t\t\t\tvec4 envMapColor = texture2D( envMap, sampleUV, specularMIPLevel );\n\t\t\t#endif\n\t\t\tenvMapColor.rgb = envMapTexelToLinear( envMapColor ).rgb;\n\t\t#elif defined( ENVMAP_TYPE_SPHERE )\n\t\t\tvec3 reflectView = normalize( ( viewMatrix * vec4( reflectVec, 0.0 ) ).xyz + vec3( 0.0,0.0,1.0 ) );\n\t\t\t#ifdef TEXTURE_LOD_EXT\n\t\t\t\tvec4 envMapColor = texture2DLodEXT( envMap, reflectView.xy * 0.5 + 0.5, specularMIPLevel );\n\t\t\t#else\n\t\t\t\tvec4 envMapColor = texture2D( envMap, reflectView.xy * 0.5 + 0.5, specularMIPLevel );\n\t\t\t#endif\n\t\t\tenvMapColor.rgb = envMapTexelToLinear( envMapColor ).rgb;\n\t\t#endif\n\t\treturn envMapColor.rgb * envMapIntensity;\n\t}\n#endif\n",
envmap_vertex:"#ifdef USE_ENVMAP\n\t#if defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( PHONG )\n\t\tvWorldPosition = worldPosition.xyz;\n\t#else\n\t\tvec3 cameraToVertex = normalize( worldPosition.xyz - cameraPosition );\n\t\tvec3 worldNormal = inverseTransformDirection( transformedNormal, viewMatrix );\n\t\t#ifdef ENVMAP_MODE_REFLECTION\n\t\t\tvReflect = reflect( cameraToVertex, worldNormal );\n\t\t#else\n\t\t\tvReflect = refract( cameraToVertex, worldNormal, refractionRatio );\n\t\t#endif\n\t#endif\n#endif\n",fog_vertex:"#ifdef USE_FOG\n\tfogDepth = -mvPosition.z;\n#endif\n",fog_pars_vertex:"#ifdef USE_FOG\n\tvarying float fogDepth;\n#endif\n",fog_fragment:"#ifdef USE_FOG\n\t#ifdef FOG_EXP2\n\t\tfloat fogFactor = whiteCompliment( exp2( - fogDensity * fogDensity * fogDepth * fogDepth * LOG2 ) );\n\t#else\n\t\tfloat fogFactor = smoothstep( fogNear, fogFar, fogDepth );\n\t#endif\n\tgl_FragColor.rgb = mix( gl_FragColor.rgb, fogColor, fogFactor );\n#endif\n",fog_pars_fragment:"#ifdef USE_FOG\n\tuniform vec3 fogColor;\n\tvarying float fogDepth;\n\t#ifdef FOG_EXP2\n\t\tuniform float fogDensity;\n\t#else\n\t\tuniform float fogNear;\n\t\tuniform float fogFar;\n\t#endif\n#endif\n",gradientmap_pars_fragment:"#ifdef TOON\n\tuniform sampler2D gradientMap;\n\tvec3 getGradientIrradiance( vec3 normal, vec3 lightDirection ) {\n\t\tfloat dotNL = dot( normal, lightDirection );\n\t\tvec2 coord = vec2( dotNL * 0.5 + 0.5, 0.0 );\n\t\t#ifdef USE_GRADIENTMAP\n\t\t\treturn texture2D( gradientMap, coord ).rgb;\n\t\t#else\n\t\t\treturn ( coord.x < 0.7 ) ? vec3( 0.7 ) : vec3( 1.0 );\n\t\t#endif\n\t}\n#endif\n",lightmap_fragment:"#ifdef USE_LIGHTMAP\n\treflectedLight.indirectDiffuse += PI * texture2D( lightMap, vUv2 ).xyz * lightMapIntensity;\n#endif\n",lightmap_pars_fragment:"#ifdef USE_LIGHTMAP\n\tuniform sampler2D lightMap;\n\tuniform float lightMapIntensity;\n#endif",lights_lambert_vertex:"vec3 diffuse = vec3( 1.0 );\nGeometricContext geometry;\ngeometry.position = mvPosition.xyz;\ngeometry.normal = normalize( transformedNormal );\ngeometry.viewDir = normalize( -mvPosition.xyz );\nGeometricContext backGeometry;\nbackGeometry.position = geometry.position;\nbackGeometry.normal = -geometry.normal;\nbackGeometry.viewDir = geometry.viewDir;\nvLightFront = vec3( 0.0 );\n#ifdef DOUBLE_SIDED\n\tvLightBack = vec3( 0.0 );\n#endif\nIncidentLight directLight;\nfloat dotNL;\nvec3 directLightColor_Diffuse;\n#if NUM_POINT_LIGHTS > 0\n\t#pragma unroll_loop\n\tfor ( int i = 0; i < NUM_POINT_LIGHTS; i ++ ) {\n\t\tgetPointDirectLightIrradiance( pointLights[ i ], geometry, directLight );\n\t\tdotNL = dot( geometry.normal, directLight.direction );\n\t\tdirectLightColor_Diffuse = PI * directLight.color;\n\t\tvLightFront += saturate( dotNL ) * directLightColor_Diffuse;\n\t\t#ifdef DOUBLE_SIDED\n\t\t\tvLightBack += saturate( -dotNL ) * directLightColor_Diffuse;\n\t\t#endif\n\t}\n#endif\n#if NUM_SPOT_LIGHTS > 0\n\t#pragma unroll_loop\n\tfor ( int i = 0; i < NUM_SPOT_LIGHTS; i ++ ) {\n\t\tgetSpotDirectLightIrradiance( spotLights[ i ], geometry, directLight );\n\t\tdotNL = dot( geometry.normal, directLight.direction );\n\t\tdirectLightColor_Diffuse = PI * directLight.color;\n\t\tvLightFront += saturate( dotNL ) * directLightColor_Diffuse;\n\t\t#ifdef DOUBLE_SIDED\n\t\t\tvLightBack += saturate( -dotNL ) * directLightColor_Diffuse;\n\t\t#endif\n\t}\n#endif\n#if NUM_DIR_LIGHTS > 0\n\t#pragma unroll_loop\n\tfor ( int i = 0; i < NUM_DIR_LIGHTS; i ++ ) {\n\t\tgetDirectionalDirectLightIrradiance( directionalLights[ i ], geometry, directLight );\n\t\tdotNL = dot( geometry.normal, directLight.direction );\n\t\tdirectLightColor_Diffuse = PI * directLight.color;\n\t\tvLightFront += saturate( dotNL ) * directLightColor_Diffuse;\n\t\t#ifdef DOUBLE_SIDED\n\t\t\tvLightBack += saturate( -dotNL ) * directLightColor_Diffuse;\n\t\t#endif\n\t}\n#endif\n#if NUM_HEMI_LIGHTS > 0\n\t#pragma unroll_loop\n\tfor ( int i = 0; i < NUM_HEMI_LIGHTS; i ++ ) {\n\t\tvLightFront += getHemisphereLightIrradiance( hemisphereLights[ i ], geometry );\n\t\t#ifdef DOUBLE_SIDED\n\t\t\tvLightBack += getHemisphereLightIrradiance( hemisphereLights[ i ], backGeometry );\n\t\t#endif\n\t}\n#endif\n",lights_pars_begin:"uniform vec3 ambientLightColor;\nvec3 getAmbientLightIrradiance( const in vec3 ambientLightColor ) {\n\tvec3 irradiance = ambientLightColor;\n\t#ifndef PHYSICALLY_CORRECT_LIGHTS\n\t\tirradiance *= PI;\n\t#endif\n\treturn irradiance;\n}\n#if NUM_DIR_LIGHTS > 0\n\tstruct DirectionalLight {\n\t\tvec3 direction;\n\t\tvec3 color;\n\t\tint shadow;\n\t\tfloat shadowBias;\n\t\tfloat shadowRadius;\n\t\tvec2 shadowMapSize;\n\t};\n\tuniform DirectionalLight directionalLights[ NUM_DIR_LIGHTS ];\n\tvoid getDirectionalDirectLightIrradiance( const in DirectionalLight directionalLight, const in GeometricContext geometry, out IncidentLight directLight ) {\n\t\tdirectLight.color = directionalLight.color;\n\t\tdirectLight.direction = directionalLight.direction;\n\t\tdirectLight.visible = true;\n\t}\n#endif\n#if NUM_POINT_LIGHTS > 0\n\tstruct PointLight {\n\t\tvec3 position;\n\t\tvec3 color;\n\t\tfloat distance;\n\t\tfloat decay;\n\t\tint shadow;\n\t\tfloat shadowBias;\n\t\tfloat shadowRadius;\n\t\tvec2 shadowMapSize;\n\t\tfloat shadowCameraNear;\n\t\tfloat shadowCameraFar;\n\t};\n\tuniform PointLight pointLights[ NUM_POINT_LIGHTS ];\n\tvoid getPointDirectLightIrradiance( const in PointLight pointLight, const in GeometricContext geometry, out IncidentLight directLight ) {\n\t\tvec3 lVector = pointLight.position - geometry.position;\n\t\tdirectLight.direction = normalize( lVector );\n\t\tfloat lightDistance = length( lVector );\n\t\tdirectLight.color = pointLight.color;\n\t\tdirectLight.color *= punctualLightIntensityToIrradianceFactor( lightDistance, pointLight.distance, pointLight.decay );\n\t\tdirectLight.visible = ( directLight.color != vec3( 0.0 ) );\n\t}\n#endif\n#if NUM_SPOT_LIGHTS > 0\n\tstruct SpotLight {\n\t\tvec3 position;\n\t\tvec3 direction;\n\t\tvec3 color;\n\t\tfloat distance;\n\t\tfloat decay;\n\t\tfloat coneCos;\n\t\tfloat penumbraCos;\n\t\tint shadow;\n\t\tfloat shadowBias;\n\t\tfloat shadowRadius;\n\t\tvec2 shadowMapSize;\n\t};\n\tuniform SpotLight spotLights[ NUM_SPOT_LIGHTS ];\n\tvoid getSpotDirectLightIrradiance( const in SpotLight spotLight, const in GeometricContext geometry, out IncidentLight directLight  ) {\n\t\tvec3 lVector = spotLight.position - geometry.position;\n\t\tdirectLight.direction = normalize( lVector );\n\t\tfloat lightDistance = length( lVector );\n\t\tfloat angleCos = dot( directLight.direction, spotLight.direction );\n\t\tif ( angleCos > spotLight.coneCos ) {\n\t\t\tfloat spotEffect = smoothstep( spotLight.coneCos, spotLight.penumbraCos, angleCos );\n\t\t\tdirectLight.color = spotLight.color;\n\t\t\tdirectLight.color *= spotEffect * punctualLightIntensityToIrradianceFactor( lightDistance, spotLight.distance, spotLight.decay );\n\t\t\tdirectLight.visible = true;\n\t\t} else {\n\t\t\tdirectLight.color = vec3( 0.0 );\n\t\t\tdirectLight.visible = false;\n\t\t}\n\t}\n#endif\n#if NUM_RECT_AREA_LIGHTS > 0\n\tstruct RectAreaLight {\n\t\tvec3 color;\n\t\tvec3 position;\n\t\tvec3 halfWidth;\n\t\tvec3 halfHeight;\n\t};\n\tuniform sampler2D ltc_1;\tuniform sampler2D ltc_2;\n\tuniform RectAreaLight rectAreaLights[ NUM_RECT_AREA_LIGHTS ];\n#endif\n#if NUM_HEMI_LIGHTS > 0\n\tstruct HemisphereLight {\n\t\tvec3 direction;\n\t\tvec3 skyColor;\n\t\tvec3 groundColor;\n\t};\n\tuniform HemisphereLight hemisphereLights[ NUM_HEMI_LIGHTS ];\n\tvec3 getHemisphereLightIrradiance( const in HemisphereLight hemiLight, const in GeometricContext geometry ) {\n\t\tfloat dotNL = dot( geometry.normal, hemiLight.direction );\n\t\tfloat hemiDiffuseWeight = 0.5 * dotNL + 0.5;\n\t\tvec3 irradiance = mix( hemiLight.groundColor, hemiLight.skyColor, hemiDiffuseWeight );\n\t\t#ifndef PHYSICALLY_CORRECT_LIGHTS\n\t\t\tirradiance *= PI;\n\t\t#endif\n\t\treturn irradiance;\n\t}\n#endif\n",lights_phong_fragment:"BlinnPhongMaterial material;\nmaterial.diffuseColor = diffuseColor.rgb;\nmaterial.specularColor = specular;\nmaterial.specularShininess = shininess;\nmaterial.specularStrength = specularStrength;\n",lights_phong_pars_fragment:"varying vec3 vViewPosition;\n#ifndef FLAT_SHADED\n\tvarying vec3 vNormal;\n#endif\nstruct BlinnPhongMaterial {\n\tvec3\tdiffuseColor;\n\tvec3\tspecularColor;\n\tfloat\tspecularShininess;\n\tfloat\tspecularStrength;\n};\nvoid RE_Direct_BlinnPhong( const in IncidentLight directLight, const in GeometricContext geometry, const in BlinnPhongMaterial material, inout ReflectedLight reflectedLight ) {\n\t#ifdef TOON\n\t\tvec3 irradiance = getGradientIrradiance( geometry.normal, directLight.direction ) * directLight.color;\n\t#else\n\t\tfloat dotNL = saturate( dot( geometry.normal, directLight.direction ) );\n\t\tvec3 irradiance = dotNL * directLight.color;\n\t#endif\n\t#ifndef PHYSICALLY_CORRECT_LIGHTS\n\t\tirradiance *= PI;\n\t#endif\n\treflectedLight.directDiffuse += irradiance * BRDF_Diffuse_Lambert( material.diffuseColor );\n\treflectedLight.directSpecular += irradiance * BRDF_Specular_BlinnPhong( directLight, geometry, material.specularColor, material.specularShininess ) * material.specularStrength;\n}\nvoid RE_IndirectDiffuse_BlinnPhong( const in vec3 irradiance, const in GeometricContext geometry, const in BlinnPhongMaterial material, inout ReflectedLight reflectedLight ) {\n\treflectedLight.indirectDiffuse += irradiance * BRDF_Diffuse_Lambert( material.diffuseColor );\n}\n#define RE_Direct\t\t\t\tRE_Direct_BlinnPhong\n#define RE_IndirectDiffuse\t\tRE_IndirectDiffuse_BlinnPhong\n#define Material_LightProbeLOD( material )\t(0)\n",lights_physical_fragment:"PhysicalMaterial material;\nmaterial.diffuseColor = diffuseColor.rgb * ( 1.0 - metalnessFactor );\nmaterial.specularRoughness = clamp( roughnessFactor, 0.04, 1.0 );\n#ifdef STANDARD\n\tmaterial.specularColor = mix( vec3( DEFAULT_SPECULAR_COEFFICIENT ), diffuseColor.rgb, metalnessFactor );\n#else\n\tmaterial.specularColor = mix( vec3( MAXIMUM_SPECULAR_COEFFICIENT * pow2( reflectivity ) ), diffuseColor.rgb, metalnessFactor );\n\tmaterial.clearCoat = saturate( clearCoat );\tmaterial.clearCoatRoughness = clamp( clearCoatRoughness, 0.04, 1.0 );\n#endif\n",lights_physical_pars_fragment:"struct PhysicalMaterial {\n\tvec3\tdiffuseColor;\n\tfloat\tspecularRoughness;\n\tvec3\tspecularColor;\n\t#ifndef STANDARD\n\t\tfloat clearCoat;\n\t\tfloat clearCoatRoughness;\n\t#endif\n};\n#define MAXIMUM_SPECULAR_COEFFICIENT 0.16\n#define DEFAULT_SPECULAR_COEFFICIENT 0.04\nfloat clearCoatDHRApprox( const in float roughness, const in float dotNL ) {\n\treturn DEFAULT_SPECULAR_COEFFICIENT + ( 1.0 - DEFAULT_SPECULAR_COEFFICIENT ) * ( pow( 1.0 - dotNL, 5.0 ) * pow( 1.0 - roughness, 2.0 ) );\n}\n#if NUM_RECT_AREA_LIGHTS > 0\n\tvoid RE_Direct_RectArea_Physical( const in RectAreaLight rectAreaLight, const in GeometricContext geometry, const in PhysicalMaterial material, inout ReflectedLight reflectedLight ) {\n\t\tvec3 normal = geometry.normal;\n\t\tvec3 viewDir = geometry.viewDir;\n\t\tvec3 position = geometry.position;\n\t\tvec3 lightPos = rectAreaLight.position;\n\t\tvec3 halfWidth = rectAreaLight.halfWidth;\n\t\tvec3 halfHeight = rectAreaLight.halfHeight;\n\t\tvec3 lightColor = rectAreaLight.color;\n\t\tfloat roughness = material.specularRoughness;\n\t\tvec3 rectCoords[ 4 ];\n\t\trectCoords[ 0 ] = lightPos - halfWidth - halfHeight;\t\trectCoords[ 1 ] = lightPos + halfWidth - halfHeight;\n\t\trectCoords[ 2 ] = lightPos + halfWidth + halfHeight;\n\t\trectCoords[ 3 ] = lightPos - halfWidth + halfHeight;\n\t\tvec2 uv = LTC_Uv( normal, viewDir, roughness );\n\t\tvec4 t1 = texture2D( ltc_1, uv );\n\t\tvec4 t2 = texture2D( ltc_2, uv );\n\t\tmat3 mInv = mat3(\n\t\t\tvec3( t1.x, 0, t1.y ),\n\t\t\tvec3(    0, 1,    0 ),\n\t\t\tvec3( t1.z, 0, t1.w )\n\t\t);\n\t\tvec3 fresnel = ( material.specularColor * t2.x + ( vec3( 1.0 ) - material.specularColor ) * t2.y );\n\t\treflectedLight.directSpecular += lightColor * fresnel * LTC_Evaluate( normal, viewDir, position, mInv, rectCoords );\n\t\treflectedLight.directDiffuse += lightColor * material.diffuseColor * LTC_Evaluate( normal, viewDir, position, mat3( 1.0 ), rectCoords );\n\t}\n#endif\nvoid RE_Direct_Physical( const in IncidentLight directLight, const in GeometricContext geometry, const in PhysicalMaterial material, inout ReflectedLight reflectedLight ) {\n\tfloat dotNL = saturate( dot( geometry.normal, directLight.direction ) );\n\tvec3 irradiance = dotNL * directLight.color;\n\t#ifndef PHYSICALLY_CORRECT_LIGHTS\n\t\tirradiance *= PI;\n\t#endif\n\t#ifndef STANDARD\n\t\tfloat clearCoatDHR = material.clearCoat * clearCoatDHRApprox( material.clearCoatRoughness, dotNL );\n\t#else\n\t\tfloat clearCoatDHR = 0.0;\n\t#endif\n\treflectedLight.directSpecular += ( 1.0 - clearCoatDHR ) * irradiance * BRDF_Specular_GGX( directLight, geometry, material.specularColor, material.specularRoughness );\n\treflectedLight.directDiffuse += ( 1.0 - clearCoatDHR ) * irradiance * BRDF_Diffuse_Lambert( material.diffuseColor );\n\t#ifndef STANDARD\n\t\treflectedLight.directSpecular += irradiance * material.clearCoat * BRDF_Specular_GGX( directLight, geometry, vec3( DEFAULT_SPECULAR_COEFFICIENT ), material.clearCoatRoughness );\n\t#endif\n}\nvoid RE_IndirectDiffuse_Physical( const in vec3 irradiance, const in GeometricContext geometry, const in PhysicalMaterial material, inout ReflectedLight reflectedLight ) {\n\treflectedLight.indirectDiffuse += irradiance * BRDF_Diffuse_Lambert( material.diffuseColor );\n}\nvoid RE_IndirectSpecular_Physical( const in vec3 radiance, const in vec3 clearCoatRadiance, const in GeometricContext geometry, const in PhysicalMaterial material, inout ReflectedLight reflectedLight ) {\n\t#ifndef STANDARD\n\t\tfloat dotNV = saturate( dot( geometry.normal, geometry.viewDir ) );\n\t\tfloat dotNL = dotNV;\n\t\tfloat clearCoatDHR = material.clearCoat * clearCoatDHRApprox( material.clearCoatRoughness, dotNL );\n\t#else\n\t\tfloat clearCoatDHR = 0.0;\n\t#endif\n\treflectedLight.indirectSpecular += ( 1.0 - clearCoatDHR ) * radiance * BRDF_Specular_GGX_Environment( geometry, material.specularColor, material.specularRoughness );\n\t#ifndef STANDARD\n\t\treflectedLight.indirectSpecular += clearCoatRadiance * material.clearCoat * BRDF_Specular_GGX_Environment( geometry, vec3( DEFAULT_SPECULAR_COEFFICIENT ), material.clearCoatRoughness );\n\t#endif\n}\n#define RE_Direct\t\t\t\tRE_Direct_Physical\n#define RE_Direct_RectArea\t\tRE_Direct_RectArea_Physical\n#define RE_IndirectDiffuse\t\tRE_IndirectDiffuse_Physical\n#define RE_IndirectSpecular\t\tRE_IndirectSpecular_Physical\n#define Material_BlinnShininessExponent( material )   GGXRoughnessToBlinnExponent( material.specularRoughness )\n#define Material_ClearCoat_BlinnShininessExponent( material )   GGXRoughnessToBlinnExponent( material.clearCoatRoughness )\nfloat computeSpecularOcclusion( const in float dotNV, const in float ambientOcclusion, const in float roughness ) {\n\treturn saturate( pow( dotNV + ambientOcclusion, exp2( - 16.0 * roughness - 1.0 ) ) - 1.0 + ambientOcclusion );\n}\n",lights_fragment_begin:"\nGeometricContext geometry;\ngeometry.position = - vViewPosition;\ngeometry.normal = normal;\ngeometry.viewDir = normalize( vViewPosition );\nIncidentLight directLight;\n#if ( NUM_POINT_LIGHTS > 0 ) && defined( RE_Direct )\n\tPointLight pointLight;\n\t#pragma unroll_loop\n\tfor ( int i = 0; i < NUM_POINT_LIGHTS; i ++ ) {\n\t\tpointLight = pointLights[ i ];\n\t\tgetPointDirectLightIrradiance( pointLight, geometry, directLight );\n\t\t#ifdef USE_SHADOWMAP\n\t\tdirectLight.color *= all( bvec2( pointLight.shadow, directLight.visible ) ) ? getPointShadow( pointShadowMap[ i ], pointLight.shadowMapSize, pointLight.shadowBias, pointLight.shadowRadius, vPointShadowCoord[ i ], pointLight.shadowCameraNear, pointLight.shadowCameraFar ) : 1.0;\n\t\t#endif\n\t\tRE_Direct( directLight, geometry, material, reflectedLight );\n\t}\n#endif\n#if ( NUM_SPOT_LIGHTS > 0 ) && defined( RE_Direct )\n\tSpotLight spotLight;\n\t#pragma unroll_loop\n\tfor ( int i = 0; i < NUM_SPOT_LIGHTS; i ++ ) {\n\t\tspotLight = spotLights[ i ];\n\t\tgetSpotDirectLightIrradiance( spotLight, geometry, directLight );\n\t\t#ifdef USE_SHADOWMAP\n\t\tdirectLight.color *= all( bvec2( spotLight.shadow, directLight.visible ) ) ? getShadow( spotShadowMap[ i ], spotLight.shadowMapSize, spotLight.shadowBias, spotLight.shadowRadius, vSpotShadowCoord[ i ] ) : 1.0;\n\t\t#endif\n\t\tRE_Direct( directLight, geometry, material, reflectedLight );\n\t}\n#endif\n#if ( NUM_DIR_LIGHTS > 0 ) && defined( RE_Direct )\n\tDirectionalLight directionalLight;\n\t#pragma unroll_loop\n\tfor ( int i = 0; i < NUM_DIR_LIGHTS; i ++ ) {\n\t\tdirectionalLight = directionalLights[ i ];\n\t\tgetDirectionalDirectLightIrradiance( directionalLight, geometry, directLight );\n\t\t#ifdef USE_SHADOWMAP\n\t\tdirectLight.color *= all( bvec2( directionalLight.shadow, directLight.visible ) ) ? getShadow( directionalShadowMap[ i ], directionalLight.shadowMapSize, directionalLight.shadowBias, directionalLight.shadowRadius, vDirectionalShadowCoord[ i ] ) : 1.0;\n\t\t#endif\n\t\tRE_Direct( directLight, geometry, material, reflectedLight );\n\t}\n#endif\n#if ( NUM_RECT_AREA_LIGHTS > 0 ) && defined( RE_Direct_RectArea )\n\tRectAreaLight rectAreaLight;\n\t#pragma unroll_loop\n\tfor ( int i = 0; i < NUM_RECT_AREA_LIGHTS; i ++ ) {\n\t\trectAreaLight = rectAreaLights[ i ];\n\t\tRE_Direct_RectArea( rectAreaLight, geometry, material, reflectedLight );\n\t}\n#endif\n#if defined( RE_IndirectDiffuse )\n\tvec3 irradiance = getAmbientLightIrradiance( ambientLightColor );\n\t#if ( NUM_HEMI_LIGHTS > 0 )\n\t\t#pragma unroll_loop\n\t\tfor ( int i = 0; i < NUM_HEMI_LIGHTS; i ++ ) {\n\t\t\tirradiance += getHemisphereLightIrradiance( hemisphereLights[ i ], geometry );\n\t\t}\n\t#endif\n#endif\n#if defined( RE_IndirectSpecular )\n\tvec3 radiance = vec3( 0.0 );\n\tvec3 clearCoatRadiance = vec3( 0.0 );\n#endif\n",lights_fragment_maps:"#if defined( RE_IndirectDiffuse )\n\t#ifdef USE_LIGHTMAP\n\t\tvec3 lightMapIrradiance = texture2D( lightMap, vUv2 ).xyz * lightMapIntensity;\n\t\t#ifndef PHYSICALLY_CORRECT_LIGHTS\n\t\t\tlightMapIrradiance *= PI;\n\t\t#endif\n\t\tirradiance += lightMapIrradiance;\n\t#endif\n\t#if defined( USE_ENVMAP ) && defined( PHYSICAL ) && defined( ENVMAP_TYPE_CUBE_UV )\n\t\tirradiance += getLightProbeIndirectIrradiance( geometry, maxMipLevel );\n\t#endif\n#endif\n#if defined( USE_ENVMAP ) && defined( RE_IndirectSpecular )\n\tradiance += getLightProbeIndirectRadiance( geometry, Material_BlinnShininessExponent( material ), maxMipLevel );\n\t#ifndef STANDARD\n\t\tclearCoatRadiance += getLightProbeIndirectRadiance( geometry, Material_ClearCoat_BlinnShininessExponent( material ), maxMipLevel );\n\t#endif\n#endif\n",lights_fragment_end:"#if defined( RE_IndirectDiffuse )\n\tRE_IndirectDiffuse( irradiance, geometry, material, reflectedLight );\n#endif\n#if defined( RE_IndirectSpecular )\n\tRE_IndirectSpecular( radiance, clearCoatRadiance, geometry, material, reflectedLight );\n#endif\n",logdepthbuf_fragment:"#if defined( USE_LOGDEPTHBUF ) && defined( USE_LOGDEPTHBUF_EXT )\n\tgl_FragDepthEXT = log2( vFragDepth ) * logDepthBufFC * 0.5;\n#endif",logdepthbuf_pars_fragment:"#if defined( USE_LOGDEPTHBUF ) && defined( USE_LOGDEPTHBUF_EXT )\n\tuniform float logDepthBufFC;\n\tvarying float vFragDepth;\n#endif\n",logdepthbuf_pars_vertex:"#ifdef USE_LOGDEPTHBUF\n\t#ifdef USE_LOGDEPTHBUF_EXT\n\t\tvarying float vFragDepth;\n\t#else\n\t\tuniform float logDepthBufFC;\n\t#endif\n#endif\n",logdepthbuf_vertex:"#ifdef USE_LOGDEPTHBUF\n\t#ifdef USE_LOGDEPTHBUF_EXT\n\t\tvFragDepth = 1.0 + gl_Position.w;\n\t#else\n\t\tgl_Position.z = log2( max( EPSILON, gl_Position.w + 1.0 ) ) * logDepthBufFC - 1.0;\n\t\tgl_Position.z *= gl_Position.w;\n\t#endif\n#endif\n",map_fragment:"#ifdef USE_MAP\n\tvec4 texelColor = texture2D( map, vUv );\n\ttexelColor = mapTexelToLinear( texelColor );\n\tdiffuseColor *= texelColor;\n#endif\n",map_pars_fragment:"#ifdef USE_MAP\n\tuniform sampler2D map;\n#endif\n",map_particle_fragment:"#ifdef USE_MAP\n\tvec2 uv = ( uvTransform * vec3( gl_PointCoord.x, 1.0 - gl_PointCoord.y, 1 ) ).xy;\n\tvec4 mapTexel = texture2D( map, uv );\n\tdiffuseColor *= mapTexelToLinear( mapTexel );\n#endif\n",map_particle_pars_fragment:"#ifdef USE_MAP\n\tuniform mat3 uvTransform;\n\tuniform sampler2D map;\n#endif\n",metalnessmap_fragment:"float metalnessFactor = metalness;\n#ifdef USE_METALNESSMAP\n\tvec4 texelMetalness = texture2D( metalnessMap, vUv );\n\tmetalnessFactor *= texelMetalness.b;\n#endif\n",metalnessmap_pars_fragment:"#ifdef USE_METALNESSMAP\n\tuniform sampler2D metalnessMap;\n#endif",morphnormal_vertex:"#ifdef USE_MORPHNORMALS\n\tobjectNormal += ( morphNormal0 - normal ) * morphTargetInfluences[ 0 ];\n\tobjectNormal += ( morphNormal1 - normal ) * morphTargetInfluences[ 1 ];\n\tobjectNormal += ( morphNormal2 - normal ) * morphTargetInfluences[ 2 ];\n\tobjectNormal += ( morphNormal3 - normal ) * morphTargetInfluences[ 3 ];\n#endif\n",morphtarget_pars_vertex:"#ifdef USE_MORPHTARGETS\n\t#ifndef USE_MORPHNORMALS\n\tuniform float morphTargetInfluences[ 8 ];\n\t#else\n\tuniform float morphTargetInfluences[ 4 ];\n\t#endif\n#endif",morphtarget_vertex:"#ifdef USE_MORPHTARGETS\n\ttransformed += ( morphTarget0 - position ) * morphTargetInfluences[ 0 ];\n\ttransformed += ( morphTarget1 - position ) * morphTargetInfluences[ 1 ];\n\ttransformed += ( morphTarget2 - position ) * morphTargetInfluences[ 2 ];\n\ttransformed += ( morphTarget3 - position ) * morphTargetInfluences[ 3 ];\n\t#ifndef USE_MORPHNORMALS\n\ttransformed += ( morphTarget4 - position ) * morphTargetInfluences[ 4 ];\n\ttransformed += ( morphTarget5 - position ) * morphTargetInfluences[ 5 ];\n\ttransformed += ( morphTarget6 - position ) * morphTargetInfluences[ 6 ];\n\ttransformed += ( morphTarget7 - position ) * morphTargetInfluences[ 7 ];\n\t#endif\n#endif\n",normal_fragment_begin:"#ifdef FLAT_SHADED\n\tvec3 fdx = vec3( dFdx( vViewPosition.x ), dFdx( vViewPosition.y ), dFdx( vViewPosition.z ) );\n\tvec3 fdy = vec3( dFdy( vViewPosition.x ), dFdy( vViewPosition.y ), dFdy( vViewPosition.z ) );\n\tvec3 normal = normalize( cross( fdx, fdy ) );\n#else\n\tvec3 normal = normalize( vNormal );\n\t#ifdef DOUBLE_SIDED\n\t\tnormal = normal * ( float( gl_FrontFacing ) * 2.0 - 1.0 );\n\t#endif\n#endif\n",normal_fragment_maps:"#ifdef USE_NORMALMAP\n\t#ifdef OBJECTSPACE_NORMALMAP\n\t\tnormal = texture2D( normalMap, vUv ).xyz * 2.0 - 1.0;\n\t\t#ifdef FLIP_SIDED\n\t\t\tnormal = - normal;\n\t\t#endif\n\t\t#ifdef DOUBLE_SIDED\n\t\t\tnormal = normal * ( float( gl_FrontFacing ) * 2.0 - 1.0 );\n\t\t#endif\n\t\tnormal = normalize( normalMatrix * normal );\n\t#else\n\t\tnormal = perturbNormal2Arb( -vViewPosition, normal );\n\t#endif\n#elif defined( USE_BUMPMAP )\n\tnormal = perturbNormalArb( -vViewPosition, normal, dHdxy_fwd() );\n#endif\n",normalmap_pars_fragment:"#ifdef USE_NORMALMAP\n\tuniform sampler2D normalMap;\n\tuniform vec2 normalScale;\n\t#ifdef OBJECTSPACE_NORMALMAP\n\t\tuniform mat3 normalMatrix;\n\t#else\n\t\tvec3 perturbNormal2Arb( vec3 eye_pos, vec3 surf_norm ) {\n\t\t\tvec3 q0 = vec3( dFdx( eye_pos.x ), dFdx( eye_pos.y ), dFdx( eye_pos.z ) );\n\t\t\tvec3 q1 = vec3( dFdy( eye_pos.x ), dFdy( eye_pos.y ), dFdy( eye_pos.z ) );\n\t\t\tvec2 st0 = dFdx( vUv.st );\n\t\t\tvec2 st1 = dFdy( vUv.st );\n\t\t\tfloat scale = sign( st1.t * st0.s - st0.t * st1.s );\n\t\t\tvec3 S = normalize( ( q0 * st1.t - q1 * st0.t ) * scale );\n\t\t\tvec3 T = normalize( ( - q0 * st1.s + q1 * st0.s ) * scale );\n\t\t\tvec3 N = normalize( surf_norm );\n\t\t\tmat3 tsn = mat3( S, T, N );\n\t\t\tvec3 mapN = texture2D( normalMap, vUv ).xyz * 2.0 - 1.0;\n\t\t\tmapN.xy *= normalScale;\n\t\t\tmapN.xy *= ( float( gl_FrontFacing ) * 2.0 - 1.0 );\n\t\t\treturn normalize( tsn * mapN );\n\t\t}\n\t#endif\n#endif\n",packing:"vec3 packNormalToRGB( const in vec3 normal ) {\n\treturn normalize( normal ) * 0.5 + 0.5;\n}\nvec3 unpackRGBToNormal( const in vec3 rgb ) {\n\treturn 2.0 * rgb.xyz - 1.0;\n}\nconst float PackUpscale = 256. / 255.;const float UnpackDownscale = 255. / 256.;\nconst vec3 PackFactors = vec3( 256. * 256. * 256., 256. * 256.,  256. );\nconst vec4 UnpackFactors = UnpackDownscale / vec4( PackFactors, 1. );\nconst float ShiftRight8 = 1. / 256.;\nvec4 packDepthToRGBA( const in float v ) {\n\tvec4 r = vec4( fract( v * PackFactors ), v );\n\tr.yzw -= r.xyz * ShiftRight8;\treturn r * PackUpscale;\n}\nfloat unpackRGBAToDepth( const in vec4 v ) {\n\treturn dot( v, UnpackFactors );\n}\nfloat viewZToOrthographicDepth( const in float viewZ, const in float near, const in float far ) {\n\treturn ( viewZ + near ) / ( near - far );\n}\nfloat orthographicDepthToViewZ( const in float linearClipZ, const in float near, const in float far ) {\n\treturn linearClipZ * ( near - far ) - near;\n}\nfloat viewZToPerspectiveDepth( const in float viewZ, const in float near, const in float far ) {\n\treturn (( near + viewZ ) * far ) / (( far - near ) * viewZ );\n}\nfloat perspectiveDepthToViewZ( const in float invClipZ, const in float near, const in float far ) {\n\treturn ( near * far ) / ( ( far - near ) * invClipZ - far );\n}\n",premultiplied_alpha_fragment:"#ifdef PREMULTIPLIED_ALPHA\n\tgl_FragColor.rgb *= gl_FragColor.a;\n#endif\n",project_vertex:"vec4 mvPosition = modelViewMatrix * vec4( transformed, 1.0 );\ngl_Position = projectionMatrix * mvPosition;\n",dithering_fragment:"#if defined( DITHERING )\n  gl_FragColor.rgb = dithering( gl_FragColor.rgb );\n#endif\n",dithering_pars_fragment:"#if defined( DITHERING )\n\tvec3 dithering( vec3 color ) {\n\t\tfloat grid_position = rand( gl_FragCoord.xy );\n\t\tvec3 dither_shift_RGB = vec3( 0.25 / 255.0, -0.25 / 255.0, 0.25 / 255.0 );\n\t\tdither_shift_RGB = mix( 2.0 * dither_shift_RGB, -2.0 * dither_shift_RGB, grid_position );\n\t\treturn color + dither_shift_RGB;\n\t}\n#endif\n",roughnessmap_fragment:"float roughnessFactor = roughness;\n#ifdef USE_ROUGHNESSMAP\n\tvec4 texelRoughness = texture2D( roughnessMap, vUv );\n\troughnessFactor *= texelRoughness.g;\n#endif\n",roughnessmap_pars_fragment:"#ifdef USE_ROUGHNESSMAP\n\tuniform sampler2D roughnessMap;\n#endif",shadowmap_pars_fragment:"#ifdef USE_SHADOWMAP\n\t#if NUM_DIR_LIGHTS > 0\n\t\tuniform sampler2D directionalShadowMap[ NUM_DIR_LIGHTS ];\n\t\tvarying vec4 vDirectionalShadowCoord[ NUM_DIR_LIGHTS ];\n\t#endif\n\t#if NUM_SPOT_LIGHTS > 0\n\t\tuniform sampler2D spotShadowMap[ NUM_SPOT_LIGHTS ];\n\t\tvarying vec4 vSpotShadowCoord[ NUM_SPOT_LIGHTS ];\n\t#endif\n\t#if NUM_POINT_LIGHTS > 0\n\t\tuniform sampler2D pointShadowMap[ NUM_POINT_LIGHTS ];\n\t\tvarying vec4 vPointShadowCoord[ NUM_POINT_LIGHTS ];\n\t#endif\n\tfloat texture2DCompare( sampler2D depths, vec2 uv, float compare ) {\n\t\treturn step( compare, unpackRGBAToDepth( texture2D( depths, uv ) ) );\n\t}\n\tfloat texture2DShadowLerp( sampler2D depths, vec2 size, vec2 uv, float compare ) {\n\t\tconst vec2 offset = vec2( 0.0, 1.0 );\n\t\tvec2 texelSize = vec2( 1.0 ) / size;\n\t\tvec2 centroidUV = floor( uv * size + 0.5 ) / size;\n\t\tfloat lb = texture2DCompare( depths, centroidUV + texelSize * offset.xx, compare );\n\t\tfloat lt = texture2DCompare( depths, centroidUV + texelSize * offset.xy, compare );\n\t\tfloat rb = texture2DCompare( depths, centroidUV + texelSize * offset.yx, compare );\n\t\tfloat rt = texture2DCompare( depths, centroidUV + texelSize * offset.yy, compare );\n\t\tvec2 f = fract( uv * size + 0.5 );\n\t\tfloat a = mix( lb, lt, f.y );\n\t\tfloat b = mix( rb, rt, f.y );\n\t\tfloat c = mix( a, b, f.x );\n\t\treturn c;\n\t}\n\tfloat getShadow( sampler2D shadowMap, vec2 shadowMapSize, float shadowBias, float shadowRadius, vec4 shadowCoord ) {\n\t\tfloat shadow = 1.0;\n\t\tshadowCoord.xyz /= shadowCoord.w;\n\t\tshadowCoord.z += shadowBias;\n\t\tbvec4 inFrustumVec = bvec4 ( shadowCoord.x >= 0.0, shadowCoord.x <= 1.0, shadowCoord.y >= 0.0, shadowCoord.y <= 1.0 );\n\t\tbool inFrustum = all( inFrustumVec );\n\t\tbvec2 frustumTestVec = bvec2( inFrustum, shadowCoord.z <= 1.0 );\n\t\tbool frustumTest = all( frustumTestVec );\n\t\tif ( frustumTest ) {\n\t\t#if defined( SHADOWMAP_TYPE_PCF )\n\t\t\tvec2 texelSize = vec2( 1.0 ) / shadowMapSize;\n\t\t\tfloat dx0 = - texelSize.x * shadowRadius;\n\t\t\tfloat dy0 = - texelSize.y * shadowRadius;\n\t\t\tfloat dx1 = + texelSize.x * shadowRadius;\n\t\t\tfloat dy1 = + texelSize.y * shadowRadius;\n\t\t\tshadow = (\n\t\t\t\ttexture2DCompare( shadowMap, shadowCoord.xy + vec2( dx0, dy0 ), shadowCoord.z ) +\n\t\t\t\ttexture2DCompare( shadowMap, shadowCoord.xy + vec2( 0.0, dy0 ), shadowCoord.z ) +\n\t\t\t\ttexture2DCompare( shadowMap, shadowCoord.xy + vec2( dx1, dy0 ), shadowCoord.z ) +\n\t\t\t\ttexture2DCompare( shadowMap, shadowCoord.xy + vec2( dx0, 0.0 ), shadowCoord.z ) +\n\t\t\t\ttexture2DCompare( shadowMap, shadowCoord.xy, shadowCoord.z ) +\n\t\t\t\ttexture2DCompare( shadowMap, shadowCoord.xy + vec2( dx1, 0.0 ), shadowCoord.z ) +\n\t\t\t\ttexture2DCompare( shadowMap, shadowCoord.xy + vec2( dx0, dy1 ), shadowCoord.z ) +\n\t\t\t\ttexture2DCompare( shadowMap, shadowCoord.xy + vec2( 0.0, dy1 ), shadowCoord.z ) +\n\t\t\t\ttexture2DCompare( shadowMap, shadowCoord.xy + vec2( dx1, dy1 ), shadowCoord.z )\n\t\t\t) * ( 1.0 / 9.0 );\n\t\t#elif defined( SHADOWMAP_TYPE_PCF_SOFT )\n\t\t\tvec2 texelSize = vec2( 1.0 ) / shadowMapSize;\n\t\t\tfloat dx0 = - texelSize.x * shadowRadius;\n\t\t\tfloat dy0 = - texelSize.y * shadowRadius;\n\t\t\tfloat dx1 = + texelSize.x * shadowRadius;\n\t\t\tfloat dy1 = + texelSize.y * shadowRadius;\n\t\t\tshadow = (\n\t\t\t\ttexture2DShadowLerp( shadowMap, shadowMapSize, shadowCoord.xy + vec2( dx0, dy0 ), shadowCoord.z ) +\n\t\t\t\ttexture2DShadowLerp( shadowMap, shadowMapSize, shadowCoord.xy + vec2( 0.0, dy0 ), shadowCoord.z ) +\n\t\t\t\ttexture2DShadowLerp( shadowMap, shadowMapSize, shadowCoord.xy + vec2( dx1, dy0 ), shadowCoord.z ) +\n\t\t\t\ttexture2DShadowLerp( shadowMap, shadowMapSize, shadowCoord.xy + vec2( dx0, 0.0 ), shadowCoord.z ) +\n\t\t\t\ttexture2DShadowLerp( shadowMap, shadowMapSize, shadowCoord.xy, shadowCoord.z ) +\n\t\t\t\ttexture2DShadowLerp( shadowMap, shadowMapSize, shadowCoord.xy + vec2( dx1, 0.0 ), shadowCoord.z ) +\n\t\t\t\ttexture2DShadowLerp( shadowMap, shadowMapSize, shadowCoord.xy + vec2( dx0, dy1 ), shadowCoord.z ) +\n\t\t\t\ttexture2DShadowLerp( shadowMap, shadowMapSize, shadowCoord.xy + vec2( 0.0, dy1 ), shadowCoord.z ) +\n\t\t\t\ttexture2DShadowLerp( shadowMap, shadowMapSize, shadowCoord.xy + vec2( dx1, dy1 ), shadowCoord.z )\n\t\t\t) * ( 1.0 / 9.0 );\n\t\t#else\n\t\t\tshadow = texture2DCompare( shadowMap, shadowCoord.xy, shadowCoord.z );\n\t\t#endif\n\t\t}\n\t\treturn shadow;\n\t}\n\tvec2 cubeToUV( vec3 v, float texelSizeY ) {\n\t\tvec3 absV = abs( v );\n\t\tfloat scaleToCube = 1.0 / max( absV.x, max( absV.y, absV.z ) );\n\t\tabsV *= scaleToCube;\n\t\tv *= scaleToCube * ( 1.0 - 2.0 * texelSizeY );\n\t\tvec2 planar = v.xy;\n\t\tfloat almostATexel = 1.5 * texelSizeY;\n\t\tfloat almostOne = 1.0 - almostATexel;\n\t\tif ( absV.z >= almostOne ) {\n\t\t\tif ( v.z > 0.0 )\n\t\t\t\tplanar.x = 4.0 - v.x;\n\t\t} else if ( absV.x >= almostOne ) {\n\t\t\tfloat signX = sign( v.x );\n\t\t\tplanar.x = v.z * signX + 2.0 * signX;\n\t\t} else if ( absV.y >= almostOne ) {\n\t\t\tfloat signY = sign( v.y );\n\t\t\tplanar.x = v.x + 2.0 * signY + 2.0;\n\t\t\tplanar.y = v.z * signY - 2.0;\n\t\t}\n\t\treturn vec2( 0.125, 0.25 ) * planar + vec2( 0.375, 0.75 );\n\t}\n\tfloat getPointShadow( sampler2D shadowMap, vec2 shadowMapSize, float shadowBias, float shadowRadius, vec4 shadowCoord, float shadowCameraNear, float shadowCameraFar ) {\n\t\tvec2 texelSize = vec2( 1.0 ) / ( shadowMapSize * vec2( 4.0, 2.0 ) );\n\t\tvec3 lightToPosition = shadowCoord.xyz;\n\t\tfloat dp = ( length( lightToPosition ) - shadowCameraNear ) / ( shadowCameraFar - shadowCameraNear );\t\tdp += shadowBias;\n\t\tvec3 bd3D = normalize( lightToPosition );\n\t\t#if defined( SHADOWMAP_TYPE_PCF ) || defined( SHADOWMAP_TYPE_PCF_SOFT )\n\t\t\tvec2 offset = vec2( - 1, 1 ) * shadowRadius * texelSize.y;\n\t\t\treturn (\n\t\t\t\ttexture2DCompare( shadowMap, cubeToUV( bd3D + offset.xyy, texelSize.y ), dp ) +\n\t\t\t\ttexture2DCompare( shadowMap, cubeToUV( bd3D + offset.yyy, texelSize.y ), dp ) +\n\t\t\t\ttexture2DCompare( shadowMap, cubeToUV( bd3D + offset.xyx, texelSize.y ), dp ) +\n\t\t\t\ttexture2DCompare( shadowMap, cubeToUV( bd3D + offset.yyx, texelSize.y ), dp ) +\n\t\t\t\ttexture2DCompare( shadowMap, cubeToUV( bd3D, texelSize.y ), dp ) +\n\t\t\t\ttexture2DCompare( shadowMap, cubeToUV( bd3D + offset.xxy, texelSize.y ), dp ) +\n\t\t\t\ttexture2DCompare( shadowMap, cubeToUV( bd3D + offset.yxy, texelSize.y ), dp ) +\n\t\t\t\ttexture2DCompare( shadowMap, cubeToUV( bd3D + offset.xxx, texelSize.y ), dp ) +\n\t\t\t\ttexture2DCompare( shadowMap, cubeToUV( bd3D + offset.yxx, texelSize.y ), dp )\n\t\t\t) * ( 1.0 / 9.0 );\n\t\t#else\n\t\t\treturn texture2DCompare( shadowMap, cubeToUV( bd3D, texelSize.y ), dp );\n\t\t#endif\n\t}\n#endif\n",
shadowmap_pars_vertex:"#ifdef USE_SHADOWMAP\n\t#if NUM_DIR_LIGHTS > 0\n\t\tuniform mat4 directionalShadowMatrix[ NUM_DIR_LIGHTS ];\n\t\tvarying vec4 vDirectionalShadowCoord[ NUM_DIR_LIGHTS ];\n\t#endif\n\t#if NUM_SPOT_LIGHTS > 0\n\t\tuniform mat4 spotShadowMatrix[ NUM_SPOT_LIGHTS ];\n\t\tvarying vec4 vSpotShadowCoord[ NUM_SPOT_LIGHTS ];\n\t#endif\n\t#if NUM_POINT_LIGHTS > 0\n\t\tuniform mat4 pointShadowMatrix[ NUM_POINT_LIGHTS ];\n\t\tvarying vec4 vPointShadowCoord[ NUM_POINT_LIGHTS ];\n\t#endif\n#endif\n",shadowmap_vertex:"#ifdef USE_SHADOWMAP\n\t#if NUM_DIR_LIGHTS > 0\n\t#pragma unroll_loop\n\tfor ( int i = 0; i < NUM_DIR_LIGHTS; i ++ ) {\n\t\tvDirectionalShadowCoord[ i ] = directionalShadowMatrix[ i ] * worldPosition;\n\t}\n\t#endif\n\t#if NUM_SPOT_LIGHTS > 0\n\t#pragma unroll_loop\n\tfor ( int i = 0; i < NUM_SPOT_LIGHTS; i ++ ) {\n\t\tvSpotShadowCoord[ i ] = spotShadowMatrix[ i ] * worldPosition;\n\t}\n\t#endif\n\t#if NUM_POINT_LIGHTS > 0\n\t#pragma unroll_loop\n\tfor ( int i = 0; i < NUM_POINT_LIGHTS; i ++ ) {\n\t\tvPointShadowCoord[ i ] = pointShadowMatrix[ i ] * worldPosition;\n\t}\n\t#endif\n#endif\n",shadowmask_pars_fragment:"float getShadowMask() {\n\tfloat shadow = 1.0;\n\t#ifdef USE_SHADOWMAP\n\t#if NUM_DIR_LIGHTS > 0\n\tDirectionalLight directionalLight;\n\t#pragma unroll_loop\n\tfor ( int i = 0; i < NUM_DIR_LIGHTS; i ++ ) {\n\t\tdirectionalLight = directionalLights[ i ];\n\t\tshadow *= bool( directionalLight.shadow ) ? getShadow( directionalShadowMap[ i ], directionalLight.shadowMapSize, directionalLight.shadowBias, directionalLight.shadowRadius, vDirectionalShadowCoord[ i ] ) : 1.0;\n\t}\n\t#endif\n\t#if NUM_SPOT_LIGHTS > 0\n\tSpotLight spotLight;\n\t#pragma unroll_loop\n\tfor ( int i = 0; i < NUM_SPOT_LIGHTS; i ++ ) {\n\t\tspotLight = spotLights[ i ];\n\t\tshadow *= bool( spotLight.shadow ) ? getShadow( spotShadowMap[ i ], spotLight.shadowMapSize, spotLight.shadowBias, spotLight.shadowRadius, vSpotShadowCoord[ i ] ) : 1.0;\n\t}\n\t#endif\n\t#if NUM_POINT_LIGHTS > 0\n\tPointLight pointLight;\n\t#pragma unroll_loop\n\tfor ( int i = 0; i < NUM_POINT_LIGHTS; i ++ ) {\n\t\tpointLight = pointLights[ i ];\n\t\tshadow *= bool( pointLight.shadow ) ? getPointShadow( pointShadowMap[ i ], pointLight.shadowMapSize, pointLight.shadowBias, pointLight.shadowRadius, vPointShadowCoord[ i ], pointLight.shadowCameraNear, pointLight.shadowCameraFar ) : 1.0;\n\t}\n\t#endif\n\t#endif\n\treturn shadow;\n}\n",skinbase_vertex:"#ifdef USE_SKINNING\n\tmat4 boneMatX = getBoneMatrix( skinIndex.x );\n\tmat4 boneMatY = getBoneMatrix( skinIndex.y );\n\tmat4 boneMatZ = getBoneMatrix( skinIndex.z );\n\tmat4 boneMatW = getBoneMatrix( skinIndex.w );\n#endif",skinning_pars_vertex:"#ifdef USE_SKINNING\n\tuniform mat4 bindMatrix;\n\tuniform mat4 bindMatrixInverse;\n\t#ifdef BONE_TEXTURE\n\t\tuniform sampler2D boneTexture;\n\t\tuniform int boneTextureSize;\n\t\tmat4 getBoneMatrix( const in float i ) {\n\t\t\tfloat j = i * 4.0;\n\t\t\tfloat x = mod( j, float( boneTextureSize ) );\n\t\t\tfloat y = floor( j / float( boneTextureSize ) );\n\t\t\tfloat dx = 1.0 / float( boneTextureSize );\n\t\t\tfloat dy = 1.0 / float( boneTextureSize );\n\t\t\ty = dy * ( y + 0.5 );\n\t\t\tvec4 v1 = texture2D( boneTexture, vec2( dx * ( x + 0.5 ), y ) );\n\t\t\tvec4 v2 = texture2D( boneTexture, vec2( dx * ( x + 1.5 ), y ) );\n\t\t\tvec4 v3 = texture2D( boneTexture, vec2( dx * ( x + 2.5 ), y ) );\n\t\t\tvec4 v4 = texture2D( boneTexture, vec2( dx * ( x + 3.5 ), y ) );\n\t\t\tmat4 bone = mat4( v1, v2, v3, v4 );\n\t\t\treturn bone;\n\t\t}\n\t#else\n\t\tuniform mat4 boneMatrices[ MAX_BONES ];\n\t\tmat4 getBoneMatrix( const in float i ) {\n\t\t\tmat4 bone = boneMatrices[ int(i) ];\n\t\t\treturn bone;\n\t\t}\n\t#endif\n#endif\n",skinning_vertex:"#ifdef USE_SKINNING\n\tvec4 skinVertex = bindMatrix * vec4( transformed, 1.0 );\n\tvec4 skinned = vec4( 0.0 );\n\tskinned += boneMatX * skinVertex * skinWeight.x;\n\tskinned += boneMatY * skinVertex * skinWeight.y;\n\tskinned += boneMatZ * skinVertex * skinWeight.z;\n\tskinned += boneMatW * skinVertex * skinWeight.w;\n\ttransformed = ( bindMatrixInverse * skinned ).xyz;\n#endif\n",skinnormal_vertex:"#ifdef USE_SKINNING\n\tmat4 skinMatrix = mat4( 0.0 );\n\tskinMatrix += skinWeight.x * boneMatX;\n\tskinMatrix += skinWeight.y * boneMatY;\n\tskinMatrix += skinWeight.z * boneMatZ;\n\tskinMatrix += skinWeight.w * boneMatW;\n\tskinMatrix  = bindMatrixInverse * skinMatrix * bindMatrix;\n\tobjectNormal = vec4( skinMatrix * vec4( objectNormal, 0.0 ) ).xyz;\n#endif\n",specularmap_fragment:"float specularStrength;\n#ifdef USE_SPECULARMAP\n\tvec4 texelSpecular = texture2D( specularMap, vUv );\n\tspecularStrength = texelSpecular.r;\n#else\n\tspecularStrength = 1.0;\n#endif",specularmap_pars_fragment:"#ifdef USE_SPECULARMAP\n\tuniform sampler2D specularMap;\n#endif",tonemapping_fragment:"#if defined( TONE_MAPPING )\n  gl_FragColor.rgb = toneMapping( gl_FragColor.rgb );\n#endif\n",tonemapping_pars_fragment:"#ifndef saturate\n\t#define saturate(a) clamp( a, 0.0, 1.0 )\n#endif\nuniform float toneMappingExposure;\nuniform float toneMappingWhitePoint;\nvec3 LinearToneMapping( vec3 color ) {\n\treturn toneMappingExposure * color;\n}\nvec3 ReinhardToneMapping( vec3 color ) {\n\tcolor *= toneMappingExposure;\n\treturn saturate( color / ( vec3( 1.0 ) + color ) );\n}\n#define Uncharted2Helper( x ) max( ( ( x * ( 0.15 * x + 0.10 * 0.50 ) + 0.20 * 0.02 ) / ( x * ( 0.15 * x + 0.50 ) + 0.20 * 0.30 ) ) - 0.02 / 0.30, vec3( 0.0 ) )\nvec3 Uncharted2ToneMapping( vec3 color ) {\n\tcolor *= toneMappingExposure;\n\treturn saturate( Uncharted2Helper( color ) / Uncharted2Helper( vec3( toneMappingWhitePoint ) ) );\n}\nvec3 OptimizedCineonToneMapping( vec3 color ) {\n\tcolor *= toneMappingExposure;\n\tcolor = max( vec3( 0.0 ), color - 0.004 );\n\treturn pow( ( color * ( 6.2 * color + 0.5 ) ) / ( color * ( 6.2 * color + 1.7 ) + 0.06 ), vec3( 2.2 ) );\n}\n",uv_pars_fragment:"#if defined( USE_MAP ) || defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( USE_SPECULARMAP ) || defined( USE_ALPHAMAP ) || defined( USE_EMISSIVEMAP ) || defined( USE_ROUGHNESSMAP ) || defined( USE_METALNESSMAP )\n\tvarying vec2 vUv;\n#endif",uv_pars_vertex:"#if defined( USE_MAP ) || defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( USE_SPECULARMAP ) || defined( USE_ALPHAMAP ) || defined( USE_EMISSIVEMAP ) || defined( USE_ROUGHNESSMAP ) || defined( USE_METALNESSMAP )\n\tvarying vec2 vUv;\n\tuniform mat3 uvTransform;\n#endif\n",uv_vertex:"#if defined( USE_MAP ) || defined( USE_BUMPMAP ) || defined( USE_NORMALMAP ) || defined( USE_SPECULARMAP ) || defined( USE_ALPHAMAP ) || defined( USE_EMISSIVEMAP ) || defined( USE_ROUGHNESSMAP ) || defined( USE_METALNESSMAP )\n\tvUv = ( uvTransform * vec3( uv, 1 ) ).xy;\n#endif",uv2_pars_fragment:"#if defined( USE_LIGHTMAP ) || defined( USE_AOMAP )\n\tvarying vec2 vUv2;\n#endif",uv2_pars_vertex:"#if defined( USE_LIGHTMAP ) || defined( USE_AOMAP )\n\tattribute vec2 uv2;\n\tvarying vec2 vUv2;\n#endif",uv2_vertex:"#if defined( USE_LIGHTMAP ) || defined( USE_AOMAP )\n\tvUv2 = uv2;\n#endif",worldpos_vertex:"#if defined( USE_ENVMAP ) || defined( DISTANCE ) || defined ( USE_SHADOWMAP )\n\tvec4 worldPosition = modelMatrix * vec4( transformed, 1.0 );\n#endif\n",cube_frag:"uniform samplerCube tCube;\nuniform float tFlip;\nuniform float opacity;\nvarying vec3 vWorldPosition;\nvoid main() {\n\tgl_FragColor = textureCube( tCube, vec3( tFlip * vWorldPosition.x, vWorldPosition.yz ) );\n\tgl_FragColor.a *= opacity;\n}\n",cube_vert:"varying vec3 vWorldPosition;\n#include <common>\nvoid main() {\n\tvWorldPosition = transformDirection( position, modelMatrix );\n\t#include <begin_vertex>\n\t#include <project_vertex>\n\tgl_Position.z = gl_Position.w;\n}\n",depth_frag:"#if DEPTH_PACKING == 3200\n\tuniform float opacity;\n#endif\n#include <common>\n#include <packing>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n\t#include <clipping_planes_fragment>\n\tvec4 diffuseColor = vec4( 1.0 );\n\t#if DEPTH_PACKING == 3200\n\t\tdiffuseColor.a = opacity;\n\t#endif\n\t#include <map_fragment>\n\t#include <alphamap_fragment>\n\t#include <alphatest_fragment>\n\t#include <logdepthbuf_fragment>\n\t#if DEPTH_PACKING == 3200\n\t\tgl_FragColor = vec4( vec3( 1.0 - gl_FragCoord.z ), opacity );\n\t#elif DEPTH_PACKING == 3201\n\t\tgl_FragColor = packDepthToRGBA( gl_FragCoord.z );\n\t#endif\n}\n",depth_vert:"#include <common>\n#include <uv_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n\t#include <uv_vertex>\n\t#include <skinbase_vertex>\n\t#ifdef USE_DISPLACEMENTMAP\n\t\t#include <beginnormal_vertex>\n\t\t#include <morphnormal_vertex>\n\t\t#include <skinnormal_vertex>\n\t#endif\n\t#include <begin_vertex>\n\t#include <morphtarget_vertex>\n\t#include <skinning_vertex>\n\t#include <displacementmap_vertex>\n\t#include <project_vertex>\n\t#include <logdepthbuf_vertex>\n\t#include <clipping_planes_vertex>\n}\n",distanceRGBA_frag:"#define DISTANCE\nuniform vec3 referencePosition;\nuniform float nearDistance;\nuniform float farDistance;\nvarying vec3 vWorldPosition;\n#include <common>\n#include <packing>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main () {\n\t#include <clipping_planes_fragment>\n\tvec4 diffuseColor = vec4( 1.0 );\n\t#include <map_fragment>\n\t#include <alphamap_fragment>\n\t#include <alphatest_fragment>\n\tfloat dist = length( vWorldPosition - referencePosition );\n\tdist = ( dist - nearDistance ) / ( farDistance - nearDistance );\n\tdist = saturate( dist );\n\tgl_FragColor = packDepthToRGBA( dist );\n}\n",distanceRGBA_vert:"#define DISTANCE\nvarying vec3 vWorldPosition;\n#include <common>\n#include <uv_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n\t#include <uv_vertex>\n\t#include <skinbase_vertex>\n\t#ifdef USE_DISPLACEMENTMAP\n\t\t#include <beginnormal_vertex>\n\t\t#include <morphnormal_vertex>\n\t\t#include <skinnormal_vertex>\n\t#endif\n\t#include <begin_vertex>\n\t#include <morphtarget_vertex>\n\t#include <skinning_vertex>\n\t#include <displacementmap_vertex>\n\t#include <project_vertex>\n\t#include <worldpos_vertex>\n\t#include <clipping_planes_vertex>\n\tvWorldPosition = worldPosition.xyz;\n}\n",equirect_frag:"uniform sampler2D tEquirect;\nvarying vec3 vWorldPosition;\n#include <common>\nvoid main() {\n\tvec3 direction = normalize( vWorldPosition );\n\tvec2 sampleUV;\n\tsampleUV.y = asin( clamp( direction.y, - 1.0, 1.0 ) ) * RECIPROCAL_PI + 0.5;\n\tsampleUV.x = atan( direction.z, direction.x ) * RECIPROCAL_PI2 + 0.5;\n\tgl_FragColor = texture2D( tEquirect, sampleUV );\n}\n",equirect_vert:"varying vec3 vWorldPosition;\n#include <common>\nvoid main() {\n\tvWorldPosition = transformDirection( position, modelMatrix );\n\t#include <begin_vertex>\n\t#include <project_vertex>\n}\n",linedashed_frag:"uniform vec3 diffuse;\nuniform float opacity;\nuniform float dashSize;\nuniform float totalSize;\nvarying float vLineDistance;\n#include <common>\n#include <color_pars_fragment>\n#include <fog_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n\t#include <clipping_planes_fragment>\n\tif ( mod( vLineDistance, totalSize ) > dashSize ) {\n\t\tdiscard;\n\t}\n\tvec3 outgoingLight = vec3( 0.0 );\n\tvec4 diffuseColor = vec4( diffuse, opacity );\n\t#include <logdepthbuf_fragment>\n\t#include <color_fragment>\n\toutgoingLight = diffuseColor.rgb;\n\tgl_FragColor = vec4( outgoingLight, diffuseColor.a );\n\t#include <premultiplied_alpha_fragment>\n\t#include <tonemapping_fragment>\n\t#include <encodings_fragment>\n\t#include <fog_fragment>\n}\n",linedashed_vert:"uniform float scale;\nattribute float lineDistance;\nvarying float vLineDistance;\n#include <common>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n\t#include <color_vertex>\n\tvLineDistance = scale * lineDistance;\n\tvec4 mvPosition = modelViewMatrix * vec4( position, 1.0 );\n\tgl_Position = projectionMatrix * mvPosition;\n\t#include <logdepthbuf_vertex>\n\t#include <clipping_planes_vertex>\n\t#include <fog_vertex>\n}\n",meshbasic_frag:"uniform vec3 diffuse;\nuniform float opacity;\n#ifndef FLAT_SHADED\n\tvarying vec3 vNormal;\n#endif\n#include <common>\n#include <color_pars_fragment>\n#include <uv_pars_fragment>\n#include <uv2_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <aomap_pars_fragment>\n#include <lightmap_pars_fragment>\n#include <envmap_pars_fragment>\n#include <fog_pars_fragment>\n#include <specularmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n\t#include <clipping_planes_fragment>\n\tvec4 diffuseColor = vec4( diffuse, opacity );\n\t#include <logdepthbuf_fragment>\n\t#include <map_fragment>\n\t#include <color_fragment>\n\t#include <alphamap_fragment>\n\t#include <alphatest_fragment>\n\t#include <specularmap_fragment>\n\tReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );\n\t#ifdef USE_LIGHTMAP\n\t\treflectedLight.indirectDiffuse += texture2D( lightMap, vUv2 ).xyz * lightMapIntensity;\n\t#else\n\t\treflectedLight.indirectDiffuse += vec3( 1.0 );\n\t#endif\n\t#include <aomap_fragment>\n\treflectedLight.indirectDiffuse *= diffuseColor.rgb;\n\tvec3 outgoingLight = reflectedLight.indirectDiffuse;\n\t#include <envmap_fragment>\n\tgl_FragColor = vec4( outgoingLight, diffuseColor.a );\n\t#include <premultiplied_alpha_fragment>\n\t#include <tonemapping_fragment>\n\t#include <encodings_fragment>\n\t#include <fog_fragment>\n}\n",meshbasic_vert:"#include <common>\n#include <uv_pars_vertex>\n#include <uv2_pars_vertex>\n#include <envmap_pars_vertex>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n\t#include <uv_vertex>\n\t#include <uv2_vertex>\n\t#include <color_vertex>\n\t#include <skinbase_vertex>\n\t#ifdef USE_ENVMAP\n\t#include <beginnormal_vertex>\n\t#include <morphnormal_vertex>\n\t#include <skinnormal_vertex>\n\t#include <defaultnormal_vertex>\n\t#endif\n\t#include <begin_vertex>\n\t#include <morphtarget_vertex>\n\t#include <skinning_vertex>\n\t#include <project_vertex>\n\t#include <logdepthbuf_vertex>\n\t#include <worldpos_vertex>\n\t#include <clipping_planes_vertex>\n\t#include <envmap_vertex>\n\t#include <fog_vertex>\n}\n",meshlambert_frag:"uniform vec3 diffuse;\nuniform vec3 emissive;\nuniform float opacity;\nvarying vec3 vLightFront;\n#ifdef DOUBLE_SIDED\n\tvarying vec3 vLightBack;\n#endif\n#include <common>\n#include <packing>\n#include <dithering_pars_fragment>\n#include <color_pars_fragment>\n#include <uv_pars_fragment>\n#include <uv2_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <aomap_pars_fragment>\n#include <lightmap_pars_fragment>\n#include <emissivemap_pars_fragment>\n#include <envmap_pars_fragment>\n#include <bsdfs>\n#include <lights_pars_begin>\n#include <fog_pars_fragment>\n#include <shadowmap_pars_fragment>\n#include <shadowmask_pars_fragment>\n#include <specularmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n\t#include <clipping_planes_fragment>\n\tvec4 diffuseColor = vec4( diffuse, opacity );\n\tReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );\n\tvec3 totalEmissiveRadiance = emissive;\n\t#include <logdepthbuf_fragment>\n\t#include <map_fragment>\n\t#include <color_fragment>\n\t#include <alphamap_fragment>\n\t#include <alphatest_fragment>\n\t#include <specularmap_fragment>\n\t#include <emissivemap_fragment>\n\treflectedLight.indirectDiffuse = getAmbientLightIrradiance( ambientLightColor );\n\t#include <lightmap_fragment>\n\treflectedLight.indirectDiffuse *= BRDF_Diffuse_Lambert( diffuseColor.rgb );\n\t#ifdef DOUBLE_SIDED\n\t\treflectedLight.directDiffuse = ( gl_FrontFacing ) ? vLightFront : vLightBack;\n\t#else\n\t\treflectedLight.directDiffuse = vLightFront;\n\t#endif\n\treflectedLight.directDiffuse *= BRDF_Diffuse_Lambert( diffuseColor.rgb ) * getShadowMask();\n\t#include <aomap_fragment>\n\tvec3 outgoingLight = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse + totalEmissiveRadiance;\n\t#include <envmap_fragment>\n\tgl_FragColor = vec4( outgoingLight, diffuseColor.a );\n\t#include <tonemapping_fragment>\n\t#include <encodings_fragment>\n\t#include <fog_fragment>\n\t#include <premultiplied_alpha_fragment>\n\t#include <dithering_fragment>\n}\n",meshlambert_vert:"#define LAMBERT\nvarying vec3 vLightFront;\n#ifdef DOUBLE_SIDED\n\tvarying vec3 vLightBack;\n#endif\n#include <common>\n#include <uv_pars_vertex>\n#include <uv2_pars_vertex>\n#include <envmap_pars_vertex>\n#include <bsdfs>\n#include <lights_pars_begin>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <shadowmap_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n\t#include <uv_vertex>\n\t#include <uv2_vertex>\n\t#include <color_vertex>\n\t#include <beginnormal_vertex>\n\t#include <morphnormal_vertex>\n\t#include <skinbase_vertex>\n\t#include <skinnormal_vertex>\n\t#include <defaultnormal_vertex>\n\t#include <begin_vertex>\n\t#include <morphtarget_vertex>\n\t#include <skinning_vertex>\n\t#include <project_vertex>\n\t#include <logdepthbuf_vertex>\n\t#include <clipping_planes_vertex>\n\t#include <worldpos_vertex>\n\t#include <envmap_vertex>\n\t#include <lights_lambert_vertex>\n\t#include <shadowmap_vertex>\n\t#include <fog_vertex>\n}\n",meshphong_frag:"#define PHONG\nuniform vec3 diffuse;\nuniform vec3 emissive;\nuniform vec3 specular;\nuniform float shininess;\nuniform float opacity;\n#include <common>\n#include <packing>\n#include <dithering_pars_fragment>\n#include <color_pars_fragment>\n#include <uv_pars_fragment>\n#include <uv2_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <aomap_pars_fragment>\n#include <lightmap_pars_fragment>\n#include <emissivemap_pars_fragment>\n#include <envmap_pars_fragment>\n#include <gradientmap_pars_fragment>\n#include <fog_pars_fragment>\n#include <bsdfs>\n#include <lights_pars_begin>\n#include <lights_phong_pars_fragment>\n#include <shadowmap_pars_fragment>\n#include <bumpmap_pars_fragment>\n#include <normalmap_pars_fragment>\n#include <specularmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n\t#include <clipping_planes_fragment>\n\tvec4 diffuseColor = vec4( diffuse, opacity );\n\tReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );\n\tvec3 totalEmissiveRadiance = emissive;\n\t#include <logdepthbuf_fragment>\n\t#include <map_fragment>\n\t#include <color_fragment>\n\t#include <alphamap_fragment>\n\t#include <alphatest_fragment>\n\t#include <specularmap_fragment>\n\t#include <normal_fragment_begin>\n\t#include <normal_fragment_maps>\n\t#include <emissivemap_fragment>\n\t#include <lights_phong_fragment>\n\t#include <lights_fragment_begin>\n\t#include <lights_fragment_maps>\n\t#include <lights_fragment_end>\n\t#include <aomap_fragment>\n\tvec3 outgoingLight = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse + reflectedLight.directSpecular + reflectedLight.indirectSpecular + totalEmissiveRadiance;\n\t#include <envmap_fragment>\n\tgl_FragColor = vec4( outgoingLight, diffuseColor.a );\n\t#include <tonemapping_fragment>\n\t#include <encodings_fragment>\n\t#include <fog_fragment>\n\t#include <premultiplied_alpha_fragment>\n\t#include <dithering_fragment>\n}\n",meshphong_vert:"#define PHONG\nvarying vec3 vViewPosition;\n#ifndef FLAT_SHADED\n\tvarying vec3 vNormal;\n#endif\n#include <common>\n#include <uv_pars_vertex>\n#include <uv2_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <envmap_pars_vertex>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <shadowmap_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n\t#include <uv_vertex>\n\t#include <uv2_vertex>\n\t#include <color_vertex>\n\t#include <beginnormal_vertex>\n\t#include <morphnormal_vertex>\n\t#include <skinbase_vertex>\n\t#include <skinnormal_vertex>\n\t#include <defaultnormal_vertex>\n#ifndef FLAT_SHADED\n\tvNormal = normalize( transformedNormal );\n#endif\n\t#include <begin_vertex>\n\t#include <morphtarget_vertex>\n\t#include <skinning_vertex>\n\t#include <displacementmap_vertex>\n\t#include <project_vertex>\n\t#include <logdepthbuf_vertex>\n\t#include <clipping_planes_vertex>\n\tvViewPosition = - mvPosition.xyz;\n\t#include <worldpos_vertex>\n\t#include <envmap_vertex>\n\t#include <shadowmap_vertex>\n\t#include <fog_vertex>\n}\n",meshphysical_frag:"#define PHYSICAL\nuniform vec3 diffuse;\nuniform vec3 emissive;\nuniform float roughness;\nuniform float metalness;\nuniform float opacity;\n#ifndef STANDARD\n\tuniform float clearCoat;\n\tuniform float clearCoatRoughness;\n#endif\nvarying vec3 vViewPosition;\n#ifndef FLAT_SHADED\n\tvarying vec3 vNormal;\n#endif\n#include <common>\n#include <packing>\n#include <dithering_pars_fragment>\n#include <color_pars_fragment>\n#include <uv_pars_fragment>\n#include <uv2_pars_fragment>\n#include <map_pars_fragment>\n#include <alphamap_pars_fragment>\n#include <aomap_pars_fragment>\n#include <lightmap_pars_fragment>\n#include <emissivemap_pars_fragment>\n#include <bsdfs>\n#include <cube_uv_reflection_fragment>\n#include <envmap_pars_fragment>\n#include <envmap_physical_pars_fragment>\n#include <fog_pars_fragment>\n#include <lights_pars_begin>\n#include <lights_physical_pars_fragment>\n#include <shadowmap_pars_fragment>\n#include <bumpmap_pars_fragment>\n#include <normalmap_pars_fragment>\n#include <roughnessmap_pars_fragment>\n#include <metalnessmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n\t#include <clipping_planes_fragment>\n\tvec4 diffuseColor = vec4( diffuse, opacity );\n\tReflectedLight reflectedLight = ReflectedLight( vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ), vec3( 0.0 ) );\n\tvec3 totalEmissiveRadiance = emissive;\n\t#include <logdepthbuf_fragment>\n\t#include <map_fragment>\n\t#include <color_fragment>\n\t#include <alphamap_fragment>\n\t#include <alphatest_fragment>\n\t#include <roughnessmap_fragment>\n\t#include <metalnessmap_fragment>\n\t#include <normal_fragment_begin>\n\t#include <normal_fragment_maps>\n\t#include <emissivemap_fragment>\n\t#include <lights_physical_fragment>\n\t#include <lights_fragment_begin>\n\t#include <lights_fragment_maps>\n\t#include <lights_fragment_end>\n\t#include <aomap_fragment>\n\tvec3 outgoingLight = reflectedLight.directDiffuse + reflectedLight.indirectDiffuse + reflectedLight.directSpecular + reflectedLight.indirectSpecular + totalEmissiveRadiance;\n\tgl_FragColor = vec4( outgoingLight, diffuseColor.a );\n\t#include <tonemapping_fragment>\n\t#include <encodings_fragment>\n\t#include <fog_fragment>\n\t#include <premultiplied_alpha_fragment>\n\t#include <dithering_fragment>\n}\n",meshphysical_vert:"#define PHYSICAL\nvarying vec3 vViewPosition;\n#ifndef FLAT_SHADED\n\tvarying vec3 vNormal;\n#endif\n#include <common>\n#include <uv_pars_vertex>\n#include <uv2_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <shadowmap_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n\t#include <uv_vertex>\n\t#include <uv2_vertex>\n\t#include <color_vertex>\n\t#include <beginnormal_vertex>\n\t#include <morphnormal_vertex>\n\t#include <skinbase_vertex>\n\t#include <skinnormal_vertex>\n\t#include <defaultnormal_vertex>\n#ifndef FLAT_SHADED\n\tvNormal = normalize( transformedNormal );\n#endif\n\t#include <begin_vertex>\n\t#include <morphtarget_vertex>\n\t#include <skinning_vertex>\n\t#include <displacementmap_vertex>\n\t#include <project_vertex>\n\t#include <logdepthbuf_vertex>\n\t#include <clipping_planes_vertex>\n\tvViewPosition = - mvPosition.xyz;\n\t#include <worldpos_vertex>\n\t#include <shadowmap_vertex>\n\t#include <fog_vertex>\n}\n",normal_frag:"#define NORMAL\nuniform float opacity;\n#if defined( FLAT_SHADED ) || defined( USE_BUMPMAP ) || ( defined( USE_NORMALMAP ) && ! defined( OBJECTSPACE_NORMALMAP ) )\n\tvarying vec3 vViewPosition;\n#endif\n#ifndef FLAT_SHADED\n\tvarying vec3 vNormal;\n#endif\n#include <packing>\n#include <uv_pars_fragment>\n#include <bumpmap_pars_fragment>\n#include <normalmap_pars_fragment>\n#include <logdepthbuf_pars_fragment>\nvoid main() {\n\t#include <logdepthbuf_fragment>\n\t#include <normal_fragment_begin>\n\t#include <normal_fragment_maps>\n\tgl_FragColor = vec4( packNormalToRGB( normal ), opacity );\n}\n",normal_vert:"#define NORMAL\n#if defined( FLAT_SHADED ) || defined( USE_BUMPMAP ) || ( defined( USE_NORMALMAP ) && ! defined( OBJECTSPACE_NORMALMAP ) )\n\tvarying vec3 vViewPosition;\n#endif\n#ifndef FLAT_SHADED\n\tvarying vec3 vNormal;\n#endif\n#include <uv_pars_vertex>\n#include <displacementmap_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <skinning_pars_vertex>\n#include <logdepthbuf_pars_vertex>\nvoid main() {\n\t#include <uv_vertex>\n\t#include <beginnormal_vertex>\n\t#include <morphnormal_vertex>\n\t#include <skinbase_vertex>\n\t#include <skinnormal_vertex>\n\t#include <defaultnormal_vertex>\n#ifndef FLAT_SHADED\n\tvNormal = normalize( transformedNormal );\n#endif\n\t#include <begin_vertex>\n\t#include <morphtarget_vertex>\n\t#include <skinning_vertex>\n\t#include <displacementmap_vertex>\n\t#include <project_vertex>\n\t#include <logdepthbuf_vertex>\n#if defined( FLAT_SHADED ) || defined( USE_BUMPMAP ) || ( defined( USE_NORMALMAP ) && ! defined( OBJECTSPACE_NORMALMAP ) )\n\tvViewPosition = - mvPosition.xyz;\n#endif\n}\n",points_frag:"uniform vec3 diffuse;\nuniform float opacity;\n#include <common>\n#include <color_pars_fragment>\n#include <map_particle_pars_fragment>\n#include <fog_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n\t#include <clipping_planes_fragment>\n\tvec3 outgoingLight = vec3( 0.0 );\n\tvec4 diffuseColor = vec4( diffuse, opacity );\n\t#include <logdepthbuf_fragment>\n\t#include <map_particle_fragment>\n\t#include <color_fragment>\n\t#include <alphatest_fragment>\n\toutgoingLight = diffuseColor.rgb;\n\tgl_FragColor = vec4( outgoingLight, diffuseColor.a );\n\t#include <premultiplied_alpha_fragment>\n\t#include <tonemapping_fragment>\n\t#include <encodings_fragment>\n\t#include <fog_fragment>\n}\n",points_vert:"uniform float size;\nuniform float scale;\n#include <common>\n#include <color_pars_vertex>\n#include <fog_pars_vertex>\n#include <morphtarget_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n\t#include <color_vertex>\n\t#include <begin_vertex>\n\t#include <morphtarget_vertex>\n\t#include <project_vertex>\n\tgl_PointSize = size;\n\t#ifdef USE_SIZEATTENUATION\n\t\tbool isPerspective = ( projectionMatrix[ 2 ][ 3 ] == - 1.0 );\n\t\tif ( isPerspective ) gl_PointSize *= ( scale / - mvPosition.z );\n\t#endif\n\t#include <logdepthbuf_vertex>\n\t#include <clipping_planes_vertex>\n\t#include <worldpos_vertex>\n\t#include <fog_vertex>\n}\n",shadow_frag:"uniform vec3 color;\nuniform float opacity;\n#include <common>\n#include <packing>\n#include <fog_pars_fragment>\n#include <bsdfs>\n#include <lights_pars_begin>\n#include <shadowmap_pars_fragment>\n#include <shadowmask_pars_fragment>\nvoid main() {\n\tgl_FragColor = vec4( color, opacity * ( 1.0 - getShadowMask() ) );\n\t#include <fog_fragment>\n}\n",shadow_vert:"#include <fog_pars_vertex>\n#include <shadowmap_pars_vertex>\nvoid main() {\n\t#include <begin_vertex>\n\t#include <project_vertex>\n\t#include <worldpos_vertex>\n\t#include <shadowmap_vertex>\n\t#include <fog_vertex>\n}\n",sprite_frag:"uniform vec3 diffuse;\nuniform float opacity;\n#include <common>\n#include <uv_pars_fragment>\n#include <map_pars_fragment>\n#include <fog_pars_fragment>\n#include <logdepthbuf_pars_fragment>\n#include <clipping_planes_pars_fragment>\nvoid main() {\n\t#include <clipping_planes_fragment>\n\tvec3 outgoingLight = vec3( 0.0 );\n\tvec4 diffuseColor = vec4( diffuse, opacity );\n\t#include <logdepthbuf_fragment>\n\t#include <map_fragment>\n\t#include <alphatest_fragment>\n\toutgoingLight = diffuseColor.rgb;\n\tgl_FragColor = vec4( outgoingLight, diffuseColor.a );\n\t#include <tonemapping_fragment>\n\t#include <encodings_fragment>\n\t#include <fog_fragment>\n}\n",sprite_vert:"uniform float rotation;\nuniform vec2 center;\n#include <common>\n#include <uv_pars_vertex>\n#include <fog_pars_vertex>\n#include <logdepthbuf_pars_vertex>\n#include <clipping_planes_pars_vertex>\nvoid main() {\n\t#include <uv_vertex>\n\tvec4 mvPosition = modelViewMatrix * vec4( 0.0, 0.0, 0.0, 1.0 );\n\tvec2 scale;\n\tscale.x = length( vec3( modelMatrix[ 0 ].x, modelMatrix[ 0 ].y, modelMatrix[ 0 ].z ) );\n\tscale.y = length( vec3( modelMatrix[ 1 ].x, modelMatrix[ 1 ].y, modelMatrix[ 1 ].z ) );\n\t#ifndef USE_SIZEATTENUATION\n\t\tbool isPerspective = ( projectionMatrix[ 2 ][ 3 ] == - 1.0 );\n\t\tif ( isPerspective ) scale *= - mvPosition.z;\n\t#endif\n\tvec2 alignedPosition = ( position.xy - ( center - vec2( 0.5 ) ) ) * scale;\n\tvec2 rotatedPosition;\n\trotatedPosition.x = cos( rotation ) * alignedPosition.x - sin( rotation ) * alignedPosition.y;\n\trotatedPosition.y = sin( rotation ) * alignedPosition.x + cos( rotation ) * alignedPosition.y;\n\tmvPosition.xy += rotatedPosition;\n\tgl_Position = projectionMatrix * mvPosition;\n\t#include <logdepthbuf_vertex>\n\t#include <clipping_planes_vertex>\n\t#include <fog_vertex>\n}\n"},na={merge:function(t){for(var e={},i=0;i<t.length;i++){var n,r=this.clone(t[i]);for(n in r)e[n]=r[n]}return e},clone:function(t){var e,i={};for(e in t){i[e]={};for(var n in t[e]){var r=t[e][n];r&&(r.isColor||r.isMatrix3||r.isMatrix4||r.isVector2||r.isVector3||r.isVector4||r.isTexture)?i[e][n]=r.clone():Array.isArray(r)?i[e][n]=r.slice():i[e][n]=r}}return i}},ra={aliceblue:15792383,antiquewhite:16444375,aqua:65535,aquamarine:8388564,azure:15794175,beige:16119260,bisque:16770244,black:0,blanchedalmond:16772045,blue:255,blueviolet:9055202,brown:10824234,burlywood:14596231,cadetblue:6266528,chartreuse:8388352,chocolate:13789470,coral:16744272,cornflowerblue:6591981,cornsilk:16775388,crimson:14423100,cyan:65535,darkblue:139,darkcyan:35723,darkgoldenrod:12092939,darkgray:11119017,darkgreen:25600,darkgrey:11119017,darkkhaki:12433259,darkmagenta:9109643,darkolivegreen:5597999,darkorange:16747520,darkorchid:10040012,darkred:9109504,darksalmon:15308410,darkseagreen:9419919,darkslateblue:4734347,darkslategray:3100495,darkslategrey:3100495,darkturquoise:52945,darkviolet:9699539,deeppink:16716947,deepskyblue:49151,dimgray:6908265,dimgrey:6908265,dodgerblue:2003199,firebrick:11674146,floralwhite:16775920,forestgreen:2263842,fuchsia:16711935,gainsboro:14474460,ghostwhite:16316671,gold:16766720,goldenrod:14329120,gray:8421504,green:32768,greenyellow:11403055,grey:8421504,honeydew:15794160,
hotpink:16738740,indianred:13458524,indigo:4915330,ivory:16777200,khaki:15787660,lavender:15132410,lavenderblush:16773365,lawngreen:8190976,lemonchiffon:16775885,lightblue:11393254,lightcoral:15761536,lightcyan:14745599,lightgoldenrodyellow:16448210,lightgray:13882323,lightgreen:9498256,lightgrey:13882323,lightpink:16758465,lightsalmon:16752762,lightseagreen:2142890,lightskyblue:8900346,lightslategray:7833753,lightslategrey:7833753,lightsteelblue:11584734,lightyellow:16777184,lime:65280,limegreen:3329330,linen:16445670,magenta:16711935,maroon:8388608,mediumaquamarine:6737322,mediumblue:205,mediumorchid:12211667,mediumpurple:9662683,mediumseagreen:3978097,mediumslateblue:8087790,mediumspringgreen:64154,mediumturquoise:4772300,mediumvioletred:13047173,midnightblue:1644912,mintcream:16121850,mistyrose:16770273,moccasin:16770229,navajowhite:16768685,navy:128,oldlace:16643558,olive:8421376,olivedrab:7048739,orange:16753920,orangered:16729344,orchid:14315734,palegoldenrod:15657130,palegreen:10025880,paleturquoise:11529966,palevioletred:14381203,papayawhip:16773077,peachpuff:16767673,peru:13468991,pink:16761035,plum:14524637,powderblue:11591910,purple:8388736,rebeccapurple:6697881,red:16711680,rosybrown:12357519,royalblue:4286945,saddlebrown:9127187,salmon:16416882,sandybrown:16032864,seagreen:3050327,seashell:16774638,sienna:10506797,silver:12632256,skyblue:8900331,slateblue:6970061,slategray:7372944,slategrey:7372944,snow:16775930,springgreen:65407,steelblue:4620980,tan:13808780,teal:32896,thistle:14204888,tomato:16737095,turquoise:4251856,violet:15631086,wheat:16113331,white:16777215,whitesmoke:16119285,yellow:16776960,yellowgreen:10145074};Object.assign(g.prototype,{isColor:!0,r:1,g:1,b:1,set:function(t){return t&&t.isColor?this.copy(t):"number"==typeof t?this.setHex(t):"string"==typeof t&&this.setStyle(t),this},setScalar:function(t){return this.b=this.g=this.r=t,this},setHex:function(t){return t=Math.floor(t),this.r=(t>>16&255)/255,this.g=(t>>8&255)/255,this.b=(255&t)/255,this},setRGB:function(t,e,i){return this.r=t,this.g=e,this.b=i,this},setHSL:function(){function t(t,e,i){return 0>i&&(i+=1),1<i&&--i,i<1/6?t+6*(e-t)*i:.5>i?e:i<2/3?t+6*(e-t)*(2/3-i):t}return function(e,i,n){return e=$r.euclideanModulo(e,1),i=$r.clamp(i,0,1),n=$r.clamp(n,0,1),0===i?this.r=this.g=this.b=n:(i=.5>=n?n*(1+i):n+i-n*i,n=2*n-i,this.r=t(n,i,e+1/3),this.g=t(n,i,e),this.b=t(n,i,e-1/3)),this}}(),setStyle:function(t){function e(t){void 0!==t&&1>parseFloat(t)&&void 0}var i;if(i=/^((?:rgb|hsl)a?)\(\s*([^\)]*)\)/.exec(t)){var n=i[2];switch(i[1]){case"rgb":case"rgba":if(i=/^(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*(,\s*([0-9]*\.?[0-9]+)\s*)?$/.exec(n))return this.r=Math.min(255,parseInt(i[1],10))/255,this.g=Math.min(255,parseInt(i[2],10))/255,this.b=Math.min(255,parseInt(i[3],10))/255,e(i[5]),this;if(i=/^(\d+)%\s*,\s*(\d+)%\s*,\s*(\d+)%\s*(,\s*([0-9]*\.?[0-9]+)\s*)?$/.exec(n))return this.r=Math.min(100,parseInt(i[1],10))/100,this.g=Math.min(100,parseInt(i[2],10))/100,this.b=Math.min(100,parseInt(i[3],10))/100,e(i[5]),this;break;case"hsl":case"hsla":if(i=/^([0-9]*\.?[0-9]+)\s*,\s*(\d+)%\s*,\s*(\d+)%\s*(,\s*([0-9]*\.?[0-9]+)\s*)?$/.exec(n)){n=parseFloat(i[1])/360;var r=parseInt(i[2],10)/100,a=parseInt(i[3],10)/100;return e(i[5]),this.setHSL(n,r,a)}}}else if(i=/^#([A-Fa-f0-9]+)$/.exec(t)){if(i=i[1],n=i.length,3===n)return this.r=parseInt(i.charAt(0)+i.charAt(0),16)/255,this.g=parseInt(i.charAt(1)+i.charAt(1),16)/255,this.b=parseInt(i.charAt(2)+i.charAt(2),16)/255,this;if(6===n)return this.r=parseInt(i.charAt(0)+i.charAt(1),16)/255,this.g=parseInt(i.charAt(2)+i.charAt(3),16)/255,this.b=parseInt(i.charAt(4)+i.charAt(5),16)/255,this}return t&&0<t.length&&(i=ra[t],void 0!==i?this.setHex(i):void 0),this},clone:function(){return new this.constructor(this.r,this.g,this.b)},copy:function(t){return this.r=t.r,this.g=t.g,this.b=t.b,this},copyGammaToLinear:function(t,e){return void 0===e&&(e=2),this.r=Math.pow(t.r,e),this.g=Math.pow(t.g,e),this.b=Math.pow(t.b,e),this},copyLinearToGamma:function(t,e){return void 0===e&&(e=2),e=0<e?1/e:1,this.r=Math.pow(t.r,e),this.g=Math.pow(t.g,e),this.b=Math.pow(t.b,e),this},convertGammaToLinear:function(t){return this.copyGammaToLinear(this,t),this},convertLinearToGamma:function(t){return this.copyLinearToGamma(this,t),this},copySRGBToLinear:function(){function t(t){return.04045>t?.0773993808*t:Math.pow(.9478672986*t+.0521327014,2.4)}return function(e){return this.r=t(e.r),this.g=t(e.g),this.b=t(e.b),this}}(),copyLinearToSRGB:function(){function t(t){return.0031308>t?12.92*t:1.055*Math.pow(t,.41666)-.055}return function(e){return this.r=t(e.r),this.g=t(e.g),this.b=t(e.b),this}}(),convertSRGBToLinear:function(){return this.copySRGBToLinear(this),this},convertLinearToSRGB:function(){return this.copyLinearToSRGB(this),this},getHex:function(){return 255*this.r<<16^255*this.g<<8^255*this.b<<0},getHexString:function(){return("000000"+this.getHex().toString(16)).slice(-6)},getHSL:function(t){void 0===t&&(t={h:0,s:0,l:0});var e,i=this.r,n=this.g,r=this.b,a=Math.max(i,n,r),o=Math.min(i,n,r),s=(o+a)/2;if(o===a)o=e=0;else{var h=a-o;switch(o=.5>=s?h/(a+o):h/(2-a-o),a){case i:e=(n-r)/h+(n<r?6:0);break;case n:e=(r-i)/h+2;break;case r:e=(i-n)/h+4}e/=6}return t.h=e,t.s=o,t.l=s,t},getStyle:function(){return"rgb("+(255*this.r|0)+","+(255*this.g|0)+","+(255*this.b|0)+")"},offsetHSL:function(){var t={};return function(e,i,n){return this.getHSL(t),t.h+=e,t.s+=i,t.l+=n,this.setHSL(t.h,t.s,t.l),this}}(),add:function(t){return this.r+=t.r,this.g+=t.g,this.b+=t.b,this},addColors:function(t,e){return this.r=t.r+e.r,this.g=t.g+e.g,this.b=t.b+e.b,this},addScalar:function(t){return this.r+=t,this.g+=t,this.b+=t,this},sub:function(t){return this.r=Math.max(0,this.r-t.r),this.g=Math.max(0,this.g-t.g),this.b=Math.max(0,this.b-t.b),this},multiply:function(t){return this.r*=t.r,this.g*=t.g,this.b*=t.b,this},multiplyScalar:function(t){return this.r*=t,this.g*=t,this.b*=t,this},lerp:function(t,e){return this.r+=(t.r-this.r)*e,this.g+=(t.g-this.g)*e,this.b+=(t.b-this.b)*e,this},lerpHSL:function(){var t={h:0,s:0,l:0},e={h:0,s:0,l:0};return function(i,n){this.getHSL(t),i.getHSL(e),i=$r.lerp(t.h,e.h,n);var r=$r.lerp(t.s,e.s,n);return n=$r.lerp(t.l,e.l,n),this.setHSL(i,r,n),this}}(),equals:function(t){return t.r===this.r&&t.g===this.g&&t.b===this.b},fromArray:function(t,e){return void 0===e&&(e=0),this.r=t[e],this.g=t[e+1],this.b=t[e+2],this},toArray:function(t,e){return void 0===t&&(t=[]),void 0===e&&(e=0),t[e]=this.r,t[e+1]=this.g,t[e+2]=this.b,t},toJSON:function(){return this.getHex()}});var aa={common:{diffuse:{value:new g(15658734)},opacity:{value:1},map:{value:null},uvTransform:{value:new o},alphaMap:{value:null}},specularmap:{specularMap:{value:null}},envmap:{envMap:{value:null},flipEnvMap:{value:-1},reflectivity:{value:1},refractionRatio:{value:.98},maxMipLevel:{value:0}},aomap:{aoMap:{value:null},aoMapIntensity:{value:1}},lightmap:{lightMap:{value:null},lightMapIntensity:{value:1}},emissivemap:{emissiveMap:{value:null}},bumpmap:{bumpMap:{value:null},bumpScale:{value:1}},normalmap:{normalMap:{value:null},normalScale:{value:new i(1,1)}},displacementmap:{displacementMap:{value:null},displacementScale:{value:1},displacementBias:{value:0}},roughnessmap:{roughnessMap:{value:null}},metalnessmap:{metalnessMap:{value:null}},gradientmap:{gradientMap:{value:null}},fog:{fogDensity:{value:25e-5},fogNear:{value:1},fogFar:{value:2e3},fogColor:{value:new g(16777215)}},lights:{ambientLightColor:{value:[]},directionalLights:{value:[],properties:{direction:{},color:{},shadow:{},shadowBias:{},shadowRadius:{},shadowMapSize:{}}},directionalShadowMap:{value:[]},directionalShadowMatrix:{value:[]},spotLights:{value:[],properties:{color:{},position:{},direction:{},distance:{},coneCos:{},penumbraCos:{},decay:{},shadow:{},shadowBias:{},shadowRadius:{},shadowMapSize:{}}},spotShadowMap:{value:[]},spotShadowMatrix:{value:[]},pointLights:{value:[],properties:{color:{},position:{},decay:{},distance:{},shadow:{},shadowBias:{},shadowRadius:{},shadowMapSize:{},shadowCameraNear:{},shadowCameraFar:{}}},pointShadowMap:{value:[]},pointShadowMatrix:{value:[]},hemisphereLights:{value:[],properties:{direction:{},skyColor:{},groundColor:{}}},rectAreaLights:{value:[],properties:{color:{},position:{},width:{},height:{}}}},points:{diffuse:{value:new g(15658734)},opacity:{value:1},size:{value:1},scale:{value:1},map:{value:null},uvTransform:{value:new o}},sprite:{diffuse:{value:new g(15658734)},opacity:{value:1},center:{value:new i(.5,.5)},rotation:{value:0},map:{value:null},uvTransform:{value:new o}}},oa={basic:{uniforms:na.merge([aa.common,aa.specularmap,aa.envmap,aa.aomap,aa.lightmap,aa.fog]),vertexShader:ia.meshbasic_vert,fragmentShader:ia.meshbasic_frag},lambert:{uniforms:na.merge([aa.common,aa.specularmap,aa.envmap,aa.aomap,aa.lightmap,aa.emissivemap,aa.fog,aa.lights,{emissive:{value:new g(0)}}]),vertexShader:ia.meshlambert_vert,fragmentShader:ia.meshlambert_frag},phong:{uniforms:na.merge([aa.common,aa.specularmap,aa.envmap,aa.aomap,aa.lightmap,aa.emissivemap,aa.bumpmap,aa.normalmap,aa.displacementmap,aa.gradientmap,aa.fog,aa.lights,{emissive:{value:new g(0)},specular:{value:new g(1118481)},shininess:{value:30}}]),vertexShader:ia.meshphong_vert,fragmentShader:ia.meshphong_frag},standard:{uniforms:na.merge([aa.common,aa.envmap,aa.aomap,aa.lightmap,aa.emissivemap,aa.bumpmap,aa.normalmap,aa.displacementmap,aa.roughnessmap,aa.metalnessmap,aa.fog,aa.lights,{emissive:{value:new g(0)},roughness:{value:.5},metalness:{value:.5},envMapIntensity:{value:1}}]),vertexShader:ia.meshphysical_vert,fragmentShader:ia.meshphysical_frag},points:{uniforms:na.merge([aa.points,aa.fog]),vertexShader:ia.points_vert,fragmentShader:ia.points_frag},dashed:{uniforms:na.merge([aa.common,aa.fog,{scale:{value:1},dashSize:{value:1},totalSize:{value:2}}]),vertexShader:ia.linedashed_vert,fragmentShader:ia.linedashed_frag},depth:{uniforms:na.merge([aa.common,aa.displacementmap]),vertexShader:ia.depth_vert,fragmentShader:ia.depth_frag},normal:{uniforms:na.merge([aa.common,aa.bumpmap,aa.normalmap,aa.displacementmap,{opacity:{value:1}}]),vertexShader:ia.normal_vert,fragmentShader:ia.normal_frag},sprite:{uniforms:na.merge([aa.sprite,aa.fog]),vertexShader:ia.sprite_vert,fragmentShader:ia.sprite_frag},cube:{uniforms:{tCube:{value:null},tFlip:{value:-1},opacity:{value:1}},vertexShader:ia.cube_vert,fragmentShader:ia.cube_frag},equirect:{uniforms:{tEquirect:{value:null}},vertexShader:ia.equirect_vert,fragmentShader:ia.equirect_frag},distanceRGBA:{uniforms:na.merge([aa.common,aa.displacementmap,{referencePosition:{value:new a},nearDistance:{value:1},farDistance:{value:1e3}}]),vertexShader:ia.distanceRGBA_vert,fragmentShader:ia.distanceRGBA_frag},shadow:{uniforms:na.merge([aa.lights,aa.fog,{color:{value:new g(0)},opacity:{value:1}}]),vertexShader:ia.shadow_vert,fragmentShader:ia.shadow_frag}};oa.physical={uniforms:na.merge([oa.standard.uniforms,{clearCoat:{value:0},clearCoatRoughness:{value:0}}]),vertexShader:ia.meshphysical_vert,fragmentShader:ia.meshphysical_frag},x.RotationOrders="XYZ YZX ZXY XZY YXZ ZYX".split(" "),x.DefaultOrder="XYZ",Object.defineProperties(x.prototype,{x:{get:function(){return this._x},set:function(t){this._x=t,this.onChangeCallback()}},y:{get:function(){return this._y},set:function(t){this._y=t,this.onChangeCallback()}},z:{get:function(){return this._z},set:function(t){this._z=t,this.onChangeCallback()}},order:{get:function(){return this._order},set:function(t){this._order=t,this.onChangeCallback()}}}),Object.assign(x.prototype,{isEuler:!0,set:function(t,e,i,n){return this._x=t,this._y=e,this._z=i,this._order=n||this._order,this.onChangeCallback(),this},clone:function(){return new this.constructor(this._x,this._y,this._z,this._order)},copy:function(t){return this._x=t._x,this._y=t._y,this._z=t._z,this._order=t._order,this.onChangeCallback(),this},setFromRotationMatrix:function(t,e,i){var n=$r.clamp,r=t.elements;t=r[0];var a=r[4],o=r[8],s=r[1],h=r[5],c=r[9],l=r[2],u=r[6];return r=r[10],e=e||this._order,"XYZ"===e?(this._y=Math.asin(n(o,-1,1)),.99999>Math.abs(o)?(this._x=Math.atan2(-c,r),this._z=Math.atan2(-a,t)):(this._x=Math.atan2(u,h),this._z=0)):"YXZ"===e?(this._x=Math.asin(-n(c,-1,1)),.99999>Math.abs(c)?(this._y=Math.atan2(o,r),this._z=Math.atan2(s,h)):(this._y=Math.atan2(-l,t),this._z=0)):"ZXY"===e?(this._x=Math.asin(n(u,-1,1)),.99999>Math.abs(u)?(this._y=Math.atan2(-l,r),this._z=Math.atan2(-a,h)):(this._y=0,this._z=Math.atan2(s,t))):"ZYX"===e?(this._y=Math.asin(-n(l,-1,1)),.99999>Math.abs(l)?(this._x=Math.atan2(u,r),this._z=Math.atan2(s,t)):(this._x=0,this._z=Math.atan2(-a,h))):"YZX"===e?(this._z=Math.asin(n(s,-1,1)),.99999>Math.abs(s)?(this._x=Math.atan2(-c,h),this._y=Math.atan2(-l,t)):(this._x=0,this._y=Math.atan2(o,r))):"XZY"===e?(this._z=Math.asin(-n(a,-1,1)),.99999>Math.abs(a)?(this._x=Math.atan2(u,h),this._y=Math.atan2(o,t)):(this._x=Math.atan2(-c,r),this._y=0)):void 0,this._order=e,!1!==i&&this.onChangeCallback(),this},setFromQuaternion:function(){var t=new n;return function(e,i,n){return t.makeRotationFromQuaternion(e),this.setFromRotationMatrix(t,i,n)}}(),setFromVector3:function(t,e){return this.set(t.x,t.y,t.z,e||this._order)},reorder:function(){var t=new r;return function(e){return t.setFromEuler(this),this.setFromQuaternion(t,e)}}(),equals:function(t){return t._x===this._x&&t._y===this._y&&t._z===this._z&&t._order===this._order},fromArray:function(t){return this._x=t[0],this._y=t[1],this._z=t[2],void 0!==t[3]&&(this._order=t[3]),this.onChangeCallback(),this},toArray:function(t,e){return void 0===t&&(t=[]),void 0===e&&(e=0),t[e]=this._x,t[e+1]=this._y,t[e+2]=this._z,t[e+3]=this._order,t},toVector3:function(t){return t?t.set(this._x,this._y,this._z):new a(this._x,this._y,this._z)},onChange:function(t){return this.onChangeCallback=t,this},onChangeCallback:function(){}}),Object.assign(_.prototype,{set:function(t){this.mask=1<<t|0},enable:function(t){this.mask=this.mask|1<<t|0},toggle:function(t){this.mask^=1<<t|0},disable:function(t){this.mask&=~(1<<t|0)},test:function(t){return 0!==(this.mask&t.mask)}});var sa=0;b.DefaultUp=new a(0,1,0),b.DefaultMatrixAutoUpdate=!0,b.prototype=Object.assign(Object.create(e.prototype),{constructor:b,isObject3D:!0,onBeforeRender:function(){},onAfterRender:function(){},applyMatrix:function(t){this.matrix.multiplyMatrices(t,this.matrix),this.matrix.decompose(this.position,this.quaternion,this.scale)},applyQuaternion:function(t){return this.quaternion.premultiply(t),this},setRotationFromAxisAngle:function(t,e){this.quaternion.setFromAxisAngle(t,e)},setRotationFromEuler:function(t){this.quaternion.setFromEuler(t,!0)},setRotationFromMatrix:function(t){this.quaternion.setFromRotationMatrix(t)},setRotationFromQuaternion:function(t){this.quaternion.copy(t)},rotateOnAxis:function(){var t=new r;return function(e,i){return t.setFromAxisAngle(e,i),this.quaternion.multiply(t),this}}(),rotateOnWorldAxis:function(){var t=new r;return function(e,i){return t.setFromAxisAngle(e,i),this.quaternion.premultiply(t),this}}(),rotateX:function(){var t=new a(1,0,0);return function(e){return this.rotateOnAxis(t,e)}}(),rotateY:function(){var t=new a(0,1,0);return function(e){return this.rotateOnAxis(t,e)}}(),rotateZ:function(){var t=new a(0,0,1);return function(e){return this.rotateOnAxis(t,e)}}(),translateOnAxis:function(){var t=new a;return function(e,i){return t.copy(e).applyQuaternion(this.quaternion),this.position.add(t.multiplyScalar(i)),this}}(),translateX:function(){var t=new a(1,0,0);return function(e){return this.translateOnAxis(t,e)}}(),translateY:function(){var t=new a(0,1,0);return function(e){return this.translateOnAxis(t,e)}}(),translateZ:function(){var t=new a(0,0,1);return function(e){return this.translateOnAxis(t,e)}}(),localToWorld:function(t){return t.applyMatrix4(this.matrixWorld)},worldToLocal:function(){var t=new n;return function(e){return e.applyMatrix4(t.getInverse(this.matrixWorld))}}(),lookAt:function(){var t=new r,e=new n,i=new a,o=new a;return function(n,r,a){n.isVector3?i.copy(n):i.set(n,r,a),n=this.parent,this.updateWorldMatrix(!0,!1),o.setFromMatrixPosition(this.matrixWorld),this.isCamera?e.lookAt(o,i,this.up):e.lookAt(i,o,this.up),this.quaternion.setFromRotationMatrix(e),n&&(e.extractRotation(n.matrixWorld),t.setFromRotationMatrix(e),this.quaternion.premultiply(t.inverse()))}}(),add:function(t){if(1<arguments.length){for(var e=0;e<arguments.length;e++)this.add(arguments[e]);return this}return t===this?this:(t&&t.isObject3D?(null!==t.parent&&t.parent.remove(t),t.parent=this,t.dispatchEvent({type:"added"}),this.children.push(t)):void 0,this)},remove:function(t){if(1<arguments.length){for(var e=0;e<arguments.length;e++)this.remove(arguments[e]);return this}return e=this.children.indexOf(t),-1!==e&&(t.parent=null,t.dispatchEvent({type:"removed"}),this.children.splice(e,1)),this},getObjectById:function(t){return this.getObjectByProperty("id",t)},getObjectByName:function(t){return this.getObjectByProperty("name",t)},getObjectByProperty:function(t,e){if(this[t]===e)return this;for(var i=0,n=this.children.length;i<n;i++){var r=this.children[i].getObjectByProperty(t,e);if(void 0!==r)return r}},getWorldPosition:function(t){return void 0===t&&(t=new a),this.updateMatrixWorld(!0),t.setFromMatrixPosition(this.matrixWorld)},getWorldQuaternion:function(){var t=new a,e=new a;return function(i){return void 0===i&&(i=new r),this.updateMatrixWorld(!0),this.matrixWorld.decompose(t,i,e),i}}(),getWorldScale:function(){var t=new a,e=new r;return function(i){return void 0===i&&(i=new a),this.updateMatrixWorld(!0),this.matrixWorld.decompose(t,e,i),i}}(),getWorldDirection:function(t){void 0===t&&(t=new a),this.updateMatrixWorld(!0);var e=this.matrixWorld.elements;return t.set(e[8],e[9],e[10]).normalize()},raycast:function(){},traverse:function(t){t(this);for(var e=this.children,i=0,n=e.length;i<n;i++)e[i].traverse(t)},traverseVisible:function(t){if(!1!==this.visible){t(this);for(var e=this.children,i=0,n=e.length;i<n;i++)e[i].traverseVisible(t)}},traverseAncestors:function(t){var e=this.parent;null!==e&&(t(e),e.traverseAncestors(t))},updateMatrix:function(){this.matrix.compose(this.position,this.quaternion,this.scale),this.matrixWorldNeedsUpdate=!0},updateMatrixWorld:function(t){this.matrixAutoUpdate&&this.updateMatrix(),(this.matrixWorldNeedsUpdate||t)&&(null===this.parent?this.matrixWorld.copy(this.matrix):this.matrixWorld.multiplyMatrices(this.parent.matrixWorld,this.matrix),this.matrixWorldNeedsUpdate=!1,t=!0);for(var e=this.children,i=0,n=e.length;i<n;i++)e[i].updateMatrixWorld(t)},updateWorldMatrix:function(t,e){var i=this.parent;if(!0===t&&null!==i&&i.updateWorldMatrix(!0,!1),this.matrixAutoUpdate&&this.updateMatrix(),null===this.parent?this.matrixWorld.copy(this.matrix):this.matrixWorld.multiplyMatrices(this.parent.matrixWorld,this.matrix),!0===e)for(t=this.children,e=0,i=t.length;e<i;e++)t[e].updateWorldMatrix(!1,!0)},toJSON:function(t){function e(e,i){return void 0===e[i.uuid]&&(e[i.uuid]=i.toJSON(t)),i.uuid}function i(t){var e,i=[];for(e in t){var n=t[e];delete n.metadata,i.push(n)}return i}var n=void 0===t||"string"==typeof t,r={};n&&(t={geometries:{},materials:{},textures:{},images:{},shapes:{}},r.metadata={version:4.5,type:"Object",generator:"Object3D.toJSON"});var a={};if(a.uuid=this.uuid,a.type=this.type,""!==this.name&&(a.name=this.name),!0===this.castShadow&&(a.castShadow=!0),!0===this.receiveShadow&&(a.receiveShadow=!0),!1===this.visible&&(a.visible=!1),!1===this.frustumCulled&&(a.frustumCulled=!1),0!==this.renderOrder&&(a.renderOrder=this.renderOrder),"{}"!==JSON.stringify(this.userData)&&(a.userData=this.userData),a.layers=this.layers.mask,a.matrix=this.matrix.toArray(),!1===this.matrixAutoUpdate&&(a.matrixAutoUpdate=!1),this.isMesh||this.isLine||this.isPoints){a.geometry=e(t.geometries,this.geometry);var o=this.geometry.parameters;if(void 0!==o&&void 0!==o.shapes)if(o=o.shapes,Array.isArray(o))for(var s=0,h=o.length;s<h;s++)e(t.shapes,o[s]);else e(t.shapes,o)}if(void 0!==this.material)if(Array.isArray(this.material)){for(o=[],s=0,h=this.material.length;s<h;s++)o.push(e(t.materials,this.material[s]));a.material=o}else a.material=e(t.materials,this.material);if(0<this.children.length)for(a.children=[],s=0;s<this.children.length;s++)a.children.push(this.children[s].toJSON(t).object);if(n){n=i(t.geometries),s=i(t.materials),h=i(t.textures);var c=i(t.images);o=i(t.shapes),0<n.length&&(r.geometries=n),0<s.length&&(r.materials=s),0<h.length&&(r.textures=h),0<c.length&&(r.images=c),0<o.length&&(r.shapes=o)}return r.object=a,r},clone:function(t){return(new this.constructor).copy(this,t)},copy:function(t,e){if(void 0===e&&(e=!0),this.name=t.name,this.up.copy(t.up),this.position.copy(t.position),this.quaternion.copy(t.quaternion),this.scale.copy(t.scale),this.matrix.copy(t.matrix),this.matrixWorld.copy(t.matrixWorld),this.matrixAutoUpdate=t.matrixAutoUpdate,this.matrixWorldNeedsUpdate=t.matrixWorldNeedsUpdate,this.layers.mask=t.layers.mask,this.visible=t.visible,this.castShadow=t.castShadow,this.receiveShadow=t.receiveShadow,this.frustumCulled=t.frustumCulled,this.renderOrder=t.renderOrder,this.userData=JSON.parse(JSON.stringify(t.userData)),!0===e)for(e=0;e<t.children.length;e++)this.add(t.children[e].clone());return this}}),M.prototype=Object.assign(Object.create(b.prototype),{constructor:M,isCamera:!0,copy:function(t,e){return b.prototype.copy.call(this,t,e),this.matrixWorldInverse.copy(t.matrixWorldInverse),this.projectionMatrix.copy(t.projectionMatrix),this.projectionMatrixInverse.copy(t.projectionMatrixInverse),this},getWorldDirection:function(t){void 0===t&&(t=new a),this.updateMatrixWorld(!0);var e=this.matrixWorld.elements;return t.set(-e[8],-e[9],-e[10]).normalize()},updateMatrixWorld:function(t){b.prototype.updateMatrixWorld.call(this,t),this.matrixWorldInverse.getInverse(this.matrixWorld)},clone:function(){return(new this.constructor).copy(this)}}),w.prototype=Object.assign(Object.create(M.prototype),{constructor:w,isOrthographicCamera:!0,copy:function(t,e){return M.prototype.copy.call(this,t,e),this.left=t.left,this.right=t.right,this.top=t.top,this.bottom=t.bottom,this.near=t.near,this.far=t.far,this.zoom=t.zoom,this.view=null===t.view?null:Object.assign({},t.view),this},setViewOffset:function(t,e,i,n,r,a){null===this.view&&(this.view={enabled:!0,fullWidth:1,fullHeight:1,offsetX:0,offsetY:0,width:1,height:1}),this.view.enabled=!0,this.view.fullWidth=t,this.view.fullHeight=e,this.view.offsetX=i,this.view.offsetY=n,this.view.width=r,this.view.height=a,this.updateProjectionMatrix()},clearViewOffset:function(){null!==this.view&&(this.view.enabled=!1),this.updateProjectionMatrix()},updateProjectionMatrix:function(){var t=(this.right-this.left)/(2*this.zoom),e=(this.top-this.bottom)/(2*this.zoom),i=(this.right+this.left)/2,n=(this.top+this.bottom)/2,r=i-t;if(i+=t,t=n+e,e=n-e,null!==this.view&&this.view.enabled){i=this.zoom/(this.view.width/this.view.fullWidth),e=this.zoom/(this.view.height/this.view.fullHeight);var a=(this.right-this.left)/this.view.width;n=(this.top-this.bottom)/this.view.height,r+=this.view.offsetX/i*a,i=r+this.view.width/i*a,t-=this.view.offsetY/e*n,e=t-this.view.height/e*n}this.projectionMatrix.makeOrthographic(r,i,t,e,this.near,this.far),this.projectionMatrixInverse.getInverse(this.projectionMatrix)},toJSON:function(t){return t=b.prototype.toJSON.call(this,t),t.object.zoom=this.zoom,t.object.left=this.left,t.object.right=this.right,t.object.top=this.top,t.object.bottom=this.bottom,t.object.near=this.near,t.object.far=this.far,null!==this.view&&(t.object.view=Object.assign({},this.view)),t}}),Object.assign(S.prototype,{clone:function(){return(new this.constructor).copy(this)},copy:function(t){this.a=t.a,this.b=t.b,this.c=t.c,this.normal.copy(t.normal),this.color.copy(t.color),this.materialIndex=t.materialIndex;for(var e=0,i=t.vertexNormals.length;e<i;e++)this.vertexNormals[e]=t.vertexNormals[e].clone();for(e=0,i=t.vertexColors.length;e<i;e++)this.vertexColors[e]=t.vertexColors[e].clone();return this}});var ha=0;A.prototype=Object.assign(Object.create(e.prototype),{constructor:A,isGeometry:!0,applyMatrix:function(t){for(var e=(new o).getNormalMatrix(t),i=0,n=this.vertices.length;i<n;i++)this.vertices[i].applyMatrix4(t);for(i=0,n=this.faces.length;i<n;i++){t=this.faces[i],t.normal.applyMatrix3(e).normalize();for(var r=0,a=t.vertexNormals.length;r<a;r++)t.vertexNormals[r].applyMatrix3(e).normalize()}return null!==this.boundingBox&&this.computeBoundingBox(),null!==this.boundingSphere&&this.computeBoundingSphere(),this.normalsNeedUpdate=this.verticesNeedUpdate=!0,this},rotateX:function(){var t=new n;return function(e){return t.makeRotationX(e),this.applyMatrix(t),this}}(),rotateY:function(){var t=new n;return function(e){return t.makeRotationY(e),this.applyMatrix(t),this}}(),rotateZ:function(){var t=new n;return function(e){return t.makeRotationZ(e),this.applyMatrix(t),this}}(),translate:function(){var t=new n;return function(e,i,n){return t.makeTranslation(e,i,n),this.applyMatrix(t),this}}(),scale:function(){var t=new n;return function(e,i,n){return t.makeScale(e,i,n),this.applyMatrix(t),this}}(),lookAt:function(){var t=new b;return function(e){t.lookAt(e),t.updateMatrix(),this.applyMatrix(t.matrix)}}(),fromBufferGeometry:function(t){function e(t,e,i,r){var a=void 0!==h?[p[t].clone(),p[e].clone(),p[i].clone()]:[],o=void 0!==c?[n.colors[t].clone(),n.colors[e].clone(),n.colors[i].clone()]:[];r=new S(t,e,i,a,o,r),n.faces.push(r),void 0!==l&&n.faceVertexUvs[0].push([d[t].clone(),d[e].clone(),d[i].clone()]),void 0!==u&&n.faceVertexUvs[1].push([f[t].clone(),f[e].clone(),f[i].clone()])}var n=this,r=null!==t.index?t.index.array:void 0,o=t.attributes,s=o.position.array,h=void 0!==o.normal?o.normal.array:void 0,c=void 0!==o.color?o.color.array:void 0,l=void 0!==o.uv?o.uv.array:void 0,u=void 0!==o.uv2?o.uv2.array:void 0;void 0!==u&&(this.faceVertexUvs[1]=[]);for(var p=[],d=[],f=[],m=o=0;o<s.length;o+=3,m+=2)n.vertices.push(new a(s[o],s[o+1],s[o+2])),void 0!==h&&p.push(new a(h[o],h[o+1],h[o+2])),void 0!==c&&n.colors.push(new g(c[o],c[o+1],c[o+2])),void 0!==l&&d.push(new i(l[m],l[m+1])),void 0!==u&&f.push(new i(u[m],u[m+1]));var v=t.groups;if(0<v.length)for(o=0;o<v.length;o++){s=v[o];var y=s.start,x=s.count;for(m=y,y+=x;m<y;m+=3)void 0!==r?e(r[m],r[m+1],r[m+2],s.materialIndex):e(m,m+1,m+2,s.materialIndex)}else if(void 0!==r)for(o=0;o<r.length;o+=3)e(r[o],r[o+1],r[o+2]);else for(o=0;o<s.length/3;o+=3)e(o,o+1,o+2);return this.computeFaceNormals(),null!==t.boundingBox&&(this.boundingBox=t.boundingBox.clone()),null!==t.boundingSphere&&(this.boundingSphere=t.boundingSphere.clone()),this},center:function(){var t=new a;return function(){return this.computeBoundingBox(),this.boundingBox.getCenter(t).negate(),this.translate(t.x,t.y,t.z),this}}(),normalize:function(){this.computeBoundingSphere();var t=this.boundingSphere.center,e=this.boundingSphere.radius;e=0===e?1:1/e;var i=new n;return i.set(e,0,0,-e*t.x,0,e,0,-e*t.y,0,0,e,-e*t.z,0,0,0,1),this.applyMatrix(i),this},computeFaceNormals:function(){for(var t=new a,e=new a,i=0,n=this.faces.length;i<n;i++){var r=this.faces[i],o=this.vertices[r.a],s=this.vertices[r.b];t.subVectors(this.vertices[r.c],s),e.subVectors(o,s),t.cross(e),t.normalize(),r.normal.copy(t)}},computeVertexNormals:function(t){void 0===t&&(t=!0);var e,i=Array(this.vertices.length),n=0;for(e=this.vertices.length;n<e;n++)i[n]=new a;if(t){var r=new a,o=new a;for(t=0,n=this.faces.length;t<n;t++){e=this.faces[t];var s=this.vertices[e.a],h=this.vertices[e.b],c=this.vertices[e.c];r.subVectors(c,h),o.subVectors(s,h),r.cross(o),i[e.a].add(r),i[e.b].add(r),i[e.c].add(r)}}else for(this.computeFaceNormals(),t=0,n=this.faces.length;t<n;t++)e=this.faces[t],i[e.a].add(e.normal),i[e.b].add(e.normal),i[e.c].add(e.normal);for(n=0,e=this.vertices.length;n<e;n++)i[n].normalize();for(t=0,n=this.faces.length;t<n;t++)e=this.faces[t],s=e.vertexNormals,3===s.length?(s[0].copy(i[e.a]),s[1].copy(i[e.b]),s[2].copy(i[e.c])):(s[0]=i[e.a].clone(),s[1]=i[e.b].clone(),s[2]=i[e.c].clone());0<this.faces.length&&(this.normalsNeedUpdate=!0)},computeFlatVertexNormals:function(){var t;this.computeFaceNormals();var e=0;for(t=this.faces.length;e<t;e++){var i=this.faces[e],n=i.vertexNormals;3===n.length?(n[0].copy(i.normal),n[1].copy(i.normal),n[2].copy(i.normal)):(n[0]=i.normal.clone(),n[1]=i.normal.clone(),n[2]=i.normal.clone())}0<this.faces.length&&(this.normalsNeedUpdate=!0)},computeMorphNormals:function(){var t,e,i=0;for(e=this.faces.length;i<e;i++){var n=this.faces[i];n.__originalFaceNormal?n.__originalFaceNormal.copy(n.normal):n.__originalFaceNormal=n.normal.clone(),n.__originalVertexNormals||(n.__originalVertexNormals=[]);var r=0;for(t=n.vertexNormals.length;r<t;r++)n.__originalVertexNormals[r]?n.__originalVertexNormals[r].copy(n.vertexNormals[r]):n.__originalVertexNormals[r]=n.vertexNormals[r].clone()}var o=new A;for(o.faces=this.faces,r=0,t=this.morphTargets.length;r<t;r++){if(!this.morphNormals[r]){this.morphNormals[r]={},this.morphNormals[r].faceNormals=[],this.morphNormals[r].vertexNormals=[],n=this.morphNormals[r].faceNormals;var s=this.morphNormals[r].vertexNormals;for(i=0,e=this.faces.length;i<e;i++){var h=new a,c={a:new a,b:new a,c:new a};n.push(h),s.push(c)}}for(s=this.morphNormals[r],o.vertices=this.morphTargets[r].vertices,o.computeFaceNormals(),o.computeVertexNormals(),i=0,e=this.faces.length;i<e;i++)n=this.faces[i],h=s.faceNormals[i],c=s.vertexNormals[i],h.copy(n.normal),c.a.copy(n.vertexNormals[0]),c.b.copy(n.vertexNormals[1]),c.c.copy(n.vertexNormals[2])}for(i=0,e=this.faces.length;i<e;i++)n=this.faces[i],n.normal=n.__originalFaceNormal,n.vertexNormals=n.__originalVertexNormals},computeBoundingBox:function(){null===this.boundingBox&&(this.boundingBox=new p),this.boundingBox.setFromPoints(this.vertices)},computeBoundingSphere:function(){null===this.boundingSphere&&(this.boundingSphere=new d),this.boundingSphere.setFromPoints(this.vertices)},merge:function(t,e,i){if(t&&t.isGeometry){var n,r=this.vertices.length,a=this.vertices,s=t.vertices,h=this.faces,c=t.faces,l=this.faceVertexUvs[0],u=t.faceVertexUvs[0],p=this.colors,d=t.colors;void 0===i&&(i=0),void 0!==e&&(n=(new o).getNormalMatrix(e)),t=0;for(var f=s.length;t<f;t++){var m=s[t].clone();void 0!==e&&m.applyMatrix4(e),a.push(m)}for(t=0,f=d.length;t<f;t++)p.push(d[t].clone());for(t=0,f=c.length;t<f;t++){s=c[t];var g=s.vertexNormals;for(d=s.vertexColors,p=new S(s.a+r,s.b+r,s.c+r),p.normal.copy(s.normal),void 0!==n&&p.normal.applyMatrix3(n).normalize(),e=0,a=g.length;e<a;e++)m=g[e].clone(),void 0!==n&&m.applyMatrix3(n).normalize(),p.vertexNormals.push(m);for(p.color.copy(s.color),e=0,a=d.length;e<a;e++)m=d[e],p.vertexColors.push(m.clone());p.materialIndex=s.materialIndex+i,h.push(p)}for(t=0,f=u.length;t<f;t++)if(i=u[t],n=[],void 0!==i){for(e=0,a=i.length;e<a;e++)n.push(i[e].clone());l.push(n)}}},mergeMesh:function(t){t&&t.isMesh?(t.matrixAutoUpdate&&t.updateMatrix(),this.merge(t.geometry,t.matrix)):void 0},mergeVertices:function(){var t,e={},i=[],n=[],r=Math.pow(10,4),a=0;for(t=this.vertices.length;a<t;a++){var o=this.vertices[a];o=Math.round(o.x*r)+"_"+Math.round(o.y*r)+"_"+Math.round(o.z*r),void 0===e[o]?(e[o]=a,i.push(this.vertices[a]),n[a]=i.length-1):n[a]=n[e[o]]}for(e=[],a=0,t=this.faces.length;a<t;a++)for(r=this.faces[a],r.a=n[r.a],r.b=n[r.b],r.c=n[r.c],r=[r.a,r.b,r.c],o=0;3>o;o++)if(r[o]===r[(o+1)%3]){e.push(a);break}for(a=e.length-1;0<=a;a--)for(r=e[a],this.faces.splice(r,1),n=0,t=this.faceVertexUvs.length;n<t;n++)this.faceVertexUvs[n].splice(r,1);return a=this.vertices.length-i.length,this.vertices=i,a},setFromPoints:function(t){this.vertices=[];for(var e=0,i=t.length;e<i;e++){var n=t[e];this.vertices.push(new a(n.x,n.y,n.z||0))}return this},sortFacesByMaterialIndex:function(){for(var t=this.faces,e=t.length,i=0;i<e;i++)t[i]._id=i;t.sort(function(t,e){return t.materialIndex-e.materialIndex});var n,r,a=this.faceVertexUvs[0],o=this.faceVertexUvs[1];for(a&&a.length===e&&(n=[]),o&&o.length===e&&(r=[]),i=0;i<e;i++){
var s=t[i]._id;n&&n.push(a[s]),r&&r.push(o[s])}n&&(this.faceVertexUvs[0]=n),r&&(this.faceVertexUvs[1]=r)},toJSON:function(){function t(t,e,i){return i?t|1<<e:t&~(1<<e)}function e(t){var e=t.x.toString()+t.y.toString()+t.z.toString();return void 0!==c[e]?c[e]:(c[e]=h.length/3,h.push(t.x,t.y,t.z),c[e])}function i(t){var e=t.r.toString()+t.g.toString()+t.b.toString();return void 0!==u[e]?u[e]:(u[e]=l.length,l.push(t.getHex()),u[e])}function n(t){var e=t.x.toString()+t.y.toString();return void 0!==d[e]?d[e]:(d[e]=p.length/2,p.push(t.x,t.y),d[e])}var r={metadata:{version:4.5,type:"Geometry",generator:"Geometry.toJSON"}};if(r.uuid=this.uuid,r.type=this.type,""!==this.name&&(r.name=this.name),void 0!==this.parameters){var a,o=this.parameters;for(a in o)void 0!==o[a]&&(r[a]=o[a]);return r}for(o=[],a=0;a<this.vertices.length;a++){var s=this.vertices[a];o.push(s.x,s.y,s.z)}s=[];var h=[],c={},l=[],u={},p=[],d={};for(a=0;a<this.faces.length;a++){var f=this.faces[a],m=void 0!==this.faceVertexUvs[0][a],g=0<f.normal.length(),v=0<f.vertexNormals.length,y=1!==f.color.r||1!==f.color.g||1!==f.color.b,x=0<f.vertexColors.length,_=0;_=t(_,0,0),_=t(_,1,!0),_=t(_,2,!1),_=t(_,3,m),_=t(_,4,g),_=t(_,5,v),_=t(_,6,y),_=t(_,7,x),s.push(_),s.push(f.a,f.b,f.c),s.push(f.materialIndex),m&&(m=this.faceVertexUvs[0][a],s.push(n(m[0]),n(m[1]),n(m[2]))),g&&s.push(e(f.normal)),v&&(g=f.vertexNormals,s.push(e(g[0]),e(g[1]),e(g[2]))),y&&s.push(i(f.color)),x&&(f=f.vertexColors,s.push(i(f[0]),i(f[1]),i(f[2])))}return r.data={},r.data.vertices=o,r.data.normals=h,0<l.length&&(r.data.colors=l),0<p.length&&(r.data.uvs=[p]),r.data.faces=s,r},clone:function(){return(new A).copy(this)},copy:function(t){var e,i,n;this.vertices=[],this.colors=[],this.faces=[],this.faceVertexUvs=[[]],this.morphTargets=[],this.morphNormals=[],this.skinWeights=[],this.skinIndices=[],this.lineDistances=[],this.boundingSphere=this.boundingBox=null,this.name=t.name;var r=t.vertices,a=0;for(e=r.length;a<e;a++)this.vertices.push(r[a].clone());for(r=t.colors,a=0,e=r.length;a<e;a++)this.colors.push(r[a].clone());for(r=t.faces,a=0,e=r.length;a<e;a++)this.faces.push(r[a].clone());for(a=0,e=t.faceVertexUvs.length;a<e;a++){var o=t.faceVertexUvs[a];for(void 0===this.faceVertexUvs[a]&&(this.faceVertexUvs[a]=[]),r=0,i=o.length;r<i;r++){var s=o[r],h=[],c=0;for(n=s.length;c<n;c++)h.push(s[c].clone());this.faceVertexUvs[a].push(h)}}for(c=t.morphTargets,a=0,e=c.length;a<e;a++){if(n={},n.name=c[a].name,void 0!==c[a].vertices)for(n.vertices=[],r=0,i=c[a].vertices.length;r<i;r++)n.vertices.push(c[a].vertices[r].clone());if(void 0!==c[a].normals)for(n.normals=[],r=0,i=c[a].normals.length;r<i;r++)n.normals.push(c[a].normals[r].clone());this.morphTargets.push(n)}for(c=t.morphNormals,a=0,e=c.length;a<e;a++){if(n={},void 0!==c[a].vertexNormals)for(n.vertexNormals=[],r=0,i=c[a].vertexNormals.length;r<i;r++)o=c[a].vertexNormals[r],s={},s.a=o.a.clone(),s.b=o.b.clone(),s.c=o.c.clone(),n.vertexNormals.push(s);if(void 0!==c[a].faceNormals)for(n.faceNormals=[],r=0,i=c[a].faceNormals.length;r<i;r++)n.faceNormals.push(c[a].faceNormals[r].clone());this.morphNormals.push(n)}for(r=t.skinWeights,a=0,e=r.length;a<e;a++)this.skinWeights.push(r[a].clone());for(r=t.skinIndices,a=0,e=r.length;a<e;a++)this.skinIndices.push(r[a].clone());for(r=t.lineDistances,a=0,e=r.length;a<e;a++)this.lineDistances.push(r[a]);return a=t.boundingBox,null!==a&&(this.boundingBox=a.clone()),a=t.boundingSphere,null!==a&&(this.boundingSphere=a.clone()),this.elementsNeedUpdate=t.elementsNeedUpdate,this.verticesNeedUpdate=t.verticesNeedUpdate,this.uvsNeedUpdate=t.uvsNeedUpdate,this.normalsNeedUpdate=t.normalsNeedUpdate,this.colorsNeedUpdate=t.colorsNeedUpdate,this.lineDistancesNeedUpdate=t.lineDistancesNeedUpdate,this.groupsNeedUpdate=t.groupsNeedUpdate,this},dispose:function(){this.dispatchEvent({type:"dispose"})}}),Object.defineProperty(T.prototype,"needsUpdate",{set:function(t){!0===t&&this.version++}}),Object.assign(T.prototype,{isBufferAttribute:!0,onUploadCallback:function(){},setArray:function(t){if(Array.isArray(t))throw new TypeError("THREE.BufferAttribute: array should be a Typed Array.");return this.count=void 0!==t?t.length/this.itemSize:0,this.array=t,this},setDynamic:function(t){return this.dynamic=t,this},copy:function(t){return this.name=t.name,this.array=new t.array.constructor(t.array),this.itemSize=t.itemSize,this.count=t.count,this.normalized=t.normalized,this.dynamic=t.dynamic,this},copyAt:function(t,e,i){t*=this.itemSize,i*=e.itemSize;for(var n=0,r=this.itemSize;n<r;n++)this.array[t+n]=e.array[i+n];return this},copyArray:function(t){return this.array.set(t),this},copyColorsArray:function(t){for(var e=this.array,i=0,n=0,r=t.length;n<r;n++){var a=t[n];void 0===a&&(a=new g),e[i++]=a.r,e[i++]=a.g,e[i++]=a.b}return this},copyVector2sArray:function(t){for(var e=this.array,n=0,r=0,a=t.length;r<a;r++){var o=t[r];void 0===o&&(o=new i),e[n++]=o.x,e[n++]=o.y}return this},copyVector3sArray:function(t){for(var e=this.array,i=0,n=0,r=t.length;n<r;n++){var o=t[n];void 0===o&&(o=new a),e[i++]=o.x,e[i++]=o.y,e[i++]=o.z}return this},copyVector4sArray:function(t){for(var e=this.array,i=0,n=0,r=t.length;n<r;n++){var a=t[n];void 0===a&&(a=new h),e[i++]=a.x,e[i++]=a.y,e[i++]=a.z,e[i++]=a.w}return this},set:function(t,e){return void 0===e&&(e=0),this.array.set(t,e),this},getX:function(t){return this.array[t*this.itemSize]},setX:function(t,e){return this.array[t*this.itemSize]=e,this},getY:function(t){return this.array[t*this.itemSize+1]},setY:function(t,e){return this.array[t*this.itemSize+1]=e,this},getZ:function(t){return this.array[t*this.itemSize+2]},setZ:function(t,e){return this.array[t*this.itemSize+2]=e,this},getW:function(t){return this.array[t*this.itemSize+3]},setW:function(t,e){return this.array[t*this.itemSize+3]=e,this},setXY:function(t,e,i){return t*=this.itemSize,this.array[t+0]=e,this.array[t+1]=i,this},setXYZ:function(t,e,i,n){return t*=this.itemSize,this.array[t+0]=e,this.array[t+1]=i,this.array[t+2]=n,this},setXYZW:function(t,e,i,n,r){return t*=this.itemSize,this.array[t+0]=e,this.array[t+1]=i,this.array[t+2]=n,this.array[t+3]=r,this},onUpload:function(t){return this.onUploadCallback=t,this},clone:function(){return new this.constructor(this.array,this.itemSize).copy(this)}}),L.prototype=Object.create(T.prototype),L.prototype.constructor=L,E.prototype=Object.create(T.prototype),E.prototype.constructor=E,C.prototype=Object.create(T.prototype),C.prototype.constructor=C,P.prototype=Object.create(T.prototype),P.prototype.constructor=P,O.prototype=Object.create(T.prototype),O.prototype.constructor=O,I.prototype=Object.create(T.prototype),I.prototype.constructor=I,N.prototype=Object.create(T.prototype),N.prototype.constructor=N,R.prototype=Object.create(T.prototype),R.prototype.constructor=R,D.prototype=Object.create(T.prototype),D.prototype.constructor=D,Object.assign(U.prototype,{computeGroups:function(t){var e=[],i=void 0;t=t.faces;for(var n=0;n<t.length;n++){var r=t[n];if(r.materialIndex!==i){i=r.materialIndex,void 0!==a&&(a.count=3*n-a.start,e.push(a));var a={start:3*n,materialIndex:i}}}void 0!==a&&(a.count=3*n-a.start,e.push(a)),this.groups=e},fromGeometry:function(t){var e=t.faces,n=t.vertices,r=t.faceVertexUvs,a=r[0]&&0<r[0].length,o=r[1]&&0<r[1].length,s=t.morphTargets,h=s.length;if(0<h){for(var c=[],l=0;l<h;l++)c[l]={name:s[l].name,data:[]};this.morphTargets.position=c}var u=t.morphNormals,p=u.length;if(0<p){var d=[];for(l=0;l<p;l++)d[l]={name:u[l].name,data:[]};this.morphTargets.normal=d}var f=t.skinIndices,m=t.skinWeights,g=f.length===n.length,v=m.length===n.length;for(0<n.length&&0===e.length&&void 0,l=0;l<e.length;l++){var y=e[l];this.vertices.push(n[y.a],n[y.b],n[y.c]);var x=y.vertexNormals;for(3===x.length?this.normals.push(x[0],x[1],x[2]):(x=y.normal,this.normals.push(x,x,x)),x=y.vertexColors,3===x.length?this.colors.push(x[0],x[1],x[2]):(x=y.color,this.colors.push(x,x,x)),!0===a&&(x=r[0][l],void 0!==x?this.uvs.push(x[0],x[1],x[2]):this.uvs.push(new i,new i,new i)),!0===o&&(x=r[1][l],void 0!==x?this.uvs2.push(x[0],x[1],x[2]):this.uvs2.push(new i,new i,new i)),x=0;x<h;x++){var _=s[x].vertices;c[x].data.push(_[y.a],_[y.b],_[y.c])}for(x=0;x<p;x++)_=u[x].vertexNormals[l],d[x].data.push(_.a,_.b,_.c);g&&this.skinIndices.push(f[y.a],f[y.b],f[y.c]),v&&this.skinWeights.push(m[y.a],m[y.b],m[y.c])}return this.computeGroups(t),this.verticesNeedUpdate=t.verticesNeedUpdate,this.normalsNeedUpdate=t.normalsNeedUpdate,this.colorsNeedUpdate=t.colorsNeedUpdate,this.uvsNeedUpdate=t.uvsNeedUpdate,this.groupsNeedUpdate=t.groupsNeedUpdate,this}});var ca=1;z.prototype=Object.assign(Object.create(e.prototype),{constructor:z,isBufferGeometry:!0,getIndex:function(){return this.index},setIndex:function(t){Array.isArray(t)?this.index=new(65535<B(t)?N:O)(t,1):this.index=t},addAttribute:function(t,e,i){return e&&e.isBufferAttribute||e&&e.isInterleavedBufferAttribute?"index"===t?(this.setIndex(e),this):(this.attributes[t]=e,this):this.addAttribute(t,new T(e,i))},getAttribute:function(t){return this.attributes[t]},removeAttribute:function(t){return delete this.attributes[t],this},addGroup:function(t,e,i){this.groups.push({start:t,count:e,materialIndex:void 0!==i?i:0})},clearGroups:function(){this.groups=[]},setDrawRange:function(t,e){this.drawRange.start=t,this.drawRange.count=e},applyMatrix:function(t){var e=this.attributes.position;return void 0!==e&&(t.applyToBufferAttribute(e),e.needsUpdate=!0),e=this.attributes.normal,void 0!==e&&((new o).getNormalMatrix(t).applyToBufferAttribute(e),e.needsUpdate=!0),null!==this.boundingBox&&this.computeBoundingBox(),null!==this.boundingSphere&&this.computeBoundingSphere(),this},rotateX:function(){var t=new n;return function(e){return t.makeRotationX(e),this.applyMatrix(t),this}}(),rotateY:function(){var t=new n;return function(e){return t.makeRotationY(e),this.applyMatrix(t),this}}(),rotateZ:function(){var t=new n;return function(e){return t.makeRotationZ(e),this.applyMatrix(t),this}}(),translate:function(){var t=new n;return function(e,i,n){return t.makeTranslation(e,i,n),this.applyMatrix(t),this}}(),scale:function(){var t=new n;return function(e,i,n){return t.makeScale(e,i,n),this.applyMatrix(t),this}}(),lookAt:function(){var t=new b;return function(e){t.lookAt(e),t.updateMatrix(),this.applyMatrix(t.matrix)}}(),center:function(){var t=new a;return function(){return this.computeBoundingBox(),this.boundingBox.getCenter(t).negate(),this.translate(t.x,t.y,t.z),this}}(),setFromObject:function(t){var e=t.geometry;if(t.isPoints||t.isLine){t=new R(3*e.vertices.length,3);var i=new R(3*e.colors.length,3);this.addAttribute("position",t.copyVector3sArray(e.vertices)),this.addAttribute("color",i.copyColorsArray(e.colors)),e.lineDistances&&e.lineDistances.length===e.vertices.length&&(t=new R(e.lineDistances.length,1),this.addAttribute("lineDistance",t.copyArray(e.lineDistances))),null!==e.boundingSphere&&(this.boundingSphere=e.boundingSphere.clone()),null!==e.boundingBox&&(this.boundingBox=e.boundingBox.clone())}else t.isMesh&&e&&e.isGeometry&&this.fromGeometry(e);return this},setFromPoints:function(t){for(var e=[],i=0,n=t.length;i<n;i++){var r=t[i];e.push(r.x,r.y,r.z||0)}return this.addAttribute("position",new R(e,3)),this},updateFromObject:function(t){var e=t.geometry;if(t.isMesh){var i=e.__directGeometry;if(!0===e.elementsNeedUpdate&&(i=void 0,e.elementsNeedUpdate=!1),void 0===i)return this.fromGeometry(e);i.verticesNeedUpdate=e.verticesNeedUpdate,i.normalsNeedUpdate=e.normalsNeedUpdate,i.colorsNeedUpdate=e.colorsNeedUpdate,i.uvsNeedUpdate=e.uvsNeedUpdate,i.groupsNeedUpdate=e.groupsNeedUpdate,e.verticesNeedUpdate=!1,e.normalsNeedUpdate=!1,e.colorsNeedUpdate=!1,e.uvsNeedUpdate=!1,e.groupsNeedUpdate=!1,e=i}return!0===e.verticesNeedUpdate&&(i=this.attributes.position,void 0!==i&&(i.copyVector3sArray(e.vertices),i.needsUpdate=!0),e.verticesNeedUpdate=!1),!0===e.normalsNeedUpdate&&(i=this.attributes.normal,void 0!==i&&(i.copyVector3sArray(e.normals),i.needsUpdate=!0),e.normalsNeedUpdate=!1),!0===e.colorsNeedUpdate&&(i=this.attributes.color,void 0!==i&&(i.copyColorsArray(e.colors),i.needsUpdate=!0),e.colorsNeedUpdate=!1),e.uvsNeedUpdate&&(i=this.attributes.uv,void 0!==i&&(i.copyVector2sArray(e.uvs),i.needsUpdate=!0),e.uvsNeedUpdate=!1),e.lineDistancesNeedUpdate&&(i=this.attributes.lineDistance,void 0!==i&&(i.copyArray(e.lineDistances),i.needsUpdate=!0),e.lineDistancesNeedUpdate=!1),e.groupsNeedUpdate&&(e.computeGroups(t.geometry),this.groups=e.groups,e.groupsNeedUpdate=!1),this},fromGeometry:function(t){return t.__directGeometry=(new U).fromGeometry(t),this.fromDirectGeometry(t.__directGeometry)},fromDirectGeometry:function(t){var e=new Float32Array(3*t.vertices.length);this.addAttribute("position",new T(e,3).copyVector3sArray(t.vertices)),0<t.normals.length&&(e=new Float32Array(3*t.normals.length),this.addAttribute("normal",new T(e,3).copyVector3sArray(t.normals))),0<t.colors.length&&(e=new Float32Array(3*t.colors.length),this.addAttribute("color",new T(e,3).copyColorsArray(t.colors))),0<t.uvs.length&&(e=new Float32Array(2*t.uvs.length),this.addAttribute("uv",new T(e,2).copyVector2sArray(t.uvs))),0<t.uvs2.length&&(e=new Float32Array(2*t.uvs2.length),this.addAttribute("uv2",new T(e,2).copyVector2sArray(t.uvs2))),this.groups=t.groups;for(var i in t.morphTargets){e=[];for(var n=t.morphTargets[i],r=0,a=n.length;r<a;r++){var o=n[r],s=new R(3*o.data.length,3);s.name=o.name,e.push(s.copyVector3sArray(o.data))}this.morphAttributes[i]=e}return 0<t.skinIndices.length&&(i=new R(4*t.skinIndices.length,4),this.addAttribute("skinIndex",i.copyVector4sArray(t.skinIndices))),0<t.skinWeights.length&&(i=new R(4*t.skinWeights.length,4),this.addAttribute("skinWeight",i.copyVector4sArray(t.skinWeights))),null!==t.boundingSphere&&(this.boundingSphere=t.boundingSphere.clone()),null!==t.boundingBox&&(this.boundingBox=t.boundingBox.clone()),this},computeBoundingBox:function(){null===this.boundingBox&&(this.boundingBox=new p);var t=this.attributes.position;void 0!==t?this.boundingBox.setFromBufferAttribute(t):this.boundingBox.makeEmpty(),(isNaN(this.boundingBox.min.x)||isNaN(this.boundingBox.min.y)||isNaN(this.boundingBox.min.z))&&void 0},computeBoundingSphere:function(){var t=new p,e=new a;return function(){null===this.boundingSphere&&(this.boundingSphere=new d);var i=this.attributes.position;if(i){var n=this.boundingSphere.center;t.setFromBufferAttribute(i),t.getCenter(n);for(var r=0,a=0,o=i.count;a<o;a++)e.x=i.getX(a),e.y=i.getY(a),e.z=i.getZ(a),r=Math.max(r,n.distanceToSquared(e));this.boundingSphere.radius=Math.sqrt(r),isNaN(this.boundingSphere.radius)&&void 0}}}(),computeFaceNormals:function(){},computeVertexNormals:function(){var t=this.index,e=this.attributes;if(e.position){var i=e.position.array;if(void 0===e.normal)this.addAttribute("normal",new T(new Float32Array(i.length),3));else for(var n=e.normal.array,r=0,o=n.length;r<o;r++)n[r]=0;n=e.normal.array;var s=new a,h=new a,c=new a,l=new a,u=new a;if(t){var p=t.array;for(r=0,o=t.count;r<o;r+=3){t=3*p[r+0];var d=3*p[r+1],f=3*p[r+2];s.fromArray(i,t),h.fromArray(i,d),c.fromArray(i,f),l.subVectors(c,h),u.subVectors(s,h),l.cross(u),n[t]+=l.x,n[t+1]+=l.y,n[t+2]+=l.z,n[d]+=l.x,n[d+1]+=l.y,n[d+2]+=l.z,n[f]+=l.x,n[f+1]+=l.y,n[f+2]+=l.z}}else for(r=0,o=i.length;r<o;r+=9)s.fromArray(i,r),h.fromArray(i,r+3),c.fromArray(i,r+6),l.subVectors(c,h),u.subVectors(s,h),l.cross(u),n[r]=l.x,n[r+1]=l.y,n[r+2]=l.z,n[r+3]=l.x,n[r+4]=l.y,n[r+5]=l.z,n[r+6]=l.x,n[r+7]=l.y,n[r+8]=l.z;this.normalizeNormals(),e.normal.needsUpdate=!0}},merge:function(t,e){if(t&&t.isBufferGeometry){void 0===e&&void(e=0);var i,n=this.attributes;for(i in n)if(void 0!==t.attributes[i]){var r=n[i].array,a=t.attributes[i],o=a.array,s=0;for(a=a.itemSize*e;s<o.length;s++,a++)r[a]=o[s]}return this}},normalizeNormals:function(){var t=new a;return function(){for(var e=this.attributes.normal,i=0,n=e.count;i<n;i++)t.x=e.getX(i),t.y=e.getY(i),t.z=e.getZ(i),t.normalize(),e.setXYZ(i,t.x,t.y,t.z)}}(),toNonIndexed:function(){if(null===this.index)return this;var t,e=new z,i=this.index.array,n=this.attributes;for(t in n){var r=n[t],a=r.array,o=r.itemSize,s=new a.constructor(i.length*o),h=0;r=0;for(var c=i.length;r<c;r++)for(var l=i[r]*o,u=0;u<o;u++)s[h++]=a[l++];e.addAttribute(t,new T(s,o))}for(i=this.groups,r=0,c=i.length;r<c;r++)n=i[r],e.addGroup(n.start,n.count,n.materialIndex);return e},toJSON:function(){var t={metadata:{version:4.5,type:"BufferGeometry",generator:"BufferGeometry.toJSON"}};if(t.uuid=this.uuid,t.type=this.type,""!==this.name&&(t.name=this.name),0<Object.keys(this.userData).length&&(t.userData=this.userData),void 0!==this.parameters){var e=this.parameters;for(r in e)void 0!==e[r]&&(t[r]=e[r]);return t}t.data={attributes:{}};var i=this.index;null!==i&&(e=Array.prototype.slice.call(i.array),t.data.index={type:i.array.constructor.name,array:e}),i=this.attributes;for(r in i){var n=i[r];e=Array.prototype.slice.call(n.array),t.data.attributes[r]={itemSize:n.itemSize,type:n.array.constructor.name,array:e,normalized:n.normalized}}var r=this.groups;return 0<r.length&&(t.data.groups=JSON.parse(JSON.stringify(r))),r=this.boundingSphere,null!==r&&(t.data.boundingSphere={center:r.center.toArray(),radius:r.radius}),t},clone:function(){return(new z).copy(this)},copy:function(t){var e;this.index=null,this.attributes={},this.morphAttributes={},this.groups=[],this.boundingSphere=this.boundingBox=null,this.name=t.name;var i=t.index;null!==i&&this.setIndex(i.clone()),i=t.attributes;for(o in i)this.addAttribute(o,i[o].clone());var n=t.morphAttributes;for(o in n){var r=[],a=n[o];for(i=0,e=a.length;i<e;i++)r.push(a[i].clone());this.morphAttributes[o]=r}var o=t.groups;for(i=0,e=o.length;i<e;i++)n=o[i],this.addGroup(n.start,n.count,n.materialIndex);return o=t.boundingBox,null!==o&&(this.boundingBox=o.clone()),o=t.boundingSphere,null!==o&&(this.boundingSphere=o.clone()),this.drawRange.start=t.drawRange.start,this.drawRange.count=t.drawRange.count,this.userData=t.userData,this},dispose:function(){this.dispatchEvent({type:"dispose"})}}),F.prototype=Object.create(A.prototype),F.prototype.constructor=F,G.prototype=Object.create(z.prototype),G.prototype.constructor=G,V.prototype=Object.create(A.prototype),V.prototype.constructor=V,k.prototype=Object.create(z.prototype),k.prototype.constructor=k;var la=0;j.prototype=Object.assign(Object.create(e.prototype),{constructor:j,isMaterial:!0,onBeforeCompile:function(){},setValues:function(t){if(void 0!==t)for(var e in t){var i=t[e];if(void 0===i);else if("shading"===e)this.flatShading=1===i;else{var n=this[e];void 0===n?void 0:n&&n.isColor?n.set(i):n&&n.isVector3&&i&&i.isVector3?n.copy(i):this[e]="overdraw"===e?Number(i):i}}},toJSON:function(t){function e(t){var e,i=[];for(e in t){var n=t[e];delete n.metadata,i.push(n)}return i}var i=void 0===t||"string"==typeof t;i&&(t={textures:{},images:{}});var n={metadata:{version:4.5,type:"Material",generator:"Material.toJSON"}};return n.uuid=this.uuid,n.type=this.type,""!==this.name&&(n.name=this.name),this.color&&this.color.isColor&&(n.color=this.color.getHex()),void 0!==this.roughness&&(n.roughness=this.roughness),void 0!==this.metalness&&(n.metalness=this.metalness),this.emissive&&this.emissive.isColor&&(n.emissive=this.emissive.getHex()),1!==this.emissiveIntensity&&(n.emissiveIntensity=this.emissiveIntensity),this.specular&&this.specular.isColor&&(n.specular=this.specular.getHex()),void 0!==this.shininess&&(n.shininess=this.shininess),void 0!==this.clearCoat&&(n.clearCoat=this.clearCoat),void 0!==this.clearCoatRoughness&&(n.clearCoatRoughness=this.clearCoatRoughness),this.map&&this.map.isTexture&&(n.map=this.map.toJSON(t).uuid),this.alphaMap&&this.alphaMap.isTexture&&(n.alphaMap=this.alphaMap.toJSON(t).uuid),this.lightMap&&this.lightMap.isTexture&&(n.lightMap=this.lightMap.toJSON(t).uuid),this.aoMap&&this.aoMap.isTexture&&(n.aoMap=this.aoMap.toJSON(t).uuid,n.aoMapIntensity=this.aoMapIntensity),this.bumpMap&&this.bumpMap.isTexture&&(n.bumpMap=this.bumpMap.toJSON(t).uuid,n.bumpScale=this.bumpScale),this.normalMap&&this.normalMap.isTexture&&(n.normalMap=this.normalMap.toJSON(t).uuid,n.normalMapType=this.normalMapType,n.normalScale=this.normalScale.toArray()),this.displacementMap&&this.displacementMap.isTexture&&(n.displacementMap=this.displacementMap.toJSON(t).uuid,n.displacementScale=this.displacementScale,n.displacementBias=this.displacementBias),this.roughnessMap&&this.roughnessMap.isTexture&&(n.roughnessMap=this.roughnessMap.toJSON(t).uuid),this.metalnessMap&&this.metalnessMap.isTexture&&(n.metalnessMap=this.metalnessMap.toJSON(t).uuid),this.emissiveMap&&this.emissiveMap.isTexture&&(n.emissiveMap=this.emissiveMap.toJSON(t).uuid),this.specularMap&&this.specularMap.isTexture&&(n.specularMap=this.specularMap.toJSON(t).uuid),this.envMap&&this.envMap.isTexture&&(n.envMap=this.envMap.toJSON(t).uuid,n.reflectivity=this.reflectivity),this.gradientMap&&this.gradientMap.isTexture&&(n.gradientMap=this.gradientMap.toJSON(t).uuid),void 0!==this.size&&(n.size=this.size),void 0!==this.sizeAttenuation&&(n.sizeAttenuation=this.sizeAttenuation),1!==this.blending&&(n.blending=this.blending),!0===this.flatShading&&(n.flatShading=this.flatShading),0!==this.side&&(n.side=this.side),0!==this.vertexColors&&(n.vertexColors=this.vertexColors),1>this.opacity&&(n.opacity=this.opacity),!0===this.transparent&&(n.transparent=this.transparent),n.depthFunc=this.depthFunc,n.depthTest=this.depthTest,n.depthWrite=this.depthWrite,0!==this.rotation&&(n.rotation=this.rotation),!0===this.polygonOffset&&(n.polygonOffset=!0),0!==this.polygonOffsetFactor&&(n.polygonOffsetFactor=this.polygonOffsetFactor),0!==this.polygonOffsetUnits&&(n.polygonOffsetUnits=this.polygonOffsetUnits),1!==this.linewidth&&(n.linewidth=this.linewidth),void 0!==this.dashSize&&(n.dashSize=this.dashSize),void 0!==this.gapSize&&(n.gapSize=this.gapSize),void 0!==this.scale&&(n.scale=this.scale),!0===this.dithering&&(n.dithering=!0),0<this.alphaTest&&(n.alphaTest=this.alphaTest),!0===this.premultipliedAlpha&&(n.premultipliedAlpha=this.premultipliedAlpha),!0===this.wireframe&&(n.wireframe=this.wireframe),1<this.wireframeLinewidth&&(n.wireframeLinewidth=this.wireframeLinewidth),"round"!==this.wireframeLinecap&&(n.wireframeLinecap=this.wireframeLinecap),"round"!==this.wireframeLinejoin&&(n.wireframeLinejoin=this.wireframeLinejoin),!0===this.morphTargets&&(n.morphTargets=!0),!0===this.skinning&&(n.skinning=!0),!1===this.visible&&(n.visible=!1),"{}"!==JSON.stringify(this.userData)&&(n.userData=this.userData),i&&(i=e(t.textures),t=e(t.images),0<i.length&&(n.textures=i),0<t.length&&(n.images=t)),n},clone:function(){return(new this.constructor).copy(this)},copy:function(t){this.name=t.name,this.fog=t.fog,this.lights=t.lights,this.blending=t.blending,this.side=t.side,this.flatShading=t.flatShading,this.vertexColors=t.vertexColors,this.opacity=t.opacity,this.transparent=t.transparent,this.blendSrc=t.blendSrc,this.blendDst=t.blendDst,this.blendEquation=t.blendEquation,this.blendSrcAlpha=t.blendSrcAlpha,this.blendDstAlpha=t.blendDstAlpha,this.blendEquationAlpha=t.blendEquationAlpha,this.depthFunc=t.depthFunc,this.depthTest=t.depthTest,this.depthWrite=t.depthWrite,this.colorWrite=t.colorWrite,this.precision=t.precision,this.polygonOffset=t.polygonOffset,this.polygonOffsetFactor=t.polygonOffsetFactor,this.polygonOffsetUnits=t.polygonOffsetUnits,this.dithering=t.dithering,this.alphaTest=t.alphaTest,this.premultipliedAlpha=t.premultipliedAlpha,this.overdraw=t.overdraw,this.visible=t.visible,this.userData=JSON.parse(JSON.stringify(t.userData)),this.clipShadows=t.clipShadows,this.clipIntersection=t.clipIntersection;var e=t.clippingPlanes,i=null;if(null!==e){var n=e.length;i=Array(n);for(var r=0;r!==n;++r)i[r]=e[r].clone()}return this.clippingPlanes=i,this.shadowSide=t.shadowSide,this},dispose:function(){this.dispatchEvent({type:"dispose"})}}),W.prototype=Object.create(j.prototype),W.prototype.constructor=W,W.prototype.isMeshBasicMaterial=!0,W.prototype.copy=function(t){return j.prototype.copy.call(this,t),this.color.copy(t.color),this.map=t.map,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.specularMap=t.specularMap,this.alphaMap=t.alphaMap,this.envMap=t.envMap,this.combine=t.combine,this.reflectivity=t.reflectivity,this.refractionRatio=t.refractionRatio,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.skinning=t.skinning,this.morphTargets=t.morphTargets,this},H.prototype=Object.create(j.prototype),H.prototype.constructor=H,H.prototype.isShaderMaterial=!0,H.prototype.copy=function(t){return j.prototype.copy.call(this,t),this.fragmentShader=t.fragmentShader,this.vertexShader=t.vertexShader,this.uniforms=na.clone(t.uniforms),this.defines=Object.assign({},t.defines),this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.lights=t.lights,this.clipping=t.clipping,this.skinning=t.skinning,this.morphTargets=t.morphTargets,this.morphNormals=t.morphNormals,this.extensions=t.extensions,this},H.prototype.toJSON=function(t){var e=j.prototype.toJSON.call(this,t);e.uniforms={};for(var i in this.uniforms){var n=this.uniforms[i].value;e.uniforms[i]=n.isTexture?{type:"t",value:n.toJSON(t).uuid}:n.isColor?{type:"c",value:n.getHex()}:n.isVector2?{type:"v2",value:n.toArray()}:n.isVector3?{type:"v3",value:n.toArray()}:n.isVector4?{type:"v4",value:n.toArray()}:n.isMatrix4?{type:"m4",value:n.toArray()}:{value:n}}return 0<Object.keys(this.defines).length&&(e.defines=this.defines),e.vertexShader=this.vertexShader,e.fragmentShader=this.fragmentShader,e},Object.assign(X.prototype,{set:function(t,e){return this.origin.copy(t),this.direction.copy(e),this},clone:function(){return(new this.constructor).copy(this)},copy:function(t){return this.origin.copy(t.origin),this.direction.copy(t.direction),this},at:function(t,e){return void 0===e&&(e=new a),e.copy(this.direction).multiplyScalar(t).add(this.origin)},lookAt:function(t){return this.direction.copy(t).sub(this.origin).normalize(),this},recast:function(){var t=new a;return function(e){return this.origin.copy(this.at(e,t)),this}}(),closestPointToPoint:function(t,e){return void 0===e&&(e=new a),e.subVectors(t,this.origin),t=e.dot(this.direction),0>t?e.copy(this.origin):e.copy(this.direction).multiplyScalar(t).add(this.origin)},distanceToPoint:function(t){return Math.sqrt(this.distanceSqToPoint(t))},distanceSqToPoint:function(){var t=new a;return function(e){var i=t.subVectors(e,this.origin).dot(this.direction);return 0>i?this.origin.distanceToSquared(e):(t.copy(this.direction).multiplyScalar(i).add(this.origin),t.distanceToSquared(e))}}(),distanceSqToSegment:function(){var t=new a,e=new a,i=new a;return function(n,r,a,o){t.copy(n).add(r).multiplyScalar(.5),e.copy(r).sub(n).normalize(),i.copy(this.origin).sub(t);var s=.5*n.distanceTo(r),h=-this.direction.dot(e),c=i.dot(this.direction),l=-i.dot(e),u=i.lengthSq(),p=Math.abs(1-h*h);if(0<p){n=h*l-c,r=h*c-l;var d=s*p;0<=n?r>=-d?r<=d?(s=1/p,n*=s,r*=s,h=n*(n+h*r+2*c)+r*(h*n+r+2*l)+u):(r=s,n=Math.max(0,-(h*r+c)),h=-n*n+r*(r+2*l)+u):(r=-s,n=Math.max(0,-(h*r+c)),h=-n*n+r*(r+2*l)+u):r<=-d?(n=Math.max(0,-(-h*s+c)),r=0<n?-s:Math.min(Math.max(-s,-l),s),h=-n*n+r*(r+2*l)+u):r<=d?(n=0,r=Math.min(Math.max(-s,-l),s),h=r*(r+2*l)+u):(n=Math.max(0,-(h*s+c)),r=0<n?s:Math.min(Math.max(-s,-l),s),h=-n*n+r*(r+2*l)+u)}else r=0<h?-s:s,n=Math.max(0,-(h*r+c)),h=-n*n+r*(r+2*l)+u;return a&&a.copy(this.direction).multiplyScalar(n).add(this.origin),o&&o.copy(e).multiplyScalar(r).add(t),h}}(),intersectSphere:function(){var t=new a;return function(e,i){t.subVectors(e.center,this.origin);var n=t.dot(this.direction),r=t.dot(t)-n*n;return e=e.radius*e.radius,r>e?null:(e=Math.sqrt(e-r),r=n-e,n+=e,0>r&&0>n?null:0>r?this.at(n,i):this.at(r,i))}}(),intersectsSphere:function(t){return this.distanceSqToPoint(t.center)<=t.radius*t.radius},distanceToPlane:function(t){var e=t.normal.dot(this.direction);return 0===e?0===t.distanceToPoint(this.origin)?0:null:(t=-(this.origin.dot(t.normal)+t.constant)/e,0<=t?t:null)},intersectPlane:function(t,e){return t=this.distanceToPlane(t),null===t?null:this.at(t,e)},intersectsPlane:function(t){var e=t.distanceToPoint(this.origin);return 0===e||0>t.normal.dot(this.direction)*e},intersectBox:function(t,e){var i=1/this.direction.x,n=1/this.direction.y,r=1/this.direction.z,a=this.origin;if(0<=i){var o=(t.min.x-a.x)*i;i*=t.max.x-a.x}else o=(t.max.x-a.x)*i,i*=t.min.x-a.x;if(0<=n){var s=(t.min.y-a.y)*n;n*=t.max.y-a.y}else s=(t.max.y-a.y)*n,n*=t.min.y-a.y;return o>n||s>i?null:((s>o||o!==o)&&(o=s),(n<i||i!==i)&&(i=n),0<=r?(s=(t.min.z-a.z)*r,t=(t.max.z-a.z)*r):(s=(t.max.z-a.z)*r,t=(t.min.z-a.z)*r),o>t||s>i?null:((s>o||o!==o)&&(o=s),(t<i||i!==i)&&(i=t),0>i?null:this.at(0<=o?o:i,e)))},intersectsBox:function(){var t=new a;return function(e){return null!==this.intersectBox(e,t)}}(),intersectTriangle:function(){var t=new a,e=new a,i=new a,n=new a;return function(r,a,o,s,h){if(e.subVectors(a,r),i.subVectors(o,r),n.crossVectors(e,i),a=this.direction.dot(n),0<a){if(s)return null;s=1}else{if(!(0>a))return null;s=-1,a=-a}return t.subVectors(this.origin,r),r=s*this.direction.dot(i.crossVectors(t,i)),0>r?null:(o=s*this.direction.dot(e.cross(t)),0>o||r+o>a?null:(r=-s*t.dot(n),0>r?null:this.at(r/a,h)))}}(),applyMatrix4:function(t){return this.origin.applyMatrix4(t),this.direction.transformDirection(t),this},equals:function(t){return t.origin.equals(this.origin)&&t.direction.equals(this.direction)}}),Object.assign(q,{getNormal:function(){var t=new a;return function(e,i,n,r){return void 0===r&&(r=new a),r.subVectors(n,i),t.subVectors(e,i),r.cross(t),e=r.lengthSq(),0<e?r.multiplyScalar(1/Math.sqrt(e)):r.set(0,0,0)}}(),getBarycoord:function(){var t=new a,e=new a,i=new a;return function(n,r,o,s,h){t.subVectors(s,r),e.subVectors(o,r),i.subVectors(n,r),n=t.dot(t),r=t.dot(e),o=t.dot(i);var c=e.dot(e);s=e.dot(i);var l=n*c-r*r;return void 0===h&&(h=new a),0===l?h.set(-2,-1,-1):(l=1/l,c=(c*o-r*s)*l,n=(n*s-r*o)*l,h.set(1-c-n,n,c))}}(),containsPoint:function(){var t=new a;return function(e,i,n,r){return q.getBarycoord(e,i,n,r,t),0<=t.x&&0<=t.y&&1>=t.x+t.y}}(),getUV:function(){var t=new a;return function(e,i,n,r,a,o,s,h){return this.getBarycoord(e,i,n,r,t),h.set(0,0),h.addScaledVector(a,t.x),h.addScaledVector(o,t.y),h.addScaledVector(s,t.z),h}}()}),Object.assign(q.prototype,{set:function(t,e,i){return this.a.copy(t),this.b.copy(e),this.c.copy(i),this},setFromPointsAndIndices:function(t,e,i,n){return this.a.copy(t[e]),this.b.copy(t[i]),this.c.copy(t[n]),this},clone:function(){return(new this.constructor).copy(this)},copy:function(t){return this.a.copy(t.a),this.b.copy(t.b),this.c.copy(t.c),this},getArea:function(){var t=new a,e=new a;return function(){return t.subVectors(this.c,this.b),e.subVectors(this.a,this.b),.5*t.cross(e).length()}}(),getMidpoint:function(t){return void 0===t&&(t=new a),t.addVectors(this.a,this.b).add(this.c).multiplyScalar(1/3)},getNormal:function(t){return q.getNormal(this.a,this.b,this.c,t)},getPlane:function(t){return void 0===t&&(t=new a),t.setFromCoplanarPoints(this.a,this.b,this.c)},getBarycoord:function(t,e){return q.getBarycoord(t,this.a,this.b,this.c,e)},containsPoint:function(t){return q.containsPoint(t,this.a,this.b,this.c)},getUV:function(t,e,i,n,r){return q.getUV(t,this.a,this.b,this.c,e,i,n,r)},intersectsBox:function(t){return t.intersectsTriangle(this)},closestPointToPoint:function(){var t=new a,e=new a,i=new a,n=new a,r=new a,o=new a;return function(s,h){void 0===h&&(h=new a);var c=this.a,l=this.b,u=this.c;t.subVectors(l,c),e.subVectors(u,c),n.subVectors(s,c);var p=t.dot(n),d=e.dot(n);if(0>=p&&0>=d)return h.copy(c);r.subVectors(s,l);var f=t.dot(r),m=e.dot(r);if(0<=f&&m<=f)return h.copy(l);var g=p*m-f*d;if(0>=g&&0<=p&&0>=f)return l=p/(p-f),h.copy(c).addScaledVector(t,l);o.subVectors(s,u),s=t.dot(o);var v=e.dot(o);return 0<=v&&s<=v?h.copy(u):(p=s*d-p*v,0>=p&&0<=d&&0>=v?(g=d/(d-v),h.copy(c).addScaledVector(e,g)):(d=f*v-s*m,
0>=d&&0<=m-f&&0<=s-v?(i.subVectors(u,l),g=(m-f)/(m-f+(s-v)),h.copy(l).addScaledVector(i,g)):(u=1/(d+p+g),l=p*u,g*=u,h.copy(c).addScaledVector(t,l).addScaledVector(e,g))))}}(),equals:function(t){return t.a.equals(this.a)&&t.b.equals(this.b)&&t.c.equals(this.c)}}),Y.prototype=Object.assign(Object.create(b.prototype),{constructor:Y,isMesh:!0,setDrawMode:function(t){this.drawMode=t},copy:function(t){return b.prototype.copy.call(this,t),this.drawMode=t.drawMode,void 0!==t.morphTargetInfluences&&(this.morphTargetInfluences=t.morphTargetInfluences.slice()),void 0!==t.morphTargetDictionary&&(this.morphTargetDictionary=Object.assign({},t.morphTargetDictionary)),this},updateMorphTargets:function(){var t=this.geometry;if(t.isBufferGeometry){t=t.morphAttributes;var e=Object.keys(t);if(0<e.length){var i=t[e[0]];if(void 0!==i)for(this.morphTargetInfluences=[],this.morphTargetDictionary={},t=0,e=i.length;t<e;t++){var n=i[t].name||String(t);this.morphTargetInfluences.push(0),this.morphTargetDictionary[n]=t}}}else if(i=t.morphTargets,void 0!==i&&0<i.length)for(this.morphTargetInfluences=[],this.morphTargetDictionary={},t=0,e=i.length;t<e;t++)n=i[t].name||String(t),this.morphTargetInfluences.push(0),this.morphTargetDictionary[n]=t},raycast:function(){function t(t,e,i,n,r,a,o,s){return null===(1===e.side?n.intersectTriangle(o,a,r,!0,s):n.intersectTriangle(r,a,o,2!==e.side,s))?null:(x.copy(s),x.applyMatrix4(t.matrixWorld),e=i.ray.origin.distanceTo(x),e<i.near||e>i.far?null:{distance:e,point:x.clone(),object:t})}function e(e,n,r,a,o,s,u,p,d){return h.fromBufferAttribute(o,u),c.fromBufferAttribute(o,p),l.fromBufferAttribute(o,d),(e=t(e,n,r,a,h,c,l,y))&&(s&&(m.fromBufferAttribute(s,u),g.fromBufferAttribute(s,p),v.fromBufferAttribute(s,d),e.uv=q.getUV(y,h,c,l,m,g,v,new i)),s=new S(u,p,d),q.getNormal(h,c,l,s.normal),e.face=s),e}var r=new n,o=new X,s=new d,h=new a,c=new a,l=new a,u=new a,p=new a,f=new a,m=new i,g=new i,v=new i,y=new a,x=new a;return function(n,a){var d=this.geometry,x=this.material,_=this.matrixWorld;if(void 0!==x&&(null===d.boundingSphere&&d.computeBoundingSphere(),s.copy(d.boundingSphere),s.applyMatrix4(_),!1!==n.ray.intersectsSphere(s)&&(r.getInverse(_),o.copy(n.ray).applyMatrix4(r),null===d.boundingBox||!1!==o.intersectsBox(d.boundingBox))))if(d.isBufferGeometry){var b=d.index,M=d.attributes.position,w=d.attributes.uv,S=d.groups;d=d.drawRange;var A;if(null!==b)if(Array.isArray(x)){var T=0;for(A=S.length;T<A;T++){var L=S[T],E=x[L.materialIndex];_=Math.max(L.start,d.start);var C=Math.min(L.start+L.count,d.start+d.count);for(L=_;L<C;L+=3){_=b.getX(L);var P=b.getX(L+1),O=b.getX(L+2);(_=e(this,E,n,o,M,w,_,P,O))&&(_.faceIndex=Math.floor(L/3),a.push(_))}}}else for(_=Math.max(0,d.start),C=Math.min(b.count,d.start+d.count),T=_,A=C;T<A;T+=3)_=b.getX(T),P=b.getX(T+1),O=b.getX(T+2),(_=e(this,x,n,o,M,w,_,P,O))&&(_.faceIndex=Math.floor(T/3),a.push(_));else if(void 0!==M)if(Array.isArray(x))for(T=0,A=S.length;T<A;T++)for(L=S[T],E=x[L.materialIndex],_=Math.max(L.start,d.start),C=Math.min(L.start+L.count,d.start+d.count),L=_;L<C;L+=3)_=L,P=L+1,O=L+2,(_=e(this,E,n,o,M,w,_,P,O))&&(_.faceIndex=Math.floor(L/3),a.push(_));else for(_=Math.max(0,d.start),C=Math.min(M.count,d.start+d.count),T=_,A=C;T<A;T+=3)_=T,P=T+1,O=T+2,(_=e(this,x,n,o,M,w,_,P,O))&&(_.faceIndex=Math.floor(T/3),a.push(_))}else if(d.isGeometry)for(M=Array.isArray(x),w=d.vertices,S=d.faces,_=d.faceVertexUvs[0],0<_.length&&(b=_),L=0,C=S.length;L<C;L++)if(P=S[L],_=M?x[P.materialIndex]:x,void 0!==_){if(T=w[P.a],A=w[P.b],E=w[P.c],!0===_.morphTargets){O=d.morphTargets;var I=this.morphTargetInfluences;h.set(0,0,0),c.set(0,0,0),l.set(0,0,0);for(var N=0,R=O.length;N<R;N++){var D=I[N];if(0!==D){var U=O[N].vertices;h.addScaledVector(u.subVectors(U[P.a],T),D),c.addScaledVector(p.subVectors(U[P.b],A),D),l.addScaledVector(f.subVectors(U[P.c],E),D)}}h.add(T),c.add(A),l.add(E),T=h,A=c,E=l}(_=t(this,_,n,o,T,A,E,y))&&(b&&b[L]&&(O=b[L],m.copy(O[0]),g.copy(O[1]),v.copy(O[2]),_.uv=q.getUV(y,T,A,E,m,g,v,new i)),_.face=P,_.faceIndex=L,a.push(_))}}}(),clone:function(){return new this.constructor(this.geometry,this.material).copy(this)}}),ot.prototype=Object.create(s.prototype),ot.prototype.constructor=ot,ot.prototype.isCubeTexture=!0,Object.defineProperty(ot.prototype,"images",{get:function(){return this.image},set:function(t){this.image=t}});var ua=new s,pa=new ot,da=[],fa=[],ma=new Float32Array(16),ga=new Float32Array(9),va=new Float32Array(4);Bt.prototype.updateCache=function(t){var e=this.cache;t instanceof Float32Array&&e.length!==t.length&&(this.cache=new Float32Array(t.length)),ct(e,t)},zt.prototype.setValue=function(t,e,i){for(var n=this.seq,r=0,a=n.length;r!==a;++r){var o=n[r];o.setValue(t,e[o.id],i)}};var ya=/([\w\d_]+)(\])?(\[|\.)?/g;Ft.prototype.setValue=function(t,e,i){e=this.map[e],void 0!==e&&e.setValue(t,i,this.renderer)},Ft.prototype.setOptional=function(t,e,i){e=e[i],void 0!==e&&this.setValue(t,i,e)},Ft.upload=function(t,e,i,n){for(var r=0,a=e.length;r!==a;++r){var o=e[r],s=i[o.id];!1!==s.needsUpdate&&o.setValue(t,s.value,n)}},Ft.seqWithValue=function(t,e){for(var i=[],n=0,r=t.length;n!==r;++n){var a=t[n];a.id in e&&i.push(a)}return i};var xa=0,_a=0;ce.prototype=Object.create(j.prototype),ce.prototype.constructor=ce,ce.prototype.isMeshDepthMaterial=!0,ce.prototype.copy=function(t){return j.prototype.copy.call(this,t),this.depthPacking=t.depthPacking,this.skinning=t.skinning,this.morphTargets=t.morphTargets,this.map=t.map,this.alphaMap=t.alphaMap,this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this},le.prototype=Object.create(j.prototype),le.prototype.constructor=le,le.prototype.isMeshDistanceMaterial=!0,le.prototype.copy=function(t){return j.prototype.copy.call(this,t),this.referencePosition.copy(t.referencePosition),this.nearDistance=t.nearDistance,this.farDistance=t.farDistance,this.skinning=t.skinning,this.morphTargets=t.morphTargets,this.map=t.map,this.alphaMap=t.alphaMap,this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this},me.prototype=Object.assign(Object.create(b.prototype),{constructor:me,isGroup:!0}),ge.prototype=Object.assign(Object.create(M.prototype),{constructor:ge,isPerspectiveCamera:!0,copy:function(t,e){return M.prototype.copy.call(this,t,e),this.fov=t.fov,this.zoom=t.zoom,this.near=t.near,this.far=t.far,this.focus=t.focus,this.aspect=t.aspect,this.view=null===t.view?null:Object.assign({},t.view),this.filmGauge=t.filmGauge,this.filmOffset=t.filmOffset,this},setFocalLength:function(t){t=.5*this.getFilmHeight()/t,this.fov=2*$r.RAD2DEG*Math.atan(t),this.updateProjectionMatrix()},getFocalLength:function(){var t=Math.tan(.5*$r.DEG2RAD*this.fov);return.5*this.getFilmHeight()/t},getEffectiveFOV:function(){return 2*$r.RAD2DEG*Math.atan(Math.tan(.5*$r.DEG2RAD*this.fov)/this.zoom)},getFilmWidth:function(){return this.filmGauge*Math.min(this.aspect,1)},getFilmHeight:function(){return this.filmGauge/Math.max(this.aspect,1)},setViewOffset:function(t,e,i,n,r,a){this.aspect=t/e,null===this.view&&(this.view={enabled:!0,fullWidth:1,fullHeight:1,offsetX:0,offsetY:0,width:1,height:1}),this.view.enabled=!0,this.view.fullWidth=t,this.view.fullHeight=e,this.view.offsetX=i,this.view.offsetY=n,this.view.width=r,this.view.height=a,this.updateProjectionMatrix()},clearViewOffset:function(){null!==this.view&&(this.view.enabled=!1),this.updateProjectionMatrix()},updateProjectionMatrix:function(){var t=this.near,e=t*Math.tan(.5*$r.DEG2RAD*this.fov)/this.zoom,i=2*e,n=this.aspect*i,r=-.5*n,a=this.view;if(null!==this.view&&this.view.enabled){var o=a.fullWidth,s=a.fullHeight;r+=a.offsetX*n/o,e-=a.offsetY*i/s,n*=a.width/o,i*=a.height/s}a=this.filmOffset,0!==a&&(r+=t*a/this.getFilmWidth()),this.projectionMatrix.makePerspective(r,r+n,e,e-i,t,this.far),this.projectionMatrixInverse.getInverse(this.projectionMatrix)},toJSON:function(t){return t=b.prototype.toJSON.call(this,t),t.object.fov=this.fov,t.object.zoom=this.zoom,t.object.near=this.near,t.object.far=this.far,t.object.focus=this.focus,t.object.aspect=this.aspect,null!==this.view&&(t.object.view=Object.assign({},this.view)),t.object.filmGauge=this.filmGauge,t.object.filmOffset=this.filmOffset,t}}),ve.prototype=Object.assign(Object.create(ge.prototype),{constructor:ve,isArrayCamera:!0}),be.prototype.isFogExp2=!0,be.prototype.clone=function(){return new be(this.color,this.density)},be.prototype.toJSON=function(){return{type:"FogExp2",color:this.color.getHex(),density:this.density}},Me.prototype.isFog=!0,Me.prototype.clone=function(){return new Me(this.color,this.near,this.far)},Me.prototype.toJSON=function(){return{type:"Fog",color:this.color.getHex(),near:this.near,far:this.far}},we.prototype=Object.assign(Object.create(b.prototype),{constructor:we,copy:function(t,e){return b.prototype.copy.call(this,t,e),null!==t.background&&(this.background=t.background.clone()),null!==t.fog&&(this.fog=t.fog.clone()),null!==t.overrideMaterial&&(this.overrideMaterial=t.overrideMaterial.clone()),this.autoUpdate=t.autoUpdate,this.matrixAutoUpdate=t.matrixAutoUpdate,this},toJSON:function(t){var e=b.prototype.toJSON.call(this,t);return null!==this.background&&(e.object.background=this.background.toJSON(t)),null!==this.fog&&(e.object.fog=this.fog.toJSON()),e}}),Object.defineProperty(Se.prototype,"needsUpdate",{set:function(t){!0===t&&this.version++}}),Object.assign(Se.prototype,{isInterleavedBuffer:!0,onUploadCallback:function(){},setArray:function(t){if(Array.isArray(t))throw new TypeError("THREE.BufferAttribute: array should be a Typed Array.");return this.count=void 0!==t?t.length/this.stride:0,this.array=t,this},setDynamic:function(t){return this.dynamic=t,this},copy:function(t){return this.array=new t.array.constructor(t.array),this.count=t.count,this.stride=t.stride,this.dynamic=t.dynamic,this},copyAt:function(t,e,i){t*=this.stride,i*=e.stride;for(var n=0,r=this.stride;n<r;n++)this.array[t+n]=e.array[i+n];return this},set:function(t,e){return void 0===e&&(e=0),this.array.set(t,e),this},clone:function(){return(new this.constructor).copy(this)},onUpload:function(t){return this.onUploadCallback=t,this}}),Object.defineProperties(Ae.prototype,{count:{get:function(){return this.data.count}},array:{get:function(){return this.data.array}}}),Object.assign(Ae.prototype,{isInterleavedBufferAttribute:!0,setX:function(t,e){return this.data.array[t*this.data.stride+this.offset]=e,this},setY:function(t,e){return this.data.array[t*this.data.stride+this.offset+1]=e,this},setZ:function(t,e){return this.data.array[t*this.data.stride+this.offset+2]=e,this},setW:function(t,e){return this.data.array[t*this.data.stride+this.offset+3]=e,this},getX:function(t){return this.data.array[t*this.data.stride+this.offset]},getY:function(t){return this.data.array[t*this.data.stride+this.offset+1]},getZ:function(t){return this.data.array[t*this.data.stride+this.offset+2]},getW:function(t){return this.data.array[t*this.data.stride+this.offset+3]},setXY:function(t,e,i){return t=t*this.data.stride+this.offset,this.data.array[t+0]=e,this.data.array[t+1]=i,this},setXYZ:function(t,e,i,n){return t=t*this.data.stride+this.offset,this.data.array[t+0]=e,this.data.array[t+1]=i,this.data.array[t+2]=n,this},setXYZW:function(t,e,i,n,r){return t=t*this.data.stride+this.offset,this.data.array[t+0]=e,this.data.array[t+1]=i,this.data.array[t+2]=n,this.data.array[t+3]=r,this}}),Te.prototype=Object.create(j.prototype),Te.prototype.constructor=Te,Te.prototype.isSpriteMaterial=!0,Te.prototype.copy=function(t){return j.prototype.copy.call(this,t),this.color.copy(t.color),this.map=t.map,this.rotation=t.rotation,this.sizeAttenuation=t.sizeAttenuation,this};var ba;Le.prototype=Object.assign(Object.create(b.prototype),{constructor:Le,isSprite:!0,raycast:function(){function t(t,e,i,n,r,a){s.subVectors(t,i).addScalar(.5).multiply(n),void 0!==r?(h.x=a*s.x-r*s.y,h.y=r*s.x+a*s.y):h.copy(s),t.copy(e),t.x+=h.x,t.y+=h.y,t.applyMatrix4(c)}var e=new a,r=new a,o=new a,s=new i,h=new i,c=new n,l=new a,u=new a,p=new a,d=new i,f=new i,m=new i;return function(n,a){r.setFromMatrixScale(this.matrixWorld),c.getInverse(this.modelViewMatrix).premultiply(this.matrixWorld),o.setFromMatrixPosition(this.modelViewMatrix);var s=this.material.rotation;if(0!==s)var h=Math.cos(s),g=Math.sin(s);s=this.center,t(l.set(-.5,-.5,0),o,s,r,g,h),t(u.set(.5,-.5,0),o,s,r,g,h),t(p.set(.5,.5,0),o,s,r,g,h),d.set(0,0),f.set(1,0),m.set(1,1);var v=n.ray.intersectTriangle(l,u,p,!1,e);null===v&&(t(u.set(-.5,.5,0),o,s,r,g,h),f.set(0,1),v=n.ray.intersectTriangle(l,p,u,!1,e),null===v)||(g=n.ray.origin.distanceTo(e),g<n.near||g>n.far||a.push({distance:g,point:e.clone(),uv:q.getUV(e,l,u,p,d,f,m,new i),face:null,object:this}))}}(),clone:function(){return new this.constructor(this.material).copy(this)},copy:function(t){return b.prototype.copy.call(this,t),void 0!==t.center&&this.center.copy(t.center),this}}),Ee.prototype=Object.assign(Object.create(b.prototype),{constructor:Ee,copy:function(t){b.prototype.copy.call(this,t,!1),t=t.levels;for(var e=0,i=t.length;e<i;e++){var n=t[e];this.addLevel(n.object.clone(),n.distance)}return this},addLevel:function(t,e){void 0===e&&(e=0),e=Math.abs(e);for(var i=this.levels,n=0;n<i.length&&!(e<i[n].distance);n++);i.splice(n,0,{distance:e,object:t}),this.add(t)},getObjectForDistance:function(t){for(var e=this.levels,i=1,n=e.length;i<n&&!(t<e[i].distance);i++);return e[i-1].object},raycast:function(){var t=new a;return function(e,i){t.setFromMatrixPosition(this.matrixWorld);var n=e.ray.origin.distanceTo(t);this.getObjectForDistance(n).raycast(e,i)}}(),update:function(){var t=new a,e=new a;return function(i){var n=this.levels;if(1<n.length){t.setFromMatrixPosition(i.matrixWorld),e.setFromMatrixPosition(this.matrixWorld),i=t.distanceTo(e),n[0].object.visible=!0;for(var r=1,a=n.length;r<a&&i>=n[r].distance;r++)n[r-1].object.visible=!1,n[r].object.visible=!0;for(;r<a;r++)n[r].object.visible=!1}}}(),toJSON:function(t){t=b.prototype.toJSON.call(this,t),t.object.levels=[];for(var e=this.levels,i=0,n=e.length;i<n;i++){var r=e[i];t.object.levels.push({object:r.object.uuid,distance:r.distance})}return t}}),Object.assign(Ce.prototype,{calculateInverses:function(){this.boneInverses=[];for(var t=0,e=this.bones.length;t<e;t++){var i=new n;this.bones[t]&&i.getInverse(this.bones[t].matrixWorld),this.boneInverses.push(i)}},pose:function(){var t,e,i=0;for(e=this.bones.length;i<e;i++)(t=this.bones[i])&&t.matrixWorld.getInverse(this.boneInverses[i]);for(i=0,e=this.bones.length;i<e;i++)(t=this.bones[i])&&(t.parent&&t.parent.isBone?(t.matrix.getInverse(t.parent.matrixWorld),t.matrix.multiply(t.matrixWorld)):t.matrix.copy(t.matrixWorld),t.matrix.decompose(t.position,t.quaternion,t.scale))},update:function(){var t=new n,e=new n;return function(){for(var i=this.bones,n=this.boneInverses,r=this.boneMatrices,a=this.boneTexture,o=0,s=i.length;o<s;o++)t.multiplyMatrices(i[o]?i[o].matrixWorld:e,n[o]),t.toArray(r,16*o);void 0!==a&&(a.needsUpdate=!0)}}(),clone:function(){return new Ce(this.bones,this.boneInverses)},getBoneByName:function(t){for(var e=0,i=this.bones.length;e<i;e++){var n=this.bones[e];if(n.name===t)return n}}}),Pe.prototype=Object.assign(Object.create(b.prototype),{constructor:Pe,isBone:!0}),Oe.prototype=Object.assign(Object.create(Y.prototype),{constructor:Oe,isSkinnedMesh:!0,initBones:function(){var t,e=[];if(this.geometry&&void 0!==this.geometry.bones){var i=0;for(t=this.geometry.bones.length;i<t;i++){var n=this.geometry.bones[i],r=new Pe;e.push(r),r.name=n.name,r.position.fromArray(n.pos),r.quaternion.fromArray(n.rotq),void 0!==n.scl&&r.scale.fromArray(n.scl)}for(i=0,t=this.geometry.bones.length;i<t;i++)n=this.geometry.bones[i],-1!==n.parent&&null!==n.parent&&void 0!==e[n.parent]?e[n.parent].add(e[i]):this.add(e[i])}return this.updateMatrixWorld(!0),e},bind:function(t,e){this.skeleton=t,void 0===e&&(this.updateMatrixWorld(!0),this.skeleton.calculateInverses(),e=this.matrixWorld),this.bindMatrix.copy(e),this.bindMatrixInverse.getInverse(e)},pose:function(){this.skeleton.pose()},normalizeSkinWeights:function(){var t;if(this.geometry&&this.geometry.isGeometry)for(t=0;t<this.geometry.skinWeights.length;t++){var e=this.geometry.skinWeights[t],i=1/e.manhattanLength();1/0!==i?e.multiplyScalar(i):e.set(1,0,0,0)}else if(this.geometry&&this.geometry.isBufferGeometry){e=new h;var n=this.geometry.attributes.skinWeight;for(t=0;t<n.count;t++)e.x=n.getX(t),e.y=n.getY(t),e.z=n.getZ(t),e.w=n.getW(t),i=1/e.manhattanLength(),1/0!==i?e.multiplyScalar(i):e.set(1,0,0,0),n.setXYZW(t,e.x,e.y,e.z,e.w)}},updateMatrixWorld:function(t){Y.prototype.updateMatrixWorld.call(this,t),"attached"===this.bindMode?this.bindMatrixInverse.getInverse(this.matrixWorld):"detached"===this.bindMode?this.bindMatrixInverse.getInverse(this.bindMatrix):void 0},clone:function(){return new this.constructor(this.geometry,this.material).copy(this)}}),Ie.prototype=Object.create(j.prototype),Ie.prototype.constructor=Ie,Ie.prototype.isLineBasicMaterial=!0,Ie.prototype.copy=function(t){return j.prototype.copy.call(this,t),this.color.copy(t.color),this.linewidth=t.linewidth,this.linecap=t.linecap,this.linejoin=t.linejoin,this},Ne.prototype=Object.assign(Object.create(b.prototype),{constructor:Ne,isLine:!0,computeLineDistances:function(){var t=new a,e=new a;return function(){var i=this.geometry;if(i.isBufferGeometry){if(null===i.index){for(var n=i.attributes.position,r=[0],a=1,o=n.count;a<o;a++)t.fromBufferAttribute(n,a-1),e.fromBufferAttribute(n,a),r[a]=r[a-1],r[a]+=t.distanceTo(e);i.addAttribute("lineDistance",new R(r,1))}}else if(i.isGeometry)for(n=i.vertices,r=i.lineDistances,r[0]=0,a=1,o=n.length;a<o;a++)r[a]=r[a-1],r[a]+=n[a-1].distanceTo(n[a]);return this}}(),raycast:function(){var t=new n,e=new X,i=new d;return function(n,r){var o=n.linePrecision,s=this.geometry,h=this.matrixWorld;if(null===s.boundingSphere&&s.computeBoundingSphere(),i.copy(s.boundingSphere),i.applyMatrix4(h),i.radius+=o,!1!==n.ray.intersectsSphere(i)){t.getInverse(h),e.copy(n.ray).applyMatrix4(t),o/=(this.scale.x+this.scale.y+this.scale.z)/3,o*=o;var c=new a,l=new a;h=new a;var u=new a,p=this&&this.isLineSegments?2:1;if(s.isBufferGeometry){var d=s.index,f=s.attributes.position.array;if(null!==d){d=d.array,s=0;for(var m=d.length-1;s<m;s+=p){var g=d[s+1];c.fromArray(f,3*d[s]),l.fromArray(f,3*g),g=e.distanceSqToSegment(c,l,u,h),g>o||(u.applyMatrix4(this.matrixWorld),g=n.ray.origin.distanceTo(u),g<n.near||g>n.far||r.push({distance:g,point:h.clone().applyMatrix4(this.matrixWorld),index:s,face:null,faceIndex:null,object:this}))}}else for(s=0,m=f.length/3-1;s<m;s+=p)c.fromArray(f,3*s),l.fromArray(f,3*s+3),g=e.distanceSqToSegment(c,l,u,h),g>o||(u.applyMatrix4(this.matrixWorld),g=n.ray.origin.distanceTo(u),g<n.near||g>n.far||r.push({distance:g,point:h.clone().applyMatrix4(this.matrixWorld),index:s,face:null,faceIndex:null,object:this}))}else if(s.isGeometry)for(c=s.vertices,l=c.length,s=0;s<l-1;s+=p)g=e.distanceSqToSegment(c[s],c[s+1],u,h),g>o||(u.applyMatrix4(this.matrixWorld),g=n.ray.origin.distanceTo(u),g<n.near||g>n.far||r.push({distance:g,point:h.clone().applyMatrix4(this.matrixWorld),index:s,face:null,faceIndex:null,object:this}))}}}(),clone:function(){return new this.constructor(this.geometry,this.material).copy(this)}}),Re.prototype=Object.assign(Object.create(Ne.prototype),{constructor:Re,isLineSegments:!0,computeLineDistances:function(){var t=new a,e=new a;return function(){var i=this.geometry;if(i.isBufferGeometry){if(null===i.index){for(var n=i.attributes.position,r=[],a=0,o=n.count;a<o;a+=2)t.fromBufferAttribute(n,a),e.fromBufferAttribute(n,a+1),r[a]=0===a?0:r[a-1],r[a+1]=r[a]+t.distanceTo(e);i.addAttribute("lineDistance",new R(r,1))}}else if(i.isGeometry)for(n=i.vertices,r=i.lineDistances,a=0,o=n.length;a<o;a+=2)t.copy(n[a]),e.copy(n[a+1]),r[a]=0===a?0:r[a-1],r[a+1]=r[a]+t.distanceTo(e);return this}}()}),De.prototype=Object.assign(Object.create(Ne.prototype),{constructor:De,isLineLoop:!0}),Ue.prototype=Object.create(j.prototype),Ue.prototype.constructor=Ue,Ue.prototype.isPointsMaterial=!0,Ue.prototype.copy=function(t){return j.prototype.copy.call(this,t),this.color.copy(t.color),this.map=t.map,this.size=t.size,this.sizeAttenuation=t.sizeAttenuation,this.morphTargets=t.morphTargets,this},Be.prototype=Object.assign(Object.create(b.prototype),{constructor:Be,isPoints:!0,raycast:function(){var t=new n,e=new X,i=new d;return function(n,r){function o(t,i){var a=e.distanceSqToPoint(t);a<u&&(e.closestPointToPoint(t,p),p.applyMatrix4(c),t=n.ray.origin.distanceTo(p),t<n.near||t>n.far||r.push({distance:t,distanceToRay:Math.sqrt(a),point:p.clone(),index:i,face:null,object:s}))}var s=this,h=this.geometry,c=this.matrixWorld,l=n.params.Points.threshold;if(null===h.boundingSphere&&h.computeBoundingSphere(),i.copy(h.boundingSphere),i.applyMatrix4(c),i.radius+=l,!1!==n.ray.intersectsSphere(i)){t.getInverse(c),e.copy(n.ray).applyMatrix4(t),l/=(this.scale.x+this.scale.y+this.scale.z)/3;var u=l*l;l=new a;var p=new a;if(h.isBufferGeometry){var d=h.index;if(h=h.attributes.position.array,null!==d){var f=d.array;d=0;for(var m=f.length;d<m;d++){var g=f[d];l.fromArray(h,3*g),o(l,g)}}else for(d=0,f=h.length/3;d<f;d++)l.fromArray(h,3*d),o(l,d)}else for(l=h.vertices,d=0,f=l.length;d<f;d++)o(l[d],d)}}}(),clone:function(){return new this.constructor(this.geometry,this.material).copy(this)}}),ze.prototype=Object.assign(Object.create(s.prototype),{constructor:ze,isVideoTexture:!0,update:function(){var t=this.image;t.readyState>=t.HAVE_CURRENT_DATA&&(this.needsUpdate=!0)}}),Fe.prototype=Object.create(s.prototype),Fe.prototype.constructor=Fe,Fe.prototype.isCompressedTexture=!0,Ge.prototype=Object.create(s.prototype),Ge.prototype.constructor=Ge,Ge.prototype.isCanvasTexture=!0,Ve.prototype=Object.create(s.prototype),Ve.prototype.constructor=Ve,Ve.prototype.isDepthTexture=!0,ke.prototype=Object.create(z.prototype),ke.prototype.constructor=ke,je.prototype=Object.create(A.prototype),je.prototype.constructor=je,We.prototype=Object.create(z.prototype),We.prototype.constructor=We,He.prototype=Object.create(A.prototype),He.prototype.constructor=He,Xe.prototype=Object.create(z.prototype),Xe.prototype.constructor=Xe,qe.prototype=Object.create(A.prototype),qe.prototype.constructor=qe,Ye.prototype=Object.create(Xe.prototype),Ye.prototype.constructor=Ye,Ze.prototype=Object.create(A.prototype),Ze.prototype.constructor=Ze,Je.prototype=Object.create(Xe.prototype),Je.prototype.constructor=Je,Qe.prototype=Object.create(A.prototype),Qe.prototype.constructor=Qe,Ke.prototype=Object.create(Xe.prototype),Ke.prototype.constructor=Ke,$e.prototype=Object.create(A.prototype),$e.prototype.constructor=$e,ti.prototype=Object.create(Xe.prototype),ti.prototype.constructor=ti,ei.prototype=Object.create(A.prototype),ei.prototype.constructor=ei,ii.prototype=Object.create(z.prototype),ii.prototype.constructor=ii,ni.prototype=Object.create(A.prototype),ni.prototype.constructor=ni,ri.prototype=Object.create(z.prototype),ri.prototype.constructor=ri,ai.prototype=Object.create(A.prototype),ai.prototype.constructor=ai,oi.prototype=Object.create(z.prototype),oi.prototype.constructor=oi;var Ma={triangulate:function(t,e,i){i=i||2;var n=e&&e.length,r=n?e[0]*i:t.length,a=si(t,0,r,i,!0),o=[];if(!a)return o;var s;if(n){var h=i;n=[];var c,l=0;for(c=e.length;l<c;l++){var u=e[l]*h,p=l<c-1?e[l+1]*h:t.length;u=si(t,u,p,h,!1),u===u.next&&(u.steiner=!0),n.push(di(u))}for(n.sort(li),l=0;l<n.length;l++)e=n[l],h=a,(h=ui(e,h))&&(e=xi(h,e),hi(e,e.next)),a=hi(a,a.next)}if(t.length>80*i){var d=s=t[0],f=n=t[1];for(h=i;h<r;h+=i)l=t[h],e=t[h+1],l<d&&(d=l),e<f&&(f=e),l>s&&(s=l),e>n&&(n=e);s=Math.max(s-d,n-f),s=0!==s?1/s:0}return ci(a,o,i,d,f,s),o}},wa={area:function(t){for(var e=t.length,i=0,n=e-1,r=0;r<e;n=r++)i+=t[n].x*t[r].y-t[r].x*t[n].y;return.5*i},isClockWise:function(t){return 0>wa.area(t)},triangulateShape:function(t,e){var i=[],n=[],r=[];wi(t),Si(i,t);var a=t.length;for(e.forEach(wi),t=0;t<e.length;t++)n.push(a),a+=e[t].length,Si(i,e[t]);for(e=Ma.triangulate(i,n),t=0;t<e.length;t+=3)r.push(e.slice(t,t+3));return r}};Ai.prototype=Object.create(A.prototype),Ai.prototype.constructor=Ai,Ai.prototype.toJSON=function(){var t=A.prototype.toJSON.call(this);return Li(this.parameters.shapes,this.parameters.options,t)},Ti.prototype=Object.create(z.prototype),Ti.prototype.constructor=Ti,Ti.prototype.toJSON=function(){var t=z.prototype.toJSON.call(this);return Li(this.parameters.shapes,this.parameters.options,t)};var Sa={generateTopUV:function(t,e,n,r,a){t=e[3*r],r=e[3*r+1];var o=e[3*a];return a=e[3*a+1],[new i(e[3*n],e[3*n+1]),new i(t,r),new i(o,a)]},generateSideWallUV:function(t,e,n,r,a,o){t=e[3*n];var s=e[3*n+1];n=e[3*n+2];var h=e[3*r],c=e[3*r+1];r=e[3*r+2];var l=e[3*a],u=e[3*a+1];a=e[3*a+2];var p=e[3*o],d=e[3*o+1];return e=e[3*o+2],.01>Math.abs(s-c)?[new i(t,1-n),new i(h,1-r),new i(l,1-a),new i(p,1-e)]:[new i(s,1-n),new i(c,1-r),new i(u,1-a),new i(d,1-e)]}};Ei.prototype=Object.create(A.prototype),Ei.prototype.constructor=Ei,Ci.prototype=Object.create(Ti.prototype),Ci.prototype.constructor=Ci,Pi.prototype=Object.create(A.prototype),Pi.prototype.constructor=Pi,Oi.prototype=Object.create(z.prototype),Oi.prototype.constructor=Oi,Ii.prototype=Object.create(A.prototype),Ii.prototype.constructor=Ii,Ni.prototype=Object.create(z.prototype),Ni.prototype.constructor=Ni,Ri.prototype=Object.create(A.prototype),Ri.prototype.constructor=Ri,Di.prototype=Object.create(z.prototype),Di.prototype.constructor=Di,Ui.prototype=Object.create(A.prototype),Ui.prototype.constructor=Ui,Ui.prototype.toJSON=function(){var t=A.prototype.toJSON.call(this);return zi(this.parameters.shapes,t)},Bi.prototype=Object.create(z.prototype),Bi.prototype.constructor=Bi,Bi.prototype.toJSON=function(){var t=z.prototype.toJSON.call(this);return zi(this.parameters.shapes,t)},Fi.prototype=Object.create(z.prototype),Fi.prototype.constructor=Fi,Gi.prototype=Object.create(A.prototype),Gi.prototype.constructor=Gi,Vi.prototype=Object.create(z.prototype),Vi.prototype.constructor=Vi,ki.prototype=Object.create(Gi.prototype),ki.prototype.constructor=ki,ji.prototype=Object.create(Vi.prototype),ji.prototype.constructor=ji,Wi.prototype=Object.create(A.prototype),Wi.prototype.constructor=Wi,Hi.prototype=Object.create(z.prototype),Hi.prototype.constructor=Hi;var Aa=Object.freeze({WireframeGeometry:ke,ParametricGeometry:je,ParametricBufferGeometry:We,TetrahedronGeometry:qe,TetrahedronBufferGeometry:Ye,OctahedronGeometry:Ze,OctahedronBufferGeometry:Je,IcosahedronGeometry:Qe,IcosahedronBufferGeometry:Ke,DodecahedronGeometry:$e,DodecahedronBufferGeometry:ti,PolyhedronGeometry:He,PolyhedronBufferGeometry:Xe,TubeGeometry:ei,TubeBufferGeometry:ii,TorusKnotGeometry:ni,TorusKnotBufferGeometry:ri,TorusGeometry:ai,TorusBufferGeometry:oi,TextGeometry:Ei,TextBufferGeometry:Ci,SphereGeometry:Pi,SphereBufferGeometry:Oi,RingGeometry:Ii,RingBufferGeometry:Ni,PlaneGeometry:V,PlaneBufferGeometry:k,LatheGeometry:Ri,LatheBufferGeometry:Di,ShapeGeometry:Ui,ShapeBufferGeometry:Bi,ExtrudeGeometry:Ai,ExtrudeBufferGeometry:Ti,EdgesGeometry:Fi,ConeGeometry:ki,ConeBufferGeometry:ji,CylinderGeometry:Gi,CylinderBufferGeometry:Vi,CircleGeometry:Wi,CircleBufferGeometry:Hi,BoxGeometry:F,BoxBufferGeometry:G});Xi.prototype=Object.create(j.prototype),Xi.prototype.constructor=Xi,Xi.prototype.isShadowMaterial=!0,Xi.prototype.copy=function(t){return j.prototype.copy.call(this,t),this.color.copy(t.color),this},qi.prototype=Object.create(H.prototype),qi.prototype.constructor=qi,qi.prototype.isRawShaderMaterial=!0,Yi.prototype=Object.create(j.prototype),Yi.prototype.constructor=Yi,Yi.prototype.isMeshStandardMaterial=!0,Yi.prototype.copy=function(t){return j.prototype.copy.call(this,t),this.defines={STANDARD:""},this.color.copy(t.color),this.roughness=t.roughness,this.metalness=t.metalness,this.map=t.map,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.emissive.copy(t.emissive),this.emissiveMap=t.emissiveMap,this.emissiveIntensity=t.emissiveIntensity,this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.roughnessMap=t.roughnessMap,this.metalnessMap=t.metalnessMap,this.alphaMap=t.alphaMap,this.envMap=t.envMap,this.envMapIntensity=t.envMapIntensity,this.refractionRatio=t.refractionRatio,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.skinning=t.skinning,this.morphTargets=t.morphTargets,this.morphNormals=t.morphNormals,this},Zi.prototype=Object.create(Yi.prototype),Zi.prototype.constructor=Zi,Zi.prototype.isMeshPhysicalMaterial=!0,Zi.prototype.copy=function(t){return Yi.prototype.copy.call(this,t),this.defines={PHYSICAL:""},this.reflectivity=t.reflectivity,this.clearCoat=t.clearCoat,this.clearCoatRoughness=t.clearCoatRoughness,this},Ji.prototype=Object.create(j.prototype),Ji.prototype.constructor=Ji,Ji.prototype.isMeshPhongMaterial=!0,Ji.prototype.copy=function(t){return j.prototype.copy.call(this,t),this.color.copy(t.color),this.specular.copy(t.specular),this.shininess=t.shininess,this.map=t.map,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.emissive.copy(t.emissive),this.emissiveMap=t.emissiveMap,this.emissiveIntensity=t.emissiveIntensity,this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.specularMap=t.specularMap,this.alphaMap=t.alphaMap,this.envMap=t.envMap,this.combine=t.combine,this.reflectivity=t.reflectivity,this.refractionRatio=t.refractionRatio,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.skinning=t.skinning,this.morphTargets=t.morphTargets,this.morphNormals=t.morphNormals,this},Qi.prototype=Object.create(Ji.prototype),Qi.prototype.constructor=Qi,Qi.prototype.isMeshToonMaterial=!0,Qi.prototype.copy=function(t){return Ji.prototype.copy.call(this,t),this.gradientMap=t.gradientMap,this},Ki.prototype=Object.create(j.prototype),Ki.prototype.constructor=Ki,Ki.prototype.isMeshNormalMaterial=!0,Ki.prototype.copy=function(t){return j.prototype.copy.call(this,t),this.bumpMap=t.bumpMap,this.bumpScale=t.bumpScale,this.normalMap=t.normalMap,this.normalMapType=t.normalMapType,this.normalScale.copy(t.normalScale),this.displacementMap=t.displacementMap,this.displacementScale=t.displacementScale,this.displacementBias=t.displacementBias,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.skinning=t.skinning,this.morphTargets=t.morphTargets,this.morphNormals=t.morphNormals,this},$i.prototype=Object.create(j.prototype),$i.prototype.constructor=$i,$i.prototype.isMeshLambertMaterial=!0,$i.prototype.copy=function(t){return j.prototype.copy.call(this,t),this.color.copy(t.color),this.map=t.map,this.lightMap=t.lightMap,this.lightMapIntensity=t.lightMapIntensity,this.aoMap=t.aoMap,this.aoMapIntensity=t.aoMapIntensity,this.emissive.copy(t.emissive),this.emissiveMap=t.emissiveMap,this.emissiveIntensity=t.emissiveIntensity,this.specularMap=t.specularMap,this.alphaMap=t.alphaMap,this.envMap=t.envMap,this.combine=t.combine,this.reflectivity=t.reflectivity,this.refractionRatio=t.refractionRatio,this.wireframe=t.wireframe,this.wireframeLinewidth=t.wireframeLinewidth,this.wireframeLinecap=t.wireframeLinecap,this.wireframeLinejoin=t.wireframeLinejoin,this.skinning=t.skinning,
this.morphTargets=t.morphTargets,this.morphNormals=t.morphNormals,this},tn.prototype=Object.create(Ie.prototype),tn.prototype.constructor=tn,tn.prototype.isLineDashedMaterial=!0,tn.prototype.copy=function(t){return Ie.prototype.copy.call(this,t),this.scale=t.scale,this.dashSize=t.dashSize,this.gapSize=t.gapSize,this};var Ta=Object.freeze({ShadowMaterial:Xi,SpriteMaterial:Te,RawShaderMaterial:qi,ShaderMaterial:H,PointsMaterial:Ue,MeshPhysicalMaterial:Zi,MeshStandardMaterial:Yi,MeshPhongMaterial:Ji,MeshToonMaterial:Qi,MeshNormalMaterial:Ki,MeshLambertMaterial:$i,MeshDepthMaterial:ce,MeshDistanceMaterial:le,MeshBasicMaterial:W,LineDashedMaterial:tn,LineBasicMaterial:Ie,Material:j}),La={enabled:!1,files:{},add:function(t,e){!1!==this.enabled&&(this.files[t]=e)},get:function(t){if(!1!==this.enabled)return this.files[t]},remove:function(t){delete this.files[t]},clear:function(){this.files={}}},Ea=new en,Ca={};Object.assign(nn.prototype,{load:function(t,e,i,n){void 0===t&&(t=""),void 0!==this.path&&(t=this.path+t),t=this.manager.resolveURL(t);var r=this,a=La.get(t);if(void 0!==a)return r.manager.itemStart(t),setTimeout(function(){e&&e(a),r.manager.itemEnd(t)},0),a;if(void 0===Ca[t]){var o=t.match(/^data:(.*?)(;base64)?,(.*)$/);if(o){i=o[1];var s=!!o[2];o=o[3],o=window.decodeURIComponent(o),s&&(o=window.atob(o));try{var h=(this.responseType||"").toLowerCase();switch(h){case"arraybuffer":case"blob":var c=new Uint8Array(o.length);for(s=0;s<o.length;s++)c[s]=o.charCodeAt(s);var l="blob"===h?new Blob([c.buffer],{type:i}):c.buffer;break;case"document":l=(new DOMParser).parseFromString(o,i);break;case"json":l=JSON.parse(o);break;default:l=o}window.setTimeout(function(){e&&e(l),r.manager.itemEnd(t)},0)}catch(u){window.setTimeout(function(){n&&n(u),r.manager.itemEnd(t),r.manager.itemError(t)},0)}}else{Ca[t]=[],Ca[t].push({onLoad:e,onProgress:i,onError:n});var p=new XMLHttpRequest;p.open("GET",t,!0),p.addEventListener("load",function(e){var i=this.response;La.add(t,i);var n=Ca[t];if(delete Ca[t],200===this.status||0===this.status){0===this.status&&void 0;for(var a=0,o=n.length;a<o;a++){var s=n[a];s.onLoad&&s.onLoad(i)}r.manager.itemEnd(t)}else{for(a=0,o=n.length;a<o;a++)s=n[a],s.onError&&s.onError(e);r.manager.itemEnd(t),r.manager.itemError(t)}},!1),p.addEventListener("progress",function(e){for(var i=Ca[t],n=0,r=i.length;n<r;n++){var a=i[n];a.onProgress&&a.onProgress(e)}},!1),p.addEventListener("error",function(e){var i=Ca[t];delete Ca[t];for(var n=0,a=i.length;n<a;n++){var o=i[n];o.onError&&o.onError(e)}r.manager.itemEnd(t),r.manager.itemError(t)},!1),p.addEventListener("abort",function(e){var i=Ca[t];delete Ca[t];for(var n=0,a=i.length;n<a;n++){var o=i[n];o.onError&&o.onError(e)}r.manager.itemEnd(t),r.manager.itemError(t)},!1),void 0!==this.responseType&&(p.responseType=this.responseType),void 0!==this.withCredentials&&(p.withCredentials=this.withCredentials),p.overrideMimeType&&p.overrideMimeType(void 0!==this.mimeType?this.mimeType:"text/plain");for(s in this.requestHeader)p.setRequestHeader(s,this.requestHeader[s]);p.send(null)}return r.manager.itemStart(t),p}Ca[t].push({onLoad:e,onProgress:i,onError:n})},setPath:function(t){return this.path=t,this},setResponseType:function(t){return this.responseType=t,this},setWithCredentials:function(t){return this.withCredentials=t,this},setMimeType:function(t){return this.mimeType=t,this},setRequestHeader:function(t){return this.requestHeader=t,this}}),Object.assign(rn.prototype,{load:function(t,e,i,n){function r(r){h.load(t[r],function(t){t=a._parser(t,!0),o[r]={width:t.width,height:t.height,format:t.format,mipmaps:t.mipmaps},c+=1,6===c&&(1===t.mipmapCount&&(s.minFilter=1006),s.format=t.format,s.needsUpdate=!0,e&&e(s))},i,n)}var a=this,o=[],s=new Fe;s.image=o;var h=new nn(this.manager);if(h.setPath(this.path),h.setResponseType("arraybuffer"),Array.isArray(t))for(var c=0,l=0,u=t.length;l<u;++l)r(l);else h.load(t,function(t){if(t=a._parser(t,!0),t.isCubemap)for(var i=t.mipmaps.length/t.mipmapCount,n=0;n<i;n++){o[n]={mipmaps:[]};for(var r=0;r<t.mipmapCount;r++)o[n].mipmaps.push(t.mipmaps[n*t.mipmapCount+r]),o[n].format=t.format,o[n].width=t.width,o[n].height=t.height}else s.image.width=t.width,s.image.height=t.height,s.mipmaps=t.mipmaps;1===t.mipmapCount&&(s.minFilter=1006),s.format=t.format,s.needsUpdate=!0,e&&e(s)},i,n);return s},setPath:function(t){return this.path=t,this}}),Object.assign(an.prototype,{load:function(t,e,i,n){var r=this,a=new u,o=new nn(this.manager);return o.setResponseType("arraybuffer"),o.load(t,function(t){(t=r._parser(t))&&(void 0!==t.image?a.image=t.image:void 0!==t.data&&(a.image.width=t.width,a.image.height=t.height,a.image.data=t.data),a.wrapS=void 0!==t.wrapS?t.wrapS:1001,a.wrapT=void 0!==t.wrapT?t.wrapT:1001,a.magFilter=void 0!==t.magFilter?t.magFilter:1006,a.minFilter=void 0!==t.minFilter?t.minFilter:1008,a.anisotropy=void 0!==t.anisotropy?t.anisotropy:1,void 0!==t.format&&(a.format=t.format),void 0!==t.type&&(a.type=t.type),void 0!==t.mipmaps&&(a.mipmaps=t.mipmaps),1===t.mipmapCount&&(a.minFilter=1006),a.needsUpdate=!0,e&&e(a,t))},i,n),a}}),Object.assign(on.prototype,{crossOrigin:"anonymous",load:function(t,e,i,n){function r(){h.removeEventListener("load",r,!1),h.removeEventListener("error",a,!1),La.add(t,this),e&&e(this),o.manager.itemEnd(t)}function a(e){h.removeEventListener("load",r,!1),h.removeEventListener("error",a,!1),n&&n(e),o.manager.itemEnd(t),o.manager.itemError(t)}void 0===t&&(t=""),void 0!==this.path&&(t=this.path+t),t=this.manager.resolveURL(t);var o=this,s=La.get(t);if(void 0!==s)return o.manager.itemStart(t),setTimeout(function(){e&&e(s),o.manager.itemEnd(t)},0),s;var h=document.createElementNS("http://www.w3.org/1999/xhtml","img");return h.addEventListener("load",r,!1),h.addEventListener("error",a,!1),"data:"!==t.substr(0,5)&&void 0!==this.crossOrigin&&(h.crossOrigin=this.crossOrigin),o.manager.itemStart(t),h.src=t,h},setCrossOrigin:function(t){return this.crossOrigin=t,this},setPath:function(t){return this.path=t,this}}),Object.assign(sn.prototype,{crossOrigin:"anonymous",load:function(t,e,i,n){function r(i){o.load(t[i],function(t){a.images[i]=t,s++,6===s&&(a.needsUpdate=!0,e&&e(a))},void 0,n)}var a=new ot,o=new on(this.manager);o.setCrossOrigin(this.crossOrigin),o.setPath(this.path);var s=0;for(i=0;i<t.length;++i)r(i);return a},setCrossOrigin:function(t){return this.crossOrigin=t,this},setPath:function(t){return this.path=t,this}}),Object.assign(hn.prototype,{crossOrigin:"anonymous",load:function(t,e,i,n){var r=new s,a=new on(this.manager);return a.setCrossOrigin(this.crossOrigin),a.setPath(this.path),a.load(t,function(i){r.image=i,i=0<t.search(/\.jpe?g$/i)||0===t.search(/^data:image\/jpeg/),r.format=i?1022:1023,r.needsUpdate=!0,void 0!==e&&e(r)},i,n),r},setCrossOrigin:function(t){return this.crossOrigin=t,this},setPath:function(t){return this.path=t,this}}),Object.assign(cn.prototype,{getPoint:function(){return null},getPointAt:function(t,e){return t=this.getUtoTmapping(t),this.getPoint(t,e)},getPoints:function(t){void 0===t&&(t=5);for(var e=[],i=0;i<=t;i++)e.push(this.getPoint(i/t));return e},getSpacedPoints:function(t){void 0===t&&(t=5);for(var e=[],i=0;i<=t;i++)e.push(this.getPointAt(i/t));return e},getLength:function(){var t=this.getLengths();return t[t.length-1]},getLengths:function(t){if(void 0===t&&(t=this.arcLengthDivisions),this.cacheArcLengths&&this.cacheArcLengths.length===t+1&&!this.needsUpdate)return this.cacheArcLengths;this.needsUpdate=!1;var e,i=[],n=this.getPoint(0),r=0;for(i.push(0),e=1;e<=t;e++){var a=this.getPoint(e/t);r+=a.distanceTo(n),i.push(r),n=a}return this.cacheArcLengths=i},updateArcLengths:function(){this.needsUpdate=!0,this.getLengths()},getUtoTmapping:function(t,e){var i=this.getLengths(),n=i.length;e=e?e:t*i[n-1];for(var r,a=0,o=n-1;a<=o;)if(t=Math.floor(a+(o-a)/2),r=i[t]-e,0>r)a=t+1;else{if(!(0<r)){o=t;break}o=t-1}return t=o,i[t]===e?t/(n-1):(a=i[t],(t+(e-a)/(i[t+1]-a))/(n-1))},getTangent:function(t){var e=t-1e-4;return t+=1e-4,0>e&&(e=0),1<t&&(t=1),e=this.getPoint(e),this.getPoint(t).clone().sub(e).normalize()},getTangentAt:function(t){return t=this.getUtoTmapping(t),this.getTangent(t)},computeFrenetFrames:function(t,e){var i,r=new a,o=[],s=[],h=[],c=new a,l=new n;for(i=0;i<=t;i++){var u=i/t;o[i]=this.getTangentAt(u),o[i].normalize()}s[0]=new a,h[0]=new a,i=Number.MAX_VALUE,u=Math.abs(o[0].x);var p=Math.abs(o[0].y),d=Math.abs(o[0].z);for(u<=i&&(i=u,r.set(1,0,0)),p<=i&&(i=p,r.set(0,1,0)),d<=i&&r.set(0,0,1),c.crossVectors(o[0],r).normalize(),s[0].crossVectors(o[0],c),h[0].crossVectors(o[0],s[0]),i=1;i<=t;i++)s[i]=s[i-1].clone(),h[i]=h[i-1].clone(),c.crossVectors(o[i-1],o[i]),c.length()>Number.EPSILON&&(c.normalize(),r=Math.acos($r.clamp(o[i-1].dot(o[i]),-1,1)),s[i].applyMatrix4(l.makeRotationAxis(c,r))),h[i].crossVectors(o[i],s[i]);if(!0===e)for(r=Math.acos($r.clamp(s[0].dot(s[t]),-1,1)),r/=t,0<o[0].dot(c.crossVectors(s[0],s[t]))&&(r=-r),i=1;i<=t;i++)s[i].applyMatrix4(l.makeRotationAxis(o[i],r*i)),h[i].crossVectors(o[i],s[i]);return{tangents:o,normals:s,binormals:h}},clone:function(){return(new this.constructor).copy(this)},copy:function(t){return this.arcLengthDivisions=t.arcLengthDivisions,this},toJSON:function(){var t={metadata:{version:4.5,type:"Curve",generator:"Curve.toJSON"}};return t.arcLengthDivisions=this.arcLengthDivisions,t.type=this.type,t},fromJSON:function(t){return this.arcLengthDivisions=t.arcLengthDivisions,this}}),ln.prototype=Object.create(cn.prototype),ln.prototype.constructor=ln,ln.prototype.isEllipseCurve=!0,ln.prototype.getPoint=function(t,e){e=e||new i;for(var n=2*Math.PI,r=this.aEndAngle-this.aStartAngle,a=Math.abs(r)<Number.EPSILON;0>r;)r+=n;for(;r>n;)r-=n;r<Number.EPSILON&&(r=a?0:n),!0!==this.aClockwise||a||(r=r===n?-n:r-n),n=this.aStartAngle+t*r,t=this.aX+this.xRadius*Math.cos(n);var o=this.aY+this.yRadius*Math.sin(n);return 0!==this.aRotation&&(n=Math.cos(this.aRotation),r=Math.sin(this.aRotation),a=t-this.aX,o-=this.aY,t=a*n-o*r+this.aX,o=a*r+o*n+this.aY),e.set(t,o)},ln.prototype.copy=function(t){return cn.prototype.copy.call(this,t),this.aX=t.aX,this.aY=t.aY,this.xRadius=t.xRadius,this.yRadius=t.yRadius,this.aStartAngle=t.aStartAngle,this.aEndAngle=t.aEndAngle,this.aClockwise=t.aClockwise,this.aRotation=t.aRotation,this},ln.prototype.toJSON=function(){var t=cn.prototype.toJSON.call(this);return t.aX=this.aX,t.aY=this.aY,t.xRadius=this.xRadius,t.yRadius=this.yRadius,t.aStartAngle=this.aStartAngle,t.aEndAngle=this.aEndAngle,t.aClockwise=this.aClockwise,t.aRotation=this.aRotation,t},ln.prototype.fromJSON=function(t){return cn.prototype.fromJSON.call(this,t),this.aX=t.aX,this.aY=t.aY,this.xRadius=t.xRadius,this.yRadius=t.yRadius,this.aStartAngle=t.aStartAngle,this.aEndAngle=t.aEndAngle,this.aClockwise=t.aClockwise,this.aRotation=t.aRotation,this},un.prototype=Object.create(ln.prototype),un.prototype.constructor=un,un.prototype.isArcCurve=!0;var Pa=new a,Oa=new pn,Ia=new pn,Na=new pn;dn.prototype=Object.create(cn.prototype),dn.prototype.constructor=dn,dn.prototype.isCatmullRomCurve3=!0,dn.prototype.getPoint=function(t,e){e=e||new a;var i=this.points,n=i.length;t*=n-(this.closed?0:1);var r=Math.floor(t);if(t-=r,this.closed?r+=0<r?0:(Math.floor(Math.abs(r)/n)+1)*n:0===t&&r===n-1&&(r=n-2,t=1),this.closed||0<r)var o=i[(r-1)%n];else Pa.subVectors(i[0],i[1]).add(i[0]),o=Pa;var s=i[r%n],h=i[(r+1)%n];if(this.closed||r+2<n?i=i[(r+2)%n]:(Pa.subVectors(i[n-1],i[n-2]).add(i[n-1]),i=Pa),"centripetal"===this.curveType||"chordal"===this.curveType){var c="chordal"===this.curveType?.5:.25;n=Math.pow(o.distanceToSquared(s),c),r=Math.pow(s.distanceToSquared(h),c),c=Math.pow(h.distanceToSquared(i),c),1e-4>r&&(r=1),1e-4>n&&(n=r),1e-4>c&&(c=r),Oa.initNonuniformCatmullRom(o.x,s.x,h.x,i.x,n,r,c),Ia.initNonuniformCatmullRom(o.y,s.y,h.y,i.y,n,r,c),Na.initNonuniformCatmullRom(o.z,s.z,h.z,i.z,n,r,c)}else"catmullrom"===this.curveType&&(Oa.initCatmullRom(o.x,s.x,h.x,i.x,this.tension),Ia.initCatmullRom(o.y,s.y,h.y,i.y,this.tension),Na.initCatmullRom(o.z,s.z,h.z,i.z,this.tension));return e.set(Oa.calc(t),Ia.calc(t),Na.calc(t)),e},dn.prototype.copy=function(t){cn.prototype.copy.call(this,t),this.points=[];for(var e=0,i=t.points.length;e<i;e++)this.points.push(t.points[e].clone());return this.closed=t.closed,this.curveType=t.curveType,this.tension=t.tension,this},dn.prototype.toJSON=function(){var t=cn.prototype.toJSON.call(this);t.points=[];for(var e=0,i=this.points.length;e<i;e++)t.points.push(this.points[e].toArray());return t.closed=this.closed,t.curveType=this.curveType,t.tension=this.tension,t},dn.prototype.fromJSON=function(t){cn.prototype.fromJSON.call(this,t),this.points=[];for(var e=0,i=t.points.length;e<i;e++){var n=t.points[e];this.points.push((new a).fromArray(n))}return this.closed=t.closed,this.curveType=t.curveType,this.tension=t.tension,this},vn.prototype=Object.create(cn.prototype),vn.prototype.constructor=vn,vn.prototype.isCubicBezierCurve=!0,vn.prototype.getPoint=function(t,e){e=e||new i;var n=this.v0,r=this.v1,a=this.v2,o=this.v3;return e.set(gn(t,n.x,r.x,a.x,o.x),gn(t,n.y,r.y,a.y,o.y)),e},vn.prototype.copy=function(t){return cn.prototype.copy.call(this,t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this.v3.copy(t.v3),this},vn.prototype.toJSON=function(){var t=cn.prototype.toJSON.call(this);return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t.v3=this.v3.toArray(),t},vn.prototype.fromJSON=function(t){return cn.prototype.fromJSON.call(this,t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this.v3.fromArray(t.v3),this},yn.prototype=Object.create(cn.prototype),yn.prototype.constructor=yn,yn.prototype.isCubicBezierCurve3=!0,yn.prototype.getPoint=function(t,e){e=e||new a;var i=this.v0,n=this.v1,r=this.v2,o=this.v3;return e.set(gn(t,i.x,n.x,r.x,o.x),gn(t,i.y,n.y,r.y,o.y),gn(t,i.z,n.z,r.z,o.z)),e},yn.prototype.copy=function(t){return cn.prototype.copy.call(this,t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this.v3.copy(t.v3),this},yn.prototype.toJSON=function(){var t=cn.prototype.toJSON.call(this);return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t.v3=this.v3.toArray(),t},yn.prototype.fromJSON=function(t){return cn.prototype.fromJSON.call(this,t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this.v3.fromArray(t.v3),this},xn.prototype=Object.create(cn.prototype),xn.prototype.constructor=xn,xn.prototype.isLineCurve=!0,xn.prototype.getPoint=function(t,e){return e=e||new i,1===t?e.copy(this.v2):(e.copy(this.v2).sub(this.v1),e.multiplyScalar(t).add(this.v1)),e},xn.prototype.getPointAt=function(t,e){return this.getPoint(t,e)},xn.prototype.getTangent=function(){return this.v2.clone().sub(this.v1).normalize()},xn.prototype.copy=function(t){return cn.prototype.copy.call(this,t),this.v1.copy(t.v1),this.v2.copy(t.v2),this},xn.prototype.toJSON=function(){var t=cn.prototype.toJSON.call(this);return t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t},xn.prototype.fromJSON=function(t){return cn.prototype.fromJSON.call(this,t),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this},_n.prototype=Object.create(cn.prototype),_n.prototype.constructor=_n,_n.prototype.isLineCurve3=!0,_n.prototype.getPoint=function(t,e){return e=e||new a,1===t?e.copy(this.v2):(e.copy(this.v2).sub(this.v1),e.multiplyScalar(t).add(this.v1)),e},_n.prototype.getPointAt=function(t,e){return this.getPoint(t,e)},_n.prototype.copy=function(t){return cn.prototype.copy.call(this,t),this.v1.copy(t.v1),this.v2.copy(t.v2),this},_n.prototype.toJSON=function(){var t=cn.prototype.toJSON.call(this);return t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t},_n.prototype.fromJSON=function(t){return cn.prototype.fromJSON.call(this,t),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this},bn.prototype=Object.create(cn.prototype),bn.prototype.constructor=bn,bn.prototype.isQuadraticBezierCurve=!0,bn.prototype.getPoint=function(t,e){e=e||new i;var n=this.v0,r=this.v1,a=this.v2;return e.set(mn(t,n.x,r.x,a.x),mn(t,n.y,r.y,a.y)),e},bn.prototype.copy=function(t){return cn.prototype.copy.call(this,t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this},bn.prototype.toJSON=function(){var t=cn.prototype.toJSON.call(this);return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t},bn.prototype.fromJSON=function(t){return cn.prototype.fromJSON.call(this,t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this},Mn.prototype=Object.create(cn.prototype),Mn.prototype.constructor=Mn,Mn.prototype.isQuadraticBezierCurve3=!0,Mn.prototype.getPoint=function(t,e){e=e||new a;var i=this.v0,n=this.v1,r=this.v2;return e.set(mn(t,i.x,n.x,r.x),mn(t,i.y,n.y,r.y),mn(t,i.z,n.z,r.z)),e},Mn.prototype.copy=function(t){return cn.prototype.copy.call(this,t),this.v0.copy(t.v0),this.v1.copy(t.v1),this.v2.copy(t.v2),this},Mn.prototype.toJSON=function(){var t=cn.prototype.toJSON.call(this);return t.v0=this.v0.toArray(),t.v1=this.v1.toArray(),t.v2=this.v2.toArray(),t},Mn.prototype.fromJSON=function(t){return cn.prototype.fromJSON.call(this,t),this.v0.fromArray(t.v0),this.v1.fromArray(t.v1),this.v2.fromArray(t.v2),this},wn.prototype=Object.create(cn.prototype),wn.prototype.constructor=wn,wn.prototype.isSplineCurve=!0,wn.prototype.getPoint=function(t,e){e=e||new i;var n=this.points,r=(n.length-1)*t;t=Math.floor(r),r-=t;var a=n[0===t?t:t-1],o=n[t],s=n[t>n.length-2?n.length-1:t+1];return n=n[t>n.length-3?n.length-1:t+2],e.set(fn(r,a.x,o.x,s.x,n.x),fn(r,a.y,o.y,s.y,n.y)),e},wn.prototype.copy=function(t){cn.prototype.copy.call(this,t),this.points=[];for(var e=0,i=t.points.length;e<i;e++)this.points.push(t.points[e].clone());return this},wn.prototype.toJSON=function(){var t=cn.prototype.toJSON.call(this);t.points=[];for(var e=0,i=this.points.length;e<i;e++)t.points.push(this.points[e].toArray());return t},wn.prototype.fromJSON=function(t){cn.prototype.fromJSON.call(this,t),this.points=[];for(var e=0,n=t.points.length;e<n;e++){var r=t.points[e];this.points.push((new i).fromArray(r))}return this};var Ra=Object.freeze({ArcCurve:un,CatmullRomCurve3:dn,CubicBezierCurve:vn,CubicBezierCurve3:yn,EllipseCurve:ln,LineCurve:xn,LineCurve3:_n,QuadraticBezierCurve:bn,QuadraticBezierCurve3:Mn,SplineCurve:wn});Sn.prototype=Object.assign(Object.create(cn.prototype),{constructor:Sn,add:function(t){this.curves.push(t)},closePath:function(){var t=this.curves[0].getPoint(0),e=this.curves[this.curves.length-1].getPoint(1);t.equals(e)||this.curves.push(new xn(e,t))},getPoint:function(t){var e=t*this.getLength(),i=this.getCurveLengths();for(t=0;t<i.length;){if(i[t]>=e)return e=i[t]-e,t=this.curves[t],i=t.getLength(),t.getPointAt(0===i?0:1-e/i);t++}return null},getLength:function(){var t=this.getCurveLengths();return t[t.length-1]},updateArcLengths:function(){this.needsUpdate=!0,this.cacheLengths=null,this.getCurveLengths()},getCurveLengths:function(){if(this.cacheLengths&&this.cacheLengths.length===this.curves.length)return this.cacheLengths;for(var t=[],e=0,i=0,n=this.curves.length;i<n;i++)e+=this.curves[i].getLength(),t.push(e);return this.cacheLengths=t},getSpacedPoints:function(t){void 0===t&&(t=40);for(var e=[],i=0;i<=t;i++)e.push(this.getPoint(i/t));return this.autoClose&&e.push(e[0]),e},getPoints:function(t){t=t||12;for(var e,i=[],n=0,r=this.curves;n<r.length;n++){var a=r[n];a=a.getPoints(a&&a.isEllipseCurve?2*t:a&&(a.isLineCurve||a.isLineCurve3)?1:a&&a.isSplineCurve?t*a.points.length:t);for(var o=0;o<a.length;o++){var s=a[o];e&&e.equals(s)||(i.push(s),e=s)}}return this.autoClose&&1<i.length&&!i[i.length-1].equals(i[0])&&i.push(i[0]),i},copy:function(t){cn.prototype.copy.call(this,t),this.curves=[];for(var e=0,i=t.curves.length;e<i;e++)this.curves.push(t.curves[e].clone());return this.autoClose=t.autoClose,this},toJSON:function(){var t=cn.prototype.toJSON.call(this);t.autoClose=this.autoClose,t.curves=[];for(var e=0,i=this.curves.length;e<i;e++)t.curves.push(this.curves[e].toJSON());return t},fromJSON:function(t){cn.prototype.fromJSON.call(this,t),this.autoClose=t.autoClose,this.curves=[];for(var e=0,i=t.curves.length;e<i;e++){var n=t.curves[e];this.curves.push((new Ra[n.type]).fromJSON(n))}return this}}),An.prototype=Object.assign(Object.create(Sn.prototype),{constructor:An,setFromPoints:function(t){this.moveTo(t[0].x,t[0].y);for(var e=1,i=t.length;e<i;e++)this.lineTo(t[e].x,t[e].y)},moveTo:function(t,e){this.currentPoint.set(t,e)},lineTo:function(t,e){var n=new xn(this.currentPoint.clone(),new i(t,e));this.curves.push(n),this.currentPoint.set(t,e)},quadraticCurveTo:function(t,e,n,r){t=new bn(this.currentPoint.clone(),new i(t,e),new i(n,r)),this.curves.push(t),this.currentPoint.set(n,r)},bezierCurveTo:function(t,e,n,r,a,o){t=new vn(this.currentPoint.clone(),new i(t,e),new i(n,r),new i(a,o)),this.curves.push(t),this.currentPoint.set(a,o)},splineThru:function(t){var e=[this.currentPoint.clone()].concat(t);e=new wn(e),this.curves.push(e),this.currentPoint.copy(t[t.length-1])},arc:function(t,e,i,n,r,a){this.absarc(t+this.currentPoint.x,e+this.currentPoint.y,i,n,r,a)},absarc:function(t,e,i,n,r,a){this.absellipse(t,e,i,i,n,r,a)},ellipse:function(t,e,i,n,r,a,o,s){this.absellipse(t+this.currentPoint.x,e+this.currentPoint.y,i,n,r,a,o,s)},absellipse:function(t,e,i,n,r,a,o,s){t=new ln(t,e,i,n,r,a,o,s),0<this.curves.length&&(e=t.getPoint(0),e.equals(this.currentPoint)||this.lineTo(e.x,e.y)),this.curves.push(t),t=t.getPoint(1),this.currentPoint.copy(t)},copy:function(t){return Sn.prototype.copy.call(this,t),this.currentPoint.copy(t.currentPoint),this},toJSON:function(){var t=Sn.prototype.toJSON.call(this);return t.currentPoint=this.currentPoint.toArray(),t},fromJSON:function(t){return Sn.prototype.fromJSON.call(this,t),this.currentPoint.fromArray(t.currentPoint),this}}),Tn.prototype=Object.assign(Object.create(An.prototype),{constructor:Tn,getPointsHoles:function(t){for(var e=[],i=0,n=this.holes.length;i<n;i++)e[i]=this.holes[i].getPoints(t);return e},extractPoints:function(t){return{shape:this.getPoints(t),holes:this.getPointsHoles(t)}},copy:function(t){An.prototype.copy.call(this,t),this.holes=[];for(var e=0,i=t.holes.length;e<i;e++)this.holes.push(t.holes[e].clone());return this},toJSON:function(){var t=An.prototype.toJSON.call(this);t.uuid=this.uuid,t.holes=[];for(var e=0,i=this.holes.length;e<i;e++)t.holes.push(this.holes[e].toJSON());return t},fromJSON:function(t){An.prototype.fromJSON.call(this,t),this.uuid=t.uuid,this.holes=[];for(var e=0,i=t.holes.length;e<i;e++){var n=t.holes[e];this.holes.push((new An).fromJSON(n))}return this}}),Ln.prototype=Object.assign(Object.create(b.prototype),{constructor:Ln,isLight:!0,copy:function(t){return b.prototype.copy.call(this,t),this.color.copy(t.color),this.intensity=t.intensity,this},toJSON:function(t){return t=b.prototype.toJSON.call(this,t),t.object.color=this.color.getHex(),t.object.intensity=this.intensity,void 0!==this.groundColor&&(t.object.groundColor=this.groundColor.getHex()),void 0!==this.distance&&(t.object.distance=this.distance),void 0!==this.angle&&(t.object.angle=this.angle),void 0!==this.decay&&(t.object.decay=this.decay),void 0!==this.penumbra&&(t.object.penumbra=this.penumbra),void 0!==this.shadow&&(t.object.shadow=this.shadow.toJSON()),t}}),En.prototype=Object.assign(Object.create(Ln.prototype),{constructor:En,isHemisphereLight:!0,copy:function(t){return Ln.prototype.copy.call(this,t),this.groundColor.copy(t.groundColor),this}}),Object.assign(Cn.prototype,{copy:function(t){return this.camera=t.camera.clone(),this.bias=t.bias,this.radius=t.radius,this.mapSize.copy(t.mapSize),this},clone:function(){return(new this.constructor).copy(this)},toJSON:function(){var t={};return 0!==this.bias&&(t.bias=this.bias),1!==this.radius&&(t.radius=this.radius),512===this.mapSize.x&&512===this.mapSize.y||(t.mapSize=this.mapSize.toArray()),t.camera=this.camera.toJSON(!1).object,delete t.camera.matrix,t}}),Pn.prototype=Object.assign(Object.create(Cn.prototype),{constructor:Pn,isSpotLightShadow:!0,update:function(t){var e=this.camera,i=2*$r.RAD2DEG*t.angle,n=this.mapSize.width/this.mapSize.height;t=t.distance||e.far,i===e.fov&&n===e.aspect&&t===e.far||(e.fov=i,e.aspect=n,e.far=t,e.updateProjectionMatrix())}}),On.prototype=Object.assign(Object.create(Ln.prototype),{constructor:On,isSpotLight:!0,copy:function(t){return Ln.prototype.copy.call(this,t),this.distance=t.distance,this.angle=t.angle,this.penumbra=t.penumbra,this.decay=t.decay,this.target=t.target.clone(),this.shadow=t.shadow.clone(),this}}),In.prototype=Object.assign(Object.create(Ln.prototype),{constructor:In,isPointLight:!0,copy:function(t){return Ln.prototype.copy.call(this,t),this.distance=t.distance,this.decay=t.decay,this.shadow=t.shadow.clone(),this}}),Nn.prototype=Object.assign(Object.create(Cn.prototype),{constructor:Nn}),Rn.prototype=Object.assign(Object.create(Ln.prototype),{constructor:Rn,isDirectionalLight:!0,copy:function(t){return Ln.prototype.copy.call(this,t),this.target=t.target.clone(),this.shadow=t.shadow.clone(),this}}),Dn.prototype=Object.assign(Object.create(Ln.prototype),{constructor:Dn,isAmbientLight:!0}),Un.prototype=Object.assign(Object.create(Ln.prototype),{constructor:Un,isRectAreaLight:!0,copy:function(t){return Ln.prototype.copy.call(this,t),this.width=t.width,this.height=t.height,this},toJSON:function(t){return t=Ln.prototype.toJSON.call(this,t),t.object.width=this.width,t.object.height=this.height,t}});var Da={arraySlice:function(t,e,i){return Da.isTypedArray(t)?new t.constructor(t.subarray(e,void 0!==i?i:t.length)):t.slice(e,i)},convertArray:function(t,e,i){return!t||!i&&t.constructor===e?t:"number"==typeof e.BYTES_PER_ELEMENT?new e(t):Array.prototype.slice.call(t)},isTypedArray:function(t){return ArrayBuffer.isView(t)&&!(t instanceof DataView)},getKeyframeOrder:function(t){for(var e=t.length,i=Array(e),n=0;n!==e;++n)i[n]=n;return i.sort(function(e,i){return t[e]-t[i]}),i},sortedArray:function(t,e,i){for(var n=t.length,r=new t.constructor(n),a=0,o=0;o!==n;++a)for(var s=i[a]*e,h=0;h!==e;++h)r[o++]=t[s+h];return r},flattenJSON:function(t,e,i,n){for(var r=1,a=t[0];void 0!==a&&void 0===a[n];)a=t[r++];if(void 0!==a){var o=a[n];if(void 0!==o)if(Array.isArray(o)){do o=a[n],void 0!==o&&(e.push(a.time),i.push.apply(i,o)),a=t[r++];while(void 0!==a)}else if(void 0!==o.toArray){do o=a[n],void 0!==o&&(e.push(a.time),o.toArray(i,i.length)),a=t[r++];while(void 0!==a)}else do o=a[n],void 0!==o&&(e.push(a.time),i.push(o)),a=t[r++];while(void 0!==a)}}};Object.assign(Bn.prototype,{evaluate:function(t){var e=this.parameterPositions,i=this._cachedIndex,n=e[i],r=e[i-1];t:{e:{i:{n:if(!(t<n)){for(var a=i+2;;){if(void 0===n){if(t<r)break n;return this._cachedIndex=i=e.length,this.afterEnd_(i-1,t,r)}if(i===a)break;if(r=n,n=e[++i],t<n)break e}n=e.length;break i}if(t>=r)break t;for(a=e[1],t<a&&(i=2,r=a),a=i-2;;){if(void 0===r)return this._cachedIndex=0,this.beforeStart_(0,t,n);if(i===a)break;if(n=r,r=e[--i-1],t>=r)break e}n=i,i=0}for(;i<n;)r=i+n>>>1,t<e[r]?n=r:i=r+1;if(n=e[i],r=e[i-1],void 0===r)return this._cachedIndex=0,this.beforeStart_(0,t,n);if(void 0===n)return this._cachedIndex=i=e.length,this.afterEnd_(i-1,r,t)}this._cachedIndex=i,this.intervalChanged_(i,r,n)}return this.interpolate_(i,r,t,n)},settings:null,DefaultSettings_:{},getSettings_:function(){return this.settings||this.DefaultSettings_},copySampleValue_:function(t){var e=this.resultBuffer,i=this.sampleValues,n=this.valueSize;t*=n;for(var r=0;r!==n;++r)e[r]=i[t+r];return e},interpolate_:function(){throw Error("call to abstract method")},intervalChanged_:function(){}}),Object.assign(Bn.prototype,{beforeStart_:Bn.prototype.copySampleValue_,afterEnd_:Bn.prototype.copySampleValue_}),zn.prototype=Object.assign(Object.create(Bn.prototype),{constructor:zn,DefaultSettings_:{endingStart:2400,endingEnd:2400},intervalChanged_:function(t,e,i){var n=this.parameterPositions,r=t-2,a=t+1,o=n[r],s=n[a];if(void 0===o)switch(this.getSettings_().endingStart){case 2401:r=t,o=2*e-i;break;case 2402:r=n.length-2,o=e+n[r]-n[r+1];break;default:r=t,o=i}if(void 0===s)switch(this.getSettings_().endingEnd){case 2401:a=t,s=2*i-e;break;case 2402:a=1,s=i+n[1]-n[0];break;default:a=t-1,s=e}t=.5*(i-e),n=this.valueSize,this._weightPrev=t/(e-o),this._weightNext=t/(s-i),this._offsetPrev=r*n,this._offsetNext=a*n},interpolate_:function(t,e,i,n){var r=this.resultBuffer,a=this.sampleValues,o=this.valueSize;t*=o;var s=t-o,h=this._offsetPrev,c=this._offsetNext,l=this._weightPrev,u=this._weightNext,p=(i-e)/(n-e);for(i=p*p,n=i*p,e=-l*n+2*l*i-l*p,l=(1+l)*n+(-1.5-2*l)*i+(-.5+l)*p+1,p=(-1-u)*n+(1.5+u)*i+.5*p,u=u*n-u*i,i=0;i!==o;++i)r[i]=e*a[h+i]+l*a[s+i]+p*a[t+i]+u*a[c+i];return r}}),Fn.prototype=Object.assign(Object.create(Bn.prototype),{constructor:Fn,interpolate_:function(t,e,i,n){var r=this.resultBuffer,a=this.sampleValues,o=this.valueSize;t*=o;var s=t-o;for(e=(i-e)/(n-e),i=1-e,n=0;n!==o;++n)r[n]=a[s+n]*i+a[t+n]*e;return r}}),Gn.prototype=Object.assign(Object.create(Bn.prototype),{constructor:Gn,interpolate_:function(t){return this.copySampleValue_(t-1)}}),Object.assign(Vn,{toJSON:function(t){var e=t.constructor;if(void 0!==e.toJSON)e=e.toJSON(t);else{e={name:t.name,times:Da.convertArray(t.times,Array),values:Da.convertArray(t.values,Array)};var i=t.getInterpolation();i!==t.DefaultInterpolation&&(e.interpolation=i)}return e.type=t.ValueTypeName,e}}),Object.assign(Vn.prototype,{constructor:Vn,TimeBufferType:Float32Array,ValueBufferType:Float32Array,DefaultInterpolation:2301,InterpolantFactoryMethodDiscrete:function(t){return new Gn(this.times,this.values,this.getValueSize(),t)},InterpolantFactoryMethodLinear:function(t){return new Fn(this.times,this.values,this.getValueSize(),t)},InterpolantFactoryMethodSmooth:function(t){return new zn(this.times,this.values,this.getValueSize(),t)},setInterpolation:function(t){switch(t){case 2300:var e=this.InterpolantFactoryMethodDiscrete;break;case 2301:e=this.InterpolantFactoryMethodLinear;break;case 2302:e=this.InterpolantFactoryMethodSmooth}if(void 0===e){if(e="unsupported interpolation for "+this.ValueTypeName+" keyframe track named "+this.name,void 0===this.createInterpolant){if(t===this.DefaultInterpolation)throw Error(e);this.setInterpolation(this.DefaultInterpolation)}return this}return this.createInterpolant=e,this},getInterpolation:function(){switch(this.createInterpolant){case this.InterpolantFactoryMethodDiscrete:return 2300;case this.InterpolantFactoryMethodLinear:return 2301;case this.InterpolantFactoryMethodSmooth:return 2302}},getValueSize:function(){return this.values.length/this.times.length},shift:function(t){if(0!==t)for(var e=this.times,i=0,n=e.length;i!==n;++i)e[i]+=t;return this},scale:function(t){if(1!==t)for(var e=this.times,i=0,n=e.length;i!==n;++i)e[i]*=t;return this},trim:function(t,e){for(var i=this.times,n=i.length,r=0,a=n-1;r!==n&&i[r]<t;)++r;for(;-1!==a&&i[a]>e;)--a;return++a,0===r&&a===n||(r>=a&&(a=Math.max(a,1),r=a-1),t=this.getValueSize(),this.times=Da.arraySlice(i,r,a),this.values=Da.arraySlice(this.values,r*t,a*t)),this},validate:function(){var t=!0,e=this.getValueSize();0!==e-Math.floor(e)&&(t=!1);var i=this.times;e=this.values;var n=i.length;0===n&&(t=!1);for(var r=null,a=0;a!==n;a++){var o=i[a];if("number"==typeof o&&isNaN(o)){t=!1;break}if(null!==r&&r>o){t=!1;break}r=o}if(void 0!==e&&Da.isTypedArray(e))for(a=0,i=e.length;a!==i;++a)if(n=e[a],isNaN(n)){t=!1;break}return t},optimize:function(){for(var t=this.times,e=this.values,i=this.getValueSize(),n=2302===this.getInterpolation(),r=1,a=t.length-1,o=1;o<a;++o){var s=!1,h=t[o];if(h!==t[o+1]&&(1!==o||h!==h[0]))if(n)s=!0;else{var c=o*i,l=c-i,u=c+i;for(h=0;h!==i;++h){var p=e[c+h];if(p!==e[l+h]||p!==e[u+h]){s=!0;break}}}if(s){if(o!==r)for(t[r]=t[o],s=o*i,c=r*i,h=0;h!==i;++h)e[c+h]=e[s+h];++r}}if(0<a){for(t[r]=t[a],s=a*i,c=r*i,h=0;h!==i;++h)e[c+h]=e[s+h];++r}return r!==t.length&&(this.times=Da.arraySlice(t,0,r),this.values=Da.arraySlice(e,0,r*i)),this}}),kn.prototype=Object.assign(Object.create(Vn.prototype),{constructor:kn,ValueTypeName:"bool",ValueBufferType:Array,DefaultInterpolation:2300,
InterpolantFactoryMethodLinear:void 0,InterpolantFactoryMethodSmooth:void 0}),jn.prototype=Object.assign(Object.create(Vn.prototype),{constructor:jn,ValueTypeName:"color"}),Wn.prototype=Object.assign(Object.create(Vn.prototype),{constructor:Wn,ValueTypeName:"number"}),Hn.prototype=Object.assign(Object.create(Bn.prototype),{constructor:Hn,interpolate_:function(t,e,i,n){var a=this.resultBuffer,o=this.sampleValues,s=this.valueSize;for(t*=s,e=(i-e)/(n-e),i=t+s;t!==i;t+=4)r.slerpFlat(a,0,o,t-s,o,t,e);return a}}),Xn.prototype=Object.assign(Object.create(Vn.prototype),{constructor:Xn,ValueTypeName:"quaternion",DefaultInterpolation:2301,InterpolantFactoryMethodLinear:function(t){return new Hn(this.times,this.values,this.getValueSize(),t)},InterpolantFactoryMethodSmooth:void 0}),qn.prototype=Object.assign(Object.create(Vn.prototype),{constructor:qn,ValueTypeName:"string",ValueBufferType:Array,DefaultInterpolation:2300,InterpolantFactoryMethodLinear:void 0,InterpolantFactoryMethodSmooth:void 0}),Yn.prototype=Object.assign(Object.create(Vn.prototype),{constructor:Yn,ValueTypeName:"vector"}),Object.assign(Zn,{parse:function(t){for(var e=[],i=t.tracks,n=1/(t.fps||1),r=0,a=i.length;r!==a;++r)e.push(Qn(i[r]).scale(n));return new Zn(t.name,t.duration,e)},toJSON:function(t){var e=[],i=t.tracks;t={name:t.name,duration:t.duration,tracks:e,uuid:t.uuid};for(var n=0,r=i.length;n!==r;++n)e.push(Vn.toJSON(i[n]));return t},CreateFromMorphTargetSequence:function(t,e,i,n){for(var r=e.length,a=[],o=0;o<r;o++){var s=[],h=[];s.push((o+r-1)%r,o,(o+1)%r),h.push(0,1,0);var c=Da.getKeyframeOrder(s);s=Da.sortedArray(s,1,c),h=Da.sortedArray(h,1,c),n||0!==s[0]||(s.push(r),h.push(h[0])),a.push(new Wn(".morphTargetInfluences["+e[o].name+"]",s,h).scale(1/i))}return new Zn(t,(-1),a)},findByName:function(t,e){var i=t;for(Array.isArray(t)||(i=t.geometry&&t.geometry.animations||t.animations),t=0;t<i.length;t++)if(i[t].name===e)return i[t];return null},CreateClipsFromMorphTargetSequences:function(t,e,i){for(var n={},r=/^([\w-]*?)([\d]+)$/,a=0,o=t.length;a<o;a++){var s=t[a],h=s.name.match(r);if(h&&1<h.length){var c=h[1];(h=n[c])||(n[c]=h=[]),h.push(s)}}t=[];for(c in n)t.push(Zn.CreateFromMorphTargetSequence(c,n[c],e,i));return t},parseAnimation:function(t,e){if(!t)return null;var i=function(t,e,i,n,r){if(0!==i.length){var a=[],o=[];Da.flattenJSON(i,a,o,n),0!==a.length&&r.push(new t(e,a,o))}},n=[],r=t.name||"default",a=t.length||-1,o=t.fps||30;t=t.hierarchy||[];for(var s=0;s<t.length;s++){var h=t[s].keys;if(h&&0!==h.length)if(h[0].morphTargets){a={};for(var c=0;c<h.length;c++)if(h[c].morphTargets)for(var l=0;l<h[c].morphTargets.length;l++)a[h[c].morphTargets[l]]=-1;for(var u in a){var p=[],d=[];for(l=0;l!==h[c].morphTargets.length;++l){var f=h[c];p.push(f.time),d.push(f.morphTarget===u?1:0)}n.push(new Wn(".morphTargetInfluence["+u+"]",p,d))}a=a.length*(o||1)}else c=".bones["+e[s].name+"]",i(Yn,c+".position",h,"pos",n),i(Xn,c+".quaternion",h,"rot",n),i(Yn,c+".scale",h,"scl",n)}return 0===n.length?null:new Zn(r,a,n)}}),Object.assign(Zn.prototype,{resetDuration:function(){for(var t=0,e=0,i=this.tracks.length;e!==i;++e){var n=this.tracks[e];t=Math.max(t,n.times[n.times.length-1])}return this.duration=t,this},trim:function(){for(var t=0;t<this.tracks.length;t++)this.tracks[t].trim(0,this.duration);return this},validate:function(){for(var t=!0,e=0;e<this.tracks.length;e++)t=t&&this.tracks[e].validate();return t},optimize:function(){for(var t=0;t<this.tracks.length;t++)this.tracks[t].optimize();return this}}),Object.assign(Kn.prototype,{load:function(t,e,i,n){var r=this;new nn(r.manager).load(t,function(t){e(r.parse(JSON.parse(t)))},i,n)},setTextures:function(t){this.textures=t},parse:function(t){function e(t){return void 0===r[t]&&void 0,r[t]}var r=this.textures,o=new Ta[t.type];if(void 0!==t.uuid&&(o.uuid=t.uuid),void 0!==t.name&&(o.name=t.name),void 0!==t.color&&o.color.setHex(t.color),void 0!==t.roughness&&(o.roughness=t.roughness),void 0!==t.metalness&&(o.metalness=t.metalness),void 0!==t.emissive&&o.emissive.setHex(t.emissive),void 0!==t.specular&&o.specular.setHex(t.specular),void 0!==t.shininess&&(o.shininess=t.shininess),void 0!==t.clearCoat&&(o.clearCoat=t.clearCoat),void 0!==t.clearCoatRoughness&&(o.clearCoatRoughness=t.clearCoatRoughness),void 0!==t.vertexColors&&(o.vertexColors=t.vertexColors),void 0!==t.fog&&(o.fog=t.fog),void 0!==t.flatShading&&(o.flatShading=t.flatShading),void 0!==t.blending&&(o.blending=t.blending),void 0!==t.side&&(o.side=t.side),void 0!==t.opacity&&(o.opacity=t.opacity),void 0!==t.transparent&&(o.transparent=t.transparent),void 0!==t.alphaTest&&(o.alphaTest=t.alphaTest),void 0!==t.depthTest&&(o.depthTest=t.depthTest),void 0!==t.depthWrite&&(o.depthWrite=t.depthWrite),void 0!==t.colorWrite&&(o.colorWrite=t.colorWrite),void 0!==t.wireframe&&(o.wireframe=t.wireframe),void 0!==t.wireframeLinewidth&&(o.wireframeLinewidth=t.wireframeLinewidth),void 0!==t.wireframeLinecap&&(o.wireframeLinecap=t.wireframeLinecap),void 0!==t.wireframeLinejoin&&(o.wireframeLinejoin=t.wireframeLinejoin),void 0!==t.rotation&&(o.rotation=t.rotation),1!==t.linewidth&&(o.linewidth=t.linewidth),void 0!==t.dashSize&&(o.dashSize=t.dashSize),void 0!==t.gapSize&&(o.gapSize=t.gapSize),void 0!==t.scale&&(o.scale=t.scale),void 0!==t.polygonOffset&&(o.polygonOffset=t.polygonOffset),void 0!==t.polygonOffsetFactor&&(o.polygonOffsetFactor=t.polygonOffsetFactor),void 0!==t.polygonOffsetUnits&&(o.polygonOffsetUnits=t.polygonOffsetUnits),void 0!==t.skinning&&(o.skinning=t.skinning),void 0!==t.morphTargets&&(o.morphTargets=t.morphTargets),void 0!==t.dithering&&(o.dithering=t.dithering),void 0!==t.visible&&(o.visible=t.visible),void 0!==t.userData&&(o.userData=t.userData),void 0!==t.uniforms)for(var s in t.uniforms){var c=t.uniforms[s];switch(o.uniforms[s]={},c.type){case"t":o.uniforms[s].value=e(c.value);break;case"c":o.uniforms[s].value=(new g).setHex(c.value);break;case"v2":o.uniforms[s].value=(new i).fromArray(c.value);break;case"v3":o.uniforms[s].value=(new a).fromArray(c.value);break;case"v4":o.uniforms[s].value=(new h).fromArray(c.value);break;case"m4":o.uniforms[s].value=(new n).fromArray(c.value);break;default:o.uniforms[s].value=c.value}}return void 0!==t.defines&&(o.defines=t.defines),void 0!==t.vertexShader&&(o.vertexShader=t.vertexShader),void 0!==t.fragmentShader&&(o.fragmentShader=t.fragmentShader),void 0!==t.shading&&(o.flatShading=1===t.shading),void 0!==t.size&&(o.size=t.size),void 0!==t.sizeAttenuation&&(o.sizeAttenuation=t.sizeAttenuation),void 0!==t.map&&(o.map=e(t.map)),void 0!==t.alphaMap&&(o.alphaMap=e(t.alphaMap),o.transparent=!0),void 0!==t.bumpMap&&(o.bumpMap=e(t.bumpMap)),void 0!==t.bumpScale&&(o.bumpScale=t.bumpScale),void 0!==t.normalMap&&(o.normalMap=e(t.normalMap)),void 0!==t.normalMapType&&(o.normalMapType=t.normalMapType),void 0!==t.normalScale&&(s=t.normalScale,!1===Array.isArray(s)&&(s=[s,s]),o.normalScale=(new i).fromArray(s)),void 0!==t.displacementMap&&(o.displacementMap=e(t.displacementMap)),void 0!==t.displacementScale&&(o.displacementScale=t.displacementScale),void 0!==t.displacementBias&&(o.displacementBias=t.displacementBias),void 0!==t.roughnessMap&&(o.roughnessMap=e(t.roughnessMap)),void 0!==t.metalnessMap&&(o.metalnessMap=e(t.metalnessMap)),void 0!==t.emissiveMap&&(o.emissiveMap=e(t.emissiveMap)),void 0!==t.emissiveIntensity&&(o.emissiveIntensity=t.emissiveIntensity),void 0!==t.specularMap&&(o.specularMap=e(t.specularMap)),void 0!==t.envMap&&(o.envMap=e(t.envMap)),void 0!==t.reflectivity&&(o.reflectivity=t.reflectivity),void 0!==t.lightMap&&(o.lightMap=e(t.lightMap)),void 0!==t.lightMapIntensity&&(o.lightMapIntensity=t.lightMapIntensity),void 0!==t.aoMap&&(o.aoMap=e(t.aoMap)),void 0!==t.aoMapIntensity&&(o.aoMapIntensity=t.aoMapIntensity),void 0!==t.gradientMap&&(o.gradientMap=e(t.gradientMap)),o}}),Object.assign($n.prototype,{load:function(t,e,i,n){var r=this;new nn(r.manager).load(t,function(t){e(r.parse(JSON.parse(t)))},i,n)},parse:function(t){var e=new z,i=t.data.index;void 0!==i&&(i=new Ua[i.type](i.array),e.setIndex(new T(i,1)));var n=t.data.attributes;for(o in n){var r=n[o];i=new Ua[r.type](r.array),e.addAttribute(o,new T(i,r.itemSize,r.normalized))}var o=t.data.groups||t.data.drawcalls||t.data.offsets;if(void 0!==o)for(i=0,n=o.length;i!==n;++i)r=o[i],e.addGroup(r.start,r.count,r.materialIndex);return t=t.data.boundingSphere,void 0!==t&&(o=new a,void 0!==t.center&&o.fromArray(t.center),e.boundingSphere=new d(o,t.radius)),e}});var Ua={Int8Array:Int8Array,Uint8Array:Uint8Array,Uint8ClampedArray:"undefined"!=typeof Uint8ClampedArray?Uint8ClampedArray:Uint8Array,Int16Array:Int16Array,Uint16Array:Uint16Array,Int32Array:Int32Array,Uint32Array:Uint32Array,Float32Array:Float32Array,Float64Array:Float64Array};tr.Handlers={handlers:[],add:function(t,e){this.handlers.push(t,e)},get:function(t){for(var e=this.handlers,i=0,n=e.length;i<n;i+=2){var r=e[i+1];if(e[i].test(t))return r}return null}},Object.assign(tr.prototype,{crossOrigin:"anonymous",onLoadStart:function(){},onLoadProgress:function(){},onLoadComplete:function(){},initMaterials:function(t,e,i){for(var n=[],r=0;r<t.length;++r)n[r]=this.createMaterial(t[r],e,i);return n},createMaterial:function(){var t={NoBlending:0,NormalBlending:1,AdditiveBlending:2,SubtractiveBlending:3,MultiplyBlending:4,CustomBlending:5},e=new g,i=new hn,n=new Kn;return function(r,a,o){function s(t,e,n,r,s){t=a+t;var h=tr.Handlers.get(t);return null!==h?t=h.load(t):(i.setCrossOrigin(o),t=i.load(t)),void 0!==e&&(t.repeat.fromArray(e),1!==e[0]&&(t.wrapS=1e3),1!==e[1]&&(t.wrapT=1e3)),void 0!==n&&t.offset.fromArray(n),void 0!==r&&("repeat"===r[0]&&(t.wrapS=1e3),"mirror"===r[0]&&(t.wrapS=1002),"repeat"===r[1]&&(t.wrapT=1e3),"mirror"===r[1]&&(t.wrapT=1002)),void 0!==s&&(t.anisotropy=s),e=$r.generateUUID(),c[e]=t,e}var h,c={},l={uuid:$r.generateUUID(),type:"MeshLambertMaterial"};for(h in r){var u=r[h];switch(h){case"DbgColor":case"DbgIndex":case"opticalDensity":case"illumination":break;case"DbgName":l.name=u;break;case"blending":l.blending=t[u];break;case"colorAmbient":case"mapAmbient":break;case"colorDiffuse":l.color=e.fromArray(u).getHex();break;case"colorSpecular":l.specular=e.fromArray(u).getHex();break;case"colorEmissive":l.emissive=e.fromArray(u).getHex();break;case"specularCoef":l.shininess=u;break;case"shading":"basic"===u.toLowerCase()&&(l.type="MeshBasicMaterial"),"phong"===u.toLowerCase()&&(l.type="MeshPhongMaterial"),"standard"===u.toLowerCase()&&(l.type="MeshStandardMaterial");break;case"mapDiffuse":l.map=s(u,r.mapDiffuseRepeat,r.mapDiffuseOffset,r.mapDiffuseWrap,r.mapDiffuseAnisotropy);break;case"mapDiffuseRepeat":case"mapDiffuseOffset":case"mapDiffuseWrap":case"mapDiffuseAnisotropy":break;case"mapEmissive":l.emissiveMap=s(u,r.mapEmissiveRepeat,r.mapEmissiveOffset,r.mapEmissiveWrap,r.mapEmissiveAnisotropy);break;case"mapEmissiveRepeat":case"mapEmissiveOffset":case"mapEmissiveWrap":case"mapEmissiveAnisotropy":break;case"mapLight":l.lightMap=s(u,r.mapLightRepeat,r.mapLightOffset,r.mapLightWrap,r.mapLightAnisotropy);break;case"mapLightRepeat":case"mapLightOffset":case"mapLightWrap":case"mapLightAnisotropy":break;case"mapAO":l.aoMap=s(u,r.mapAORepeat,r.mapAOOffset,r.mapAOWrap,r.mapAOAnisotropy);break;case"mapAORepeat":case"mapAOOffset":case"mapAOWrap":case"mapAOAnisotropy":break;case"mapBump":l.bumpMap=s(u,r.mapBumpRepeat,r.mapBumpOffset,r.mapBumpWrap,r.mapBumpAnisotropy);break;case"mapBumpScale":l.bumpScale=u;break;case"mapBumpRepeat":case"mapBumpOffset":case"mapBumpWrap":case"mapBumpAnisotropy":break;case"mapNormal":l.normalMap=s(u,r.mapNormalRepeat,r.mapNormalOffset,r.mapNormalWrap,r.mapNormalAnisotropy);break;case"mapNormalFactor":l.normalScale=u;break;case"mapNormalRepeat":case"mapNormalOffset":case"mapNormalWrap":case"mapNormalAnisotropy":break;case"mapSpecular":l.specularMap=s(u,r.mapSpecularRepeat,r.mapSpecularOffset,r.mapSpecularWrap,r.mapSpecularAnisotropy);break;case"mapSpecularRepeat":case"mapSpecularOffset":case"mapSpecularWrap":case"mapSpecularAnisotropy":break;case"mapMetalness":l.metalnessMap=s(u,r.mapMetalnessRepeat,r.mapMetalnessOffset,r.mapMetalnessWrap,r.mapMetalnessAnisotropy);break;case"mapMetalnessRepeat":case"mapMetalnessOffset":case"mapMetalnessWrap":case"mapMetalnessAnisotropy":break;case"mapRoughness":l.roughnessMap=s(u,r.mapRoughnessRepeat,r.mapRoughnessOffset,r.mapRoughnessWrap,r.mapRoughnessAnisotropy);break;case"mapRoughnessRepeat":case"mapRoughnessOffset":case"mapRoughnessWrap":case"mapRoughnessAnisotropy":break;case"mapAlpha":l.alphaMap=s(u,r.mapAlphaRepeat,r.mapAlphaOffset,r.mapAlphaWrap,r.mapAlphaAnisotropy);break;case"mapAlphaRepeat":case"mapAlphaOffset":case"mapAlphaWrap":case"mapAlphaAnisotropy":break;case"flipSided":l.side=1;break;case"doubleSided":l.side=2;break;case"transparency":l.opacity=u;break;case"depthTest":case"depthWrite":case"colorWrite":case"opacity":case"reflectivity":case"transparent":case"visible":case"wireframe":l[h]=u;break;case"vertexColors":!0===u&&(l.vertexColors=2),"face"===u&&(l.vertexColors=1)}}return"MeshBasicMaterial"===l.type&&delete l.emissive,"MeshPhongMaterial"!==l.type&&delete l.specular,1>l.opacity&&(l.transparent=!0),n.setTextures(c),n.parse(l)}}()});var Ba={decodeText:function(t){if("undefined"!=typeof TextDecoder)return(new TextDecoder).decode(t);for(var e="",i=0,n=t.length;i<n;i++)e+=String.fromCharCode(t[i]);return decodeURIComponent(escape(e))},extractUrlBase:function(t){var e=t.lastIndexOf("/");return-1===e?"./":t.substr(0,e+1)}};Object.assign(er.prototype,{crossOrigin:"anonymous",load:function(t,e,i,n){var r=this,a=this.texturePath&&"string"==typeof this.texturePath?this.texturePath:Ba.extractUrlBase(t),o=new nn(this.manager);o.setWithCredentials(this.withCredentials),o.load(t,function(t){t=JSON.parse(t);var i=t.metadata;void 0!==i&&(i=i.type,void 0!==i&&"object"===i.toLowerCase())||(t=r.parse(t,a),e(t.geometry,t.materials))},i,n)},setCrossOrigin:function(t){return this.crossOrigin=t,this},setTexturePath:function(t){return this.texturePath=t,this},parse:function(){return function(t,e){void 0!==t.data&&(t=t.data),t.scale=void 0!==t.scale?1/t.scale:1;var n,r,o,s=new A,c=t,l=c.faces,u=c.vertices,p=c.normals,d=c.colors,f=c.scale,m=0;if(void 0!==c.uvs){for(n=0;n<c.uvs.length;n++)c.uvs[n].length&&m++;for(n=0;n<m;n++)s.faceVertexUvs[n]=[]}var v=0;for(o=u.length;v<o;)n=new a,n.x=u[v++]*f,n.y=u[v++]*f,n.z=u[v++]*f,s.vertices.push(n);for(v=0,o=l.length;v<o;){u=l[v++];var y=1&u,x=2&u;n=8&u;var _=16&u,b=32&u;if(f=64&u,u&=128,y){y=new S,y.a=l[v],y.b=l[v+1],y.c=l[v+3];var M=new S;if(M.a=l[v+1],M.b=l[v+2],M.c=l[v+3],v+=4,x&&(x=l[v++],y.materialIndex=x,M.materialIndex=x),x=s.faces.length,n)for(n=0;n<m;n++){var w=c.uvs[n];for(s.faceVertexUvs[n][x]=[],s.faceVertexUvs[n][x+1]=[],r=0;4>r;r++){var T=l[v++],L=w[2*T];T=w[2*T+1],L=new i(L,T),2!==r&&s.faceVertexUvs[n][x].push(L),0!==r&&s.faceVertexUvs[n][x+1].push(L)}}if(_&&(_=3*l[v++],y.normal.set(p[_++],p[_++],p[_]),M.normal.copy(y.normal)),b)for(n=0;4>n;n++)_=3*l[v++],b=new a(p[_++],p[_++],p[_]),2!==n&&y.vertexNormals.push(b),0!==n&&M.vertexNormals.push(b);if(f&&(f=l[v++],f=d[f],y.color.setHex(f),M.color.setHex(f)),u)for(n=0;4>n;n++)f=l[v++],f=d[f],2!==n&&y.vertexColors.push(new g(f)),0!==n&&M.vertexColors.push(new g(f));s.faces.push(y),s.faces.push(M)}else{if(y=new S,y.a=l[v++],y.b=l[v++],y.c=l[v++],x&&(x=l[v++],y.materialIndex=x),x=s.faces.length,n)for(n=0;n<m;n++)for(w=c.uvs[n],s.faceVertexUvs[n][x]=[],r=0;3>r;r++)T=l[v++],L=w[2*T],T=w[2*T+1],L=new i(L,T),s.faceVertexUvs[n][x].push(L);if(_&&(_=3*l[v++],y.normal.set(p[_++],p[_++],p[_])),b)for(n=0;3>n;n++)_=3*l[v++],b=new a(p[_++],p[_++],p[_]),y.vertexNormals.push(b);if(f&&(f=l[v++],y.color.setHex(d[f])),u)for(n=0;3>n;n++)f=l[v++],y.vertexColors.push(new g(d[f]));s.faces.push(y)}}if(c=t,v=void 0!==c.influencesPerVertex?c.influencesPerVertex:2,c.skinWeights)for(o=0,l=c.skinWeights.length;o<l;o+=v)s.skinWeights.push(new h(c.skinWeights[o],1<v?c.skinWeights[o+1]:0,2<v?c.skinWeights[o+2]:0,3<v?c.skinWeights[o+3]:0));if(c.skinIndices)for(o=0,l=c.skinIndices.length;o<l;o+=v)s.skinIndices.push(new h(c.skinIndices[o],1<v?c.skinIndices[o+1]:0,2<v?c.skinIndices[o+2]:0,3<v?c.skinIndices[o+3]:0));if(s.bones=c.bones,s.bones&&0<s.bones.length&&(s.skinWeights.length!==s.skinIndices.length||s.skinIndices.length!==s.vertices.length)&&void 0,o=t,l=o.scale,void 0!==o.morphTargets)for(c=0,v=o.morphTargets.length;c<v;c++)for(s.morphTargets[c]={},s.morphTargets[c].name=o.morphTargets[c].name,s.morphTargets[c].vertices=[],p=s.morphTargets[c].vertices,d=o.morphTargets[c].vertices,m=0,u=d.length;m<u;m+=3)f=new a,f.x=d[m]*l,f.y=d[m+1]*l,f.z=d[m+2]*l,p.push(f);if(void 0!==o.morphColors&&0<o.morphColors.length)for(l=s.faces,o=o.morphColors[0].colors,c=0,v=l.length;c<v;c++)l[c].color.fromArray(o,3*c);for(o=t,c=[],v=[],void 0!==o.animation&&v.push(o.animation),void 0!==o.animations&&(o.animations.length?v=v.concat(o.animations):v.push(o.animations)),o=0;o<v.length;o++)(l=Zn.parseAnimation(v[o],s.bones))&&c.push(l);return s.morphTargets&&(v=Zn.CreateClipsFromMorphTargetSequences(s.morphTargets,10),c=c.concat(v)),0<c.length&&(s.animations=c),s.computeFaceNormals(),s.computeBoundingSphere(),void 0===t.materials||0===t.materials.length?{geometry:s}:(t=tr.prototype.initMaterials(t.materials,e,this.crossOrigin),{geometry:s,materials:t})}}()}),Object.assign(ir.prototype,{crossOrigin:"anonymous",load:function(t,e,i,n){""===this.texturePath&&(this.texturePath=t.substring(0,t.lastIndexOf("/")+1));var r=this;new nn(r.manager).load(t,function(t){var i=null;try{i=JSON.parse(t)}catch(a){return void(void 0!==n&&n(a))}t=i.metadata,void 0===t||void 0===t.type||"geometry"===t.type.toLowerCase()?void 0:r.parse(i,e)},i,n)},setTexturePath:function(t){return this.texturePath=t,this},setCrossOrigin:function(t){return this.crossOrigin=t,this},parse:function(t,e){var i=this.parseShape(t.shapes);i=this.parseGeometries(t.geometries,i);var n=this.parseImages(t.images,function(){void 0!==e&&e(r)});n=this.parseTextures(t.textures,n),n=this.parseMaterials(t.materials,n);var r=this.parseObject(t.object,i,n);return t.animations&&(r.animations=this.parseAnimations(t.animations)),void 0!==t.images&&0!==t.images.length||void 0===e||e(r),r},parseShape:function(t){var e={};if(void 0!==t)for(var i=0,n=t.length;i<n;i++){var r=(new Tn).fromJSON(t[i]);e[r.uuid]=r}return e},parseGeometries:function(t,e){var i={};if(void 0!==t)for(var n=new er,r=new $n,a=0,o=t.length;a<o;a++){var s=t[a];switch(s.type){case"PlaneGeometry":case"PlaneBufferGeometry":var h=new Aa[s.type](s.width,s.height,s.widthSegments,s.heightSegments);break;case"BoxGeometry":case"BoxBufferGeometry":case"CubeGeometry":h=new Aa[s.type](s.width,s.height,s.depth,s.widthSegments,s.heightSegments,s.depthSegments);break;case"CircleGeometry":case"CircleBufferGeometry":h=new Aa[s.type](s.radius,s.segments,s.thetaStart,s.thetaLength);break;case"CylinderGeometry":case"CylinderBufferGeometry":h=new Aa[s.type](s.radiusTop,s.radiusBottom,s.height,s.radialSegments,s.heightSegments,s.openEnded,s.thetaStart,s.thetaLength);break;case"ConeGeometry":case"ConeBufferGeometry":h=new Aa[s.type](s.radius,s.height,s.radialSegments,s.heightSegments,s.openEnded,s.thetaStart,s.thetaLength);break;case"SphereGeometry":case"SphereBufferGeometry":h=new Aa[s.type](s.radius,s.widthSegments,s.heightSegments,s.phiStart,s.phiLength,s.thetaStart,s.thetaLength);break;case"DodecahedronGeometry":case"DodecahedronBufferGeometry":case"IcosahedronGeometry":case"IcosahedronBufferGeometry":case"OctahedronGeometry":case"OctahedronBufferGeometry":case"TetrahedronGeometry":case"TetrahedronBufferGeometry":h=new Aa[s.type](s.radius,s.detail);break;case"RingGeometry":case"RingBufferGeometry":h=new Aa[s.type](s.innerRadius,s.outerRadius,s.thetaSegments,s.phiSegments,s.thetaStart,s.thetaLength);break;case"TorusGeometry":case"TorusBufferGeometry":h=new Aa[s.type](s.radius,s.tube,s.radialSegments,s.tubularSegments,s.arc);break;case"TorusKnotGeometry":case"TorusKnotBufferGeometry":h=new Aa[s.type](s.radius,s.tube,s.tubularSegments,s.radialSegments,s.p,s.q);break;case"LatheGeometry":case"LatheBufferGeometry":h=new Aa[s.type](s.points,s.segments,s.phiStart,s.phiLength);break;case"PolyhedronGeometry":case"PolyhedronBufferGeometry":h=new Aa[s.type](s.vertices,s.indices,s.radius,s.details);break;case"ShapeGeometry":case"ShapeBufferGeometry":h=[];for(var c=0,l=s.shapes.length;c<l;c++){var u=e[s.shapes[c]];h.push(u)}h=new Aa[s.type](h,s.curveSegments);break;case"ExtrudeGeometry":case"ExtrudeBufferGeometry":for(h=[],c=0,l=s.shapes.length;c<l;c++)u=e[s.shapes[c]],h.push(u);c=s.options.extrudePath,void 0!==c&&(s.options.extrudePath=(new Ra[c.type]).fromJSON(c)),h=new Aa[s.type](h,s.options);break;case"BufferGeometry":h=r.parse(s);break;case"Geometry":h=n.parse(s,this.texturePath).geometry;break;default:continue}h.uuid=s.uuid,void 0!==s.name&&(h.name=s.name),!0===h.isBufferGeometry&&void 0!==s.userData&&(h.userData=s.userData),i[s.uuid]=h}return i},parseMaterials:function(t,e){var i={};if(void 0!==t){var n=new Kn;n.setTextures(e),e=0;for(var r=t.length;e<r;e++){var a=t[e];if("MultiMaterial"===a.type){for(var o=[],s=0;s<a.materials.length;s++)o.push(n.parse(a.materials[s]));i[a.uuid]=o}else i[a.uuid]=n.parse(a)}}return i},parseAnimations:function(t){for(var e=[],i=0;i<t.length;i++){var n=t[i],r=Zn.parse(n);void 0!==n.uuid&&(r.uuid=n.uuid),e.push(r)}return e},parseImages:function(t,e){function i(t){return n.manager.itemStart(t),a.load(t,function(){n.manager.itemEnd(t)},void 0,function(){n.manager.itemEnd(t),n.manager.itemError(t)})}var n=this,r={};if(void 0!==t&&0<t.length){e=new en(e);var a=new on(e);a.setCrossOrigin(this.crossOrigin),e=0;for(var o=t.length;e<o;e++){var s=t[e],h=s.url;if(Array.isArray(h)){r[s.uuid]=[];for(var c=0,l=h.length;c<l;c++){var u=h[c];u=/^(\/\/)|([a-z]+:(\/\/)?)/i.test(u)?u:n.texturePath+u,r[s.uuid].push(i(u))}}else u=/^(\/\/)|([a-z]+:(\/\/)?)/i.test(s.url)?s.url:n.texturePath+s.url,r[s.uuid]=i(u)}}return r},parseTextures:function(t,e){function i(t,e){return"number"==typeof t?t:e[t]}var n={};if(void 0!==t)for(var r=0,a=t.length;r<a;r++){var o=t[r];void 0===o.image&&void 0,void 0===e[o.image]&&void 0;var h=Array.isArray(e[o.image])?new ot(e[o.image]):new s(e[o.image]);h.needsUpdate=!0,h.uuid=o.uuid,void 0!==o.name&&(h.name=o.name),void 0!==o.mapping&&(h.mapping=i(o.mapping,za)),void 0!==o.offset&&h.offset.fromArray(o.offset),void 0!==o.repeat&&h.repeat.fromArray(o.repeat),void 0!==o.center&&h.center.fromArray(o.center),void 0!==o.rotation&&(h.rotation=o.rotation),void 0!==o.wrap&&(h.wrapS=i(o.wrap[0],Fa),h.wrapT=i(o.wrap[1],Fa)),void 0!==o.format&&(h.format=o.format),void 0!==o.minFilter&&(h.minFilter=i(o.minFilter,Ga)),void 0!==o.magFilter&&(h.magFilter=i(o.magFilter,Ga)),void 0!==o.anisotropy&&(h.anisotropy=o.anisotropy),void 0!==o.flipY&&(h.flipY=o.flipY),n[o.uuid]=h}return n},parseObject:function(t,e,i){function n(t){return void 0===e[t]&&void 0,e[t]}function r(t){if(void 0!==t){if(Array.isArray(t)){for(var e=[],n=0,r=t.length;n<r;n++){var a=t[n];void 0===i[a]&&void 0,e.push(i[a])}return e}return void 0===i[t]&&void 0,i[t]}}switch(t.type){case"Scene":var a=new we;void 0!==t.background&&Number.isInteger(t.background)&&(a.background=new g(t.background)),void 0!==t.fog&&("Fog"===t.fog.type?a.fog=new Me(t.fog.color,t.fog.near,t.fog.far):"FogExp2"===t.fog.type&&(a.fog=new be(t.fog.color,t.fog.density)));break;case"PerspectiveCamera":a=new ge(t.fov,t.aspect,t.near,t.far),void 0!==t.focus&&(a.focus=t.focus),void 0!==t.zoom&&(a.zoom=t.zoom),void 0!==t.filmGauge&&(a.filmGauge=t.filmGauge),void 0!==t.filmOffset&&(a.filmOffset=t.filmOffset),void 0!==t.view&&(a.view=Object.assign({},t.view));break;case"OrthographicCamera":a=new w(t.left,t.right,t.top,t.bottom,t.near,t.far),void 0!==t.zoom&&(a.zoom=t.zoom),void 0!==t.view&&(a.view=Object.assign({},t.view));break;case"AmbientLight":a=new Dn(t.color,t.intensity);break;case"DirectionalLight":a=new Rn(t.color,t.intensity);break;case"PointLight":a=new In(t.color,t.intensity,t.distance,t.decay);break;case"RectAreaLight":a=new Un(t.color,t.intensity,t.width,t.height);break;case"SpotLight":a=new On(t.color,t.intensity,t.distance,t.angle,t.penumbra,t.decay);break;case"HemisphereLight":a=new En(t.color,t.groundColor,t.intensity);break;case"SkinnedMesh":case"Mesh":a=n(t.geometry);var o=r(t.material);a=a.bones&&0<a.bones.length?new Oe(a,o):new Y(a,o);break;case"LOD":a=new Ee;break;case"Line":a=new Ne(n(t.geometry),r(t.material),t.mode);break;case"LineLoop":a=new De(n(t.geometry),r(t.material));break;case"LineSegments":a=new Re(n(t.geometry),r(t.material));break;case"PointCloud":case"Points":a=new Be(n(t.geometry),r(t.material));break;case"Sprite":a=new Le(r(t.material));break;case"Group":a=new me;break;default:a=new b}if(a.uuid=t.uuid,void 0!==t.name&&(a.name=t.name),void 0!==t.matrix?(a.matrix.fromArray(t.matrix),void 0!==t.matrixAutoUpdate&&(a.matrixAutoUpdate=t.matrixAutoUpdate),a.matrixAutoUpdate&&a.matrix.decompose(a.position,a.quaternion,a.scale)):(void 0!==t.position&&a.position.fromArray(t.position),void 0!==t.rotation&&a.rotation.fromArray(t.rotation),void 0!==t.quaternion&&a.quaternion.fromArray(t.quaternion),void 0!==t.scale&&a.scale.fromArray(t.scale)),void 0!==t.castShadow&&(a.castShadow=t.castShadow),void 0!==t.receiveShadow&&(a.receiveShadow=t.receiveShadow),t.shadow&&(void 0!==t.shadow.bias&&(a.shadow.bias=t.shadow.bias),void 0!==t.shadow.radius&&(a.shadow.radius=t.shadow.radius),void 0!==t.shadow.mapSize&&a.shadow.mapSize.fromArray(t.shadow.mapSize),void 0!==t.shadow.camera&&(a.shadow.camera=this.parseObject(t.shadow.camera))),void 0!==t.visible&&(a.visible=t.visible),void 0!==t.frustumCulled&&(a.frustumCulled=t.frustumCulled),void 0!==t.renderOrder&&(a.renderOrder=t.renderOrder),void 0!==t.userData&&(a.userData=t.userData),void 0!==t.layers&&(a.layers.mask=t.layers),void 0!==t.children){o=t.children;for(var s=0;s<o.length;s++)a.add(this.parseObject(o[s],e,i))}if("LOD"===t.type)for(t=t.levels,o=0;o<t.length;o++){s=t[o];var h=a.getObjectByProperty("uuid",s.object);void 0!==h&&a.addLevel(h,s.distance)}return a}});var za={UVMapping:300,CubeReflectionMapping:301,CubeRefractionMapping:302,EquirectangularReflectionMapping:303,EquirectangularRefractionMapping:304,SphericalReflectionMapping:305,CubeUVReflectionMapping:306,CubeUVRefractionMapping:307},Fa={RepeatWrapping:1e3,ClampToEdgeWrapping:1001,MirroredRepeatWrapping:1002},Ga={NearestFilter:1003,NearestMipMapNearestFilter:1004,NearestMipMapLinearFilter:1005,LinearFilter:1006,LinearMipMapNearestFilter:1007,LinearMipMapLinearFilter:1008};nr.prototype={constructor:nr,setOptions:function(t){return this.options=t,this},load:function(t,e,i,n){void 0===t&&(t=""),void 0!==this.path&&(t=this.path+t),t=this.manager.resolveURL(t);var r=this,a=La.get(t);return void 0!==a?(r.manager.itemStart(t),setTimeout(function(){e&&e(a),r.manager.itemEnd(t)},0),a):void fetch(t).then(function(t){return t.blob()}).then(function(t){return createImageBitmap(t,r.options)}).then(function(i){La.add(t,i),e&&e(i),r.manager.itemEnd(t)})["catch"](function(e){n&&n(e),r.manager.itemEnd(t),r.manager.itemError(t)})},setCrossOrigin:function(){return this},setPath:function(t){return this.path=t,this}},Object.assign(rr.prototype,{moveTo:function(t,e){this.currentPath=new An,this.subPaths.push(this.currentPath),this.currentPath.moveTo(t,e)},lineTo:function(t,e){this.currentPath.lineTo(t,e)},quadraticCurveTo:function(t,e,i,n){this.currentPath.quadraticCurveTo(t,e,i,n)},bezierCurveTo:function(t,e,i,n,r,a){this.currentPath.bezierCurveTo(t,e,i,n,r,a)},splineThru:function(t){this.currentPath.splineThru(t)},toShapes:function(t,e){function i(t){for(var e=[],i=0,n=t.length;i<n;i++){var r=t[i],a=new Tn;a.curves=r.curves,e.push(a)}return e}function n(t,e){for(var i=e.length,n=!1,r=i-1,a=0;a<i;r=a++){var o=e[r],s=e[a],h=s.x-o.x,c=s.y-o.y;if(Math.abs(c)>Number.EPSILON){if(0>c&&(o=e[a],h=-h,s=e[r],c=-c),!(t.y<o.y||t.y>s.y))if(t.y===o.y){if(t.x===o.x)return!0}else{if(r=c*(t.x-o.x)-h*(t.y-o.y),0===r)return!0;0>r||(n=!n)}}else if(t.y===o.y&&(s.x<=t.x&&t.x<=o.x||o.x<=t.x&&t.x<=s.x))return!0}return n}var r=wa.isClockWise,a=this.subPaths;if(0===a.length)return[];if(!0===e)return i(a);if(e=[],1===a.length){var o=a[0],s=new Tn;return s.curves=o.curves,e.push(s),e}var h=!r(a[0].getPoints());h=t?!h:h,s=[];var c=[],l=[],u=0;c[u]=void 0,l[u]=[];for(var p=0,d=a.length;p<d;p++){o=a[p];var f=o.getPoints(),m=r(f);(m=t?!m:m)?(!h&&c[u]&&u++,c[u]={s:new Tn,p:f},c[u].s.curves=o.curves,h&&u++,l[u]=[]):l[u].push({h:o,p:f[0]})}if(!c[0])return i(a);if(1<c.length){for(p=!1,t=[],r=0,a=c.length;r<a;r++)s[r]=[];for(r=0,a=c.length;r<a;r++)for(o=l[r],m=0;m<o.length;m++){for(h=o[m],u=!0,f=0;f<c.length;f++)n(h.p,c[f].p)&&(r!==f&&t.push({froms:r,tos:f,hole:m}),u?(u=!1,s[f].push(h)):p=!0);u&&s[r].push(h)}0<t.length&&(p||(l=s))}for(p=0,r=c.length;p<r;p++)for(s=c[p].s,e.push(s),t=l[p],a=0,o=t.length;a<o;a++)s.holes.push(t[a].h);return e}}),Object.assign(ar.prototype,{isFont:!0,generateShapes:function(t,e){void 0===e&&(e=100);var i=[],n=e;e=this.data;var r=Array.from?Array.from(t):String(t).split("");n/=e.resolution;var a=(e.boundingBox.yMax-e.boundingBox.yMin+e.underlineThickness)*n;t=[];for(var o=0,s=0,h=0;h<r.length;h++){var c=r[h];if("\n"===c)o=0,s-=a;else{var l=n,u=o,p=s;if(c=e.glyphs[c]||e.glyphs["?"]){var d=new rr;if(c.o)for(var f=c._cachedOutline||(c._cachedOutline=c.o.split(" ")),m=0,g=f.length;m<g;)switch(f[m++]){case"m":var v=f[m++]*l+u,y=f[m++]*l+p;d.moveTo(v,y);break;case"l":v=f[m++]*l+u,y=f[m++]*l+p,d.lineTo(v,y);break;case"q":var x=f[m++]*l+u,_=f[m++]*l+p,b=f[m++]*l+u,M=f[m++]*l+p;d.quadraticCurveTo(b,M,x,_);break;case"b":x=f[m++]*l+u,_=f[m++]*l+p,b=f[m++]*l+u,M=f[m++]*l+p,v=f[m++]*l+u,y=f[m++]*l+p,d.bezierCurveTo(b,M,v,y,x,_)}l={offsetX:c.ha*l,path:d}}else l=void 0;o+=l.offsetX,t.push(l.path)}}for(e=0,r=t.length;e<r;e++)Array.prototype.push.apply(i,t[e].toShapes());return i}}),Object.assign(or.prototype,{load:function(t,e,i,n){var r=this,a=new nn(this.manager);a.setPath(this.path),a.load(t,function(t){try{var i=JSON.parse(t)}catch(n){i=JSON.parse(t.substring(65,t.length-2))}t=r.parse(i),e&&e(t)},i,n)},parse:function(t){return new ar(t)},setPath:function(t){return this.path=t,this}});var Va,ka={getContext:function(){return void 0===Va&&(Va=new(window.AudioContext||window.webkitAudioContext)),Va},setContext:function(t){Va=t}};Object.assign(sr.prototype,{load:function(t,e,i,n){var r=new nn(this.manager);r.setResponseType("arraybuffer"),r.load(t,function(t){t=t.slice(0),ka.getContext().decodeAudioData(t,function(t){e(t)})},i,n)}}),Object.assign(hr.prototype,{update:function(){var t,e,i,r,a,o,s,h,c=new n,l=new n;return function(n){if(t!==this||e!==n.focus||i!==n.fov||r!==n.aspect*this.aspect||a!==n.near||o!==n.far||s!==n.zoom||h!==this.eyeSep){t=this,e=n.focus,i=n.fov,r=n.aspect*this.aspect,a=n.near,o=n.far,s=n.zoom;var u=n.projectionMatrix.clone();h=this.eyeSep/2;var p=h*a/e,d=a*Math.tan($r.DEG2RAD*i*.5)/s;l.elements[12]=-h,c.elements[12]=h;var f=-d*r+p,m=d*r+p;u.elements[0]=2*a/(m-f),u.elements[8]=(m+f)/(m-f),this.cameraL.projectionMatrix.copy(u),f=-d*r-p,m=d*r-p,u.elements[0]=2*a/(m-f),u.elements[8]=(m+f)/(m-f),this.cameraR.projectionMatrix.copy(u)}this.cameraL.matrixWorld.copy(n.matrixWorld).multiply(l),this.cameraR.matrixWorld.copy(n.matrixWorld).multiply(c)}}()}),cr.prototype=Object.create(b.prototype),cr.prototype.constructor=cr,lr.prototype=Object.assign(Object.create(b.prototype),{constructor:lr,getInput:function(){return this.gain},removeFilter:function(){return null!==this.filter&&(this.gain.disconnect(this.filter),this.filter.disconnect(this.context.destination),this.gain.connect(this.context.destination),this.filter=null),this},getFilter:function(){return this.filter},setFilter:function(t){return null!==this.filter?(this.gain.disconnect(this.filter),this.filter.disconnect(this.context.destination)):this.gain.disconnect(this.context.destination),this.filter=t,this.gain.connect(this.filter),this.filter.connect(this.context.destination),this},getMasterVolume:function(){
return this.gain.gain.value},setMasterVolume:function(t){return this.gain.gain.setTargetAtTime(t,this.context.currentTime,.01),this},updateMatrixWorld:function(){var t=new a,e=new r,i=new a,n=new a;return function(r){b.prototype.updateMatrixWorld.call(this,r),r=this.context.listener;var a=this.up;this.matrixWorld.decompose(t,e,i),n.set(0,0,-1).applyQuaternion(e),r.positionX?(r.positionX.setValueAtTime(t.x,this.context.currentTime),r.positionY.setValueAtTime(t.y,this.context.currentTime),r.positionZ.setValueAtTime(t.z,this.context.currentTime),r.forwardX.setValueAtTime(n.x,this.context.currentTime),r.forwardY.setValueAtTime(n.y,this.context.currentTime),r.forwardZ.setValueAtTime(n.z,this.context.currentTime),r.upX.setValueAtTime(a.x,this.context.currentTime),r.upY.setValueAtTime(a.y,this.context.currentTime),r.upZ.setValueAtTime(a.z,this.context.currentTime)):(r.setPosition(t.x,t.y,t.z),r.setOrientation(n.x,n.y,n.z,a.x,a.y,a.z))}}()}),ur.prototype=Object.assign(Object.create(b.prototype),{constructor:ur,getOutput:function(){return this.gain},setNodeSource:function(t){return this.hasPlaybackControl=!1,this.sourceType="audioNode",this.source=t,this.connect(),this},setMediaElementSource:function(t){return this.hasPlaybackControl=!1,this.sourceType="mediaNode",this.source=this.context.createMediaElementSource(t),this.connect(),this},setBuffer:function(t){return this.buffer=t,this.sourceType="buffer",this.autoplay&&this.play(),this},play:function(){if(!0===this.isPlaying);else if(!1!==this.hasPlaybackControl){var t=this.context.createBufferSource();return t.buffer=this.buffer,t.loop=this.loop,t.onended=this.onEnded.bind(this),t.playbackRate.setValueAtTime(this.playbackRate,this.startTime),this.startTime=this.context.currentTime,t.start(this.startTime,this.offset),this.isPlaying=!0,this.source=t,this.connect()}},pause:function(){if(!1!==this.hasPlaybackControl)return!0===this.isPlaying&&(this.source.stop(),this.source.onended=null,this.offset+=(this.context.currentTime-this.startTime)*this.playbackRate,this.isPlaying=!1),this},stop:function(){if(!1!==this.hasPlaybackControl)return this.source.stop(),this.source.onended=null,this.offset=0,this.isPlaying=!1,this},connect:function(){if(0<this.filters.length){this.source.connect(this.filters[0]);for(var t=1,e=this.filters.length;t<e;t++)this.filters[t-1].connect(this.filters[t]);this.filters[this.filters.length-1].connect(this.getOutput())}else this.source.connect(this.getOutput());return this},disconnect:function(){if(0<this.filters.length){this.source.disconnect(this.filters[0]);for(var t=1,e=this.filters.length;t<e;t++)this.filters[t-1].disconnect(this.filters[t]);this.filters[this.filters.length-1].disconnect(this.getOutput())}else this.source.disconnect(this.getOutput());return this},getFilters:function(){return this.filters},setFilters:function(t){return t||(t=[]),!0===this.isPlaying?(this.disconnect(),this.filters=t,this.connect()):this.filters=t,this},getFilter:function(){return this.getFilters()[0]},setFilter:function(t){return this.setFilters(t?[t]:[])},setPlaybackRate:function(t){if(!1!==this.hasPlaybackControl)return this.playbackRate=t,!0===this.isPlaying&&this.source.playbackRate.setValueAtTime(this.playbackRate,this.context.currentTime),this},getPlaybackRate:function(){return this.playbackRate},onEnded:function(){this.isPlaying=!1},getLoop:function(){return!1!==this.hasPlaybackControl&&this.loop},setLoop:function(t){if(!1!==this.hasPlaybackControl)return this.loop=t,!0===this.isPlaying&&(this.source.loop=this.loop),this},getVolume:function(){return this.gain.gain.value},setVolume:function(t){return this.gain.gain.setTargetAtTime(t,this.context.currentTime,.01),this}}),pr.prototype=Object.assign(Object.create(ur.prototype),{constructor:pr,getOutput:function(){return this.panner},getRefDistance:function(){return this.panner.refDistance},setRefDistance:function(t){return this.panner.refDistance=t,this},getRolloffFactor:function(){return this.panner.rolloffFactor},setRolloffFactor:function(t){return this.panner.rolloffFactor=t,this},getDistanceModel:function(){return this.panner.distanceModel},setDistanceModel:function(t){return this.panner.distanceModel=t,this},getMaxDistance:function(){return this.panner.maxDistance},setMaxDistance:function(t){return this.panner.maxDistance=t,this},setDirectionalCone:function(t,e,i){return this.panner.coneInnerAngle=t,this.panner.coneOuterAngle=e,this.panner.coneOuterGain=i,this},updateMatrixWorld:function(){var t=new a,e=new r,i=new a,n=new a;return function(r){b.prototype.updateMatrixWorld.call(this,r),r=this.panner,this.matrixWorld.decompose(t,e,i),n.set(0,0,1).applyQuaternion(e),r.setPosition(t.x,t.y,t.z),r.setOrientation(n.x,n.y,n.z)}}()}),Object.assign(dr.prototype,{getFrequencyData:function(){return this.analyser.getByteFrequencyData(this.data),this.data},getAverageFrequency:function(){for(var t=0,e=this.getFrequencyData(),i=0;i<e.length;i++)t+=e[i];return t/e.length}}),Object.assign(fr.prototype,{accumulate:function(t,e){var i=this.buffer,n=this.valueSize;t=t*n+n;var r=this.cumulativeWeight;if(0===r){for(r=0;r!==n;++r)i[t+r]=i[r];r=e}else r+=e,this._mixBufferRegion(i,t,0,e/r,n);this.cumulativeWeight=r},apply:function(t){var e=this.valueSize,i=this.buffer;t=t*e+e;var n=this.cumulativeWeight,r=this.binding;this.cumulativeWeight=0,1>n&&this._mixBufferRegion(i,t,3*e,1-n,e),n=e;for(var a=e+e;n!==a;++n)if(i[n]!==i[n+e]){r.setValue(i,t);break}},saveOriginalState:function(){var t=this.buffer,e=this.valueSize,i=3*e;this.binding.getValue(t,i);for(var n=e;n!==i;++n)t[n]=t[i+n%e];this.cumulativeWeight=0},restoreOriginalState:function(){this.binding.setValue(this.buffer,3*this.valueSize)},_select:function(t,e,i,n,r){if(.5<=n)for(n=0;n!==r;++n)t[e+n]=t[i+n]},_slerp:function(t,e,i,n){r.slerpFlat(t,e,t,e,t,i,n)},_lerp:function(t,e,i,n,r){for(var a=1-n,o=0;o!==r;++o){var s=e+o;t[s]=t[s]*a+t[i+o]*n}}}),Object.assign(mr.prototype,{getValue:function(t,e){this.bind();var i=this._bindings[this._targetGroup.nCachedObjects_];void 0!==i&&i.getValue(t,e)},setValue:function(t,e){for(var i=this._bindings,n=this._targetGroup.nCachedObjects_,r=i.length;n!==r;++n)i[n].setValue(t,e)},bind:function(){for(var t=this._bindings,e=this._targetGroup.nCachedObjects_,i=t.length;e!==i;++e)t[e].bind()},unbind:function(){for(var t=this._bindings,e=this._targetGroup.nCachedObjects_,i=t.length;e!==i;++e)t[e].unbind()}}),Object.assign(gr,{Composite:mr,create:function(t,e,i){return t&&t.isAnimationObjectGroup?new gr.Composite(t,e,i):new gr(t,e,i)},sanitizeNodeName:function(){var t=/[\[\]\.:\/]/g;return function(e){return e.replace(/\s/g,"_").replace(t,"")}}(),parseTrackName:function(){var t="[^"+"\\[\\]\\.:\\/".replace("\\.","")+"]",e=/((?:WC+[\/:])*)/.source.replace("WC","[^\\[\\]\\.:\\/]");t=/(WCOD+)?/.source.replace("WCOD",t);var i=/(?:\.(WC+)(?:\[(.+)\])?)?/.source.replace("WC","[^\\[\\]\\.:\\/]"),n=/\.(WC+)(?:\[(.+)\])?/.source.replace("WC","[^\\[\\]\\.:\\/]"),r=new RegExp("^"+e+t+i+n+"$"),a=["material","materials","bones"];return function(t){var e=r.exec(t);if(!e)throw Error("PropertyBinding: Cannot parse trackName: "+t);e={nodeName:e[2],objectName:e[3],objectIndex:e[4],propertyName:e[5],propertyIndex:e[6]};var i=e.nodeName&&e.nodeName.lastIndexOf(".");if(void 0!==i&&-1!==i){var n=e.nodeName.substring(i+1);-1!==a.indexOf(n)&&(e.nodeName=e.nodeName.substring(0,i),e.objectName=n)}if(null===e.propertyName||0===e.propertyName.length)throw Error("PropertyBinding: can not parse propertyName from trackName: "+t);return e}}(),findNode:function(t,e){if(!e||""===e||"root"===e||"."===e||-1===e||e===t.name||e===t.uuid)return t;if(t.skeleton){var i=t.skeleton.getBoneByName(e);if(void 0!==i)return i}if(t.children){var n=function(t){for(var i=0;i<t.length;i++){var r=t[i];if(r.name===e||r.uuid===e||(r=n(r.children)))return r}return null};if(t=n(t.children))return t}return null}}),Object.assign(gr.prototype,{_getValue_unavailable:function(){},_setValue_unavailable:function(){},BindingType:{Direct:0,EntireArray:1,ArrayElement:2,HasFromToArray:3},Versioning:{None:0,NeedsUpdate:1,MatrixWorldNeedsUpdate:2},GetterByBindingType:[function(t,e){t[e]=this.node[this.propertyName]},function(t,e){for(var i=this.resolvedProperty,n=0,r=i.length;n!==r;++n)t[e++]=i[n]},function(t,e){t[e]=this.resolvedProperty[this.propertyIndex]},function(t,e){this.resolvedProperty.toArray(t,e)}],SetterByBindingTypeAndVersioning:[[function(t,e){this.targetObject[this.propertyName]=t[e]},function(t,e){this.targetObject[this.propertyName]=t[e],this.targetObject.needsUpdate=!0},function(t,e){this.targetObject[this.propertyName]=t[e],this.targetObject.matrixWorldNeedsUpdate=!0}],[function(t,e){for(var i=this.resolvedProperty,n=0,r=i.length;n!==r;++n)i[n]=t[e++]},function(t,e){for(var i=this.resolvedProperty,n=0,r=i.length;n!==r;++n)i[n]=t[e++];this.targetObject.needsUpdate=!0},function(t,e){for(var i=this.resolvedProperty,n=0,r=i.length;n!==r;++n)i[n]=t[e++];this.targetObject.matrixWorldNeedsUpdate=!0}],[function(t,e){this.resolvedProperty[this.propertyIndex]=t[e]},function(t,e){this.resolvedProperty[this.propertyIndex]=t[e],this.targetObject.needsUpdate=!0},function(t,e){this.resolvedProperty[this.propertyIndex]=t[e],this.targetObject.matrixWorldNeedsUpdate=!0}],[function(t,e){this.resolvedProperty.fromArray(t,e)},function(t,e){this.resolvedProperty.fromArray(t,e),this.targetObject.needsUpdate=!0},function(t,e){this.resolvedProperty.fromArray(t,e),this.targetObject.matrixWorldNeedsUpdate=!0}]],getValue:function(t,e){this.bind(),this.getValue(t,e)},setValue:function(t,e){this.bind(),this.setValue(t,e)},bind:function(){var t=this.node,e=this.parsedPath,i=e.objectName,n=e.propertyName,r=e.propertyIndex;if(t||(this.node=t=gr.findNode(this.rootNode,e.nodeName)||this.rootNode),this.getValue=this._getValue_unavailable,this.setValue=this._setValue_unavailable,t){if(i){var a=e.objectIndex;switch(i){case"materials":if(!t.material)return;if(!t.material.materials)return;t=t.material.materials;break;case"bones":if(!t.skeleton)return;for(t=t.skeleton.bones,i=0;i<t.length;i++)if(t[i].name===a){a=i;break}break;default:if(void 0===t[i])return;t=t[i]}if(void 0!==a){if(void 0===t[a])return;t=t[a]}}if(a=t[n],void 0===a);else{if(e=this.Versioning.None,this.targetObject=t,void 0!==t.needsUpdate?e=this.Versioning.NeedsUpdate:void 0!==t.matrixWorldNeedsUpdate&&(e=this.Versioning.MatrixWorldNeedsUpdate),i=this.BindingType.Direct,void 0!==r){if("morphTargetInfluences"===n){if(!t.geometry)return;if(t.geometry.isBufferGeometry){if(!t.geometry.morphAttributes)return;for(i=0;i<this.node.geometry.morphAttributes.position.length;i++)if(t.geometry.morphAttributes.position[i].name===r){r=i;break}}else{if(!t.geometry.morphTargets)return;for(i=0;i<this.node.geometry.morphTargets.length;i++)if(t.geometry.morphTargets[i].name===r){r=i;break}}}i=this.BindingType.ArrayElement,this.resolvedProperty=a,this.propertyIndex=r}else void 0!==a.fromArray&&void 0!==a.toArray?(i=this.BindingType.HasFromToArray,this.resolvedProperty=a):Array.isArray(a)?(i=this.BindingType.EntireArray,this.resolvedProperty=a):this.propertyName=n;this.getValue=this.GetterByBindingType[i],this.setValue=this.SetterByBindingTypeAndVersioning[i][e]}}},unbind:function(){this.node=null,this.getValue=this._getValue_unbound,this.setValue=this._setValue_unbound}}),Object.assign(gr.prototype,{_getValue_unbound:gr.prototype.getValue,_setValue_unbound:gr.prototype.setValue}),Object.assign(vr.prototype,{isAnimationObjectGroup:!0,add:function(){for(var t=this._objects,e=t.length,i=this.nCachedObjects_,n=this._indicesByUUID,r=this._paths,a=this._parsedPaths,o=this._bindings,s=o.length,h=void 0,c=0,l=arguments.length;c!==l;++c){var u=arguments[c],p=u.uuid,d=n[p];if(void 0===d){d=e++,n[p]=d,t.push(u),p=0;for(var f=s;p!==f;++p)o[p].push(new gr(u,r[p],a[p]))}else if(d<i){h=t[d];var m=--i;for(f=t[m],n[f.uuid]=d,t[d]=f,n[p]=m,t[m]=u,p=0,f=s;p!==f;++p){var g=o[p],v=g[d];g[d]=g[m],void 0===v&&(v=new gr(u,r[p],a[p])),g[m]=v}}else t[d]!==h&&void 0}this.nCachedObjects_=i},remove:function(){for(var t=this._objects,e=this.nCachedObjects_,i=this._indicesByUUID,n=this._bindings,r=n.length,a=0,o=arguments.length;a!==o;++a){var s=arguments[a],h=s.uuid,c=i[h];if(void 0!==c&&c>=e){var l=e++,u=t[l];for(i[u.uuid]=c,t[c]=u,i[h]=l,t[l]=s,s=0,h=r;s!==h;++s){u=n[s];var p=u[c];u[c]=u[l],u[l]=p}}}this.nCachedObjects_=e},uncache:function(){for(var t=this._objects,e=t.length,i=this.nCachedObjects_,n=this._indicesByUUID,r=this._bindings,a=r.length,o=0,s=arguments.length;o!==s;++o){var h=arguments[o].uuid,c=n[h];if(void 0!==c)if(delete n[h],c<i){h=--i;var l=t[h],u=--e,p=t[u];for(n[l.uuid]=c,t[c]=l,n[p.uuid]=h,t[h]=p,t.pop(),l=0,p=a;l!==p;++l){var d=r[l],f=d[u];d[c]=d[h],d[h]=f,d.pop()}}else for(u=--e,p=t[u],n[p.uuid]=c,t[c]=p,t.pop(),l=0,p=a;l!==p;++l)d=r[l],d[c]=d[u],d.pop()}this.nCachedObjects_=i},subscribe_:function(t,e){var i=this._bindingsIndicesByPath,n=i[t],r=this._bindings;if(void 0!==n)return r[n];var a=this._paths,o=this._parsedPaths,s=this._objects,h=this.nCachedObjects_,c=Array(s.length);for(n=r.length,i[t]=n,a.push(t),o.push(e),r.push(c),i=h,n=s.length;i!==n;++i)c[i]=new gr(s[i],t,e);return c},unsubscribe_:function(t){var e=this._bindingsIndicesByPath,i=e[t];if(void 0!==i){var n=this._paths,r=this._parsedPaths,a=this._bindings,o=a.length-1,s=a[o];e[t[o]]=i,a[i]=s,a.pop(),r[i]=r[o],r.pop(),n[i]=n[o],n.pop()}}}),Object.assign(yr.prototype,{play:function(){return this._mixer._activateAction(this),this},stop:function(){return this._mixer._deactivateAction(this),this.reset()},reset:function(){return this.paused=!1,this.enabled=!0,this.time=0,this._loopCount=-1,this._startTime=null,this.stopFading().stopWarping()},isRunning:function(){return this.enabled&&!this.paused&&0!==this.timeScale&&null===this._startTime&&this._mixer._isActiveAction(this)},isScheduled:function(){return this._mixer._isActiveAction(this)},startAt:function(t){return this._startTime=t,this},setLoop:function(t,e){return this.loop=t,this.repetitions=e,this},setEffectiveWeight:function(t){return this.weight=t,this._effectiveWeight=this.enabled?t:0,this.stopFading()},getEffectiveWeight:function(){return this._effectiveWeight},fadeIn:function(t){return this._scheduleFading(t,0,1)},fadeOut:function(t){return this._scheduleFading(t,1,0)},crossFadeFrom:function(t,e,i){if(t.fadeOut(e),this.fadeIn(e),i){i=this._clip.duration;var n=t._clip.duration,r=i/n;t.warp(1,n/i,e),this.warp(r,1,e)}return this},crossFadeTo:function(t,e,i){return t.crossFadeFrom(this,e,i)},stopFading:function(){var t=this._weightInterpolant;return null!==t&&(this._weightInterpolant=null,this._mixer._takeBackControlInterpolant(t)),this},setEffectiveTimeScale:function(t){return this.timeScale=t,this._effectiveTimeScale=this.paused?0:t,this.stopWarping()},getEffectiveTimeScale:function(){return this._effectiveTimeScale},setDuration:function(t){return this.timeScale=this._clip.duration/t,this.stopWarping()},syncWith:function(t){return this.time=t.time,this.timeScale=t.timeScale,this.stopWarping()},halt:function(t){return this.warp(this._effectiveTimeScale,0,t)},warp:function(t,e,i){var n=this._mixer,r=n.time,a=this._timeScaleInterpolant,o=this.timeScale;return null===a&&(this._timeScaleInterpolant=a=n._lendControlInterpolant()),n=a.parameterPositions,a=a.sampleValues,n[0]=r,n[1]=r+i,a[0]=t/o,a[1]=e/o,this},stopWarping:function(){var t=this._timeScaleInterpolant;return null!==t&&(this._timeScaleInterpolant=null,this._mixer._takeBackControlInterpolant(t)),this},getMixer:function(){return this._mixer},getClip:function(){return this._clip},getRoot:function(){return this._localRoot||this._mixer._root},_update:function(t,e,i,n){if(this.enabled){var r=this._startTime;if(null!==r){if(e=(t-r)*i,0>e||0===i)return;this._startTime=null,e*=i}if(e*=this._updateTimeScale(t),i=this._updateTime(e),t=this._updateWeight(t),0<t){e=this._interpolants,r=this._propertyBindings;for(var a=0,o=e.length;a!==o;++a)e[a].evaluate(i),r[a].accumulate(n,t)}}else this._updateWeight(t)},_updateWeight:function(t){var e=0;if(this.enabled){e=this.weight;var i=this._weightInterpolant;if(null!==i){var n=i.evaluate(t)[0];e*=n,t>i.parameterPositions[1]&&(this.stopFading(),0===n&&(this.enabled=!1))}}return this._effectiveWeight=e},_updateTimeScale:function(t){var e=0;if(!this.paused){e=this.timeScale;var i=this._timeScaleInterpolant;if(null!==i){var n=i.evaluate(t)[0];e*=n,t>i.parameterPositions[1]&&(this.stopWarping(),0===e?this.paused=!0:this.timeScale=e)}}return this._effectiveTimeScale=e},_updateTime:function(t){var e=this.time+t,i=this._clip.duration,n=this.loop,r=this._loopCount,a=2202===n;if(0===t)return-1===r?e:a&&1===(1&r)?i-e:e;if(2200===n)t:{if(-1===r&&(this._loopCount=0,this._setEndings(!0,!0,!1)),e>=i)e=i;else{if(!(0>e))break t;e=0}this.clampWhenFinished?this.paused=!0:this.enabled=!1,this._mixer.dispatchEvent({type:"finished",action:this,direction:0>t?-1:1})}else{if(-1===r&&(0<=t?(r=0,this._setEndings(!0,0===this.repetitions,a)):this._setEndings(0===this.repetitions,!0,a)),e>=i||0>e){n=Math.floor(e/i),e-=i*n,r+=Math.abs(n);var o=this.repetitions-r;0>=o?(this.clampWhenFinished?this.paused=!0:this.enabled=!1,e=0<t?i:0,this._mixer.dispatchEvent({type:"finished",action:this,direction:0<t?1:-1})):(1===o?(t=0>t,this._setEndings(t,!t,a)):this._setEndings(!1,!1,a),this._loopCount=r,this._mixer.dispatchEvent({type:"loop",action:this,loopDelta:n}))}if(a&&1===(1&r))return this.time=e,i-e}return this.time=e},_setEndings:function(t,e,i){var n=this._interpolantSettings;i?(n.endingStart=2401,n.endingEnd=2401):(n.endingStart=t?this.zeroSlopeAtStart?2401:2400:2402,n.endingEnd=e?this.zeroSlopeAtEnd?2401:2400:2402)},_scheduleFading:function(t,e,i){var n=this._mixer,r=n.time,a=this._weightInterpolant;return null===a&&(this._weightInterpolant=a=n._lendControlInterpolant()),n=a.parameterPositions,a=a.sampleValues,n[0]=r,a[0]=e,n[1]=r+t,a[1]=i,this}}),xr.prototype=Object.assign(Object.create(e.prototype),{constructor:xr,_bindAction:function(t,e){var i=t._localRoot||this._root,n=t._clip.tracks,r=n.length,a=t._propertyBindings;t=t._interpolants;var o=i.uuid,s=this._bindingsByRootAndName,h=s[o];for(void 0===h&&(h={},s[o]=h),s=0;s!==r;++s){var c=n[s],l=c.name,u=h[l];if(void 0===u){if(u=a[s],void 0!==u){null===u._cacheIndex&&(++u.referenceCount,this._addInactiveBinding(u,o,l));continue}u=new fr(gr.create(i,l,e&&e._propertyBindings[s].binding.parsedPath),c.ValueTypeName,c.getValueSize()),++u.referenceCount,this._addInactiveBinding(u,o,l)}a[s]=u,t[s].resultBuffer=u.buffer}},_activateAction:function(t){if(!this._isActiveAction(t)){if(null===t._cacheIndex){var e=(t._localRoot||this._root).uuid,i=t._clip.uuid,n=this._actionsByClip[i];this._bindAction(t,n&&n.knownActions[0]),this._addInactiveAction(t,i,e)}for(e=t._propertyBindings,i=0,n=e.length;i!==n;++i){var r=e[i];0===r.useCount++&&(this._lendBinding(r),r.saveOriginalState())}this._lendAction(t)}},_deactivateAction:function(t){if(this._isActiveAction(t)){for(var e=t._propertyBindings,i=0,n=e.length;i!==n;++i){var r=e[i];0===--r.useCount&&(r.restoreOriginalState(),this._takeBackBinding(r))}this._takeBackAction(t)}},_initMemoryManager:function(){this._actions=[],this._nActiveActions=0,this._actionsByClip={},this._bindings=[],this._nActiveBindings=0,this._bindingsByRootAndName={},this._controlInterpolants=[],this._nActiveControlInterpolants=0;var t=this;this.stats={actions:{get total(){return t._actions.length},get inUse(){return t._nActiveActions}},bindings:{get total(){return t._bindings.length},get inUse(){return t._nActiveBindings}},controlInterpolants:{get total(){return t._controlInterpolants.length},get inUse(){return t._nActiveControlInterpolants}}}},_isActiveAction:function(t){return t=t._cacheIndex,null!==t&&t<this._nActiveActions},_addInactiveAction:function(t,e,i){var n=this._actions,r=this._actionsByClip,a=r[e];void 0===a?(a={knownActions:[t],actionByRoot:{}},t._byClipCacheIndex=0,r[e]=a):(e=a.knownActions,t._byClipCacheIndex=e.length,e.push(t)),t._cacheIndex=n.length,n.push(t),a.actionByRoot[i]=t},_removeInactiveAction:function(t){var e=this._actions,i=e[e.length-1],n=t._cacheIndex;i._cacheIndex=n,e[n]=i,e.pop(),t._cacheIndex=null,e=t._clip.uuid,i=this._actionsByClip,n=i[e];var r=n.knownActions,a=r[r.length-1],o=t._byClipCacheIndex;a._byClipCacheIndex=o,r[o]=a,r.pop(),t._byClipCacheIndex=null,delete n.actionByRoot[(t._localRoot||this._root).uuid],0===r.length&&delete i[e],this._removeInactiveBindingsForAction(t)},_removeInactiveBindingsForAction:function(t){t=t._propertyBindings;for(var e=0,i=t.length;e!==i;++e){var n=t[e];0===--n.referenceCount&&this._removeInactiveBinding(n)}},_lendAction:function(t){var e=this._actions,i=t._cacheIndex,n=this._nActiveActions++,r=e[n];t._cacheIndex=n,e[n]=t,r._cacheIndex=i,e[i]=r},_takeBackAction:function(t){var e=this._actions,i=t._cacheIndex,n=--this._nActiveActions,r=e[n];t._cacheIndex=n,e[n]=t,r._cacheIndex=i,e[i]=r},_addInactiveBinding:function(t,e,i){var n=this._bindingsByRootAndName,r=n[e],a=this._bindings;void 0===r&&(r={},n[e]=r),r[i]=t,t._cacheIndex=a.length,a.push(t)},_removeInactiveBinding:function(t){var e=this._bindings,i=t.binding,n=i.rootNode.uuid;i=i.path;var r=this._bindingsByRootAndName,a=r[n],o=e[e.length-1];t=t._cacheIndex,o._cacheIndex=t,e[t]=o,e.pop(),delete a[i];t:{for(var s in a)break t;delete r[n]}},_lendBinding:function(t){var e=this._bindings,i=t._cacheIndex,n=this._nActiveBindings++,r=e[n];t._cacheIndex=n,e[n]=t,r._cacheIndex=i,e[i]=r},_takeBackBinding:function(t){var e=this._bindings,i=t._cacheIndex,n=--this._nActiveBindings,r=e[n];t._cacheIndex=n,e[n]=t,r._cacheIndex=i,e[i]=r},_lendControlInterpolant:function(){var t=this._controlInterpolants,e=this._nActiveControlInterpolants++,i=t[e];return void 0===i&&(i=new Fn(new Float32Array(2),new Float32Array(2),1,this._controlInterpolantsResultBuffer),i.__cacheIndex=e,t[e]=i),i},_takeBackControlInterpolant:function(t){var e=this._controlInterpolants,i=t.__cacheIndex,n=--this._nActiveControlInterpolants,r=e[n];t.__cacheIndex=n,e[n]=t,r.__cacheIndex=i,e[i]=r},_controlInterpolantsResultBuffer:new Float32Array(1),clipAction:function(t,e){var i=e||this._root,n=i.uuid;i="string"==typeof t?Zn.findByName(i,t):t,t=null!==i?i.uuid:t;var r=this._actionsByClip[t],a=null;if(void 0!==r){if(a=r.actionByRoot[n],void 0!==a)return a;a=r.knownActions[0],null===i&&(i=a._clip)}return null===i?null:(e=new yr(this,i,e),this._bindAction(e,a),this._addInactiveAction(e,t,n),e)},existingAction:function(t,e){var i=e||this._root;return e=i.uuid,i="string"==typeof t?Zn.findByName(i,t):t,t=this._actionsByClip[i?i.uuid:t],void 0!==t?t.actionByRoot[e]||null:null},stopAllAction:function(){for(var t=this._actions,e=this._nActiveActions,i=this._bindings,n=this._nActiveBindings,r=this._nActiveBindings=this._nActiveActions=0;r!==e;++r)t[r].reset();for(r=0;r!==n;++r)i[r].useCount=0;return this},update:function(t){t*=this.timeScale;for(var e=this._actions,i=this._nActiveActions,n=this.time+=t,r=Math.sign(t),a=this._accuIndex^=1,o=0;o!==i;++o)e[o]._update(n,t,r,a);for(t=this._bindings,e=this._nActiveBindings,o=0;o!==e;++o)t[o].apply(a);return this},getRoot:function(){return this._root},uncacheClip:function(t){var e=this._actions;t=t.uuid;var i=this._actionsByClip,n=i[t];if(void 0!==n){n=n.knownActions;for(var r=0,a=n.length;r!==a;++r){var o=n[r];this._deactivateAction(o);var s=o._cacheIndex,h=e[e.length-1];o._cacheIndex=null,o._byClipCacheIndex=null,h._cacheIndex=s,e[s]=h,e.pop(),this._removeInactiveBindingsForAction(o)}delete i[t]}},uncacheRoot:function(t){t=t.uuid;var e=this._actionsByClip;for(n in e){var i=e[n].actionByRoot[t];void 0!==i&&(this._deactivateAction(i),this._removeInactiveAction(i))}var n=this._bindingsByRootAndName[t];if(void 0!==n)for(var r in n)t=n[r],t.restoreOriginalState(),this._removeInactiveBinding(t)},uncacheAction:function(t,e){t=this.existingAction(t,e),null!==t&&(this._deactivateAction(t),this._removeInactiveAction(t))}}),_r.prototype.clone=function(){return new _r(void 0===this.value.clone?this.value:this.value.clone())},br.prototype=Object.assign(Object.create(z.prototype),{constructor:br,isInstancedBufferGeometry:!0,copy:function(t){return z.prototype.copy.call(this,t),this.maxInstancedCount=t.maxInstancedCount,this},clone:function(){return(new this.constructor).copy(this)}}),Mr.prototype=Object.assign(Object.create(Se.prototype),{constructor:Mr,isInstancedInterleavedBuffer:!0,copy:function(t){return Se.prototype.copy.call(this,t),this.meshPerAttribute=t.meshPerAttribute,this}}),wr.prototype=Object.assign(Object.create(T.prototype),{constructor:wr,isInstancedBufferAttribute:!0,copy:function(t){return T.prototype.copy.call(this,t),this.meshPerAttribute=t.meshPerAttribute,this}}),Object.assign(Sr.prototype,{linePrecision:1,set:function(t,e){this.ray.set(t,e)},setFromCamera:function(t,e){e&&e.isPerspectiveCamera?(this.ray.origin.setFromMatrixPosition(e.matrixWorld),this.ray.direction.set(t.x,t.y,.5).unproject(e).sub(this.ray.origin).normalize()):e&&e.isOrthographicCamera?(this.ray.origin.set(t.x,t.y,(e.near+e.far)/(e.near-e.far)).unproject(e),this.ray.direction.set(0,0,-1).transformDirection(e.matrixWorld)):void 0},intersectObject:function(t,e,i){return i=i||[],Tr(t,this,i,e),i.sort(Ar),i},intersectObjects:function(t,e,i){if(i=i||[],!1===Array.isArray(t))return i;for(var n=0,r=t.length;n<r;n++)Tr(t[n],this,i,e);return i.sort(Ar),i}}),Object.assign(Lr.prototype,{start:function(){this.oldTime=this.startTime=("undefined"==typeof performance?Date:performance).now(),this.elapsedTime=0,this.running=!0},stop:function(){this.getElapsedTime(),this.autoStart=this.running=!1},getElapsedTime:function(){return this.getDelta(),this.elapsedTime},getDelta:function(){var t=0;if(this.autoStart&&!this.running)return this.start(),0;if(this.running){var e=("undefined"==typeof performance?Date:performance).now();t=(e-this.oldTime)/1e3,this.oldTime=e,this.elapsedTime+=t}return t}}),Object.assign(Er.prototype,{set:function(t,e,i){return this.radius=t,this.phi=e,this.theta=i,this},clone:function(){return(new this.constructor).copy(this)},copy:function(t){return this.radius=t.radius,this.phi=t.phi,this.theta=t.theta,this},makeSafe:function(){return this.phi=Math.max(1e-6,Math.min(Math.PI-1e-6,this.phi)),this},setFromVector3:function(t){return this.setFromCartesianCoords(t.x,t.y,t.z)},setFromCartesianCoords:function(t,e,i){return this.radius=Math.sqrt(t*t+e*e+i*i),0===this.radius?this.phi=this.theta=0:(this.theta=Math.atan2(t,i),this.phi=Math.acos($r.clamp(e/this.radius,-1,1))),this}}),Object.assign(Cr.prototype,{set:function(t,e,i){return this.radius=t,this.theta=e,this.y=i,this},clone:function(){return(new this.constructor).copy(this)},copy:function(t){return this.radius=t.radius,this.theta=t.theta,this.y=t.y,this},setFromVector3:function(t){return this.setFromCartesianCoords(t.x,t.y,t.z)},setFromCartesianCoords:function(t,e,i){return this.radius=Math.sqrt(t*t+i*i),this.theta=Math.atan2(t,i),this.y=e,this}}),Object.assign(Pr.prototype,{set:function(t,e){return this.min.copy(t),this.max.copy(e),this},setFromPoints:function(t){this.makeEmpty();for(var e=0,i=t.length;e<i;e++)this.expandByPoint(t[e]);return this},setFromCenterAndSize:function(){var t=new i;return function(e,i){return i=t.copy(i).multiplyScalar(.5),this.min.copy(e).sub(i),this.max.copy(e).add(i),this}}(),clone:function(){return(new this.constructor).copy(this)},copy:function(t){return this.min.copy(t.min),this.max.copy(t.max),this},makeEmpty:function(){return this.min.x=this.min.y=1/0,this.max.x=this.max.y=-(1/0),this},isEmpty:function(){return this.max.x<this.min.x||this.max.y<this.min.y},getCenter:function(t){return void 0===t&&(t=new i),this.isEmpty()?t.set(0,0):t.addVectors(this.min,this.max).multiplyScalar(.5)},getSize:function(t){return void 0===t&&(t=new i),this.isEmpty()?t.set(0,0):t.subVectors(this.max,this.min)},expandByPoint:function(t){return this.min.min(t),this.max.max(t),this},expandByVector:function(t){return this.min.sub(t),this.max.add(t),this},expandByScalar:function(t){return this.min.addScalar(-t),this.max.addScalar(t),this},containsPoint:function(t){return!(t.x<this.min.x||t.x>this.max.x||t.y<this.min.y||t.y>this.max.y)},containsBox:function(t){return this.min.x<=t.min.x&&t.max.x<=this.max.x&&this.min.y<=t.min.y&&t.max.y<=this.max.y},getParameter:function(t,e){return void 0===e&&(e=new i),e.set((t.x-this.min.x)/(this.max.x-this.min.x),(t.y-this.min.y)/(this.max.y-this.min.y))},intersectsBox:function(t){return!(t.max.x<this.min.x||t.min.x>this.max.x||t.max.y<this.min.y||t.min.y>this.max.y)},clampPoint:function(t,e){return void 0===e&&(e=new i),e.copy(t).clamp(this.min,this.max)},distanceToPoint:function(){var t=new i;return function(e){return t.copy(e).clamp(this.min,this.max).sub(e).length()}}(),intersect:function(t){return this.min.max(t.min),this.max.min(t.max),this},union:function(t){return this.min.min(t.min),this.max.max(t.max),this},translate:function(t){return this.min.add(t),this.max.add(t),this},equals:function(t){return t.min.equals(this.min)&&t.max.equals(this.max)}}),Object.assign(Or.prototype,{set:function(t,e){return this.start.copy(t),this.end.copy(e),this},clone:function(){return(new this.constructor).copy(this)},copy:function(t){return this.start.copy(t.start),this.end.copy(t.end),this},getCenter:function(t){return void 0===t&&(t=new a),t.addVectors(this.start,this.end).multiplyScalar(.5)},delta:function(t){return void 0===t&&(t=new a),t.subVectors(this.end,this.start)},distanceSq:function(){return this.start.distanceToSquared(this.end)},distance:function(){return this.start.distanceTo(this.end)},at:function(t,e){return void 0===e&&(e=new a),this.delta(e).multiplyScalar(t).add(this.start)},closestPointToPointParameter:function(){var t=new a,e=new a;return function(i,n){return t.subVectors(i,this.start),e.subVectors(this.end,this.start),i=e.dot(e),i=e.dot(t)/i,n&&(i=$r.clamp(i,0,1)),i}}(),closestPointToPoint:function(t,e,i){return t=this.closestPointToPointParameter(t,e),void 0===i&&(i=new a),this.delta(i).multiplyScalar(t).add(this.start)},applyMatrix4:function(t){return this.start.applyMatrix4(t),this.end.applyMatrix4(t),this},equals:function(t){return t.start.equals(this.start)&&t.end.equals(this.end)}}),Ir.prototype=Object.create(b.prototype),Ir.prototype.constructor=Ir,Ir.prototype.isImmediateRenderObject=!0,Nr.prototype=Object.create(Re.prototype),Nr.prototype.constructor=Nr,Nr.prototype.update=function(){var t=new a,e=new a,i=new o;return function(){var n=["a","b","c"];this.object.updateMatrixWorld(!0),i.getNormalMatrix(this.object.matrixWorld);var r=this.object.matrixWorld,a=this.geometry.attributes.position,o=this.object.geometry;if(o&&o.isGeometry)for(var s=o.vertices,h=o.faces,c=o=0,l=h.length;c<l;c++)for(var u=h[c],p=0,d=u.vertexNormals.length;p<d;p++){var f=u.vertexNormals[p];t.copy(s[u[n[p]]]).applyMatrix4(r),e.copy(f).applyMatrix3(i).normalize().multiplyScalar(this.size).add(t),a.setXYZ(o,t.x,t.y,t.z),o+=1,a.setXYZ(o,e.x,e.y,e.z),o+=1}else if(o&&o.isBufferGeometry)for(n=o.attributes.position,s=o.attributes.normal,p=o=0,d=n.count;p<d;p++)t.set(n.getX(p),n.getY(p),n.getZ(p)).applyMatrix4(r),e.set(s.getX(p),s.getY(p),s.getZ(p)),e.applyMatrix3(i).normalize().multiplyScalar(this.size).add(t),a.setXYZ(o,t.x,t.y,t.z),o+=1,a.setXYZ(o,e.x,e.y,e.z),o+=1;a.needsUpdate=!0}}(),Rr.prototype=Object.create(b.prototype),Rr.prototype.constructor=Rr,Rr.prototype.dispose=function(){this.cone.geometry.dispose(),this.cone.material.dispose()},Rr.prototype.update=function(){var t=new a,e=new a;return function(){this.light.updateMatrixWorld();var i=this.light.distance?this.light.distance:1e3,n=i*Math.tan(this.light.angle);this.cone.scale.set(n,n,i),t.setFromMatrixPosition(this.light.matrixWorld),e.setFromMatrixPosition(this.light.target.matrixWorld),this.cone.lookAt(e.sub(t)),void 0!==this.color?this.cone.material.color.set(this.color):this.cone.material.color.copy(this.light.color)}}(),Ur.prototype=Object.create(Re.prototype),Ur.prototype.constructor=Ur,Ur.prototype.updateMatrixWorld=function(){var t=new a,e=new n,i=new n;
return function(n){var r=this.bones,a=this.geometry,o=a.getAttribute("position");i.getInverse(this.root.matrixWorld);for(var s=0,h=0;s<r.length;s++){var c=r[s];c.parent&&c.parent.isBone&&(e.multiplyMatrices(i,c.matrixWorld),t.setFromMatrixPosition(e),o.setXYZ(h,t.x,t.y,t.z),e.multiplyMatrices(i,c.parent.matrixWorld),t.setFromMatrixPosition(e),o.setXYZ(h+1,t.x,t.y,t.z),h+=2)}a.getAttribute("position").needsUpdate=!0,b.prototype.updateMatrixWorld.call(this,n)}}(),Br.prototype=Object.create(Y.prototype),Br.prototype.constructor=Br,Br.prototype.dispose=function(){this.geometry.dispose(),this.material.dispose()},Br.prototype.update=function(){void 0!==this.color?this.material.color.set(this.color):this.material.color.copy(this.light.color)},zr.prototype=Object.create(b.prototype),zr.prototype.constructor=zr,zr.prototype.dispose=function(){this.children[0].geometry.dispose(),this.children[0].material.dispose()},zr.prototype.update=function(){var t=.5*this.light.width,e=.5*this.light.height,i=this.line.geometry.attributes.position,n=i.array;n[0]=t,n[1]=-e,n[2]=0,n[3]=t,n[4]=e,n[5]=0,n[6]=-t,n[7]=e,n[8]=0,n[9]=-t,n[10]=-e,n[11]=0,n[12]=t,n[13]=-e,n[14]=0,i.needsUpdate=!0,void 0!==this.color?this.line.material.color.set(this.color):this.line.material.color.copy(this.light.color)},Fr.prototype=Object.create(b.prototype),Fr.prototype.constructor=Fr,Fr.prototype.dispose=function(){this.children[0].geometry.dispose(),this.children[0].material.dispose()},Fr.prototype.update=function(){var t=new a,e=new g,i=new g;return function(){var n=this.children[0];if(void 0!==this.color)this.material.color.set(this.color);else{var r=n.geometry.getAttribute("color");e.copy(this.light.color),i.copy(this.light.groundColor);for(var a=0,o=r.count;a<o;a++){var s=a<o/2?e:i;r.setXYZ(a,s.r,s.g,s.b)}r.needsUpdate=!0}n.lookAt(t.setFromMatrixPosition(this.light.matrixWorld).negate())}}(),Gr.prototype=Object.create(Re.prototype),Gr.prototype.constructor=Gr,Vr.prototype=Object.create(Re.prototype),Vr.prototype.constructor=Vr,kr.prototype=Object.create(Re.prototype),kr.prototype.constructor=kr,kr.prototype.update=function(){var t=new a,e=new a,i=new o;return function(){this.object.updateMatrixWorld(!0),i.getNormalMatrix(this.object.matrixWorld);var n=this.object.matrixWorld,r=this.geometry.attributes.position,a=this.object.geometry,o=a.vertices;a=a.faces;for(var s=0,h=0,c=a.length;h<c;h++){var l=a[h],u=l.normal;t.copy(o[l.a]).add(o[l.b]).add(o[l.c]).divideScalar(3).applyMatrix4(n),e.copy(u).applyMatrix3(i).normalize().multiplyScalar(this.size).add(t),r.setXYZ(s,t.x,t.y,t.z),s+=1,r.setXYZ(s,e.x,e.y,e.z),s+=1}r.needsUpdate=!0}}(),jr.prototype=Object.create(b.prototype),jr.prototype.constructor=jr,jr.prototype.dispose=function(){this.lightPlane.geometry.dispose(),this.lightPlane.material.dispose(),this.targetLine.geometry.dispose(),this.targetLine.material.dispose()},jr.prototype.update=function(){var t=new a,e=new a,i=new a;return function(){t.setFromMatrixPosition(this.light.matrixWorld),e.setFromMatrixPosition(this.light.target.matrixWorld),i.subVectors(e,t),this.lightPlane.lookAt(i),void 0!==this.color?(this.lightPlane.material.color.set(this.color),this.targetLine.material.color.set(this.color)):(this.lightPlane.material.color.copy(this.light.color),this.targetLine.material.color.copy(this.light.color)),this.targetLine.lookAt(i),this.targetLine.scale.z=i.length()}}(),Wr.prototype=Object.create(Re.prototype),Wr.prototype.constructor=Wr,Wr.prototype.update=function(){function t(t,a,o,s){if(n.set(a,o,s).unproject(r),t=i[t],void 0!==t)for(a=e.getAttribute("position"),o=0,s=t.length;o<s;o++)a.setXYZ(t[o],n.x,n.y,n.z)}var e,i,n=new a,r=new M;return function(){e=this.geometry,i=this.pointMap,r.projectionMatrix.copy(this.camera.projectionMatrix),t("c",0,0,-1),t("t",0,0,1),t("n1",-1,-1,-1),t("n2",1,-1,-1),t("n3",-1,1,-1),t("n4",1,1,-1),t("f1",-1,-1,1),t("f2",1,-1,1),t("f3",-1,1,1),t("f4",1,1,1),t("u1",.7,1.1,-1),t("u2",-.7,1.1,-1),t("u3",0,2,-1),t("cf1",-1,0,1),t("cf2",1,0,1),t("cf3",0,-1,1),t("cf4",0,1,1),t("cn1",-1,0,-1),t("cn2",1,0,-1),t("cn3",0,-1,-1),t("cn4",0,1,-1),e.getAttribute("position").needsUpdate=!0}}(),Hr.prototype=Object.create(Re.prototype),Hr.prototype.constructor=Hr,Hr.prototype.update=function(){var t=new p;return function(e){if(void 0!==this.object&&t.setFromObject(this.object),!t.isEmpty()){e=t.min;var i=t.max,n=this.geometry.attributes.position,r=n.array;r[0]=i.x,r[1]=i.y,r[2]=i.z,r[3]=e.x,r[4]=i.y,r[5]=i.z,r[6]=e.x,r[7]=e.y,r[8]=i.z,r[9]=i.x,r[10]=e.y,r[11]=i.z,r[12]=i.x,r[13]=i.y,r[14]=e.z,r[15]=e.x,r[16]=i.y,r[17]=e.z,r[18]=e.x,r[19]=e.y,r[20]=e.z,r[21]=i.x,r[22]=e.y,r[23]=e.z,n.needsUpdate=!0,this.geometry.computeBoundingSphere()}}}(),Hr.prototype.setFromObject=function(t){return this.object=t,this.update(),this},Xr.prototype=Object.create(Re.prototype),Xr.prototype.constructor=Xr,Xr.prototype.updateMatrixWorld=function(t){var e=this.box;e.isEmpty()||(e.getCenter(this.position),e.getSize(this.scale),this.scale.multiplyScalar(.5),b.prototype.updateMatrixWorld.call(this,t))},qr.prototype=Object.create(Ne.prototype),qr.prototype.constructor=qr,qr.prototype.updateMatrixWorld=function(t){var e=-this.plane.constant;1e-8>Math.abs(e)&&(e=1e-8),this.scale.set(.5*this.size,.5*this.size,e),this.children[0].material.side=0>e?1:0,this.lookAt(this.plane.normal),b.prototype.updateMatrixWorld.call(this,t)};var ja,Wa;Yr.prototype=Object.create(b.prototype),Yr.prototype.constructor=Yr,Yr.prototype.setDirection=function(){var t,e=new a;return function(i){.99999<i.y?this.quaternion.set(0,0,0,1):-.99999>i.y?this.quaternion.set(1,0,0,0):(e.set(i.z,0,-i.x).normalize(),t=Math.acos(i.y),this.quaternion.setFromAxisAngle(e,t))}}(),Yr.prototype.setLength=function(t,e,i){void 0===e&&(e=.2*t),void 0===i&&(i=.2*e),this.line.scale.set(1,Math.max(0,t-e),1),this.line.updateMatrix(),this.cone.scale.set(i,e,i),this.cone.position.y=t,this.cone.updateMatrix()},Yr.prototype.setColor=function(t){this.line.material.color.copy(t),this.cone.material.color.copy(t)},Zr.prototype=Object.create(Re.prototype),Zr.prototype.constructor=Zr,cn.create=function(t,e){return t.prototype=Object.create(cn.prototype),t.prototype.constructor=t,t.prototype.getPoint=e,t},Object.assign(Sn.prototype,{createPointsGeometry:function(t){return t=this.getPoints(t),this.createGeometry(t)},createSpacedPointsGeometry:function(t){return t=this.getSpacedPoints(t),this.createGeometry(t)},createGeometry:function(t){for(var e=new A,i=0,n=t.length;i<n;i++){var r=t[i];e.vertices.push(new a(r.x,r.y,r.z||0))}return e}}),Object.assign(An.prototype,{fromPoints:function(t){this.setFromPoints(t)}}),Jr.prototype=Object.create(dn.prototype),Qr.prototype=Object.create(dn.prototype),Kr.prototype=Object.create(dn.prototype),Object.assign(Kr.prototype,{initFromArray:function(){},getControlPointsArray:function(){},reparametrizeByArcLength:function(){}}),Gr.prototype.setColors=function(){},Ur.prototype.update=function(){},Object.assign(tr.prototype,{extractUrlBase:function(t){return Ba.extractUrlBase(t)}}),Object.assign(Pr.prototype,{center:function(t){return this.getCenter(t)},empty:function(){return this.isEmpty()},isIntersectionBox:function(t){return this.intersectsBox(t)},size:function(t){return this.getSize(t)}}),Object.assign(p.prototype,{center:function(t){return this.getCenter(t)},empty:function(){return this.isEmpty()},isIntersectionBox:function(t){return this.intersectsBox(t)},isIntersectionSphere:function(t){return this.intersectsSphere(t)},size:function(t){return this.getSize(t)}}),Or.prototype.center=function(t){return this.getCenter(t)},Object.assign($r,{random16:function(){return Math.random()},nearestPowerOfTwo:function(t){return $r.floorPowerOfTwo(t)},nextPowerOfTwo:function(t){return $r.ceilPowerOfTwo(t)}}),Object.assign(o.prototype,{flattenToArrayOffset:function(t,e){return this.toArray(t,e)},multiplyVector3:function(t){return t.applyMatrix3(this)},multiplyVector3Array:function(){},applyToBuffer:function(t){return this.applyToBufferAttribute(t)},applyToVector3Array:function(){}}),Object.assign(n.prototype,{extractPosition:function(t){return this.copyPosition(t)},flattenToArrayOffset:function(t,e){return this.toArray(t,e)},getPosition:function(){var t;return function(){return void 0===t&&(t=new a),t.setFromMatrixColumn(this,3)}}(),setRotationFromQuaternion:function(t){return this.makeRotationFromQuaternion(t)},multiplyToArray:function(){},multiplyVector3:function(t){return t.applyMatrix4(this)},multiplyVector4:function(t){return t.applyMatrix4(this)},multiplyVector3Array:function(){},rotateAxis:function(t){t.transformDirection(this)},crossVector:function(t){return t.applyMatrix4(this)},translate:function(){},rotateX:function(){},rotateY:function(){},rotateZ:function(){},rotateByAxis:function(){},applyToBuffer:function(t){return this.applyToBufferAttribute(t)},applyToVector3Array:function(){},makeFrustum:function(t,e,i,n,r,a){return this.makePerspective(t,e,n,i,r,a)}}),f.prototype.isIntersectionLine=function(t){return this.intersectsLine(t)},r.prototype.multiplyVector3=function(t){return t.applyQuaternion(this)},Object.assign(X.prototype,{isIntersectionBox:function(t){return this.intersectsBox(t)},isIntersectionPlane:function(t){return this.intersectsPlane(t)},isIntersectionSphere:function(t){return this.intersectsSphere(t)}}),Object.assign(q.prototype,{area:function(){return this.getArea()},barycoordFromPoint:function(t,e){return this.getBarycoord(t,e)},midpoint:function(t){return this.getMidpoint(t)},normal:function(t){return this.getNormal(t)},plane:function(t){return this.getPlane(t)}}),Object.assign(q,{barycoordFromPoint:function(t,e,i,n,r){return q.getBarycoord(t,e,i,n,r)},normal:function(t,e,i,n){return q.getNormal(t,e,i,n)}}),Object.assign(Tn.prototype,{extractAllPoints:function(t){return this.extractPoints(t)},extrude:function(t){return new Ai(this,t)},makeGeometry:function(t){return new Ui(this,t)}}),Object.assign(i.prototype,{fromAttribute:function(t,e,i){return this.fromBufferAttribute(t,e,i)},distanceToManhattan:function(t){return this.manhattanDistanceTo(t)},lengthManhattan:function(){return this.manhattanLength()}}),Object.assign(a.prototype,{setEulerFromRotationMatrix:function(){},setEulerFromQuaternion:function(){},getPositionFromMatrix:function(t){return this.setFromMatrixPosition(t)},getScaleFromMatrix:function(t){return this.setFromMatrixScale(t)},getColumnFromMatrix:function(t,e){return this.setFromMatrixColumn(e,t)},applyProjection:function(t){return this.applyMatrix4(t)},fromAttribute:function(t,e,i){return this.fromBufferAttribute(t,e,i)},distanceToManhattan:function(t){return this.manhattanDistanceTo(t)},lengthManhattan:function(){return this.manhattanLength()}}),Object.assign(h.prototype,{fromAttribute:function(t,e,i){return this.fromBufferAttribute(t,e,i)},lengthManhattan:function(){return this.manhattanLength()}}),Object.assign(A.prototype,{computeTangents:function(){},computeLineDistances:function(){}}),Object.assign(b.prototype,{getChildByName:function(t){return this.getObjectByName(t)},renderDepth:function(){},translate:function(t,e){return this.translateOnAxis(e,t)},getWorldRotation:function(){}}),Object.defineProperties(b.prototype,{eulerOrder:{get:function(){return this.rotation.order},set:function(t){this.rotation.order=t}},useQuaternion:{get:function(){},set:function(){}}}),Object.defineProperties(Ee.prototype,{objects:{get:function(){return this.levels}}}),Object.defineProperty(Ce.prototype,"useVertexTexture",{get:function(){},set:function(){}}),Object.defineProperty(cn.prototype,"__arcLengthDivisions",{get:function(){return this.arcLengthDivisions},set:function(t){this.arcLengthDivisions=t}}),ge.prototype.setLens=function(t,e){void 0!==e&&(this.filmGauge=e),this.setFocalLength(t)},Object.defineProperties(Ln.prototype,{onlyShadow:{set:function(){}},shadowCameraFov:{set:function(t){this.shadow.camera.fov=t}},shadowCameraLeft:{set:function(t){this.shadow.camera.left=t}},shadowCameraRight:{set:function(t){this.shadow.camera.right=t}},shadowCameraTop:{set:function(t){this.shadow.camera.top=t}},shadowCameraBottom:{set:function(t){this.shadow.camera.bottom=t}},shadowCameraNear:{set:function(t){this.shadow.camera.near=t}},shadowCameraFar:{set:function(t){this.shadow.camera.far=t}},shadowCameraVisible:{set:function(){}},shadowBias:{set:function(t){this.shadow.bias=t}},shadowDarkness:{set:function(){}},shadowMapWidth:{set:function(t){this.shadow.mapSize.width=t}},shadowMapHeight:{set:function(t){this.shadow.mapSize.height=t}}}),Object.defineProperties(T.prototype,{length:{get:function(){return this.array.length}},copyIndicesArray:function(){}}),Object.assign(z.prototype,{addIndex:function(t){this.setIndex(t)},addDrawCall:function(t,e,i){this.addGroup(t,e)},clearDrawCalls:function(){this.clearGroups()},computeTangents:function(){},computeOffsets:function(){}}),Object.defineProperties(z.prototype,{drawcalls:{get:function(){return this.groups}},offsets:{get:function(){return this.groups}}}),Object.assign(Ti.prototype,{getArrays:function(){},addShapeList:function(){},addShape:function(){}}),Object.defineProperties(_r.prototype,{dynamic:{set:function(){}},onUpdate:{value:function(){return this}}}),Object.defineProperties(j.prototype,{wrapAround:{get:function(){},set:function(){}},wrapRGB:{get:function(){return new g}},shading:{get:function(){},set:function(t){this.flatShading=1===t}}}),Object.defineProperties(Ji.prototype,{metal:{get:function(){return!1},set:function(){}}}),Object.defineProperties(H.prototype,{derivatives:{get:function(){return this.extensions.derivatives},set:function(t){this.extensions.derivatives=t}}}),Object.assign(_e.prototype,{animate:function(t){this.setAnimationLoop(t)},getCurrentRenderTarget:function(){return this.getRenderTarget()},getMaxAnisotropy:function(){return this.capabilities.getMaxAnisotropy()},getPrecision:function(){return this.capabilities.precision},resetGLState:function(){return this.state.reset()},supportsFloatTextures:function(){return this.extensions.get("OES_texture_float")},supportsHalfFloatTextures:function(){return this.extensions.get("OES_texture_half_float")},supportsStandardDerivatives:function(){return this.extensions.get("OES_standard_derivatives")},supportsCompressedTextureS3TC:function(){return this.extensions.get("WEBGL_compressed_texture_s3tc")},supportsCompressedTexturePVRTC:function(){return this.extensions.get("WEBGL_compressed_texture_pvrtc")},supportsBlendMinMax:function(){return this.extensions.get("EXT_blend_minmax")},supportsVertexTextures:function(){return this.capabilities.vertexTextures},supportsInstancedArrays:function(){return this.extensions.get("ANGLE_instanced_arrays")},enableScissorTest:function(t){this.setScissorTest(t)},initMaterial:function(){},addPrePlugin:function(){},addPostPlugin:function(){},updateShadowMap:function(){},setFaceCulling:function(){}}),Object.defineProperties(_e.prototype,{shadowMapEnabled:{get:function(){return this.shadowMap.enabled},set:function(t){this.shadowMap.enabled=t}},shadowMapType:{get:function(){return this.shadowMap.type},set:function(t){this.shadowMap.type=t}},shadowMapCullFace:{get:function(){},set:function(){}}}),Object.defineProperties(ue.prototype,{cullFace:{get:function(){},set:function(){}},renderReverseSided:{get:function(){},set:function(){}},renderSingleSided:{get:function(){},set:function(){}}}),Object.defineProperties(c.prototype,{wrapS:{get:function(){return this.texture.wrapS},set:function(t){this.texture.wrapS=t}},wrapT:{get:function(){return this.texture.wrapT},set:function(t){this.texture.wrapT=t}},magFilter:{get:function(){return this.texture.magFilter},set:function(t){this.texture.magFilter=t}},minFilter:{get:function(){return this.texture.minFilter},set:function(t){this.texture.minFilter=t}},anisotropy:{get:function(){return this.texture.anisotropy},set:function(t){this.texture.anisotropy=t}},offset:{get:function(){return this.texture.offset},set:function(t){this.texture.offset=t}},repeat:{get:function(){return this.texture.repeat},set:function(t){this.texture.repeat=t}},format:{get:function(){return this.texture.format},set:function(t){this.texture.format=t}},type:{get:function(){return this.texture.type},set:function(t){this.texture.type=t}},generateMipmaps:{get:function(){return this.texture.generateMipmaps},set:function(t){this.texture.generateMipmaps=t}}}),Object.defineProperties(ye.prototype,{standing:{set:function(){}},userHeight:{set:function(){}}}),ur.prototype.load=function(t){var e=this;return(new sr).load(t,function(t){e.setBuffer(t)}),this},dr.prototype.getData=function(){return this.getFrequencyData()},cr.prototype.updateCubeMap=function(t,e){return this.update(t,e)},ta.crossOrigin=void 0,ta.loadTexture=function(t,e,i,n){var r=new hn;return r.setCrossOrigin(this.crossOrigin),t=r.load(t,i,void 0,n),e&&(t.mapping=e),t},ta.loadTextureCube=function(t,e,i,n){var r=new sn;return r.setCrossOrigin(this.crossOrigin),t=r.load(t,i,void 0,n),e&&(t.mapping=e),t},ta.loadCompressedTexture=function(){},ta.loadCompressedTextureCube=function(){},t.WebGLRenderTargetCube=l,t.WebGLRenderTarget=c,t.WebGLRenderer=_e,t.ShaderLib=oa,t.UniformsLib=aa,t.UniformsUtils=na,t.ShaderChunk=ia,t.FogExp2=be,t.Fog=Me,t.Scene=we,t.Sprite=Le,t.LOD=Ee,t.SkinnedMesh=Oe,t.Skeleton=Ce,t.Bone=Pe,t.Mesh=Y,t.LineSegments=Re,t.LineLoop=De,t.Line=Ne,t.Points=Be,t.Group=me,t.VideoTexture=ze,t.DataTexture=u,t.CompressedTexture=Fe,t.CubeTexture=ot,t.CanvasTexture=Ge,t.DepthTexture=Ve,t.Texture=s,t.CompressedTextureLoader=rn,t.DataTextureLoader=an,t.CubeTextureLoader=sn,t.TextureLoader=hn,t.ObjectLoader=ir,t.MaterialLoader=Kn,t.BufferGeometryLoader=$n,t.DefaultLoadingManager=Ea,t.LoadingManager=en,t.JSONLoader=er,t.ImageLoader=on,t.ImageBitmapLoader=nr,t.FontLoader=or,t.FileLoader=nn,t.Loader=tr,t.LoaderUtils=Ba,t.Cache=La,t.AudioLoader=sr,t.SpotLightShadow=Pn,t.SpotLight=On,t.PointLight=In,t.RectAreaLight=Un,t.HemisphereLight=En,t.DirectionalLightShadow=Nn,t.DirectionalLight=Rn,t.AmbientLight=Dn,t.LightShadow=Cn,t.Light=Ln,t.StereoCamera=hr,t.PerspectiveCamera=ge,t.OrthographicCamera=w,t.CubeCamera=cr,t.ArrayCamera=ve,t.Camera=M,t.AudioListener=lr,t.PositionalAudio=pr,t.AudioContext=ka,t.AudioAnalyser=dr,t.Audio=ur,t.VectorKeyframeTrack=Yn,t.StringKeyframeTrack=qn,t.QuaternionKeyframeTrack=Xn,t.NumberKeyframeTrack=Wn,t.ColorKeyframeTrack=jn,t.BooleanKeyframeTrack=kn,t.PropertyMixer=fr,t.PropertyBinding=gr,t.KeyframeTrack=Vn,t.AnimationUtils=Da,t.AnimationObjectGroup=vr,t.AnimationMixer=xr,t.AnimationClip=Zn,t.Uniform=_r,t.InstancedBufferGeometry=br,t.BufferGeometry=z,t.Geometry=A,t.InterleavedBufferAttribute=Ae,t.InstancedInterleavedBuffer=Mr,t.InterleavedBuffer=Se,t.InstancedBufferAttribute=wr,t.Face3=S,t.Object3D=b,t.Raycaster=Sr,t.Layers=_,t.EventDispatcher=e,t.Clock=Lr,t.QuaternionLinearInterpolant=Hn,t.LinearInterpolant=Fn,t.DiscreteInterpolant=Gn,t.CubicInterpolant=zn,t.Interpolant=Bn,t.Triangle=q,t.Math=$r,t.Spherical=Er,t.Cylindrical=Cr,t.Plane=f,t.Frustum=m,t.Sphere=d,t.Ray=X,t.Matrix4=n,t.Matrix3=o,t.Box3=p,t.Box2=Pr,t.Line3=Or,t.Euler=x,t.Vector4=h,t.Vector3=a,t.Vector2=i,t.Quaternion=r,t.Color=g,t.ImmediateRenderObject=Ir,t.VertexNormalsHelper=Nr,t.SpotLightHelper=Rr,t.SkeletonHelper=Ur,t.PointLightHelper=Br,t.RectAreaLightHelper=zr,t.HemisphereLightHelper=Fr,t.GridHelper=Gr,t.PolarGridHelper=Vr,t.FaceNormalsHelper=kr,t.DirectionalLightHelper=jr,t.CameraHelper=Wr,t.BoxHelper=Hr,t.Box3Helper=Xr,t.PlaneHelper=qr,t.ArrowHelper=Yr,t.AxesHelper=Zr,t.Shape=Tn,t.Path=An,t.ShapePath=rr,t.Font=ar,t.CurvePath=Sn,t.Curve=cn,t.ImageUtils=ta,t.ShapeUtils=wa,t.WebGLUtils=fe,t.WireframeGeometry=ke,t.ParametricGeometry=je,t.ParametricBufferGeometry=We,t.TetrahedronGeometry=qe,t.TetrahedronBufferGeometry=Ye,t.OctahedronGeometry=Ze,t.OctahedronBufferGeometry=Je,t.IcosahedronGeometry=Qe,t.IcosahedronBufferGeometry=Ke,t.DodecahedronGeometry=$e,t.DodecahedronBufferGeometry=ti,t.PolyhedronGeometry=He,t.PolyhedronBufferGeometry=Xe,t.TubeGeometry=ei,t.TubeBufferGeometry=ii,t.TorusKnotGeometry=ni,t.TorusKnotBufferGeometry=ri,t.TorusGeometry=ai,t.TorusBufferGeometry=oi,t.TextGeometry=Ei,t.TextBufferGeometry=Ci,t.SphereGeometry=Pi,t.SphereBufferGeometry=Oi,t.RingGeometry=Ii,t.RingBufferGeometry=Ni,t.PlaneGeometry=V,t.PlaneBufferGeometry=k,t.LatheGeometry=Ri,t.LatheBufferGeometry=Di,t.ShapeGeometry=Ui,t.ShapeBufferGeometry=Bi,t.ExtrudeGeometry=Ai,t.ExtrudeBufferGeometry=Ti,t.EdgesGeometry=Fi,t.ConeGeometry=ki,t.ConeBufferGeometry=ji,t.CylinderGeometry=Gi,t.CylinderBufferGeometry=Vi,t.CircleGeometry=Wi,t.CircleBufferGeometry=Hi,t.BoxGeometry=F,t.BoxBufferGeometry=G,t.ShadowMaterial=Xi,t.SpriteMaterial=Te,t.RawShaderMaterial=qi,t.ShaderMaterial=H,t.PointsMaterial=Ue,t.MeshPhysicalMaterial=Zi,t.MeshStandardMaterial=Yi,t.MeshPhongMaterial=Ji,t.MeshToonMaterial=Qi,t.MeshNormalMaterial=Ki,t.MeshLambertMaterial=$i,t.MeshDepthMaterial=ce,t.MeshDistanceMaterial=le,t.MeshBasicMaterial=W,t.LineDashedMaterial=tn,t.LineBasicMaterial=Ie,t.Material=j,t.Float64BufferAttribute=D,t.Float32BufferAttribute=R,t.Uint32BufferAttribute=N,t.Int32BufferAttribute=I,t.Uint16BufferAttribute=O,t.Int16BufferAttribute=P,t.Uint8ClampedBufferAttribute=C,t.Uint8BufferAttribute=E,t.Int8BufferAttribute=L,t.BufferAttribute=T,t.ArcCurve=un,t.CatmullRomCurve3=dn,t.CubicBezierCurve=vn,t.CubicBezierCurve3=yn,t.EllipseCurve=ln,t.LineCurve=xn,t.LineCurve3=_n,t.QuadraticBezierCurve=bn,t.QuadraticBezierCurve3=Mn,t.SplineCurve=wn,t.REVISION="96",t.MOUSE={LEFT:0,MIDDLE:1,RIGHT:2},t.CullFaceNone=0,t.CullFaceBack=1,t.CullFaceFront=2,t.CullFaceFrontBack=3,t.FrontFaceDirectionCW=0,t.FrontFaceDirectionCCW=1,t.BasicShadowMap=0,t.PCFShadowMap=1,t.PCFSoftShadowMap=2,t.FrontSide=0,t.BackSide=1,t.DoubleSide=2,t.FlatShading=1,t.SmoothShading=2,t.NoColors=0,t.FaceColors=1,t.VertexColors=2,t.NoBlending=0,t.NormalBlending=1,t.AdditiveBlending=2,t.SubtractiveBlending=3,t.MultiplyBlending=4,t.CustomBlending=5,t.AddEquation=100,t.SubtractEquation=101,t.ReverseSubtractEquation=102,t.MinEquation=103,t.MaxEquation=104,t.ZeroFactor=200,t.OneFactor=201,t.SrcColorFactor=202,t.OneMinusSrcColorFactor=203,t.SrcAlphaFactor=204,t.OneMinusSrcAlphaFactor=205,t.DstAlphaFactor=206,t.OneMinusDstAlphaFactor=207,t.DstColorFactor=208,t.OneMinusDstColorFactor=209,t.SrcAlphaSaturateFactor=210,t.NeverDepth=0,t.AlwaysDepth=1,t.LessDepth=2,t.LessEqualDepth=3,t.EqualDepth=4,t.GreaterEqualDepth=5,t.GreaterDepth=6,t.NotEqualDepth=7,t.MultiplyOperation=0,t.MixOperation=1,t.AddOperation=2,t.NoToneMapping=0,t.LinearToneMapping=1,t.ReinhardToneMapping=2,t.Uncharted2ToneMapping=3,t.CineonToneMapping=4,t.UVMapping=300,t.CubeReflectionMapping=301,t.CubeRefractionMapping=302,t.EquirectangularReflectionMapping=303,t.EquirectangularRefractionMapping=304,t.SphericalReflectionMapping=305,t.CubeUVReflectionMapping=306,t.CubeUVRefractionMapping=307,t.RepeatWrapping=1e3,t.ClampToEdgeWrapping=1001,t.MirroredRepeatWrapping=1002,t.NearestFilter=1003,t.NearestMipMapNearestFilter=1004,t.NearestMipMapLinearFilter=1005,t.LinearFilter=1006,t.LinearMipMapNearestFilter=1007,t.LinearMipMapLinearFilter=1008,t.UnsignedByteType=1009,t.ByteType=1010,t.ShortType=1011,t.UnsignedShortType=1012,t.IntType=1013,t.UnsignedIntType=1014,t.FloatType=1015,t.HalfFloatType=1016,t.UnsignedShort4444Type=1017,t.UnsignedShort5551Type=1018,t.UnsignedShort565Type=1019,t.UnsignedInt248Type=1020,t.AlphaFormat=1021,t.RGBFormat=1022,t.RGBAFormat=1023,t.LuminanceFormat=1024,t.LuminanceAlphaFormat=1025,t.RGBEFormat=1023,t.DepthFormat=1026,t.DepthStencilFormat=1027,t.RGB_S3TC_DXT1_Format=33776,t.RGBA_S3TC_DXT1_Format=33777,t.RGBA_S3TC_DXT3_Format=33778,t.RGBA_S3TC_DXT5_Format=33779,t.RGB_PVRTC_4BPPV1_Format=35840,t.RGB_PVRTC_2BPPV1_Format=35841,t.RGBA_PVRTC_4BPPV1_Format=35842,t.RGBA_PVRTC_2BPPV1_Format=35843,t.RGB_ETC1_Format=36196,t.RGBA_ASTC_4x4_Format=37808,t.RGBA_ASTC_5x4_Format=37809,t.RGBA_ASTC_5x5_Format=37810,t.RGBA_ASTC_6x5_Format=37811,t.RGBA_ASTC_6x6_Format=37812,t.RGBA_ASTC_8x5_Format=37813,t.RGBA_ASTC_8x6_Format=37814,t.RGBA_ASTC_8x8_Format=37815,t.RGBA_ASTC_10x5_Format=37816,t.RGBA_ASTC_10x6_Format=37817,t.RGBA_ASTC_10x8_Format=37818,t.RGBA_ASTC_10x10_Format=37819,t.RGBA_ASTC_12x10_Format=37820,t.RGBA_ASTC_12x12_Format=37821,t.LoopOnce=2200,t.LoopRepeat=2201,t.LoopPingPong=2202,t.InterpolateDiscrete=2300,t.InterpolateLinear=2301,t.InterpolateSmooth=2302,t.ZeroCurvatureEnding=2400,t.ZeroSlopeEnding=2401,t.WrapAroundEnding=2402,t.TrianglesDrawMode=0,t.TriangleStripDrawMode=1,t.TriangleFanDrawMode=2,t.LinearEncoding=3e3,t.sRGBEncoding=3001,t.GammaEncoding=3007,t.RGBEEncoding=3002,t.LogLuvEncoding=3003,t.RGBM7Encoding=3004,t.RGBM16Encoding=3005,t.RGBDEncoding=3006,t.BasicDepthPacking=3200,t.RGBADepthPacking=3201,t.TangentSpaceNormalMap=0,t.ObjectSpaceNormalMap=1,t.CubeGeometry=F,t.Face4=function(t,e,i,n,r,a,o){return new S(t,e,i,r,a,o)},t.LineStrip=0,t.LinePieces=1,t.MeshFaceMaterial=function(t){return t},t.MultiMaterial=function(t){return void 0===t&&(t=[]),t.isMultiMaterial=!0,t.materials=t,t.clone=function(){return t.slice()},t},t.PointCloud=function(t,e){return new Be(t,e)},t.Particle=function(t){return new Le(t)},t.ParticleSystem=function(t,e){return new Be(t,e)},t.PointCloudMaterial=function(t){return new Ue(t)},t.ParticleBasicMaterial=function(t){return new Ue(t)},t.ParticleSystemMaterial=function(t){return new Ue(t)},t.Vertex=function(t,e,i){return new a(t,e,i)},t.DynamicBufferAttribute=function(t,e){return new T(t,e).setDynamic(!0)},t.Int8Attribute=function(t,e){return new L(t,e)},t.Uint8Attribute=function(t,e){return new E(t,e)},t.Uint8ClampedAttribute=function(t,e){return new C(t,e)},t.Int16Attribute=function(t,e){return new P(t,e)},t.Uint16Attribute=function(t,e){return new O(t,e)},t.Int32Attribute=function(t,e){return new I(t,e)},t.Uint32Attribute=function(t,e){return new N(t,e)},t.Float32Attribute=function(t,e){return new R(t,e)},t.Float64Attribute=function(t,e){return new D(t,e)},t.ClosedSplineCurve3=Jr,t.SplineCurve3=Qr,t.Spline=Kr,t.AxisHelper=function(t){return new Zr(t)},t.BoundingBoxHelper=function(t,e){return new Hr(t,e)},t.EdgesHelper=function(t,e){return new Re(new Fi(t.geometry),new Ie({color:void 0!==e?e:16777215}))},t.WireframeHelper=function(t,e){return new Re(new ke(t.geometry),new Ie({color:void 0!==e?e:16777215}))},t.XHRLoader=function(t){return new nn(t)},t.BinaryTextureLoader=function(t){return new an(t)},t.GeometryUtils={merge:function(t,e,i){if(e.isMesh){e.matrixAutoUpdate&&e.updateMatrix();var n=e.matrix;e=e.geometry}t.merge(e,n,i)},center:function(t){return t.center()}},t.Projector=function(){this.projectVector=function(t,e){t.project(e)},this.unprojectVector=function(t,e){t.unproject(e)},this.pickingRay=function(){}},t.CanvasRenderer=function(){this.domElement=document.createElementNS("http://www.w3.org/1999/xhtml","canvas"),this.clear=function(){},this.render=function(){},this.setClearColor=function(){},this.setSize=function(){}},t.SceneUtils={createMultiMaterialObject:function(){},detach:function(){},attach:function(){}},t.LensFlare=function(){},Object.defineProperty(t,"__esModule",{value:!0})});
var TWEEN=TWEEN||function(){var n=[];return{getAll:function(){return n},removeAll:function(){n=[]},add:function(t){n.push(t)},remove:function(t){var r=n.indexOf(t);-1!==r&&n.splice(r,1)},update:function(t,r){if(0===n.length)return!1;var i=0;for(t=void 0!==t?t:TWEEN.now();i<n.length;)n[i].update(t)||r?i++:n.splice(i,1);return!0}}}();!function(){void 0===this.window&&void 0!==this.process?TWEEN.now=function(){var n=process.hrtime();return 1e3*n[0]+n[1]/1e3}:void 0!==this.window&&void 0!==window.performance&&void 0!==window.performance.now?TWEEN.now=window.performance.now.bind(window.performance):void 0!==Date.now?TWEEN.now=Date.now:TWEEN.now=function(){return(new Date).getTime()}}(),TWEEN.Tween=function(n){var t=n,r={},i={},o={},u=1e3,e=0,a=!1,f=!1,c=!1,s=0,h=null,l=TWEEN.Easing.Linear.None,E=TWEEN.Interpolation.Linear,p=[],d=null,v=!1,w=null,I=null,M=null;for(var T in n)r[T]=parseFloat(n[T],10);this.to=function(n,t){return void 0!==t&&(u=t),i=n,this},this.start=function(n){TWEEN.add(this),f=!0,v=!1,h=void 0!==n?n:TWEEN.now(),h+=s;for(var u in i){if(i[u]instanceof Array){if(0===i[u].length)continue;i[u]=[t[u]].concat(i[u])}void 0!==r[u]&&(r[u]=t[u],r[u]instanceof Array==0&&(r[u]*=1),o[u]=r[u]||0)}return this},this.stop=function(){return f?(TWEEN.remove(this),f=!1,null!==M&&M.call(t),this.stopChainedTweens(),this):this},this.stopChainedTweens=function(){for(var n=0,t=p.length;t>n;n++)p[n].stop()},this.delay=function(n){return s=n,this},this.repeat=function(n){return e=n,this},this.yoyo=function(n){return a=n,this},this.easing=function(n){return l=n,this},this.interpolation=function(n){return E=n,this},this.chain=function(){return p=arguments,this},this.onStart=function(n){return d=n,this},this.onUpdate=function(n){return w=n,this},this.onComplete=function(n){return I=n,this},this.onStop=function(n){return M=n,this},this.update=function(n){var f,M,T;if(h>n)return!0;v===!1&&(null!==d&&d.call(t),v=!0),M=(n-h)/u,M=M>1?1:M,T=l(M);for(f in i)if(void 0!==r[f]){var N=r[f]||0,W=i[f];W instanceof Array?t[f]=E(W,T):("string"==typeof W&&(W="+"===W.charAt(0)||"-"===W.charAt(0)?N+parseFloat(W,10):parseFloat(W,10)),"number"==typeof W&&(t[f]=N+(W-N)*T))}if(null!==w&&w.call(t,T),1===M){if(e>0){isFinite(e)&&e--;for(f in o){if("string"==typeof i[f]&&(o[f]=o[f]+parseFloat(i[f],10)),a){var O=o[f];o[f]=i[f],i[f]=O}r[f]=o[f]}return a&&(c=!c),h=n+s,!0}null!==I&&I.call(t);for(var m=0,g=p.length;g>m;m++)p[m].start(h+u);return!1}return!0}},TWEEN.Easing={Linear:{None:function(n){return n}},Quadratic:{In:function(n){return n*n},Out:function(n){return n*(2-n)},InOut:function(n){return(n*=2)<1?.5*n*n:-.5*(--n*(n-2)-1)}},Cubic:{In:function(n){return n*n*n},Out:function(n){return--n*n*n+1},InOut:function(n){return(n*=2)<1?.5*n*n*n:.5*((n-=2)*n*n+2)}},Quartic:{In:function(n){return n*n*n*n},Out:function(n){return 1- --n*n*n*n},InOut:function(n){return(n*=2)<1?.5*n*n*n*n:-.5*((n-=2)*n*n*n-2)}},Quintic:{In:function(n){return n*n*n*n*n},Out:function(n){return--n*n*n*n*n+1},InOut:function(n){return(n*=2)<1?.5*n*n*n*n*n:.5*((n-=2)*n*n*n*n+2)}},Sinusoidal:{In:function(n){return 1-Math.cos(n*Math.PI/2)},Out:function(n){return Math.sin(n*Math.PI/2)},InOut:function(n){return.5*(1-Math.cos(Math.PI*n))}},Exponential:{In:function(n){return 0===n?0:Math.pow(1024,n-1)},Out:function(n){return 1===n?1:1-Math.pow(2,-10*n)},InOut:function(n){return 0===n?0:1===n?1:(n*=2)<1?.5*Math.pow(1024,n-1):.5*(-Math.pow(2,-10*(n-1))+2)}},Circular:{In:function(n){return 1-Math.sqrt(1-n*n)},Out:function(n){return Math.sqrt(1- --n*n)},InOut:function(n){return(n*=2)<1?-.5*(Math.sqrt(1-n*n)-1):.5*(Math.sqrt(1-(n-=2)*n)+1)}},Elastic:{In:function(n){return 0===n?0:1===n?1:-Math.pow(2,10*(n-1))*Math.sin(5*(n-1.1)*Math.PI)},Out:function(n){return 0===n?0:1===n?1:Math.pow(2,-10*n)*Math.sin(5*(n-.1)*Math.PI)+1},InOut:function(n){return 0===n?0:1===n?1:(n*=2,1>n?-.5*Math.pow(2,10*(n-1))*Math.sin(5*(n-1.1)*Math.PI):.5*Math.pow(2,-10*(n-1))*Math.sin(5*(n-1.1)*Math.PI)+1)}},Back:{In:function(n){var t=1.70158;return n*n*((t+1)*n-t)},Out:function(n){var t=1.70158;return--n*n*((t+1)*n+t)+1},InOut:function(n){var t=2.5949095;return(n*=2)<1?.5*(n*n*((t+1)*n-t)):.5*((n-=2)*n*((t+1)*n+t)+2)}},Bounce:{In:function(n){return 1-TWEEN.Easing.Bounce.Out(1-n)},Out:function(n){return 1/2.75>n?7.5625*n*n:2/2.75>n?7.5625*(n-=1.5/2.75)*n+.75:2.5/2.75>n?7.5625*(n-=2.25/2.75)*n+.9375:7.5625*(n-=2.625/2.75)*n+.984375},InOut:function(n){return.5>n?.5*TWEEN.Easing.Bounce.In(2*n):.5*TWEEN.Easing.Bounce.Out(2*n-1)+.5}}},TWEEN.Interpolation={Linear:function(n,t){var r=n.length-1,i=r*t,o=Math.floor(i),u=TWEEN.Interpolation.Utils.Linear;return 0>t?u(n[0],n[1],i):t>1?u(n[r],n[r-1],r-i):u(n[o],n[o+1>r?r:o+1],i-o)},Bezier:function(n,t){for(var r=0,i=n.length-1,o=Math.pow,u=TWEEN.Interpolation.Utils.Bernstein,e=0;i>=e;e++)r+=o(1-t,i-e)*o(t,e)*n[e]*u(i,e);return r},CatmullRom:function(n,t){var r=n.length-1,i=r*t,o=Math.floor(i),u=TWEEN.Interpolation.Utils.CatmullRom;return n[0]===n[r]?(0>t&&(o=Math.floor(i=r*(1+t))),u(n[(o-1+r)%r],n[o],n[(o+1)%r],n[(o+2)%r],i-o)):0>t?n[0]-(u(n[0],n[0],n[1],n[1],-i)-n[0]):t>1?n[r]-(u(n[r],n[r],n[r-1],n[r-1],i-r)-n[r]):u(n[o?o-1:0],n[o],n[o+1>r?r:o+1],n[o+2>r?r:o+2],i-o)},Utils:{Linear:function(n,t,r){return(t-n)*r+n},Bernstein:function(n,t){var r=TWEEN.Interpolation.Utils.Factorial;return r(n)/r(t)/r(n-t)},Factorial:function(){var n=[1];return function(t){var r=1;if(n[t])return n[t];for(var i=t;i>1;i--)r*=i;return n[t]=r,r}}(),CatmullRom:function(n,t,r,i,o){var u=.5*(r-n),e=.5*(i-t),a=o*o,f=o*a;return(2*t-2*r+u+e)*f+(-3*t+3*r-2*u-e)*a+u*o+t}}},function(n){"function"==typeof define&&define.amd?define([],function(){return TWEEN}):"undefined"!=typeof module&&"object"==typeof exports?module.exports=TWEEN:void 0!==n&&(n.TWEEN=TWEEN)}(this);
var Stats=function(){var e=Date.now(),t=e,n=0,i=1/0,a=0,o=0,d=1/0,l=0,s=0,r=0,c=0,p=0,h=document.createElement("div");h.id="stats",h.addEventListener("mousedown",function(e){e.preventDefault(),M(++r%2)},!1),h.style.cssText="width:80px;opacity:0.9;cursor:pointer";var f=document.createElement("div");f.id="fps",f.style.cssText="padding:0 0 3px 3px;text-align:left;background-color:#002",h.appendChild(f);var x=document.createElement("div");x.id="fpsText",x.style.cssText="color:#0ff;font-family:Helvetica,Arial,sans-serif;font-size:9px;font-weight:bold;line-height:15px",x.innerHTML="FPS",f.appendChild(x);var m=document.createElement("div");for(m.id="fpsGraph",m.style.cssText="position:relative;width:74px;height:30px;background-color:#0ff",f.appendChild(m);m.children.length<74;){var u=document.createElement("span");u.style.cssText="width:1px;height:30px;float:left;background-color:#113",m.appendChild(u)}var v=document.createElement("div");v.id="ms",v.style.cssText="padding:0 0 3px 3px;text-align:left;background-color:#020;display:none",h.appendChild(v);var g=document.createElement("div");g.id="msText",g.style.cssText="color:#0f0;font-family:Helvetica,Arial,sans-serif;font-size:9px;font-weight:bold;line-height:15px",g.innerHTML="MS",v.appendChild(g);var y=document.createElement("div");for(y.id="msGraph",y.style.cssText="position:relative;width:74px;height:30px;background-color:#0f0",v.appendChild(y);y.children.length<74;){var u=document.createElement("span");u.style.cssText="width:1px;height:30px;float:left;background-color:#131",y.appendChild(u)}var M=function(e){switch(r=e){case 0:f.style.display="block",v.style.display="none";break;case 1:f.style.display="none",v.style.display="block"}},b=function(e,t){var n=e.appendChild(e.firstChild);n.style.height=t+"px"};return{REVISION:12,domElement:h,setMode:M,begin:function(){e=Date.now()},end:function(){var r=Date.now();return n=r-e,i=Math.min(i,n),a=Math.max(a,n),g.textContent=n+" MS ("+i+"-"+a+")",b(y,Math.min(30,30-n/200*30)),s++,r>t+1e3&&(c++,o=Math.round(1e3*s/(r-t)),p+=o,d=Math.min(d,o),l=Math.max(l,o),x.textContent=o+" FPS ("+d+"-"+l+")",b(m,Math.min(30,30-o/100*30)),t=r,s=0),r},update:function(){e=this.end()},getMinMax:function(){var e={fpsMin:d,fpsMax:l,fpsAvg:p/c};return e},setMin:function(){p=0,c=0}}};"object"==typeof module&&(module.exports=Stats);
THREE.OBJLoader=function(e){this.manager=void 0!==e?e:THREE.DefaultLoadingManager},THREE.OBJLoader.prototype={constructor:THREE.OBJLoader,load:function(e,t,r,a){var n=this,d=new THREE.XHRLoader(n.manager);d.setCrossOrigin(this.crossOrigin),d.load(e,function(e){t(n.parse(e))},r,a)},parse:function(e){function t(e){var t=parseInt(e);return 3*(t>=0?t-1:t+E.length/3)}function r(e){var t=parseInt(e);return 3*(t>=0?t-1:t+m.length/3)}function a(e){var t=parseInt(e);return 2*(t>=0?t-1:t+c.length/2)}function n(e,t,r){u.vertices.push(E[e],E[e+1],E[e+2],E[t],E[t+1],E[t+2],E[r],E[r+1],E[r+2])}function d(e,t,r){u.normals.push(m[e],m[e+1],m[e+2],m[t],m[t+1],m[t+2],m[r],m[r+1],m[r+2])}function o(e,t,r){u.uvs.push(c[e],c[e+1],c[t],c[t+1],c[r],c[r+1])}function s(e,s,i,u,l,v,E,m,c,f,p,h){var g,H=t(e),R=t(s),T=t(i);void 0===u?n(H,R,T):(g=t(u),n(H,R,g),n(R,T,g)),void 0!==l&&(H=a(l),R=a(v),T=a(E),void 0===u?o(H,R,T):(g=a(m),o(H,R,g),o(R,T,g))),void 0!==c&&(H=r(c),R=r(f),T=r(p),void 0===u?d(H,R,T):(g=r(h),d(H,R,g),d(R,T,g)))}var i,u,l,v=[];/^o /gm.test(e)===!1&&(u={vertices:[],normals:[],uvs:[]},l={name:""},i={name:"",geometry:u,material:l},v.push(i));for(var E=[],m=[],c=[],f=/v( +[\d|\.|\+|\-|e|E]+)( +[\d|\.|\+|\-|e|E]+)( +[\d|\.|\+|\-|e|E]+)/,p=/vn( +[\d|\.|\+|\-|e|E]+)( +[\d|\.|\+|\-|e|E]+)( +[\d|\.|\+|\-|e|E]+)/,h=/vt( +[\d|\.|\+|\-|e|E]+)( +[\d|\.|\+|\-|e|E]+)/,g=/f( +-?\d+)( +-?\d+)( +-?\d+)( +-?\d+)?/,H=/f( +(-?\d+)\/(-?\d+))( +(-?\d+)\/(-?\d+))( +(-?\d+)\/(-?\d+))( +(-?\d+)\/(-?\d+))?/,R=/f( +(-?\d+)\/(-?\d+)\/(-?\d+))( +(-?\d+)\/(-?\d+)\/(-?\d+))( +(-?\d+)\/(-?\d+)\/(-?\d+))( +(-?\d+)\/(-?\d+)\/(-?\d+))?/,T=/f( +(-?\d+)\/\/(-?\d+))( +(-?\d+)\/\/(-?\d+))( +(-?\d+)\/\/(-?\d+))( +(-?\d+)\/\/(-?\d+))?/,b=e.split("\n"),w=0;w<b.length;w++){var F=b[w];F=F.trim();var A;0!==F.length&&"#"!==F.charAt(0)&&(null!==(A=f.exec(F))?E.push(parseFloat(A[1]),parseFloat(A[2]),parseFloat(A[3])):null!==(A=p.exec(F))?m.push(parseFloat(A[1]),parseFloat(A[2]),parseFloat(A[3])):null!==(A=h.exec(F))?c.push(parseFloat(A[1]),parseFloat(A[2])):null!==(A=g.exec(F))?s(A[1],A[2],A[3],A[4]):null!==(A=H.exec(F))?s(A[2],A[5],A[8],A[11],A[3],A[6],A[9],A[12]):null!==(A=R.exec(F))?s(A[2],A[6],A[10],A[14],A[3],A[7],A[11],A[15],A[4],A[8],A[12],A[16]):null!==(A=T.exec(F))?s(A[2],A[5],A[8],A[11],void 0,void 0,void 0,void 0,A[3],A[6],A[9],A[12]):/^o /.test(F)?(u={vertices:[],normals:[],uvs:[]},l={name:""},i={name:F.substring(2).trim(),geometry:u,material:l},v.push(i)):/^g /.test(F)||(/^usemtl /.test(F)?l.name=F.substring(7).trim():/^mtllib /.test(F)||/^s /.test(F)))}for(var y=new THREE.Object3D,w=0,x=v.length;w<x;w++){i=v[w],u=i.geometry;var B=new THREE.BufferGeometry;B.addAttribute("position",new THREE.BufferAttribute(new Float32Array(u.vertices),3)),u.normals.length>0&&B.addAttribute("normal",new THREE.BufferAttribute(new Float32Array(u.normals),3)),u.uvs.length>0&&B.addAttribute("uv",new THREE.BufferAttribute(new Float32Array(u.uvs),2)),l=new THREE.MeshLambertMaterial,l.name=i.material.name;var L=new THREE.Mesh(B,l);L.name=i.name,y.add(L)}return y}};
var THREEx=THREEx||{};THREEx.RendererStats=function(){var e=document.createElement("div");e.style.cssText="width:80px;opacity:0.9;cursor:pointer";var t=document.createElement("div");t.style.cssText="padding:0 0 3px 3px;text-align:left;background-color:#200;",e.appendChild(t);var n=document.createElement("div");n.style.cssText="color:#f00;font-family:Helvetica,Arial,sans-serif;font-size:9px;font-weight:bold;line-height:15px",n.innerHTML="WebGLRenderer",t.appendChild(n);for(var o=[],r=9,i=0;i<r;i++)o[i]=document.createElement("div"),o[i].style.cssText="color:#f00;background-color:#311;font-family:Helvetica,Arial,sans-serif;font-size:9px;font-weight:bold;line-height:15px",t.appendChild(o[i]),o[i].innerHTML="-";var a=Date.now();return{domElement:e,update:function(e){if(!(Date.now()-a<1e3/30)){a=Date.now();var t=0;o[t++].textContent="== Memory =====",o[t++].textContent="Programs: "+e.info.memory.programs,o[t++].textContent="Geometries: "+e.info.memory.geometries,o[t++].textContent="Textures: "+e.info.memory.textures,o[t++].textContent="== Render =====",o[t++].textContent="Calls: "+e.info.render.calls,o[t++].textContent="Vertices: "+e.info.render.vertices,o[t++].textContent="Faces: "+e.info.render.faces,o[t++].textContent="Points: "+e.info.render.points}}}};
!function(n,r){"use strict";var t={};n.PubSub=t,r(t),"function"==typeof define&&define.amd?define(function(){return t}):"object"==typeof exports&&(void 0!==module&&module.exports&&(exports=module.exports=t),exports.PubSub=t,module.exports=exports=t)}("object"==typeof window&&window||this,function(n){"use strict";function r(n){var r;for(r in n)if(n.hasOwnProperty(r))return!0;return!1}function t(n){return function(){throw n}}function e(n,r,e){try{n(r,e)}catch(n){setTimeout(t(n),0)}}function o(n,r,t){n(r,t)}function i(n,r,t,i){var u,f=c[r],s=i?o:e;if(c.hasOwnProperty(r))for(u in f)f.hasOwnProperty(u)&&s(f[u],n,t)}function u(n,r,t){return function(){var e=String(n),o=e.lastIndexOf(".");for(i(n,n,r,t);o!==-1;)e=e.substr(0,o),o=e.lastIndexOf("."),i(n,e,r,t)}}function f(n){for(var t=String(n),e=Boolean(c.hasOwnProperty(t)&&r(c[t])),o=t.lastIndexOf(".");!e&&o!==-1;)t=t.substr(0,o),o=t.lastIndexOf("."),e=Boolean(c.hasOwnProperty(t)&&r(c[t]));return e}function s(n,r,t,e){var o=u(n,r,e),i=f(n);return!!i&&(t===!0?o():setTimeout(o,0),!0)}var c={},a=-1;n.publish=function(r,t){return s(r,t,!1,n.immediateExceptions)},n.publishSync=function(r,t){return s(r,t,!0,n.immediateExceptions)},n.subscribe=function(n,r){if("function"!=typeof r)return!1;c.hasOwnProperty(n)||(c[n]={});var t="uid_"+String(++a);return c[n][t]=r,t},n.clearAllSubscriptions=function(){c={}},n.clearSubscriptions=function(n){var r;for(r in c)c.hasOwnProperty(r)&&0===r.indexOf(n)&&delete c[r]},n.unsubscribe=function(r){var t,e,o,i=function(n){var r;for(r in c)if(c.hasOwnProperty(r)&&0===r.indexOf(n))return!0;return!1},u="string"==typeof r&&(c.hasOwnProperty(r)||i(r)),f=!u&&"string"==typeof r,s="function"==typeof r,a=!1;if(u)return void n.clearSubscriptions(r);for(t in c)if(c.hasOwnProperty(t)){if(e=c[t],f&&e[r]){delete e[r],a=r;break}if(s)for(o in e)e.hasOwnProperty(o)&&e[o]===r&&(delete e[o],a=!0)}return a}});