Pokrótce na temat logiki budowy szafy
- całe zewnętrze - c<PERSON><PERSON> zewnę<PERSON><PERSON>na rama, d<PERSON><PERSON>, co<PERSON><PERSON><PERSON>, ale także wszystkie elementy szuflady są zbudowane z tego samego obiektu - T03_box.obj, ktory ma wymiary 1x1x1cm i nie wymaga obracania - jest zawsze wrzucony w tej samej orientacji, w zależności od tego jaką częścią szafy jest przyjmuje tylko różne tekstury i skalę, więc jeżeli jest np. drzwiami o wymiarach 50x200x1,2 to przyjmuje skalę X=50 Y=200 Z=1,2
wszystkie tekstury które pasują na ten obiekt nazwyają się T03_box_...
wnętrze jest budowane podobnie do regałów tzn. mamy obiekt T03_interior.obj, który jest analogiczny do shadow_box z regałów, czyli jest w wsypadku szafy boxem z pięcioma ścianami "na lewą stronę" (bark przedniej ściany) i jest wymiarów danego kompartmenta - czyli jeżeli między półkami na wysokość jest dystans w osiach 180 cm to wtedy T03_interior będzie miał w pionie skalę 180-grubość płyty(1,8) = 172,2, pomiędzy T03_interior analogicznie do regałów jest obiekt T03_horizontal_stripe, który jest "opaską" udającą krawędź półki, a także T03_vertical_stripe (to samo, tylko w pionie)
-jest jeszcze T03_bar - bar skalujemy tylko w jedej osi - jak drążek ma 80cm to skala X=80, z tym samym picotem jest T03_bar_shadow, który skalujemy prawie tak samo, ale powinniśmy odjąć pewnie 2mm, żeby nie było z-fightów - czyli jak drążek jest 80cm to T03_bar_shadow skala X=79,8 (to jest do wytestowania, bo może musi być większa różnica)
-T03_handle i T03_handle shadow, nie skalujemy tylko ustawiamy w odpowiednim miejscu


T03_interior = to stary shadow box
T03_vertical_stripe = to stary vertical, zwany w Production Systemie: Slab
T03_horizontal_stripe = to stary horizontal, zwany w Production Systemie: Wall
T03_handle i T03_handle_shadow = wiadomo klamka + cien do klamki,
T03_box = obj do ram i maskownicy, wszystkie elementy szuflad i drzwi
T03_bar, T03_bar_shadow = drążek + cień do niego…
T03_backpanel = to tylko tekstura na tył… czyli jakby część T03_interior od tyłu…

Cienie:
shadow_floor_M skalujesz na szerokość mebla minus 10 cm, a także na głębokość mebla (czyli mebel, który ma 180 cm i głębokość 53 przyjmuje skalę 17 na 5,3

analogicznie shadow_wall_M na szerokość mebel minus 10 cm, a na wysokość skala równa wysokości mebla