{% verbatim %}

<div class="background-overlay sa-cart-overlay z-30">
    <div class="sa-cart-overlay-inner">
        <div class="sa-cart-overlay-inner-box vertically-centered">
            <img data-pin-nopin="true" src class="sa-cart-overlay-thumbnail-image">
        </div>
    </div>
</div>
<div class="sa-cart-container assembly-cart luke-sidebar luke-sidebar-assembly flex flex-column z-40"
    data-script="sidebarCartHeight">
    <section class="sa-cart-section-header px-16 py-12">
        <h3 class="sa-cart-header-title sa-cart-column-30 text-black th-4-m">{{:trans_emily_cart_header_1_1}}</h3>

        <div class="sa-cart-column sa-cart-column-70">
            <span class="th-6-m text-grey-900">
                {{if cartItemsCount > 1 }}
                {{:cartItemsCount }} {{:trans_common_items}}
                {{else cartItemsCount == 1 }}
                1 {{:trans_common_item}}
                {{/if}}
            </span>
            <div class="sa-cart-position-right sa-cart-position-right--close">
                <img data-pin-nopin="true" class="sa-cart-header-exit" src="{{:static_header_exit}}" width="15"
                    height="15" alt="Close Cart">
            </div>
        </div>
    </section>

    {{if saleEnabled && cartRibbonEnabled}}
    <p class="sa-cart-ribbon px-16 mb-12 th-6-m" id="sa-cart-promo-bar-value">{{:trans_ribbon_promo_code_alert_1}}</p>
    {{/if}}

    {{if cartItemsCount > 0}}
    <section class="sa-cart-section-items px-16">
        <ul class="sa-cart-list">
            {{for cartItems}}
            <li class="sa-cart-item py-12">
                {{if shelf_type === 3 && #parent.parent.parent.data.waitingListExpireDate}}
                <p class="sa-cart-item__above-information bg-offwhite-600 text-offblack-600 normal-12 py-8 px-12 mb-12">
                    {{>#parent.parent.parent.data.trans_cwatwar_soldout_cart_edit_active}}
                    <strong class="bold-12"> {{>#parent.parent.parent.data.waitingListExpireDate}}</strong>
                </p>
                {{/if}}
                <div>
                    <div class="sa-cart-column sa-cart-column-30 text-center modifier-wrapper">
                        <a href="{{:itemUrl}}">
                            <div class="sa-cart-item-thumbnail">
                                <img data-pin-nopin="true" src="{{:itemImageUrl}}" width="100" height="75" alt=""
                                    class="sa-cart-item-thumbnail-image">
                                {{if itemFurnitureType !== 'samplebox'}}
                                <button class="modifier-button text-orange th-5-m bg-transparent">
                                    {{>#parent.parent.parent.data.trans_emily_cart_item_edit_buton_2 }}
                                </button>
                                {{/if}}
                            </div>
                        </a>
                    </div>

                    <div class="sa-cart-column sa-cart-column-70 pl-16">
                        <div class="cart-item-price-crossed-out mb-8">
                            {{if promotion > 0}}
                            <div
                                class="cart-item-price-crossed-out__container {{if is_strikethrough_promo === false }}cart-item-price-crossed-out__container--hidden{{/if}} mr-4">
                                <span class="text-orange th-6-m art-item-price-crossed-out__voucher px-4 mr-4">
                                    -{{:promotion}}%
                                </span>
                                <h5 class="cart-item-price-crossed-out__regular-price">
                                    {{:itemPriceWithoutDiscount}}
                                </h5>
                            </div>
                            {{/if}}
                            <h3 class="sa-cart-item-price text-black th-2-m">
                                {{if promotion > 0}}
                                <span
                                    class="cart-item-price-crossed-out__item-price {{if is_strikethrough_promo === false }}cart-item-price-crossed-out__item-price--hidden{{/if}}">{{:itemPriceRegionalized}}</span>
                                <span
                                    class="cart-item-price-crossed-out__item-price-withDiscount {{if is_strikethrough_promo === false }}cart-item-price-crossed-out__item-price--visible{{/if}}">{{:itemPriceWithoutDiscount}}</span>
                                {{else}}
                                <span>{{:itemPriceRegionalized}}</span>
                                {{/if}}
                            </h3>
                        </div>

                        {{if itemFurnitureType === 'samplebox'}}
                        <p class="text-black th-5-m">
                            {{:itemDescriptionMaterial}}
                        </p>
                        {{else}}
                        <p class="text-black th-5-m mb-8">
                            {{if shelf_type === 1}}
                            {{>#parent.parent.parent.parent.data.martin_filter_menu_section_4_label_2}}
                            {{else shelf_type === 0}}
                            {{>#parent.parent.parent.parent.data.t01v_pdp_explore_line}}
                            {{else shelf_type === 3}}
                            {{>#parent.parent.parent.parent.data.type03_wardrobe_sidecart_name}}
                            {{else }}
                            {{>#parent.parent.parent.parent.data.veneer_shelf_name}}
                            {{/if}}
                            <span class="text-capitalize">{{:translated_category_display}}</span>
                        </p>
                        <p class="text-black th-6-m mb-4">
                            {{>#parent.parent.parent.data.trans_emily_cart_item_load_header}}: {{:itemMaxLoad}} kg
                        </p>
                        {{/if}}
                        {{if itemFurnitureType !== 'samplebox'}}
                        <p class="text-black th-6-m">
                            {{:itemDescriptionDimensions}}
                        </p>
                        {{/if}}

                        <div class="sa-cart-position-right">
                            <a href="#" data-shop-trigger='{"action": "dropAsk"}'
                                data-target-action='{"action": "dropProduct", "id": "{{:itemId }}", "type": "{{:itemFurnitureType }}"}'
                                data-target-popup='#merge-popup'>
                                <img data-pin-nopin="true" class="sa-cart-item-delete"
                                    src="{{>#parent.parent.data.static_trash2}}" width="24" height="24"
                                    alt="Delete Item">
                            </a>
                        </div>
                    </div>
                </div>
            </li>
            {{/for}}
        </ul>
    </section>

    <section class="sa-cart-section-bottom px-16 pt-16 lg:pt-0 pb-12 md:pb-4">
        <div class="sa-cart-benefit mobile-hidden mb-8 mt-16">
            <img data-pin-nopin="true" src="{{:static_free_delivery}}" width="48" height="48" alt=""
                class="sa-cart-benefit-image">
            <p class="sa-cart-benefit-description normal-12 text-grey-900">{{:trans_emily_cart_icon_1_header_2}}</p>
        </div>

        <div class="sa-cart-price mobile-hidden mb-8">
            <div class="sa-cart-column sa-cart-column-30">
                <h3 class="sa-cart-summary-heading th-4-m">{{:trans_emily_cart_header_3_1}}</h3>
            </div>
            <div class="sa-cart-column sa-cart-column-70 text-right">
                <p class="sa-cart-summary-heading th-3-m sa-cart-price-value">
                    {{:cartTotalPriceBeforeDiscountRegionalized}}</p>
            </div>

        </div>

        {{if is_sku_available}}
        <div class="sa-cart-delivery mobile-hidden mb-8">
            <div class="sa-cart-column sa-cart-column-50">
                <h3 class="sa-cart-summary-heading th-4-m">
                    {{:trans_emily_cart_header_3_2}}
                    {{if !is_sku}}
                    <p class="pdp-2018-tooltip-wrapper inline">
                        <span class="question-mark pdp-2018-tooltip-trigger" data-tooltip-trigger="assembly">
                            <span class="pdp-2018-tooltip position-top text-grey-900" id="assembly">
                                {{:vueCheckout_sku_tooltip_html}}
                            </span>
                        </span>
                    </p>
                    {{/if}}
                </h3>
                {{if is_sku}}<p class="th-6-m text-grey-900">{{:vueCheckout_sku_heading_html}}</p>{{/if}}
            </div>
            <div class="sa-cart-column sa-cart-column-50 text-right">
                <p class="sa-cart-summary-heading th-7-m">{{:trans_emily_cart_header_3_3}}</p>
            </div>
        </div>
        {{else}}
        <div class="sa-cart-delivery mobile-hidden mb-8">
            <div class="sa-cart-column sa-cart-column-30">
                <h3 class="sa-cart-summary-heading th-4-m">{{:trans_emily_cart_header_3_2}}</h3>
            </div>
            <div class="sa-cart-column sa-cart-column-70 text-right">
                <p class="sa-cart-summary-heading th-7-m">{{:trans_emily_cart_header_3_3}}</p>
            </div>
        </div>
        {{/if}}

        <div class="sa-cart-wardrobe-assembly mb-8">
            <div class="sa-cart-column sa-cart-column-70">
                <h3 class="sa-cart-summary-heading th-4-m">{{:wardrobe_assembly}}</h3>
            </div>
            <div class="sa-cart-column sa-cart-column-30 text-right">
                <p class="sa-cart-summary-heading th-7-m">{{:trans_emily_cart_header_3_3}}</p>
            </div>
        </div>

        <div class="sa-cart-border-bottom"></div>

        <div class="sa-cart-services" data-script="Tooltip">
            <ul class="services-list">
                {{if region_name === 'netherlands' && !cartUsedAssembly && is_fast_track_available}}
                <li class="services-list-item fasttrack-wrapper">
                    <div class="sa-cart-add-fasttrack-wrapper">
                        <button class="sa-cart-add-fasttrack" data-script="PopupTrigger">
                            <span class="plus-icon mr-8"></span>
                            <p class="fasttrack-item-text mr-8 th-4-m py-16 text-black">
                                {{:fasttrack_service_cart_button}}
                            </p>
                        </button>
                        {{if is_mobile }}
                        <p class="pdp-2018-tooltip-wrapper text-center mobile-fasttrack-tooltip">
                            <span class="question-mark pdp-2018-tooltip-wrapper"></span>
                        </p>
                        {{else}}
                        <p class="pdp-2018-tooltip-wrapper">
                            <span class="question-mark pdp-2018-tooltip-trigger" data-tooltip-trigger="fasttrack">
                                <span class="pdp-2018-tooltip position-top text-grey-900" id="fasttrack">
                                    <span id="fasttrack-available">
                                        {{:fasttrack_service_available_tooltip}}
                                    </span>
                                    <span id="fasttrack-unavailable">
                                        {{:fasttrack_service_unavailable_tooltip}}
                                    </span>
                                </span>
                            </span>
                        </p>
                        {{/if}}
                    </div>
                    <div class="sa-cart-fasttrack">
                        <div class="remove-fasttrack-wrapper">
                            <button class="sa-cart-remove-fasttrack">
                                <span class="plus-icon mr-8 sa-cart-remove-fasttrack-image"></span>
                            </button>
                            <p class="text-black mr-8 th-4-m py-16">
                                {{:fasttrack_service_cart_shelf_assembly_2}}
                            </p>
                            {{if is_mobile }}
                            <p class="pdp-2018-tooltip-wrapper text-center mobile-fasttrack-tooltip">
                                <span class="question-mark pdp-2018-tooltip-wrapper"></span>
                            </p>
                            {{else}}
                            <p class="pdp-2018-tooltip-wrapper">
                                <span class="question-mark pdp-2018-tooltip-trigger" data-tooltip-trigger="fasttrack">
                                    <span class="pdp-2018-tooltip position-top text-grey-900" id="fasttrack">
                                        {{:fasttrack_service_available_tooltip}}
                                    </span>
                                </span>
                            </p>
                            {{/if}}
                        </div>
                        <span class="text-black th-3-m">
                            <span class="sa-cart-fasttrack-price"></span>
                        </span>
                    </div>
                </li>
                {{/if}}

                {{if is_assembly && hasAssemblyPossible}}
                <li class="services-list-item assembly-wrapper">
                    <div class="sa-cart-add-assembly-wrapper">
                        <button class="sa-cart-add-assembly" data-script="PopupTrigger">
                            <span class="plus-icon mr-8"></span>
                            <p class="assembly-item-text text-black mr-8 th-4-m py-16">
                                {{:dorothy_shelf_assembly_service_cart_button}}
                            </p>
                        </button>
                        {{if is_mobile }}
                        <p class="pdp-2018-tooltip-wrapper text-center mobile-assembly-tooltip">
                            <span class="question-mark pdp-2018-tooltip-wrapper"></span>
                        </p>
                        {{else}}
                        <p class="pdp-2018-tooltip-wrapper">
                            <span class="question-mark pdp-2018-tooltip-trigger" data-tooltip-trigger="assembly">
                                <span class="pdp-2018-tooltip position-top text-grey-900" id="assembly">
                                    {{:dorothy_assembly_service_cart_tooltip_1}}
                                    <a href="{{:url_faq}}" target="_blank"
                                        class="text-orange">{{:dorothy_assembly_service_cart_popup_text_1_2}}</a>
                                    <span class="sa-cart-wardrobe-assembly-info">
                                        <br><br>
                                        {{:dorothy_assembly_service_cart_popup_text_1_3}}
                                    </span>
                                </span>
                            </span>
                        </p>
                        {{/if}}
                    </div>

                    <div class="sa-cart-assembly">
                        <div class="remove-assembly-wrapper">
                            <button class="sa-cart-remove-assembly">
                                <span class="plus-icon mr-8 sa-cart-remove-assembly-image"></span>
                            </button>
                            <p class="text-black mr-8 th-4-m py-16">
                                {{:dorothy_assembly_service_cart_shelf_assembly_2}}
                            </p>
                            {{if is_mobile }}
                            <p class="pdp-2018-tooltip-wrapper text-center mobile-assembly-tooltip">
                                <span class="question-mark pdp-2018-tooltip-wrapper"></span>
                            </p>
                            {{else}}
                            <p class="pdp-2018-tooltip-wrapper">
                                <span class="question-mark pdp-2018-tooltip-trigger" data-tooltip-trigger="assembly">
                                    <span class="pdp-2018-tooltip position-top text-grey-900" id="assembly">
                                        {{:dorothy_assembly_service_cart_tooltip_1}}
                                        <a href="{{:url_faq}}" target="_blank"
                                            class="text-orange">{{:dorothy_assembly_service_cart_popup_text_1_2}}</a>
                                        <span class="sa-cart-wardrobe-assembly-info">
                                            <br><br>
                                            {{:dorothy_assembly_service_cart_popup_text_1_3}}
                                        </span>
                                    </span>
                                </span>
                            </p>
                            {{/if}}
                        </div>
                        <span class="text-black th-3-m">
                            <span class="sa-cart-assembly-price"></span>
                        </span>
                    </div>
                </li>
                {{/if}}
                <li
                    class="services-list-item sa-cart-promocode {{if cartUsedPromo }}sa-cart-promocode-visible-result{{/if}}">
                    <div class="sa-cart-promocode-toggle-wrapper">
                        <div class="sa-cart-promocode-toggle py-16">
                            <span class="plus-icon mr-8"></span>
                            <p class="sa-cart-promocode-icon-text th-4-m">
                                {{:trans_emily_cart_header_4_1}}
                            </p>
                        </div>
                    </div>
                    <div class="sa-cart-promocode-entry-wrapper">
                        <div class="sa-cart-promocode-entry sa-cart-mobile-popup-container luke-promocode-entry py-16">
                            <div class="sa-cart-mobile-popup-overlay">
                                <div class="sa-cart-mobile-popup-body flex">
                                    <label class="visually-hidden" for="promo" required>{{:trans_Promo_code}}</label>
                                    <input id="promo" class="sa-cart-promocode-input pl-16 mr-8" type="text"
                                        tabindex="-1" name="sa-cart-promocode-input"
                                        placeholder="{{:trans_emily_cart_input_code}}">
                                    <input type="hidden" id="order_actual_value" name='ignored_value'
                                        value="{{:cartTotalPriceBeforeDiscountRounded}}" />
                                    <button class="sa-cart-promocode-input-button btn-cta btn-cta--border" tabindex="-1"
                                        type="button" data-shop-trigger='{"action": "sidebarCartCheckPromo"}'>
                                        {{:trans_emily_cart_buton_2}}
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="sa-cart-promocode-result-wrapper">
                        <div class="sa-cart-promocode-result py-16">
                            <div
                                class="sa-cart-column remove-wrapper sa-cart-column-80 sa-cart-column--promo-container">
                                <span class="plus-icon mr-8 sa-cart-promocode-icon-remove"
                                    data-shop-trigger='{"action": "sidebarCartDropPromo"}'></span>
                                <p class="sa-cart-promocode-icon-text th-4-m">
                                    <span
                                        class="sa-cart-promocode-code-title">{{:trans_emily_cart_header_4_2}}&nbsp;</span>
                                    <span class="text-orange text-uppercase" id="promo-text">{{if promocodeName
                                        }}{{:promocodeName}}{{/if}}</span>
                                </p>
                            </div>
                            <div
                                class="sa-cart-column sa-cart-column-30 text-right sa-cart-no-gutter-mobile sa-cart-column--promo">
                                <p class="sa-cart-promocode-value th-3-m text-black" id="promo-value">
                                    {{:promocodeValueRegionalized}}
                                </p>
                            </div>
                        </div>
                    </div>
                </li>
            </ul>
        </div>

        <div class="sa-cart-total mb-16 mt-8">
            {{if region_name === 'france'}}<div class="sa-cart-column sa-cart-column-60">{{else}}<div
                    class="sa-cart-column sa-cart-column-50">{{/if}}
                    <h3 class="sa-cart-summary-heading normal-16">
                        <span class="th-7-m text-black mr-4">{{:trans_emily_cart_header_3_4}}</span>
                        {{if region_name === 'france'}}
                        <span class="th-6-m text-grey-900">{{:scart_eco_tax_desc}}</span>
                        <span
                            class="sa-cart-recycle-tax normal-12 text-orange text-underline cursor-pointer text-nowrap">{{:scart_eco_tax}}</span>
                        {{else}}
                        <span class="th-6-m text-grey-900">{{:trans_emily_cart_header_5_2}}</span>
                        {{/if}}
                    </h3>
                </div>{{if region_name === 'france'}}<div class="sa-cart-column sa-cart-column-40 text-right">{{else}}
                    <div class="sa-cart-column sa-cart-column-50 text-right">{{/if}}
                        <p class="jsa-cart-total-value sa-cart-summary-heading th-7-m text-black sa-cart-total-value"
                            id="total-value">
                            {{:cartTotalPriceRegionalized}}
                        </p>
                    </div>
                </div>
                <div class="s-cart-button text-center mb-16">
                    <a href="{{:url_front_checkout}}"
                        class="btn-cta w-full border-box">{{:trans_emily_cart_buton_1}}</a>
                </div>
                <div class="s-cart-reffer" id="targetForMM"></div>
    </section>

    {{else}}
    <!-- if cart is empty -->
    <div class="sa-cart-no-items-box px-16">
        <div class="sa-cart-container-vertically-centered">
            <p class="text-center">{{:trans_bubble_cart_empty}}<br>{{:trans_bubble_empty_add_new_designs}}</p>
            <a href="{{:url_front_products}}" class="btn-cta w-full border-box sa-cart-no-items-button mt-16">
                {{:trans_emily_cart_buton_3}}
            </a>
        </div>
    </div>
    {{/if}}
</div>

{% endverbatim %}