# Generated by Django 1.11.29 on 2021-03-22 15:35
from django.db import (
    migrations,
    models,
)

import gallery.enums


def set_watty_configurator_mixed(apps, schema_editor):
    Watty = apps.get_model('gallery', 'Watty')

    Watty.objects.update(
        configurator_type=gallery.enums.ConfiguratorTypeEnum.MIXED_ROW_COLUMN,
    )


def revert_watty_configurator_column(apps, schema_editor):
    Watty = apps.get_model('gallery', 'Watty')

    Watty.objects.update(
        configurator_type=gallery.enums.ConfiguratorTypeEnum.COLUMN,
    )


class Migration(migrations.Migration):

    dependencies = [
        ('gallery', '0035_remove_preset_order_field'),
    ]

    operations = [
        migrations.AlterField(
            model_name='customdna',
            name='configurator_type',
            field=models.PositiveSmallIntegerField(
                choices=[(1, 'ROW'), (2, 'COLUMN'), (3, 'MIXED_ROW_COLUMN')],
                default=gallery.enums.ConfiguratorTypeEnum(1),
            ),
        ),
        migrations.AlterField(
            model_name='jetty',
            name='configurator_type',
            field=models.PositiveSmallIntegerField(
                choices=[(1, 'ROW'), (2, 'COLUMN'), (3, 'MIXED_ROW_COLUMN')],
                default=gallery.enums.ConfiguratorTypeEnum(1),
            ),
        ),
        migrations.AlterField(
            model_name='watty',
            name='configurator_type',
            field=models.PositiveSmallIntegerField(
                choices=[(1, 'ROW'), (2, 'COLUMN'), (3, 'MIXED_ROW_COLUMN')],
                default=gallery.enums.ConfiguratorTypeEnum(3),
            ),
        ),
        migrations.RunPython(
            set_watty_configurator_mixed,
            revert_watty_configurator_column,
            elidable=True,
        ),
    ]
