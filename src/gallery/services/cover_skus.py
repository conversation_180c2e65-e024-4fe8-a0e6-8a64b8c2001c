from gallery.enums import SottyModuleType
from gallery.models import Sotty


class SottySingleModuleRepository:
    def __init__(self, sotty: Sotty, use_covers: bool):
        self.sotty = sotty
        self.module_type = self.get_module_type(sotty)
        if use_covers:
            self.presets = Sotty.objects.get_cover_presets()

    @staticmethod
    def get_module_type(sotty: Sotty) -> SottyModuleType:
        return sotty.get_single_module_types()[0]

    def get_attributes_map(self) -> dict[str, dict[int, int | None]]:
        """
        Returns a map for every attribute value to ids of a preset
        that share other attributes with the given sotty.

        Example:
        {
            'width': {
                100: 1,
                120: 2,
                140: 3,
            },
            'depth': {
                100: 4,
                120: 5,
                140: 6,
            },
        }
        """
        attributes = self.get_attributes()
        attributes_map = {}
        for attribute in attributes:
            pass
            # attributes_map[attribute] = self._get_attribute_map(attribute)
        return attributes_map
