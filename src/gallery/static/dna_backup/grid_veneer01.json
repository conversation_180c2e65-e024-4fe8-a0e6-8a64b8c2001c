{"superior_object_type": "container", "superior_object_ids": [1], "superior_object_line": 2, "serialized_at": "2020-08-17T09:52:56.992304", "serialization": {"container": {"1": {"setups": {"single-setup": {"configs": [{"parameters": {"config_id": 1, "mesh_id": 2169, "height": 300, "mesh_setup": null, "row_style_table": {"11": {"#open_a": "O", "#open_b": "O"}, "12": {"#open_a": "O", "#open_b": "D"}, "13": {"#open_a": "D", "#open_b": "D"}, "21": {"#open_a": "O", "#open_b": "T"}, "22": {"#open_a": "T", "#open_b": "D"}, "31": {"#open_a": "T", "#open_b": "T"}}}}], "parameters": {"density_mode": "grid", "container_mode": "old_configurator"}}}, "parameters": {"density_mode": "grid", "container_mode": "old_configurator"}}}, "mesh": {"2169": {"presets": {}, "setups": {"2": {"configs": [{"parameters": {"config_id": 70025, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70026, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}], "parameters": {"density_mode": "setup_range_slider", "plinth__height": "0", "distortion_available": false, "setup_id": 14574, "dim_x": [682, 1150]}}, "3": {"configs": [{"parameters": {"config_id": 70027, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70028, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70029, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}], "parameters": {"density_mode": "setup_range_slider", "plinth__height": "0", "distortion_available": false, "setup_id": 14575, "dim_x": [861, 1720]}}, "4": {"configs": [{"parameters": {"config_id": 70030, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70031, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back_a": "support_none", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70032, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back_a": "support_none", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70033, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}], "parameters": {"density_mode": "setup_range_slider", "plinth__height": "0", "distortion_available": false, "setup_id": 14576, "dim_x": [1141, 2290]}}, "5": {"configs": [{"parameters": {"config_id": 70034, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70035, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back_a": "support_none", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70036, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back_b": "support_none", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70037, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back_a": "support_none", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70038, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}], "parameters": {"density_mode": "setup_range_slider", "plinth__height": "0", "distortion_available": false, "setup_id": 14577, "dim_x": [1421, 2550]}}, "6": {"configs": [{"parameters": {"config_id": 70039, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70040, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70041, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_back_b": "support_none", "#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70042, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70043, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}], "parameters": {"density_mode": "setup_range_slider", "plinth__height": "0", "distortion_available": false, "setup_id": 14578, "dim_x": [2551, 2850]}}, "7": {"configs": [{"parameters": {"config_id": 70044, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70045, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70046, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70047, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70048, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70049, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}], "parameters": {"density_mode": "setup_range_slider", "plinth__height": "0", "distortion_available": false, "setup_id": 14579, "dim_x": [1721, 2550]}}, "8": {"configs": [{"parameters": {"config_id": 70050, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70051, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70052, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70053, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70054, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70055, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}], "parameters": {"density_mode": "setup_range_slider", "plinth__height": "0", "distortion_available": false, "setup_id": 14580, "dim_x": [2551, 2790]}}, "9": {"configs": [{"parameters": {"config_id": 70056, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70057, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70058, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70059, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70060, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70061, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}], "parameters": {"density_mode": "setup_range_slider", "plinth__height": "0", "distortion_available": false, "setup_id": 14581, "dim_x": [2791, 3410]}}, "10": {"configs": [{"parameters": {"config_id": 70062, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70063, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70064, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70065, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70066, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70067, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70068, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 7, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}], "parameters": {"density_mode": "setup_range_slider", "plinth__height": "0", "distortion_available": false, "setup_id": 14582, "dim_x": [1991, 2590]}}, "11": {"configs": [{"parameters": {"config_id": 70069, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70070, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70071, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70072, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70073, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70074, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70075, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 7, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}], "parameters": {"density_mode": "setup_range_slider", "plinth__height": "0", "distortion_available": false, "setup_id": 14583, "dim_x": [2591, 3990]}}, "12": {"configs": [{"parameters": {"config_id": 70076, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70077, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70078, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70079, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70080, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70081, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70082, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 7, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70083, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 8, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}], "parameters": {"density_mode": "setup_range_slider", "plinth__height": "0", "distortion_available": false, "setup_id": 14584, "dim_x": [2291, 2600]}}, "13": {"configs": [{"parameters": {"config_id": 70084, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70085, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70086, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70087, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70088, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70089, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70090, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 7, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70091, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 8, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}], "parameters": {"density_mode": "setup_range_slider", "plinth__height": "0", "distortion_available": false, "setup_id": 14585, "dim_x": [2601, 4500]}}, "14": {"configs": [{"parameters": {"config_id": 70092, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70093, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70094, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70095, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_back": 2, "#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70096, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70097, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70098, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 7, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70099, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 8, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70100, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 9, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_back": "3 ", "#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}], "parameters": {"density_mode": "setup_range_slider", "plinth__height": "0", "distortion_available": false, "setup_id": 14586, "dim_x": [2551, 2780]}}, "15": {"configs": [{"parameters": {"config_id": 70101, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70102, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70103, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_back": 3, "#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70104, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70105, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70106, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70107, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 7, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70108, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 8, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70109, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 9, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_back": "3 ", "#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}], "parameters": {"density_mode": "setup_range_slider", "plinth__height": "0", "distortion_available": false, "setup_id": 14587, "dim_x": [2781, 4250]}}, "16": {"configs": [{"parameters": {"config_id": 70110, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_back": 2, "#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70111, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70112, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70113, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70114, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70115, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70116, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 7, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70117, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 8, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70118, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 9, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70119, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 10, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}], "parameters": {"density_mode": "setup_range_slider", "plinth__height": "0", "distortion_available": false, "setup_id": 14588, "dim_x": [2841, 3980]}}, "17": {"configs": [{"parameters": {"config_id": 70120, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_back": 2, "#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70121, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70122, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70123, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70124, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70125, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70126, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 7, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70127, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 8, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70128, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 9, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70129, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 10, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}], "parameters": {"density_mode": "setup_range_slider", "plinth__height": "0", "distortion_available": false, "setup_id": 14589, "dim_x": [3981, 4500]}}, "18": {"configs": [{"parameters": {"config_id": 70130, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_back": 2, "#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70131, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70132, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70133, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70134, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70135, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70136, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 7, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70137, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 8, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70138, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 9, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70139, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 10, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70140, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 11, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}], "parameters": {"density_mode": "setup_range_slider", "plinth__height": "0", "distortion_available": false, "setup_id": 14590, "dim_x": [3111, 4390]}}, "19": {"configs": [{"parameters": {"config_id": 70141, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_back": 2, "#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70142, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70143, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70144, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70145, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70146, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70147, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 7, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70148, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 8, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70149, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 9, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70150, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 10, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70151, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 11, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70152, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 12, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}], "parameters": {"density_mode": "setup_range_slider", "plinth__height": "0", "distortion_available": false, "setup_id": 14591, "dim_x": [3401, 4500]}}, "20": {"configs": [{"parameters": {"config_id": 70153, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_back": 2, "#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70154, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70155, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70156, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70157, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70158, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70159, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 7, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70160, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 8, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70161, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 9, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70162, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 10, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70163, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 11, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70164, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 12, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70165, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 13, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}], "parameters": {"density_mode": "setup_range_slider", "plinth__height": "0", "distortion_available": false, "setup_id": 14592, "dim_x": [3681, 4500]}}, "21": {"configs": [{"parameters": {"config_id": 70166, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_back": 2, "#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70167, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70168, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70169, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70170, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70171, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70172, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 7, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70173, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 8, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70174, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 9, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70175, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 10, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70176, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 11, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70177, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 12, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70178, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 13, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70179, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 14, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}], "parameters": {"density_mode": "setup_range_slider", "plinth__height": "0", "distortion_available": false, "setup_id": 14593, "dim_x": [3981, 4500]}}, "22": {"configs": [{"parameters": {"config_id": 70180, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_back": 2, "#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70181, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70182, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70183, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70184, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70185, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70186, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 7, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70187, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 8, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70188, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 9, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70189, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 10, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70190, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 11, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_back": 2, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70191, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 12, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70192, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 13, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_a", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70193, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 14, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}, {"parameters": {"config_id": 70194, "comp_id": 790, "component": null, "table": 790, "series_pick": 3021, "channel": 15, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_slider", "plinth__height": "0"}, "constants": {"#open_type": "#open_b", "#open_back": 3, "#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}], "parameters": {"density_mode": "setup_range_slider", "plinth__height": "0", "distortion_available": false, "setup_id": 14594, "dim_x": [4241, 4500]}}}, "parameters": {"density_mode": "setup_range_slider", "plinth__height": "0", "size_y": [300, 400, 500, 600, 700, 800], "size_x": [1200, 4500], "object_type": 1}, "constants": {"#open_a": 0, "#open_b": 0, "#object_type": 1, "pattern": null}}}, "component_table": {"790": {"configs": {"grid": 3021}}}, "component_series": {"3021": {"setups": {"300": 39455}, "parameters": {"size_y": [300], "name": "grid", "series_id": 3021}}}, "component": {"39455": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101708, "type": "#open_type", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": "#open_h", "face__s_back": "#open_back", "dim_x": "100-1000", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}, "constants": {"#open_h": 200, "#open_type": 0, "#open_back": 0}}], "parameters": {"dim_x": "100-1000", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}}}, "configurator_data": {"table": {"790": {"300": [{"series_id": 3021, "component_id": 39455, "series_name": "grid", "series_groups": "", "order": 0, "inconsequent": false}]}}, "component": {"39455": {"id": 39455, "name": "grid - 1"}}}, "superior_object_collection": "Sideboard"}