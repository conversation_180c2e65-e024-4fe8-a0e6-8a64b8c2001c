{"superior_object_type": "mesh", "superior_object_ids": [2174], "superior_object_line": 0, "serialized_at": "2021-02-09T15:21:19.172792", "serialization": {"mesh": {"2174": {"presets": {"666": {"id": 6227, "image": "/media/mesh_presets/76661d8f-fbbd-4b92-9f5f-61c0e1fc1889.webp", "geom_id": null, "depth": 400, "height": 600, "width": 2240, "density": 0, "distortion": 0, "plinth": true, "configurator_custom_params": {"lines": {"1_2": 0, "2_3": 0, "3_4": 0}, "setups": {"undefined": {"70370": {"cables": false, "door_flip": null, "series_id": 3039, "distortion": {}}, "70371": {"cables": false, "door_flip": null, "series_id": 3042, "distortion": {}}, "70372": {"cables": false, "door_flip": null, "series_id": 3039, "distortion": {}}, "70373": {"cables": false, "door_flip": "right", "series_id": 3037, "distortion": {}}}}, "channels": {"1": {"cables": false, "door_flip": null, "series_id": 3039, "distortion": {}}, "2": {"cables": false, "door_flip": null, "series_id": 3042, "distortion": {}}, "3": {"cables": false, "door_flip": null, "series_id": 3039, "distortion": {}}, "4": {"cables": false, "door_flip": null, "series_id": 3037, "distortion": {}}}}, "owner": "stanislaw<PERSON>s<PERSON>", "rating": 0, "comment": "", "tags": ""}}, "setups": {"2": {"configs": [{"parameters": {"config_id": 70365, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 1, "table_dim_x": "320-840", "division_ratio": "640x", "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70366, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 2, "table_dim_x": "320-840", "division_ratio": "720x", "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}], "parameters": {"distortion_mode": "edge", "distortion_available": false, "setup_id": 14637, "dim_x": [1100, 1480]}}, "3": {"configs": [{"parameters": {"config_id": 70367, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 1, "table_dim_x": "320-840", "division_ratio": 478, "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70368, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70369, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}], "parameters": {"distortion_mode": "edge", "distortion_available": false, "setup_id": 14638, "dim_x": [1481, 1904]}}, "4": {"configs": [{"parameters": {"config_id": 70370, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 1, "table_dim_x": "320-840", "division_ratio": 478, "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70371, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 2, "table_dim_x": "320-840", "division_ratio": 708, "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70372, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70373, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}], "parameters": {"distortion_mode": "edge", "distortion_available": false, "setup_id": 14639, "dim_x": [1905, 2407]}}, "5": {"configs": [{"parameters": {"config_id": 70374, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 1, "table_dim_x": "320-840", "division_ratio": 478, "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70375, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 2, "table_dim_x": "320-840", "division_ratio": 708, "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70376, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 3, "table_dim_x": "320-840", "division_ratio": 478, "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70377, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70378, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}], "parameters": {"distortion_mode": "edge", "distortion_available": false, "setup_id": 14640, "dim_x": [2408, 3037]}}, "6": {"configs": [{"parameters": {"config_id": 70379, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 1, "table_dim_x": "320-840", "division_ratio": 478, "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70380, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 2, "table_dim_x": "320-840", "division_ratio": 708, "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70381, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 3, "table_dim_x": "320-840", "division_ratio": 478, "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70382, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 4, "table_dim_x": "320-840", "division_ratio": 638, "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70383, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70384, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}], "parameters": {"distortion_mode": "edge", "distortion_available": false, "setup_id": 14641, "dim_x": [3038, 3655]}}, "7": {"configs": [{"parameters": {"config_id": 70385, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 1, "table_dim_x": "320-840", "division_ratio": 478, "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70386, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 2, "table_dim_x": "320-840", "division_ratio": 708, "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70387, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 3, "table_dim_x": "320-840", "division_ratio": 478, "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70388, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 4, "table_dim_x": "320-840", "division_ratio": 638, "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70389, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 5, "table_dim_x": "320-840", "division_ratio": 638, "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70390, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70391, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 7, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}], "parameters": {"distortion_mode": "edge", "distortion_available": false, "setup_id": 14642, "dim_x": [3656, 4043]}}, "8": {"configs": [{"parameters": {"config_id": 70392, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 1, "table_dim_x": "320-840", "division_ratio": 478, "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70393, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 2, "table_dim_x": "320-840", "division_ratio": 708, "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70394, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 3, "table_dim_x": "320-840", "division_ratio": 478, "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70395, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 4, "table_dim_x": "320-840", "division_ratio": 638, "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70396, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 5, "table_dim_x": "320-840", "division_ratio": 638, "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70397, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 6, "table_dim_x": "320-840", "division_ratio": 388, "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70398, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 7, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}, {"parameters": {"config_id": 70399, "comp_id": 794, "component": null, "table": 794, "series_pick": 3042, "channel": 8, "table_dim_x": "320-840", "division_ratio": "1x", "distortion_mode": "edge"}, "constants": {"#object_type": 0, "pattern": 1}}], "parameters": {"distortion_mode": "edge", "distortion_available": false, "setup_id": 14643, "dim_x": [4044, 4500]}}}, "parameters": {"distortion_mode": "edge", "size_y": [300, 400, 500, 600, 700, 800], "size_x": [1100, 4500], "object_type": 0}, "constants": {"#object_type": 0, "pattern": 1}}}, "component_table": {"794": {"configs": {"mix": 3041, "drawer": 3039, "insert": 3038, "expo": 3040, "open": 3042, "door": 3037}}}, "component_series": {"3037": {"setups": {"300": 39543, "400": 39546, "500": 39552, "600": 39558, "700": 39564, "800": 39570}, "parameters": {"size_y": [800, 700, 600, 500, 400, 300], "name": "door", "series_id": 3037}}, "3038": {"setups": {"300": 39545, "400": 39547, "500": 39553, "600": 39559, "700": 39565, "800": 39571}, "parameters": {"size_y": [800, 700, 600, 500, 400, 300], "name": "insert", "series_id": 3038}}, "3039": {"setups": {"300": 39544, "400": 39549, "500": 39554, "600": 39560, "700": 39566, "800": 39572}, "parameters": {"size_y": [800, 700, 600, 500, 400, 300], "name": "drawer", "series_id": 3039}}, "3040": {"setups": {"300": 39545, "400": 39550, "500": 39555, "600": 39561, "700": 39567, "800": 39573}, "parameters": {"size_y": [800, 700, 600, 500, 400, 300], "name": "expo", "series_id": 3040}}, "3041": {"setups": {"300": 39544, "400": 39548, "500": 39556, "600": 39562, "700": 39568, "800": 39574}, "parameters": {"size_y": [800, 700, 600, 500, 400, 300], "name": "mix", "series_id": 3041}}, "3042": {"setups": {"300": 39545, "400": 39551, "500": 39557, "600": 39563, "700": 39569, "800": 39575}, "parameters": {"size_y": [800, 700, 600, 500, 400, 300], "name": "open", "series_id": 3042}}}, "component": {"39543": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101831, "type": "D", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39544": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101832, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39545": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101833, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39546": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101834, "type": "D", "e_id": 286, "cable__pos_y": 250, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": 200, "e_size_y": 400, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39547": {"configs": [{"subconfigs": [{"parameters": {"c_config_id": 101888, "type": "FB", "s_id": 286, "cable__pos_y": 50, "cable__pos_x": "1/4", "drawer_exterior": false, "drawer_autofill": false, "insert__dom_x": "1/2", "trans__start": 416, "trans__stop": 623, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"parameters": {"c_config_id": 101887, "type": "FB", "s_id": 286, "cable__pos_y": 50, "cable__pos_x": "1/6", "drawer_exterior": false, "drawer_autofill": false, "insert__dom_x": "1/3, 2/3", "trans__start": 624, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"c_config_id": 101835, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39548": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101836, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39549": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101838, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 101837, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39550": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101839, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39551": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 110587, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 101840, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39552": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101841, "type": "D", "e_id": 286, "cable__pos_y": 350, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": 300, "e_size_y": 500, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39553": {"configs": [{"subconfigs": [{"parameters": {"c_config_id": 101886, "type": "FB", "s_id": 286, "cable__pos_y": 50, "cable__pos_x": "1/4", "drawer_exterior": false, "drawer_autofill": false, "insert__dom_x": "1/2", "trans__start": 416, "trans__stop": 623, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"parameters": {"c_config_id": 101842, "type": "FB", "s_id": 286, "cable__pos_y": 50, "cable__pos_x": "1/6", "drawer_exterior": false, "drawer_autofill": false, "insert__dom_x": "1/3, 2/3", "trans__start": 624, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"c_config_id": 101843, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 500, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39554": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101845, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 101844, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39555": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101846, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 500, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39556": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101848, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 101847, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39557": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 110588, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 101849, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39558": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101850, "type": "D", "e_id": 286, "cable__pos_y": 450, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": 400, "e_size_y": 600, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39559": {"configs": [{"subconfigs": [{"parameters": {"c_config_id": 101885, "type": "FB", "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_x": "1/2", "trans__start": 416, "trans__stop": 623, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"parameters": {"c_config_id": 101884, "type": "FB", "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_x": "1/3, 2/3", "trans__start": 624, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"c_config_id": 101852, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 101851, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39560": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101854, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 101853, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39561": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101855, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 600, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39562": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101857, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 101856, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39563": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 110589, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 101858, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39564": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101859, "type": "D", "e_id": 286, "cable__pos_y": 450, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": 400, "e_size_y": 700, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39565": {"configs": [{"subconfigs": [{"parameters": {"c_config_id": 101881, "type": "FB", "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_x": "1/2", "trans__start": 416, "trans__stop": 623, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"parameters": {"c_config_id": 101880, "type": "FB", "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_x": "1/3, 2/3", "trans__start": 624, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"c_config_id": 101861, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 101860, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39566": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101863, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 101862, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39567": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101864, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 700, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39568": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101866, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 101865, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39569": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 110590, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 101867, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39570": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101868, "type": "D", "e_id": 286, "cable__pos_y": 450, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": 400, "e_size_y": 800, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39571": {"configs": [{"subconfigs": [{"parameters": {"c_config_id": 101882, "type": "FB", "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_x": "1/2", "trans__start": 416, "trans__stop": 623, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"parameters": {"c_config_id": 101883, "type": "FB", "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_x": "1/3, 2/3", "trans__start": 624, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"c_config_id": 101870, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [{"parameters": {"c_config_id": 101879, "type": "FB", "s_id": 286, "cable__pos_y": 50, "cable__pos_x": "1/4", "drawer_exterior": false, "drawer_autofill": false, "insert__dom_x": "1/2", "trans__start": 416, "trans__stop": 623, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"parameters": {"c_config_id": 101878, "type": "FB", "s_id": 286, "cable__pos_y": 50, "cable__pos_x": "1/6", "drawer_exterior": false, "drawer_autofill": false, "insert__dom_x": "1/3, 2/3", "trans__start": 624, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"c_config_id": 101869, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39572": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101872, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 101871, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39573": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101873, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 800, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39574": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 101875, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 101874, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "39575": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 110591, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 101876, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "40537": {"configs": [{"subconfigs": [{"parameters": {"c_config_id": 104621, "type": "FB", "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_x": "1/3, 2/3", "insert__dom_y": 400, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"c_config_id": 104620, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [{"parameters": {"c_config_id": 104619, "type": "FB", "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "face__s_bottom": 0, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"c_config_id": 104618, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-858", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}}}, "configurator_data": {"table": {"794": {"300": [{"series_id": 3041, "component_id": 39544, "series_name": "mix", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3039, "component_id": 39544, "series_name": "drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3038, "component_id": 39545, "series_name": "insert", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3040, "component_id": 39545, "series_name": "expo", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3042, "component_id": 39545, "series_name": "open", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3037, "component_id": 39543, "series_name": "door", "series_groups": "", "order": 0, "inconsequent": false}], "400": [{"series_id": 3041, "component_id": 39548, "series_name": "mix", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3039, "component_id": 39549, "series_name": "drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3038, "component_id": 39547, "series_name": "insert", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3040, "component_id": 39550, "series_name": "expo", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3042, "component_id": 39551, "series_name": "open", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3037, "component_id": 39546, "series_name": "door", "series_groups": "", "order": 0, "inconsequent": false}], "500": [{"series_id": 3041, "component_id": 39556, "series_name": "mix", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3039, "component_id": 39554, "series_name": "drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3038, "component_id": 39553, "series_name": "insert", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3040, "component_id": 39555, "series_name": "expo", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3042, "component_id": 39557, "series_name": "open", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3037, "component_id": 39552, "series_name": "door", "series_groups": "", "order": 0, "inconsequent": false}], "600": [{"series_id": 3041, "component_id": 39562, "series_name": "mix", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3039, "component_id": 39560, "series_name": "drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3038, "component_id": 39559, "series_name": "insert", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3040, "component_id": 39561, "series_name": "expo", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3042, "component_id": 39563, "series_name": "open", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3037, "component_id": 39558, "series_name": "door", "series_groups": "", "order": 0, "inconsequent": false}], "700": [{"series_id": 3041, "component_id": 39568, "series_name": "mix", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3039, "component_id": 39566, "series_name": "drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3038, "component_id": 39565, "series_name": "insert", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3040, "component_id": 39567, "series_name": "expo", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3042, "component_id": 39569, "series_name": "open", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3037, "component_id": 39564, "series_name": "door", "series_groups": "", "order": 0, "inconsequent": false}], "800": [{"series_id": 3041, "component_id": 39574, "series_name": "mix", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3039, "component_id": 39572, "series_name": "drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3038, "component_id": 39571, "series_name": "insert", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3040, "component_id": 39573, "series_name": "expo", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3042, "component_id": 39575, "series_name": "open", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3037, "component_id": 39570, "series_name": "door", "series_groups": "", "order": 0, "inconsequent": false}]}}, "component": {"39543": {"id": 39543, "name": "door - door"}, "39544": {"id": 39544, "name": "mix - drawer"}, "39545": {"id": 39545, "name": "insert - open"}, "39546": {"id": 39546, "name": "door - door"}, "39547": {"id": 39547, "name": "insert - insert"}, "39548": {"id": 39548, "name": "mix - drawer"}, "39549": {"id": 39549, "name": "drawer - drawer2"}, "39550": {"id": 39550, "name": "expo - expo"}, "39551": {"id": 39551, "name": "open - open"}, "39552": {"id": 39552, "name": "door - door"}, "39553": {"id": 39553, "name": "insert - insert"}, "39554": {"id": 39554, "name": "drawer - drawer"}, "39555": {"id": 39555, "name": "expo - expo"}, "39556": {"id": 39556, "name": "mix - mix"}, "39557": {"id": 39557, "name": "open - open"}, "39558": {"id": 39558, "name": "door - door"}, "39559": {"id": 39559, "name": "insert - insert"}, "39560": {"id": 39560, "name": "drawer - drawer"}, "39561": {"id": 39561, "name": "expo - expo"}, "39562": {"id": 39562, "name": "mix - mix"}, "39563": {"id": 39563, "name": "open - open"}, "39564": {"id": 39564, "name": "door - door"}, "39565": {"id": 39565, "name": "insert - insert"}, "39566": {"id": 39566, "name": "drawer - drawer"}, "39567": {"id": 39567, "name": "expo - expo"}, "39568": {"id": 39568, "name": "mix - mix"}, "39569": {"id": 39569, "name": "open - open"}, "39570": {"id": 39570, "name": "door - door"}, "39571": {"id": 39571, "name": "insert - insert"}, "39572": {"id": 39572, "name": "drawer - drawer"}, "39573": {"id": 39573, "name": "expo - expo"}, "39574": {"id": 39574, "name": "mix - mix"}, "39575": {"id": 39575, "name": "open - open"}}}, "superior_object_collection": "Sideboard"}