{"superior_object_type": "mesh", "superior_object_ids": [2296], "superior_object_line": 2, "serialized_at": "2021-02-09T15:21:15.180406", "serialization": {"mesh": {"2296": {"presets": {"666": {"id": 6222, "image": "/media/mesh_presets/b98dc3e1-5f80-434e-ba70-93793e30f0ec.webp", "geom_id": null, "depth": 400, "height": 600, "width": 2080, "density": 0, "distortion": 0, "plinth": true, "configurator_custom_params": {"lines": {"1_2": 0, "2_3": 0, "3_4": 0}, "setups": {"15763": {"76548": {"cables": false, "door_flip": null, "series_id": 3677, "distortion": {}}, "76549": {"cables": false, "door_flip": null, "series_id": 3585, "distortion": {}}, "76550": {"cables": false, "door_flip": "right", "series_id": 3592, "distortion": {}}, "76551": {"cables": false, "door_flip": "right", "series_id": 3584, "distortion": {}}}}, "channels": {"1": {"cables": false, "door_flip": null, "series_id": 3677, "distortion": {}}, "2": {"cables": false, "door_flip": null, "series_id": 3585, "distortion": {}}, "3": {"cables": false, "door_flip": "right", "series_id": 3592, "distortion": {}}, "4": {"cables": false, "door_flip": "right", "series_id": 3584, "distortion": {}}}}, "owner": "stanislaw<PERSON>s<PERSON>", "rating": 0, "comment": "", "tags": ""}}, "setups": {"2": {"configs": [{"parameters": {"config_id": 76543, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76544, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}], "parameters": {"density_mode": "setup_range_stepper", "distortion_available": false, "setup_id": 15761, "dim_x": [1100, 1154]}}, "3": {"configs": [{"parameters": {"config_id": 76545, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76546, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76547, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}], "parameters": {"density_mode": "setup_range_stepper", "distortion_available": false, "setup_id": 15762, "dim_x": [1155, 1722]}}, "4": {"configs": [{"parameters": {"config_id": 76548, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76549, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76550, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76551, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}], "parameters": {"density_mode": "setup_range_stepper", "distortion_available": false, "setup_id": 15763, "dim_x": [1170, 2290]}}, "5": {"configs": [{"parameters": {"config_id": 76552, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76553, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76554, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76555, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76556, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}], "parameters": {"density_mode": "setup_range_stepper", "distortion_available": false, "setup_id": 15764, "dim_x": [1458, 2858]}}, "6": {"configs": [{"parameters": {"config_id": 76557, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76558, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76559, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76560, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76561, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76562, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}], "parameters": {"density_mode": "setup_range_stepper", "distortion_available": false, "setup_id": 15765, "dim_x": [1746, 3426]}}, "7": {"configs": [{"parameters": {"config_id": 76541, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76542, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76563, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76564, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76565, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76566, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76567, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 7, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}], "parameters": {"density_mode": "setup_range_stepper", "distortion_available": false, "setup_id": 15766, "dim_x": [2034, 3994]}}, "8": {"configs": [{"parameters": {"config_id": 76568, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76569, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76570, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76571, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76572, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76573, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76574, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 7, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76575, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 8, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}], "parameters": {"density_mode": "setup_range_stepper", "distortion_available": false, "setup_id": 15767, "dim_x": [2322, 4500]}}, "9": {"configs": [{"parameters": {"config_id": 76576, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76577, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76578, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76579, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76580, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76581, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76582, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 7, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76583, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 8, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76584, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 9, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}], "parameters": {"density_mode": "setup_range_stepper", "distortion_available": false, "setup_id": 15768, "dim_x": [2610, 4500]}}, "10": {"configs": [{"parameters": {"config_id": 76585, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76586, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76587, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76588, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76589, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76590, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76591, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 7, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76592, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 8, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76593, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 9, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76594, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 10, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}], "parameters": {"density_mode": "setup_range_stepper", "distortion_available": false, "setup_id": 15769, "dim_x": [2898, 4500]}}, "11": {"configs": [{"parameters": {"config_id": 76595, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76596, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76597, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76598, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76599, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76600, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76601, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 7, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76602, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 8, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76603, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 9, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76604, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 10, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76605, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 11, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}], "parameters": {"density_mode": "setup_range_stepper", "distortion_available": false, "setup_id": 15770, "dim_x": [3186, 4500]}}, "12": {"configs": [{"parameters": {"config_id": 76606, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 1, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76607, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 2, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76608, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 3, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76609, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 4, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76610, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 5, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76611, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 6, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76612, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 7, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76613, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 8, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76614, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 9, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76615, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 10, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76616, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 11, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}, {"parameters": {"config_id": 76617, "comp_id": 894, "component": null, "table": 894, "series_pick": 3677, "channel": 12, "table_dim_x": "320-840", "division_ratio": "1x", "density_mode": "setup_range_stepper"}, "constants": {"#object_type": 2, "pattern": 3}}], "parameters": {"density_mode": "setup_range_stepper", "distortion_available": false, "setup_id": 15771, "dim_x": [3474, 4500]}}}, "parameters": {"density_mode": "setup_range_stepper", "size_y": [300, 400, 500, 600, 700, 800], "size_x": [1100, 4500], "object_type": 2}, "constants": {"#object_type": 2, "pattern": 3}}}, "component_table": {"894": {"configs": {"open_expo": 3677, "vinyl": 3592, "some_drawer": 3590, "mix": 3588, "drawer": 3585, "door": 3584}}}, "component_series": {"3584": {"setups": {"300": 42921, "400": 42924, "500": 42931, "600": 42937, "700": 42943, "800": 42951}, "parameters": {"size_y": [800, 700, 600, 500, 400, 300], "name": "door", "series_id": 3584}}, "3585": {"setups": {"300": 42922, "400": 42925, "500": 42932, "600": 42938, "700": 42944, "800": 42952}, "parameters": {"size_y": [800, 700, 600, 500, 400, 300], "name": "drawer", "series_id": 3585}}, "3588": {"setups": {"300": 42921, "400": 42928, "500": 42934, "600": 42940, "700": 42947, "800": 42955}, "parameters": {"size_y": [800, 700, 600, 500, 400, 300], "name": "mix", "series_id": 3588}}, "3590": {"setups": {"300": 42922, "400": 42930, "500": 42936, "600": 42942, "700": 42949, "800": 42957}, "parameters": {"size_y": [800, 700, 600, 500, 400, 300], "name": "some_drawer", "series_id": 3590}}, "3592": {"setups": {"300": 42923, "400": 42965, "500": 42964, "600": 42963, "700": 42962, "800": 42961}, "parameters": {"size_y": [800, 700, 600, 500, 400, 300], "name": "vinyl", "series_id": 3592}}, "3677": {"setups": {"300": 42923, "400": 42927, "500": 42935, "600": 42941, "700": 42948, "800": 42956}, "parameters": {"size_y": [800, 700, 600, 500, 400, 300], "name": "open_expo", "series_id": 3677}}}, "component": {"42921": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114085, "type": "D", "e_id": 286, "fill__split": 0, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42922": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114086, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42923": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114087, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42924": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114088, "type": "D", "e_id": 286, "fill__split": 0, "cable__pos_y": 250, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": 200, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42925": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114089, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42926": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114091, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114090, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42927": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114092, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42928": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114093, "type": "D", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42929": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114095, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114094, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42930": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114097, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114096, "type": "FB", "e_id": 286, "fill__split": 0, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42931": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114098, "type": "D", "e_id": 286, "fill__split": 0, "cable__pos_y": 350, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": 300, "e_size_y": 500, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42932": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114100, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114099, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42933": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114101, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 500, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42934": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114103, "type": "D", "e_id": 286, "fill__split": 0, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114102, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42935": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114105, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114104, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42936": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114107, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114106, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42937": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114108, "type": "D", "e_id": 286, "fill__split": 0, "cable__pos_y": 450, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": 400, "e_size_y": 600, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42938": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114110, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114109, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42939": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114111, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 600, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42940": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114113, "type": "D", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114112, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42941": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114115, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114114, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42942": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114117, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114116, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42943": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114118, "type": "D", "e_id": 286, "fill__split": 0, "cable__pos_y": 450, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": 400, "e_size_y": 700, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42944": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114120, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114119, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42945": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114123, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114122, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114121, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42946": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114124, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 700, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42947": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114126, "type": "D", "e_id": 286, "fill__split": 0, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114125, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42948": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114128, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114127, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42949": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114130, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114129, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42950": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114133, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114132, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114131, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42951": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114134, "type": "D", "e_id": 286, "fill__split": 0, "cable__pos_y": 450, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_y": 400, "e_size_y": 800, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42952": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114136, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114135, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42953": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114140, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114139, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114138, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114137, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42954": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114141, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 800, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42955": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114143, "type": "D", "e_id": 286, "fill__split": 0, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114142, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42956": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114145, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114144, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42957": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114147, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114146, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42958": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114150, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114149, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114148, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42959": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114153, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114152, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114151, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42960": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114156, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114155, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114154, "type": "T", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42961": {"configs": [{"subconfigs": [{"parameters": {"c_config_id": 114160, "type": "FB", "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_x": "1/2", "trans__start": 416, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"c_config_id": 114158, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [{"parameters": {"c_config_id": 114159, "type": "FB", "s_id": 286, "cable__pos_y": 50, "cable__pos_x": "1/4", "drawer_exterior": false, "drawer_autofill": false, "insert__dom_x": "1/2", "trans__start": 416, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"c_config_id": 114157, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42962": {"configs": [{"subconfigs": [{"parameters": {"c_config_id": 114161, "type": "FB", "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_x": "1/2", "trans__start": 416, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"c_config_id": 114163, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114162, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42963": {"configs": [{"subconfigs": [{"parameters": {"c_config_id": 114165, "type": "FB", "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "insert__dom_x": "1/2", "trans__start": 416, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"c_config_id": 114166, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [], "parameters": {"c_config_id": 114164, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 200, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42964": {"configs": [{"subconfigs": [{"parameters": {"c_config_id": 114168, "type": "FB", "s_id": 286, "cable__pos_y": 50, "cable__pos_x": "1/4", "drawer_exterior": false, "drawer_autofill": false, "insert__dom_x": "1/2", "trans__start": 416, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"c_config_id": 114167, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 500, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "42965": {"configs": [{"subconfigs": [{"parameters": {"c_config_id": 114169, "type": "FB", "s_id": 286, "cable__pos_y": 50, "cable__pos_x": "1/4", "drawer_exterior": false, "drawer_autofill": false, "insert__dom_x": "1/2", "trans__start": 416, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"c_config_id": 114170, "type": "FB", "e_id": 286, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "44301": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 118177, "type": "D", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 400, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, {"subconfigs": [{"parameters": {"c_config_id": 118175, "type": "D", "s_id": 286, "drawer_exterior": false, "drawer_autofill": false, "part__value": ["2"], "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"c_config_id": 118176, "type": "FB", "e_id": 286, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "double__dim": "1/2", "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}, "44302": {"configs": [{"subconfigs": [], "parameters": {"c_config_id": 114085, "type": "D", "e_id": 286, "fill__split": 0, "cable__pos_y": 50, "drawer_exterior": false, "drawer_autofill": false, "e_size_y": 300, "dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}], "parameters": {"dim_x": "288-568", "exterior_doors_split_y_start": "", "exterior_doors_split_y_value": "", "exterior_doors_split_x_start": "", "exterior_doors_split_x_value": ""}}}}, "configurator_data": {"table": {"878": {"300": [{"series_id": 3585, "component_id": 42922, "series_name": "drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3588, "component_id": 42921, "series_name": "mix", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3584, "component_id": 42921, "series_name": "door", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3589, "component_id": 42923, "series_name": "open", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3587, "component_id": 42923, "series_name": "expo", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3590, "component_id": 42922, "series_name": "some_drawer", "series_groups": "", "order": 0, "inconsequent": false}], "400": [{"series_id": 3585, "component_id": 42925, "series_name": "drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3588, "component_id": 42928, "series_name": "mix", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3584, "component_id": 42924, "series_name": "door", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3589, "component_id": 42929, "series_name": "open", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3587, "component_id": 42927, "series_name": "expo", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3590, "component_id": 42930, "series_name": "some_drawer", "series_groups": "", "order": 0, "inconsequent": false}], "500": [{"series_id": 3585, "component_id": 42932, "series_name": "drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3588, "component_id": 42934, "series_name": "mix", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3584, "component_id": 42931, "series_name": "door", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3589, "component_id": 42935, "series_name": "open", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3587, "component_id": 42933, "series_name": "expo", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3590, "component_id": 42936, "series_name": "some_drawer", "series_groups": "", "order": 0, "inconsequent": false}], "600": [{"series_id": 3585, "component_id": 42938, "series_name": "drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3588, "component_id": 42940, "series_name": "mix", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3584, "component_id": 42937, "series_name": "door", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3589, "component_id": 42941, "series_name": "open", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3587, "component_id": 42939, "series_name": "expo", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3590, "component_id": 42942, "series_name": "some_drawer", "series_groups": "", "order": 0, "inconsequent": false}], "700": [{"series_id": 3585, "component_id": 42944, "series_name": "drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3588, "component_id": 42947, "series_name": "mix", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3584, "component_id": 42943, "series_name": "door", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3589, "component_id": 42948, "series_name": "open", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3587, "component_id": 42946, "series_name": "expo", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3590, "component_id": 42949, "series_name": "some_drawer", "series_groups": "", "order": 0, "inconsequent": false}], "800": [{"series_id": 3585, "component_id": 42952, "series_name": "drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3588, "component_id": 42955, "series_name": "mix", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3584, "component_id": 42951, "series_name": "door", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3589, "component_id": 42956, "series_name": "open", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3587, "component_id": 42954, "series_name": "expo", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3590, "component_id": 42957, "series_name": "some_drawer", "series_groups": "", "order": 0, "inconsequent": false}]}, "894": {"300": [{"series_id": 3677, "component_id": 42923, "series_name": "open_expo", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3592, "component_id": 42923, "series_name": "vinyl", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3590, "component_id": 42922, "series_name": "some_drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3588, "component_id": 42921, "series_name": "mix", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3585, "component_id": 42922, "series_name": "drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3584, "component_id": 42921, "series_name": "door", "series_groups": "", "order": 0, "inconsequent": false}], "400": [{"series_id": 3677, "component_id": 42927, "series_name": "open_expo", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3592, "component_id": 42965, "series_name": "vinyl", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3590, "component_id": 42930, "series_name": "some_drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3588, "component_id": 42928, "series_name": "mix", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3585, "component_id": 42925, "series_name": "drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3584, "component_id": 42924, "series_name": "door", "series_groups": "", "order": 0, "inconsequent": false}], "500": [{"series_id": 3677, "component_id": 42935, "series_name": "open_expo", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3592, "component_id": 42964, "series_name": "vinyl", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3590, "component_id": 42936, "series_name": "some_drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3588, "component_id": 42934, "series_name": "mix", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3585, "component_id": 42932, "series_name": "drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3584, "component_id": 42931, "series_name": "door", "series_groups": "", "order": 0, "inconsequent": false}], "600": [{"series_id": 3677, "component_id": 42941, "series_name": "open_expo", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3592, "component_id": 42963, "series_name": "vinyl", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3590, "component_id": 42942, "series_name": "some_drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3588, "component_id": 42940, "series_name": "mix", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3585, "component_id": 42938, "series_name": "drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3584, "component_id": 42937, "series_name": "door", "series_groups": "", "order": 0, "inconsequent": false}], "700": [{"series_id": 3677, "component_id": 42948, "series_name": "open_expo", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3592, "component_id": 42962, "series_name": "vinyl", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3590, "component_id": 42949, "series_name": "some_drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3588, "component_id": 42947, "series_name": "mix", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3585, "component_id": 42944, "series_name": "drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3584, "component_id": 42943, "series_name": "door", "series_groups": "", "order": 0, "inconsequent": false}], "800": [{"series_id": 3677, "component_id": 42956, "series_name": "open_expo", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3592, "component_id": 42961, "series_name": "vinyl", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3590, "component_id": 42957, "series_name": "some_drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3588, "component_id": 42955, "series_name": "mix", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3585, "component_id": 42952, "series_name": "drawer", "series_groups": "", "order": 0, "inconsequent": false}, {"series_id": 3584, "component_id": 42951, "series_name": "door", "series_groups": "", "order": 0, "inconsequent": false}]}}, "component": {"42921": {"id": 42921, "name": "mix - door"}, "42922": {"id": 42922, "name": "drawer - drawer"}, "42923": {"id": 42923, "name": "open - open"}, "42924": {"id": 42924, "name": "door - door"}, "42925": {"id": 42925, "name": "drawer - drawer"}, "42927": {"id": 42927, "name": "expo - expo"}, "42928": {"id": 42928, "name": "mix - mix"}, "42929": {"id": 42929, "name": "open - open"}, "42930": {"id": 42930, "name": "some_drawer - some_drawer"}, "42931": {"id": 42931, "name": "door - door"}, "42932": {"id": 42932, "name": "drawer - drawer"}, "42933": {"id": 42933, "name": "expo - expo"}, "42934": {"id": 42934, "name": "mix - mix"}, "42935": {"id": 42935, "name": "open - open"}, "42936": {"id": 42936, "name": "some_drawer - some_drawer"}, "42937": {"id": 42937, "name": "door - door"}, "42938": {"id": 42938, "name": "drawer - drawer"}, "42939": {"id": 42939, "name": "expo - expo"}, "42940": {"id": 42940, "name": "mix - mix"}, "42941": {"id": 42941, "name": "open - open"}, "42942": {"id": 42942, "name": "some_drawer - some_drawer"}, "42943": {"id": 42943, "name": "door - door"}, "42944": {"id": 42944, "name": "drawer - drawer"}, "42946": {"id": 42946, "name": "expo - expo"}, "42947": {"id": 42947, "name": "mix - mix"}, "42948": {"id": 42948, "name": "open - open"}, "42949": {"id": 42949, "name": "some_drawer - some_drawer"}, "42951": {"id": 42951, "name": "door - door"}, "42952": {"id": 42952, "name": "drawer - drawer"}, "42954": {"id": 42954, "name": "expo - expo"}, "42955": {"id": 42955, "name": "mix - mix"}, "42956": {"id": 42956, "name": "open - open"}, "42957": {"id": 42957, "name": "some_drawer - some_drawer"}, "42961": {"id": 42961, "name": "vinyl - vinyl"}, "42962": {"id": 42962, "name": "vinyl - vinyl"}, "42963": {"id": 42963, "name": "vinyl - vinyl"}, "42964": {"id": 42964, "name": "vinyl - vinyl"}, "42965": {"id": 42965, "name": "vinyl - vinyl"}}}, "superior_object_collection": "Sideboard"}