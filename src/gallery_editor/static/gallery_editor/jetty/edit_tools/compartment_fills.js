import {
  allowedDrawerDepths,
  geometryFields,
  insertsDepthOffset,
  maxBackWidth,
  supportThicknessT01,
  supportThicknessT02,
  supportWidth,

} from '../constants.js';
import { getFlatGeometry } from "../../utils.js";
import { getAllInsertsDepths, getDoorsCount, getDrawersCount } from "../compartments.js";
import { shelfTypeValues, thickness } from "../../constants.js";

export const addSupportToCompartment = (compartment) => {
  if (compartment.supports.length) {
    compartment.supports.forEach(support => {
      support.removed = false;
    })
    return;
  }
  let thickness = (
    compartment.shelfType === shelfTypeValues.TYPE_01 ?
    supportThicknessT01 :
    supportThicknessT02
  )
  let newSupport = {
    x1: compartment.x1,
    x2: compartment.x1 + supportWidth,
    y1: compartment.y1,
    y2: compartment.y2,
    z1: compartment.z1,
    z2: compartment.z1 + thickness,
    flip: 0,
  }
  jetty.supports.push(newSupport);
  compartment.supports.push(newSupport);
}
export const addBacksToCompartment = (compartment) => {
  if (compartment.backs.length) {
    compartment.backs.forEach(back => {
      back.removed = true
    });
  }

  let newBacks = [
  {
    x1: compartment.x1,
    x2: compartment.x2,
    y1: compartment.y1,
    y2: compartment.y2,
    z1: compartment.z1,
    z2: compartment.z1 + 12,
  }];
  let compartmentWidth = (compartment.x2 - compartment.x1);
  if (compartmentWidth > maxBackWidth) {
    newBacks.push({...newBacks[0]});
    newBacks[0].x2 = newBacks[0].x1 + compartmentWidth / 2;
    newBacks[1].x1 = newBacks[0].x2;
  }
  jetty.backs.push(...newBacks);
  compartment.backs.push(...newBacks);
}

export const addGrommetToCompartment = (compartment, grommetHeight) => {
  if (!grommetHeight) {
    compartment.grommets.forEach(grommet => grommet.removed = false);
    return;
  }
  let matchingGrommet = compartment.grommets.find(
    grommet => grommet.y1 - compartment.y1 === grommetHeight
  )
  if (matchingGrommet) {
    matchingGrommet.removed = false;
    return;
  }
  let compartmentXAxis = (compartment.x1 + compartment.x2) / 2
  let newGrommet = {
    x1: compartmentXAxis,
    x2: compartmentXAxis,
    y1: compartment.y1 + grommetHeight,
    y2: compartment.y1 + grommetHeight,
    z1: compartment.z1,
    z2: compartment.z1,
  }
  jetty.cable_management.push(newGrommet);
  compartment.grommets.push(newGrommet);
}
export const addHorizontalInsertToCompartment = (compartment, insertHeight) => {
  let matchingInsert = compartment.horizontalInserts.find(
    insert => insert.y2 - compartment.y1 === insertHeight
  )
  if (matchingInsert) {
    matchingInsert.removed = false;
    return;
  }
  let newInsert = {
    x1: compartment.x1,
    x2: compartment.x2,
    y1: compartment.y1 + insertHeight - thickness,
    y2: compartment.y1 + insertHeight,
    z1: compartment.z1 + 20,
    z2: compartment.z2 - insertsDepthOffset['standard'],
    subtype: 'h',
  }
  jetty.inserts.push(newInsert);
  compartment.horizontalInserts.push(newInsert);
}

export const adjustHorizontalInserts = (compartment) => {
  // NOTE inserts come in a few variants:
  //  - standard depth with subtype 'h' and a front offset
  //  - full-depth with subtype 't' and no front offset (Mosaic also, different tylkliks)
  //  - full-depth with subtype 'h' and no front offset (Mosaic, to keep uniform look)

  // check if compartment has a pre-set inserts depth, if so, use it
  let insertOffset = insertsDepthOffset['standard'];
  if (compartment.insertsDepth !== undefined)
    insertOffset = compartment.insertsDepth;

  // always use full-depth inserts if there are double drawers in the compartment
  let hasDoubleDrawers = getDrawersCount(compartment) === 2;
  if (hasDoubleDrawers)
    insertOffset = insertsDepthOffset['full-depth'];

  // always use standard inserts behind doors
  let hasDoors = getDoorsCount(compartment) > 0;
  if (hasDoors)
    insertOffset = insertsDepthOffset['standard'];

  // subtype depends on the presence of double drawers only, not on the offset
  let subtype = hasDoubleDrawers ? 't' : 'h';

  // adjust front offset – drawer insert has none
  compartment.horizontalInserts.forEach(insert => {
    insert.subtype = subtype;
    insert.z2 = compartment.z2 - insertOffset;
  });
}

export const setCompartmentInsertsDepth = (compartment, value, jetty) => {
  let insertsDepth = insertsDepthOffset[value];

  // if depth is not explicitly set, try to use depth consistent with existing inserts
  if (insertsDepth === undefined) {
    let allInsertsDepth = getAllInsertsDepths(jetty);
    insertsDepth = allInsertsDepth.length > 0 ? allInsertsDepth[0] : insertsDepthOffset['standard'];
  }
  compartment.insertsDepth = insertsDepth;
};

export const clearCompartment = (compartment) => {
  compartment.doors.forEach(door => {
    door.removed = true;
  })
  compartment.drawers.forEach(drawer => {
    drawer.removed = true;
  })
  compartment.backs.forEach(back => {
    back.removed = true;
  })
  compartment.supports.forEach(support => {
    support.removed = true;
  })
}
export const clearGrommets = (compartment) => {
  compartment.grommets.forEach(grommet => {
    grommet.removed = true;
  })
}
export const clearHorizontalInserts = (compartment) => {
  compartment.horizontalInserts.forEach(insert => {
    insert.removed = true;
  })
}
export const changeDepth = (depthChange, compartments) => {
  let oldDepth = jetty.depth;
  let middleStart = oldDepth / 2 - 9;
  let middleEnd = oldDepth / 2 + 9;
  let fieldsWithoutDrawers = geometryFields.filter(value => value !== 'drawers')
  let allGeometry = getFlatGeometry(jetty, fieldsWithoutDrawers);
  [...allGeometry, compartments].forEach(geom => {
    ['z1', 'z2', 'z'].forEach(coordinate => {
      if (geom[coordinate] > middleEnd)
        geom[coordinate] += depthChange;
      else if (geom[coordinate] >= middleStart)
        geom[coordinate] += depthChange / 2;  // keep plinth beam in the middle
    });
  })
  let maxAllowedDrawerDepth = allowedDrawerDepths
    .sort()
    .reverse()
    .find(value => value <= oldDepth + depthChange);

  jetty.drawers.forEach(drawer => {
    if (maxAllowedDrawerDepth === undefined)
      drawer.removed = true;
    else {
      drawer.z2 = oldDepth + depthChange; // always at the front
      drawer.z1 = drawer.z2 - maxAllowedDrawerDepth;
    }
  })

  let plinthNotAllowed = oldDepth + depthChange < 320
  jetty.plinth.forEach(plinth => {plinth.removed = plinthNotAllowed;});
}
