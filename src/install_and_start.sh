#!/bin/bash

PYTHON_VERSION="3.10.15"
POETRY_VERSION=2.1.2
REINSTALL_VENV='false'
RUN_MIGRATIONS='false'
LOAD_MINI_DB='false'

if [ "${BASH_SOURCE[0]}" ]
then
  SCRIPT_DIR=$( cd -- "$( dirname -- "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )
else
  SCRIPT_DIR="$(pwd)/$(dirname "$0")"
fi
(
  while test $# -gt 0; do
    case "$1" in
      -h|--help)
        echo "CSTM backend - install & start"
        echo " "
        echo "./install_and_start.sh [options]"
        echo " "
        echo "options:"
        echo "-h, --help                     show brief help"
        echo "--reinstall                    reinstall virtual environment"
        echo "--load-mini-db                 run load_mini_db script"
        echo "-m, --migrations               run database migrations"
        exit 0
        ;;
      --reinstall)
        shift
        REINSTALL_VENV='true'
        ;;
      --load-mini-db)
        shift
        LOAD_MINI_DB='true'
        ;;
      -m|--migrations)
        shift
        RUN_MIGRATIONS='true'
        ;;
      *)
        break
        ;;
    esac
  done

  update_pyenv_versions() {
    echo "UPDATING pyenv"
    pyenv_path="$(which pyenv)"
    cd "${pyenv_path%/bin/pyenv}" || exit 1
    git pull
    cd "$SCRIPT_DIR" || exit 1
  }

  install_python() {
    echo "Check python version"
    if [ "$(pyenv versions | grep -c $PYTHON_VERSION)" -eq 0 ]
    then
      update_pyenv_versions
      echo "Installing Python $PYTHON_VERSION"
      pyenv install $PYTHON_VERSION
    fi
  }

  install_venv() {
    echo "Installing venv"
    cd "$SCRIPT_DIR/.." || exit 1
    install_python
    pyenv local $POETRY_VERSION
    python -m venv venv
    source venv/bin/activate
    cd "$SCRIPT_DIR" || exit 1
    pip install poetry==$POETRY_VERSION
    poetry install
    echo "Venv installed"
    cd "$SCRIPT_DIR/.." || exit 1
    pre-commit install --install-hooks
    cd "$SCRIPT_DIR" || exit 1
  }

  cd "$SCRIPT_DIR" || exit 1
  # check if venv exists
  if [ "$(ls "$SCRIPT_DIR/.." | grep -c "^venv$")" -eq 1 ]
  then
    echo "Venv found"
    source "$SCRIPT_DIR/../venv/bin/activate"
    if [ "$(python -V | awk '{print $2}')" != $PYTHON_VERSION ] || [ "$REINSTALL_VENV" = 'true' ]
    then
      echo "Python version does not match - Reinstalling"
      deactivate
      rm -rf "$SCRIPT_DIR/../venv"
      install_venv
    else
      poetry install
    fi
  else
    echo "Venv not found"
    install_venv
  fi

  if [ "$LOAD_MINI_DB" = 'true' ]
  then
    echo "Run load_mini_db script"
    source "$SCRIPT_DIR/load_mini_db.sh"
  fi

  if [ "$RUN_MIGRATIONS" = 'true' ]
  then
    echo "Run db migrations"
    python manage.py migrate
  fi

  python manage.py runserver
)
