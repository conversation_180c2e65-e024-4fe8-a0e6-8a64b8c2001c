{% load static %}{% load defaulttags %}{% load invoice_tags %}{% with correction_differences=invoice_object.get_correction_differences %}
<!doctype html>
<html lang="pl">
<head>
    <meta charset="UTF-8">
    <title> {{ invoice.pretty_id }} </title>

    <link rel="stylesheet" href="{% static 'invoice/styles/print.css' %}"/>
    <link rel="stylesheet" href="{% static 'invoice/styles/styles.css' %}"/>
    <style>
        @font-face {
            font-family: 'LetteraTextPro';
            src: url('/r_static/invoice/fonts/LetteraTextPro.otf');
        }

        @font-face {
            font-family: "PxGrotesk";
            src: url('/r_static/invoice/fonts/PxGrotesk-Light.otf');
            /*font-weight: lighter;*/
        }

        tr.no-border-bottom td {
            border-bottom: none !important;
        }

        tr.no-border-top td {
            border-top: none !important;
        }
    </style>
</head>
<body class="correction">
<table id="main-details">
    <tr>
        <td id="invoice-details">
        {% if invoice_object.order.region_vat and invoice_object.order.country == 'austria'%}
            <h5>Credit note</h5>
        {% else %}
            <h5>Correction Invoice / KOREKTA</h5>
        {% endif %}
        {% if invoice.status == 2 %}
            <h5>PRO FORMA/{{ invoice.get_order_no }}</h5>
        {% else %}
            <h5>no. {{invoice.pretty_id}}</h5>
        {% endif %}
            <p><em>Invoice Date: {{invoice.issued_at|date:"d.m.Y"}} Warsaw</em></p>
            <p><em>Sale Date: {{invoice.sell_at|date:"d.m.Y"}} Warsaw</em></p>
        <br/>
        {% if invoice.corrected_invoice.corrected_invoice %}
            {% if invoice.corrected_invoice.corrected_invoice.corrected_invoice %}
                <p>Correction for invoice no. {{ invoice.corrected_invoice.corrected_invoice.corrected_invoice.pretty_id }}</p>
                <p>Correction for invoice issued at: {{ invoice.corrected_invoice.corrected_invoice.corrected_invoice.issued_at|date:'d.m.Y' }}</p>
            {% else %}
                <p>Correction for invoice no. {{ invoice.corrected_invoice.corrected_invoice.pretty_id }}</p>
                <p>Correction for invoice issued at: {{ invoice.corrected_invoice.corrected_invoice.issued_at|date:'d.m.Y' }}</p>
            {% endif %}
        {% else %}
            <p>Correction for invoice no. {{ invoice.corrected_invoice.pretty_id }}</p>
            <p>Correction for invoice issued at: {{ invoice.corrected_invoice.issued_at|date:'d.m.Y' }}</p>
        {% endif %}


            {% if invoice.additional_top != None %}
                <p>{{ invoice.additional_top|safe }}</p>
            {% endif %}
{#            Temporary disabled - it will back when we split assembly service to new invoice #}
{#            {% if invoice.vat_status == 1 and invoice.delivery_address.outside_eu %}#}
{#                <p>VAT REVERSE CHARGE UNDER art.44 OF VAT DIRECTIVE 2006/112/WE</p>#}
{#            {% endif %}#}
        {% if invoice.delivery_address.outside_eu and not invoice.delivery_address.use_dap %}
            <p>Delivery terms: Incoterms DDP <br/>(Delivered Duty Paid)<br/> {{ invoice.invoice_address.city }}</p>
            <p>Country of origin: EU</p>
            {% if invoice.invoice_address.country == 'switzerland' %}
            <p>The exporter PL113288237400000 of products covered by this document <br/>declares that, unless otherwise <br/>clearly indicated, these products<br/> are of EU preferential origin</p>
            {% endif %}
        {% elif invoice.delivery_address.outside_eu %}
            <p>Delivery terms: Incoterms DAP (Delivered At Place)</p>
            <p>Country of origin: Poland</p>
            {% if invoice.invoice_address.country == 'switzerland' %}
            <p>The exporter of products covered by this document <br/>declares that, unless otherwise <br/>clearly indicated, these products<br/> are of EU preferential origin</p>
            {% endif %}
        {% endif %}
        </td>
        {% if invoice.show_both_address == True %}
        <td></td>
        {% endif %}

        <td id="company-details" class="inverted-fonts">
            <img src="{% static 'invoice/assets/logo.png' %}">
            <hr>
            <p>Tylko S.A.</p>
            <p>ul. Czerska 8/10</p>
            <p>00-732 Warszawa</p>
            <p>Poland</p>
            {% if invoice_object.order.region_vat and invoice_object.order.vat_type == 0 %}
                {% if invoice_object.order.country == 'united_kingdom' %}
                    <p>VAT NUMBER: 323 7722 10</p>
                {% else %}
                    <p>NIP: PL1132882374</p>
                {% endif %}
            {% else %}
                <p>NIP: PL1132882374</p>
            {% endif %}
            <p>Numer BDO: *********</p>
            <p>(BDO number: *********)</p>
            {% if 'klarna' not in payment.form %}
                <p>Bank: Pekao SA</p>
                <p>Bank Address: ul. Grzybowska 53/57</p>
                <p>00-950 Warszawa, Poland</p>
                <p>Swift: PKOPPLPW</p>
                <p>IBAN: {{ invoice_object.get_international_bank_account_number }}</p>
            {% endif %}
        </td>
    </tr>
    <tr>
        <td id="client-details"{% if invoice.show_both_address == True %} style="width:30%"{% endif %}>
        {% if invoice.additional_address_1 %}
            {{ invoice.additional_address_1|safe }}
        {% else %}
            {% if invoice.invoice_address.company_name %}
                <h5>{{invoice.invoice_address.company_name|default_if_none:""}} <br/> {{invoice.invoice_address.first_name|default_if_none:""}} {{invoice.invoice_address.last_name|default_if_none:""}} </h5>
            {% else %}
                <h5>{{invoice.invoice_address.first_name|default_if_none:""}} {{invoice.invoice_address.last_name|default_if_none:""}} </h5>
            {% endif %}
            <p>{{invoice.invoice_address.street_address_1|default_if_none:""}} {{invoice.invoice_address.street_address_2|default_if_none:""}} </p>
            <p>{{invoice.invoice_address.postal_code|default_if_none:""}} {{invoice.invoice_address.city|default_if_none:""}} </p>
            <p>{% country_trans invoice.invoice_address.country 'en' %}</p>
            {% if invoice.invoice_address.vat %}
                <p>TAX: <em>{{invoice.invoice_address.vat}}</em></p>
            {% endif %}
        {% endif %}
        </td>
        {% if invoice.show_both_address == True %}
            {% if invoice.invoice_address.additional_address_2  %}
            <td>
            <h5>Delivery Address:</h5>
                {{ invoice.invoice_address.additional_address_2|safe }}
            </td>
            {% else %}
            <td>
            <h5>Delivery Address:</h5>
            {% if invoice.delivery_address.company_name %}
                <h5>{{ invoice.delivery_address.company_name|default_if_none:"" }} <br/> {{ invoice.delivery_address.first_name|default_if_none:"" }} {{ invoice.delivery_address.last_name|default_if_none:"" }} </h5>
            {% else %}
                <h5>{{ invoice.delivery_address.first_name|default_if_none:"" }} {{ invoice.delivery_address.last_name|default_if_none:"" }} </h5>
            {% endif %}
            <p>{{ invoice.delivery_address.street_address_1|default_if_none:"" }} {{ invoice.delivery_address.street_address_2|default_if_none:"" }} </p>
            <p>{{ invoice.delivery_address.postal_code|default_if_none:"" }} {{ invoice.delivery_address.city|default_if_none:"" }} </p>
            <p>{% country_trans invoice.delivery_address.country 'en' %}</p>
            {% if invoice.invoice_address.vat %}
                <p>TAX: <em>{{ invoice.invoice_address.vat }}</em></p>
            {% endif %}
            </td>
            {% endif %}
    {% endif %}
        <td id="order-details">
            <article class="inverted-fonts">
                <p><strong>Order no. <em>{{ invoice.order }}</em></strong></p>
            {% if not invoice_object.order.region_vat %}
                <p>Vat exchange:  {{ invoice.exchange_rate }} PLN</p>
                <p>Exchange Date: {{ invoice.exchange_date|date:"d.m.Y" }}</p>
                <p>Vat PLN: {{ invoice.vat_in_pln }} PLN</p>
            {% else %}
                <p>Exchange:  {{ invoice.exchange_rate }} PLN</p>
                <p>Exchange Date: {{ invoice.exchange_date|date:"d-m-Y" }}</p>
            {% endif %}
            {% if invoice.status == 0 %}
                <P>Paid</P>
            {% endif %}
                {% if invoice.due_date %}
                <p>Due Date: {{ invoice.due_date|date:"d.m.Y" }}</p>
                {% endif %}
                {% if invoice.delivery_date_at %}
                    <p>Expected delivery at: {{ invoice.delivery_date_at|date:"d.m.Y" }}</p>
                {% endif %}
            </article>
        </td>
    </tr>
</table>

<table id="order-changes-reason">
    <tbody>
    <tr>
        <td><p>Reason for correction/<br/> Powód korekty:</p></td>
        <td><p>{{ invoice.corrected_notes|safe }}</p></td>
    </tr>
    </tbody>
</table>
<br/>
<br/>

{% if invoice_object.correction_changes.count > 0 %}
    <table id="order-changes-in-data">
        <thead>
        <tr>
            <td>Changes/Zmiany</td>
            <td>Previous state/ Stan poprzedni</td>
            <td>Current state/ Stan aktualny</td>
        </tr>
        </thead>
        <tbody>
        {% for correction_change in invoice_object.correction_changes.all %}
            <tr>
                <td>{{ correction_change.name|safe }}</td>
                <td>{{ correction_change.previous_state|safe }}</td>
                <td>{{ correction_change.current_state|safe }}</td>
            </tr>
        {% endfor %}
        </tbody>
    </table>
{% endif %}

{% if invoice_object.has_correction_value_differences and invoice_items.0|length > 0 or invoice_items.1|length > 0 or invoice_items.2|length > 0 or invoice_items.3|length > 0%}
<table id="order-items-details" class="inverted-fonts">
        <thead>
            <tr class="table-bar--preview">
                <td class="nowrap">Item Description</td>
                <td>Quantity</td>
                <td>Net price</td>
                <td>Discount value</td>
                <td>Net value</td>
                <td>VAT rate</td>
                <td>VAT amount</td>
                {% if invoice.delivery_address.outside_eu %}
                    <td>Net weight</td>
                    <td>Gross weight</td>
                {% endif %}
                <td>Gross price</td>
            </tr>
        </thead>
        <tbody>
            <tr>
                <td colspan="8">Previous state/ Stan poprzedni</td>
            </tr>

        {% for item in corrected_invoice_items.0 %}
            <tr {% if invoice_object.order.country == 'france' and item.recycle_tax_value %}class="no-border-bottom"{% endif %}>
                <td>
                    {{ item.item_name }}
                    {% if item.item_type == 0 %}
                        <br/>{{ item.item_material }}
                        <br/>{{ item.dimensions }}
                    {% endif %}
                </td>
                <td class="nowrap"><em>{{ item.quantity }}</em></td>
                <td class="nowrap"><em>{{ item.net_price }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.discount_value }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.net_value }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{% widthratio item.vat_rate 1 100 %}%</em></td>
                <td class="nowrap"><em>{{ item.vat_amount }}{{ invoice.currency_symbol }}</em></td>
                {% if invoice.delivery_address.outside_eu %}
                    <td>{{ item.net_weight }} kg</td>
                    <td>{{ item.gross_weight }} kg</td>
                {% endif %}
                <td class="nowrap"><em>{{ item.gross_price }}{{ invoice.currency_symbol }}</em></td>
            </tr>
            {% if invoice_object.order.country == 'france' and item.recycle_tax_value %}
                <tr>
                    <td>Eco-fee included</td>
                    <td class="nowrap"></td>
                    <td class="nowrap"></td>
                    <td class="nowrap"></td>
                    <td class="nowrap"></td>
                    <td class="nowrap"></td>
                    <td class="nowrap"></td>
                    {% if invoice.delivery_address.outside_eu %}
                        <td></td>
                        <td></td>
                    {% endif %}
                    <td class="nowrap"><em>{{ item.recycle_tax_value }}{{ invoice.currency_symbol }}</em></td>
                </tr>
            {% endif %}
        {% endfor %}

        {% for item in corrected_invoice_items.1 %}
            <tr>
                <td>
                    {{ item.item_name }}
                    {% if item.item_type == 0 %}
                        <br/>{{ item.item_material }}
                        <br/>{{ item.dimensions }}
                    {% endif %}
                </td>
                <td class="nowrap"><em>{{ item.quantity }}</em></td>
                <td class="nowrap"><em>{{ item.net_price }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.discount_value }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.net_value }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{% widthratio item.vat_rate 1 100 %}%</em></td>
                <td class="nowrap"><em>{{ item.vat_amount }}{{ invoice.currency_symbol }}</em></td>
                {% if invoice.delivery_address.outside_eu %}
                    <td>{{ item.net_weight }} kg</td>
                    <td>{{ item.gross_weight }} kg</td>
                {% endif %}
                <td class="nowrap"><em>{{ item.gross_price }}{{ invoice.currency_symbol }}</em></td>
            </tr>
        {% endfor %}

        {% for item in corrected_invoice_items.2 %}
            <tr>
                <td>
                    {{ item.item_name }}
                    {% if item.item_type == 0 %}
                        <br/>{{ item.item_material|default_if_none:"" }}
                        <br/>{{ item.dimensions|default_if_none:"" }}
                    {% endif %}
                </td>
                <td class="nowrap"><em>{{ item.quantity }}</em></td>
                <td class="nowrap"><em>{{ item.net_price }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.discount_value }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.net_value }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{% widthratio item.vat_rate 1 100 %}%</em></td>
                <td class="nowrap"><em>{{ item.vat_amount }}{{ invoice.currency_symbol }}</em></td>
                {% if invoice.delivery_address.outside_eu %}
                    <td>{{ item.net_weight }} kg</td>
                    <td>{{ item.gross_weight }} kg</td>
                {% endif %}
                <td class="nowrap"><em>{{ item.gross_price }}{{ invoice.currency_symbol }}</em></td>
            </tr>
        {% endfor %}

        {% for item in corrected_invoice_items.3 %}
            <tr>
                <td>
                    {{ item.item_name }}
                    {% if item.item_type == 0 %}
                        <br/>{{ item.item_material|default_if_none:""}}
                        <br/>{{ item.dimensions|default_if_none:""}}
                    {% endif %}
                </td>
                <td class="nowrap"><em>{{ item.quantity }}</em></td>
                <td class="nowrap"><em>{{ item.net_price }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.discount_value }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.net_value }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{% widthratio item.vat_rate 1 100 %}%</em></td>
                <td class="nowrap"><em>{{ item.vat_amount }}{{ invoice.currency_symbol }}</em></td>
                {% if invoice.delivery_address.outside_eu %}
                    <td>{{ item.net_weight }} kg</td>
                    <td>{{ item.gross_weight }} kg</td>
                {% endif %}
                <td class="nowrap"><em>{{ item.gross_price }}{{ invoice.currency_symbol }}</em></td>
            </tr>
        {% endfor %}
        <tr>
            <td colspan="8">Current state/ Stan aktualny</td>
        </tr>

        {% for item in invoice_items.0 %}
            <tr {% if invoice_object.order.country == 'france' and item.recycle_tax_value %}class="no-border-bottom"{% endif %}>
                <td>
                    {{ item.item_name }}
                    {% if item.item_type == 0 %}
                        <br/>{{ item.item_material|default_if_none:"" }}
                        <br/>{{ item.dimensions|default_if_none:"" }}
                    {% endif %}
                </td>
                <td class="nowrap"><em>{{ item.quantity }}</em></td>
                <td class="nowrap"><em>{{ item.net_price }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.discount_value }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.net_value }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{% widthratio item.vat_rate 1 100 %}%</em></td>
                <td class="nowrap"><em>{{ item.vat_amount }}{{ invoice.currency_symbol }}</em></td>
                {% if invoice.delivery_address.outside_eu %}
                    <td>{{ item.net_weight }} kg</td>
                    <td>{{ item.gross_weight }} kg</td>
                {% endif %}
                <td class="nowrap"><em>{{ item.gross_price }}{{ invoice.currency_symbol }}</em></td>
            </tr>
            {% if invoice_object.order.country == 'france' and item.recycle_tax_value %}
                <tr>
                    <td>Eco-fee included</td>
                    <td class="nowrap"></td>
                    <td class="nowrap"></td>
                    <td class="nowrap"></td>
                    <td class="nowrap"></td>
                    <td class="nowrap"></td>
                    <td class="nowrap"></td>
                    {% if invoice.delivery_address.outside_eu %}
                        <td></td>
                        <td></td>
                    {% endif %}
                    <td class="nowrap"><em>{{ item.recycle_tax_value }}{{ invoice.currency_symbol }}</em></td>
                </tr>
            {% endif %}
        {% endfor %}

        {% for item in invoice_items.1 %}
            <tr>
                <td>
                    {{ item.item_name }}
                    {% if item.item_type == 0 %}
                        <br/>{{ item.item_material|default_if_none:"" }}
                        <br/>{{ item.dimensions|default_if_none:"" }}
                    {% endif %}
                </td>
                <td class="nowrap"><em>{{ item.quantity }}</em></td>
                <td class="nowrap"><em>{{ item.net_price }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.discount_value }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.net_value }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{% widthratio item.vat_rate 1 100 %}%</em></td>
                <td class="nowrap"><em>{{ item.vat_amount }}{{ invoice.currency_symbol }}</em></td>
                {% if invoice.delivery_address.outside_eu %}
                    <td>{{ item.net_weight }} kg</td>
                    <td>{{ item.gross_weight }} kg</td>
                {% endif %}
                <td class="nowrap"><em>{{ item.gross_price }}{{ invoice.currency_symbol }}</em></td>
            </tr>
        {% endfor %}

        {% for item in invoice_items.2 %}
            <tr>
                <td>
                    {{ item.item_name }}
                    {% if item.item_type == 0 %}
                        <br/>{{ item.item_material|default_if_none:"" }}
                        <br/>{{ item.dimensions|default_if_none:"" }}
                    {% endif %}
                </td>
                <td class="nowrap"><em>{{ item.quantity }}</em></td>
                <td class="nowrap"><em>{{ item.net_price }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.discount_value }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.net_value }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{% widthratio item.vat_rate 1 100 %}%</em></td>
                <td class="nowrap"><em>{{ item.vat_amount }}{{ invoice.currency_symbol }}</em></td>
                {% if invoice.delivery_address.outside_eu %}
                    <td>{{ item.net_weight }} kg</td>
                    <td>{{ item.gross_weight }} kg</td>
                {% endif %}
                <td class="nowrap"><em>{{ item.gross_price }}{{ invoice.currency_symbol }}</em></td>
            </tr>
        {% endfor %}

        {% for item in invoice_items.3 %}
            <tr>
                <td>
                    {{ item.item_name }}
                    {% if item.item_type == 0 %}
                        <br/>{{ item.item_material|default_if_none:"" }}
                        <br/>{{ item.dimensions|default_if_none:"" }}
                    {% endif %}
                </td>
                <td class="nowrap"><em>{{ item.quantity }}</em></td>
                <td class="nowrap"><em>{{ item.net_price }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.discount_value }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.net_value }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{% widthratio item.vat_rate 1 100 %}%</em></td>
                <td class="nowrap"><em>{{ item.vat_amount }}{{ invoice.currency_symbol }}</em></td>
                {% if invoice.delivery_address.outside_eu %}
                    <td>{{ item.net_weight }} kg</td>
                    <td>{{ item.gross_weight }} kg</td>
                {% endif %}
                <td class="nowrap"><em>{{ item.gross_price }}{{ invoice.currency_symbol }}</em></td>
            </tr>
        {% endfor %}
        {% if invoice_object.is_correction_from_neutralization %}
        <tr>
            <td colspan="8">Correction value/ Wartość korekty</td>
        </tr>
        {% for item in corrected_invoice_items.0 %}
            <tr {% if invoice_object.order.country == 'france' and item.recycle_tax_value %}class="no-border-bottom"{% endif %}>
                <td>
                    {{ item.item_name }}
                    {% if item.item_type == 0 %}
                        <br/>{{ item.item_material }}
                        <br/>{{ item.dimensions }}
                    {% endif %}
                </td>
                <td class="nowrap"><em>{{ item.quantity }}</em></td>
                <td class="nowrap"><em>{{ item.net_price|format_opposite }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.discount_value|format_opposite }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.net_value|format_opposite }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{% widthratio item.vat_rate 1 100 %}%</em></td>
                <td class="nowrap"><em>{{ item.vat_amount|format_opposite }}{{ invoice.currency_symbol }}</em></td>
                {% if invoice.delivery_address.outside_eu %}
                    <td>{{ item.net_weight }} kg</td>
                    <td>{{ item.gross_weight }} kg</td>
                {% endif %}
                <td class="nowrap"><em>{{ item.gross_price|format_opposite }}{{ invoice.currency_symbol }}</em></td>
            </tr>
            {% if invoice_object.order.country == 'france' and item.recycle_tax_value %}
                <tr>
                    <td>Eco-fee included</td>
                    <td class="nowrap"></td>
                    <td class="nowrap"></td>
                    <td class="nowrap"></td>
                    <td class="nowrap"></td>
                    <td class="nowrap"></td>
                    <td class="nowrap"></td>
                    {% if invoice.delivery_address.outside_eu %}
                        <td></td>
                        <td></td>
                    {% endif %}
                    <td class="nowrap"><em>{{ item.recycle_tax_value|format_opposite }}{{ invoice.currency_symbol }}</em></td>
                </tr>
            {% endif %}
        {% endfor %}

        {% for item in corrected_invoice_items.1 %}
            <tr>
                <td>
                    {{ item.item_name }}
                    {% if item.item_type == 0 %}
                        <br/>{{ item.item_material }}
                        <br/>{{ item.dimensions }}
                    {% endif %}
                </td>
                <td class="nowrap"><em>{{ item.quantity }}</em></td>
                <td class="nowrap"><em>{{ item.net_price|format_opposite }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.discount_value|format_opposite }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.net_value|format_opposite }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{% widthratio item.vat_rate 1 100 %}%</em></td>
                <td class="nowrap"><em>{{ item.vat_amount|format_opposite }}{{ invoice.currency_symbol }}</em></td>
                {% if invoice.delivery_address.outside_eu %}
                    <td>{{ item.net_weight }} kg</td>
                    <td>{{ item.gross_weight }} kg</td>
                {% endif %}
                <td class="nowrap"><em>{{ item.gross_price|format_opposite }}{{ invoice.currency_symbol }}</em></td>
            </tr>
        {% endfor %}

        {% for item in corrected_invoice_items.2 %}
            <tr>
                <td>
                    {{ item.item_name }}
                    {% if item.item_type == 0 %}
                        <br/>{{ item.item_material|default_if_none:"" }}
                        <br/>{{ item.dimensions|default_if_none:"" }}
                    {% endif %}
                </td>
                <td class="nowrap"><em>{{ item.quantity }}</em></td>
                <td class="nowrap"><em>{{ item.net_price|format_opposite }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.discount_value|format_opposite }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.net_value|format_opposite }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{% widthratio item.vat_rate 1 100 %}%</em></td>
                <td class="nowrap"><em>{{ item.vat_amount|format_opposite }}{{ invoice.currency_symbol }}</em></td>
                {% if invoice.delivery_address.outside_eu %}
                    <td>{{ item.net_weight }} kg</td>
                    <td>{{ item.gross_weight }} kg</td>
                {% endif %}
                <td class="nowrap"><em>{{ item.gross_price|format_opposite }}{{ invoice.currency_symbol }}</em></td>
            </tr>
        {% endfor %}

        {% for item in corrected_invoice_items.3 %}
            <tr>
                <td>
                    {{ item.item_name }}
                    {% if item.item_type == 0 %}
                        <br/>{{ item.item_material|default_if_none:"" }}
                        <br/>{{ item.dimensions|default_if_none:"" }}
                    {% endif %}
                </td>
                <td class="nowrap"><em>{{ item.quantity }}</em></td>
                <td class="nowrap"><em>{{ item.net_price|format_opposite }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.discount_value|format_opposite }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{{ item.net_value|format_opposite }}{{ invoice.currency_symbol }}</em></td>
                <td class="nowrap"><em>{% widthratio item.vat_rate 1 100 %}%</em></td>
                <td class="nowrap"><em>{{ item.vat_amount|format_opposite }}{{ invoice.currency_symbol }}</em></td>
                {% if invoice.delivery_address.outside_eu %}
                    <td>{{ item.net_weight }} kg</td>
                    <td>{{ item.gross_weight }} kg</td>
                {% endif %}
                <td class="nowrap"><em>{{ item.gross_price|format_opposite }}{{ invoice.currency_symbol }}</em></td>
            </tr>
        {% endfor %}
        {% endif %}

        </tbody>
    </table>
{% endif %}

{% if invoice_object.has_correction_value_differences %}
    {% if not invoice_object.order.region_vat and invoice_object.order.vat_type == 0 or  invoice_object.order.region_vat %}
    <div class="no-page-break">
    <table id="vat-change-summary" style="page-break-before:always;">
        <tbody>
        <tr><td colspan="4"><p> Diffrence/ Różnica</p></td></tr>
        <tr>
            <td>Vat rate/<br/>Stawka vat</td>
            <td>Net amount/<br/>Wartość netto</td>
            <td>Vat amount/<br/>Kwota VAT</td>
            <td>Brutto amount/<br/>Wartość brutto</td>
        </tr>
        {% for difference_label, differences in correction_differences.items %}
            {% if difference_label != "totals" %}
                <tr>
                    <td>{{ difference_label }}%</td>
                    <td>{{ differences.net }} PLN</td>
                    <td>{{ differences.vat }} PLN</td>
                    <td>{{ differences.gross }} PLN</td>
                </tr>
            {% endif %}
        {% endfor %}
        </tbody>
        <tfoot>
        <tr>
            <td>Total/ <br>Razem:</td>
            <td>{{ correction_differences.totals.net_diff }} PLN</td>
            <td>{{ correction_differences.totals.vat_diff }} PLN</td>
            <td>{{ correction_differences.totals.gross_diff }} PLN</td>
        </tr>
        </tfoot>
    </table>
    </div>
    {% endif %}

<div class="no-page-break">
    <table id="order-summary" style="margin-top: 6pt ;page-break-before:always;">
        <tbody>
        <tr>
            <td></td>
            <td>Previous state<br/> Stan poprzedni</td>
            <td>Current state <br/> Stan aktualny</td>
            {% if invoice_object.is_correction_from_neutralization %}
                <td>Correction value <br/> Wartość korekty</td>
            {% endif %}
            <td></td>
        </tr>
            <tr>
                <td><p>Net value:</p></td>
                <td><p>{{ correction_differences.totals.net_old }}{{ invoice.currency_symbol }}</p></td>
                <td><p>{{ correction_differences.totals.net }}{{ invoice.currency_symbol }}</p></td>
                {% if invoice_object.is_correction_from_neutralization %}
                    <td><p>{{ correction_differences.totals.net_old|format_opposite }}{{ invoice.currency_symbol }}</p></td>
                {% endif %}
            </tr>
            <tr>
                <td><p>Vat:</p></td>
                <td><p>{{ correction_differences.totals.vat_old }}{{ invoice.currency_symbol }}</p></td>
                <td><p>{{ correction_differences.totals.vat }}{{ invoice.currency_symbol }}</p></td>
                {% if invoice_object.is_correction_from_neutralization %}
                    <td><p>{{ correction_differences.totals.vat_old|format_opposite }}{{ invoice.currency_symbol }}</p></td>
                {% endif %}
            </tr>
        </tbody>
        <tfoot>
            <tr>
                <td><h5>Total:</h5></td>
                <td><h5>{{ correction_differences.totals.gross_old }}{{ invoice.currency_symbol }}</h5></td>
                <td><h5>{{ correction_differences.totals.gross }}{{ invoice.currency_symbol }}</h5></td>
                {% if invoice_object.is_correction_from_neutralization %}
                    <td><h5>{{ correction_differences.totals.gross_old|format_opposite }}{{ invoice.currency_symbol }}</h5></td>
                {% endif %}
            </tr>
            {% if invoice_object.order.country == 'france' %}
                {% if correction_differences.totals.recycle_tax_old or correction_differences.totals.recycle_tax %}
                    <tr>
                        <td><p>Eco-fee included:</p></td>
                        <td>
                            <p>
                                {% if correction_differences.totals.recycle_tax_old %}
                                    {{ correction_differences.totals.recycle_tax_old }}{{ invoice.currency_symbol }}
                                {% else %}
                                    -
                                {% endif %}
                            </p>
                        </td>
                        <td>
                            <p>
                                {% if correction_differences.totals.recycle_tax %}
                                    {{ correction_differences.totals.recycle_tax }}{{ invoice.currency_symbol }}
                                {% else %}
                                    -
                                {% endif %}
                            </p>
                        </td>
                        {% if invoice_object.is_correction_from_neutralization %}
                            <td>
                                <p>
                                    {% if correction_differences.totals.recycle_tax_old %}
                                        {{ correction_differences.totals.recycle_tax_old|format_opposite }}{{ invoice.currency_symbol }}
                                    {% else %}
                                        -
                                    {% endif %}
                                </p>
                            </td>
                        {% endif %}
                    </tr>
                {% endif %}
            {% endif %}
            {% if invoice.additional_total_text and invoice.additional_total_value %}
            <tr>
                <td style="border-top: 0px;" colspan="3"><h5>{{ invoice.additional_total_text }}</h5></td>
                <td style="border-top: 0px;"><h5>{{ invoice.additional_total_value }}</h5></td>
            </tr>
            {% endif %}
        </tfoot>
    </table>
</div>
{% endif %}
    <br>
</body>
</html>
{% endwith %}
