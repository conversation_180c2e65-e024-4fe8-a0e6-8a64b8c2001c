from django.db import models


class ItemsForRenderApproach(models.IntegerChoices):
    ABSOLUTE = 0, 'ABSOLUTE'
    RATIO = 1, 'RATIO'
    PERCENTAGE_OF_OPENINGS = 2, 'PERCENTAGE OF OPENINGS'
    PERCENTAGE_OF_FRONT_AREA = 3, 'PERCENTAGE OF FRONT AREA'


class FreeSpaceSelectionApproach(models.IntegerChoices):
    DISTANCE_FROM_START = 0, 'DISTANCE FROM START'
    SMALLEST_BOX_FIRST = 1, 'SMALLEST BOX FIRST'


class ItemsSnapping(models.IntegerChoices):
    NO_SNAPPING = 0, 'NO SNAPPING'
    SNAP_TO_CENTER = 1, 'SNAP TO CENTER'
    SNAP_TO_LEFT = 2, 'SNAP TO LEFT'
    SNAP_TO_RIGHT = 3, 'SNAP TO RIGHT'
    SNAP_TO_LEFT_OR_RIGHT = 4, 'SNAP TO LEFT OR RIGHT'


class FurnitureCategory(models.TextChoices):
    BOOKCASE = 'bookcase'
    SIDEBOARD = 'sideboard'
    TV_STAND = 'tv-stand'
    SHOERACK = 'shoerack'
    WALL_STORAGE = 'wallstorage'
    CHEST_OF_DRAWERS = 'chest-of-drawers'
    VINYL_STORAGE = 'vinyl_storage'


class CornerSnap(models.TextChoices):
    BOTTOM_CENTER = 'bottom_center', 'Bottom center'
    BOTTOM_LEFT = 'bottom_left', 'Bottom left'
    BOTTOM_RIGHT = 'bottom_right', 'Bottom right'
    TOP_CENTER = 'top_center', 'Top center'
    TOP_LEFT = 'top_left', 'Top left'
    TOP_RIGHT = 'top_right', 'Top right'
