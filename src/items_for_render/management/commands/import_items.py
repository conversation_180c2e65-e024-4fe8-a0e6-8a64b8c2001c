import json
import os

from contextlib import ExitStack
from tempfile import TemporaryDirectory
from zipfile import ZipFile

from django.core.files.base import ContentFile
from django.core.management.base import (
    BaseCommand,
    CommandError,
)

from items_for_render.serializers import ItemSerializer


class Command(BaseCommand):
    help = 'Import items, textures from previously exported by admin action'

    def add_arguments(self, parser):
        parser.add_argument('input', nargs='+', type=str)

    def handle(self, *args, **options):
        # FIXME: What?
        if not options['input']:
            raise CommandError('Specify the file for import')

        with ExitStack() as stack:
            sample_zip = stack.enter_context(ZipFile(options['input'][0], 'r'))
            temp_dir = stack.enter_context(TemporaryDirectory())

            sample_zip.extractall(temp_dir)
            items = json.load(
                open(os.path.join(temp_dir, 'items_serialization.json'), 'r')
            )
            for item in items:
                del item['id']
                for field_name in ['model', 'shadow_model', 'shadow_texture']:
                    if item[field_name]:
                        with open(
                            item[field_name].replace(
                                '/uploaded', '{}'.format(temp_dir)
                            ),
                            'rb',
                        ) as file_to_import:
                            item[field_name] = ContentFile(
                                file_to_import.read(),
                                name=item[field_name].split('/')[-1],
                            )

                for texture in item['textures']:
                    for field_name in [
                        'texture_file',
                    ]:
                        if texture[field_name]:
                            with open(
                                texture[field_name].replace(
                                    '/uploaded', '{}'.format(temp_dir)
                                ),
                                'rb',
                            ) as file_to_import:
                                texture[field_name] = ContentFile(
                                    file_to_import.read(),
                                    name=texture[field_name].split('/')[-1],
                                )

                new_item = ItemSerializer(data=item)
                if new_item.is_valid():
                    new_item.save(owner_id=1)
