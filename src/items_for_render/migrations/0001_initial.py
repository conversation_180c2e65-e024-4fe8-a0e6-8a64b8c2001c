# Generated by Django 1.11.24 on 2020-04-28 12:45
from __future__ import unicode_literals

import django.db.models.deletion

from django.conf import settings
from django.db import (
    migrations,
    models,
)

import custom.models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Item',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(max_length=256)),
                ('enabled', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('model', models.FileField(upload_to='items/models')),
                (
                    'shadow_model',
                    models.FileField(
                        blank=True, null=True, upload_to='items/models_shadow'
                    ),
                ),
                (
                    'shadow_texture',
                    models.FileField(
                        blank=True, null=True, upload_to='items/models_texture'
                    ),
                ),
                (
                    'categories',
                    custom.models.ChoiceArrayField(
                        base_field=models.CharField(
                            blank=True,
                            choices=[
                                ('bookcase', 'bookcase'),
                                ('sideboard', 'sideboard'),
                                ('tv-stand', 'tv-stand'),
                                ('shoerack', 'shoerack'),
                                ('wallstorage', 'wallstorage'),
                                ('chest-of-drawers', 'chest-of-drawers'),
                                ('vinyl_storage', 'vinyl_storage'),
                            ],
                            max_length=25,
                        ),
                        blank=True,
                        default=list,
                        size=None,
                    ),
                ),
                (
                    'scale_x_min',
                    models.IntegerField(default=100, help_text='100 == 100%'),
                ),
                (
                    'scale_x_max',
                    models.IntegerField(default=100, help_text='100 == 100%'),
                ),
                (
                    'scale_y_min',
                    models.IntegerField(default=100, help_text='100 == 100%'),
                ),
                (
                    'scale_y_max',
                    models.IntegerField(default=100, help_text='100 == 100%'),
                ),
                (
                    'scale_z_min',
                    models.IntegerField(default=100, help_text='100 == 100%'),
                ),
                (
                    'scale_z_max',
                    models.IntegerField(default=100, help_text='100 == 100%'),
                ),
                ('default_x', models.IntegerField(default=0, help_text='in cm')),
                ('default_y', models.IntegerField(default=0, help_text='in cm')),
                ('default_z', models.IntegerField(default=0, help_text='in cm')),
                ('max_y', models.IntegerField(default=500, help_text='in cm')),
                ('min_y', models.IntegerField(default=0, help_text='in cm')),
                ('margin_min_left', models.IntegerField(default=0)),
                ('margin_min_right', models.IntegerField(default=0)),
                ('margin_min_top', models.IntegerField(default=0)),
                ('only_on_top', models.BooleanField(default=False)),
                (
                    'outside',
                    models.BooleanField(
                        default=False,
                        help_text='May be rendered outside shelf/wardrobe',
                    ),
                ),
                (
                    'corner_to_snap',
                    models.CharField(
                        choices=[
                            ('bottom_center', 'Bottom center'),
                            ('bottom_left', 'Bottom left'),
                            ('bottom_right', 'Bottom right'),
                            ('bottom_center', 'Top center'),
                            ('bottom_left', 'Top left'),
                            ('bottom_right', 'Top right'),
                        ],
                        default=('bottom_center', 'Bottom center'),
                        max_length=50,
                    ),
                ),
                ('snapping_corner_offset_x', models.IntegerField(default=0)),
                ('snapping_corner_offset_y', models.IntegerField(default=0)),
                ('snapping_corner_offset_z', models.IntegerField(default=0)),
                (
                    'owner',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='ItemGroup',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(max_length=250)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                (
                    'categories',
                    custom.models.ChoiceArrayField(
                        base_field=models.CharField(
                            blank=True,
                            choices=[
                                ('bookcase', 'bookcase'),
                                ('sideboard', 'sideboard'),
                                ('tv-stand', 'tv-stand'),
                                ('shoerack', 'shoerack'),
                                ('wallstorage', 'wallstorage'),
                                ('chest-of-drawers', 'chest-of-drawers'),
                                ('vinyl_storage', 'vinyl_storage'),
                            ],
                            max_length=25,
                        ),
                        blank=True,
                        default=list,
                        size=None,
                    ),
                ),
                ('items', models.ManyToManyField(to='items_for_render.Item')),
            ],
        ),
        migrations.CreateModel(
            name='ItemTextureVariant',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(default='default', max_length=250)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('texture_file', models.FileField(upload_to='items/textures')),
                (
                    'availability',
                    custom.models.ChoiceArrayField(
                        base_field=models.CharField(
                            blank=True,
                            choices=[
                                ['ShelfType:0_Material:0', 'ShelfType:0_Material:0'],
                                ['ShelfType:0_Material:1', 'ShelfType:0_Material:1'],
                                ['ShelfType:0_Material:3', 'ShelfType:0_Material:3'],
                                ['ShelfType:0_Material:4', 'ShelfType:0_Material:4'],
                                ['ShelfType:0_Material:5', 'ShelfType:0_Material:5'],
                                ['ShelfType:1_Material:0', 'ShelfType:1_Material:0'],
                                ['ShelfType:1_Material:1', 'ShelfType:1_Material:1'],
                                ['ShelfType:1_Material:2', 'ShelfType:1_Material:2'],
                                ['ShelfType:1_Material:3', 'ShelfType:1_Material:3'],
                                ['ShelfType:1_Material:4', 'ShelfType:1_Material:4'],
                                ['ShelfType:2_Material:0', 'ShelfType:2_Material:0'],
                                ['ShelfType:2_Material:1', 'ShelfType:2_Material:1'],
                            ],
                            max_length=25,
                        ),
                        blank=True,
                        default=list,
                        size=None,
                    ),
                ),
                (
                    'item',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='textures',
                        to='items_for_render.Item',
                    ),
                ),
            ],
        ),
    ]
