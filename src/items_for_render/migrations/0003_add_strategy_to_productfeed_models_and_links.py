# Generated by Django 1.11.24 on 2020-05-07 08:42
from __future__ import unicode_literals

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('items_for_render', '0002_changed_defaults_for_texture_variants'),
    ]

    operations = [
        migrations.CreateModel(
            name='Strategy',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(max_length=250)),
                ('description', models.TextField(blank=True, default='')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                (
                    'number_of_items',
                    models.IntegerField(default=0, help_text='absolute, or %'),
                ),
                (
                    'number_of_items_approach',
                    models.IntegerField(
                        choices=[
                            (0, 'ABSOLUTE'),
                            (1, 'RATIO'),
                            (2, 'PERCENTAGE OF OPENINGS'),
                            (3, 'PERCENTAGE OF FRONT AREA'),
                        ],
                        default=0,
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='StrategyGroupRule',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('description', models.TextField(blank=True, default='')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('slot_number', models.IntegerField(default=0)),
                ('min_number_of_items', models.IntegerField(default=0)),
                ('max_number_of_items', models.IntegerField(default=64)),
                ('proposed_number_of_items', models.IntegerField(default=1)),
                (
                    'item_group',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='used_in_strategies',
                        to='items_for_render.ItemGroup',
                    ),
                ),
                (
                    'strategy',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        related_name='rules',
                        to='items_for_render.Strategy',
                    ),
                ),
            ],
            options={
                'ordering': ('slot_number', 'id'),
            },
        ),
    ]
