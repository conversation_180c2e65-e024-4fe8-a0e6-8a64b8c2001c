# Generated by Django 1.11.24 on 2020-07-01 17:36
from __future__ import unicode_literals

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('items_for_render', '0004_added_transparency_option_to_item_textures'),
    ]

    operations = [
        migrations.RenameField(
            model_name='item',
            old_name='only_on_top',
            new_name='can_be_on_top_horizontal',
        ),
        migrations.AddField(
            model_name='item',
            name='snapping',
            field=models.IntegerField(
                choices=[
                    (0, 'NO SNAPPING'),
                    (1, 'SNAP TO CENTER'),
                    (2, 'SNAP TO LEFT'),
                    (3, 'SNAP TO RIGHT'),
                    (4, 'SNAP TO LEFT OR RIGHT'),
                ],
                default=0,
            ),
        ),
        migrations.AddField(
            model_name='strategy',
            name='free_space_selection_approach',
            field=models.IntegerField(
                choices=[(0, 'DISTANCE FROM START'), (1, 'SMALLEST BOX FIRST')],
                default=0,
            ),
        ),
    ]
