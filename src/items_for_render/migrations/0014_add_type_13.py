# Generated by Django 3.2.14 on 2022-08-24 13:05

from django.db import (
    migrations,
    models,
)

import custom.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('items_for_render', '0013_alter_itemtexturevariant_availability'),
    ]

    operations = [
        migrations.AlterField(
            model_name='itemtexturevariant',
            name='availability',
            field=custom.models.fields.ChoiceArrayField(
                base_field=models.CharField(
                    blank=True,
                    choices=[
                        ['ShelfType:0_Material:0', 'ShelfType:0_Material:0'],
                        ['ShelfType:0_Material:1', 'ShelfType:0_Material:1'],
                        ['ShelfType:0_Material:3', 'ShelfType:0_Material:3'],
                        ['ShelfType:0_Material:4', 'ShelfType:0_Material:4'],
                        ['ShelfType:0_Material:5', 'ShelfType:0_Material:5'],
                        ['ShelfType:0_Material:6', 'ShelfType:0_Material:6'],
                        ['ShelfType:0_Material:7', 'ShelfType:0_Material:7'],
                        ['ShelfType:0_Material:8', 'ShelfType:0_Material:8'],
                        ['ShelfType:1_Material:0', 'ShelfType:1_Material:0'],
                        ['ShelfType:1_Material:1', 'ShelfType:1_Material:1'],
                        ['ShelfType:1_Material:2', 'ShelfType:1_Material:2'],
                        ['ShelfType:1_Material:3', 'ShelfType:1_Material:3'],
                        ['ShelfType:1_Material:4', 'ShelfType:1_Material:4'],
                        ['ShelfType:1_Material:6', 'ShelfType:1_Material:6'],
                        ['ShelfType:1_Material:7', 'ShelfType:1_Material:7'],
                        ['ShelfType:1_Material:8', 'ShelfType:1_Material:8'],
                        ['ShelfType:1_Material:9', 'ShelfType:1_Material:9'],
                        ['ShelfType:1_Material:10', 'ShelfType:1_Material:10'],
                        ['ShelfType:1_Material:11', 'ShelfType:1_Material:11'],
                        ['ShelfType:1_Material:12', 'ShelfType:1_Material:12'],
                        ['ShelfType:1_Material:13', 'ShelfType:1_Material:13'],
                        ['ShelfType:1_Material:14', 'ShelfType:1_Material:14'],
                        ['ShelfType:2_Material:0', 'ShelfType:2_Material:0'],
                        ['ShelfType:2_Material:1', 'ShelfType:2_Material:1'],
                        ['ShelfType:3_Material:0', 'ShelfType:3_Material:0'],
                        ['ShelfType:3_Material:1', 'ShelfType:3_Material:1'],
                        ['ShelfType:3_Material:2', 'ShelfType:3_Material:2'],
                        ['ShelfType:3_Material:3', 'ShelfType:3_Material:3'],
                        ['ShelfType:3_Material:4', 'ShelfType:3_Material:4'],
                        ['ShelfType:3_Material:5', 'ShelfType:3_Material:5'],
                        ['ShelfType:3_Material:6', 'ShelfType:3_Material:6'],
                        ['ShelfType:3_Material:7', 'ShelfType:3_Material:7'],
                        ['ShelfType:3_Material:8', 'ShelfType:3_Material:8'],
                        ['ShelfType:3_Material:9', 'ShelfType:3_Material:9'],
                        ['ShelfType:3_Material:10', 'ShelfType:3_Material:10'],
                        ['ShelfType:3_Material:11', 'ShelfType:3_Material:11'],
                        ['ShelfType:4_Material:0', 'ShelfType:4_Material:0'],
                        ['ShelfType:4_Material:1', 'ShelfType:4_Material:1'],
                        ['ShelfType:4_Material:2', 'ShelfType:4_Material:2'],
                        ['ShelfType:4_Material:3', 'ShelfType:4_Material:3'],
                        ['ShelfType:4_Material:4', 'ShelfType:4_Material:4'],
                        ['ShelfType:4_Material:5', 'ShelfType:4_Material:5'],
                        ['ShelfType:4_Material:6', 'ShelfType:4_Material:6'],
                        ['ShelfType:4_Material:7', 'ShelfType:4_Material:7'],
                    ],
                    max_length=25,
                ),
                blank=True,
                default=list,
                size=None,
            ),
        ),
    ]
