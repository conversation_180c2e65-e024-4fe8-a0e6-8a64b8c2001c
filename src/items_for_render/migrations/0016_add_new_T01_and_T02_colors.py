# Generated by Django 4.1.8 on 2023-05-29 13:43

from django.db import (
    migrations,
    models,
)

import custom.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('items_for_render', '0015_add_new_T03_and_T13_colors'),
    ]

    operations = [
        migrations.AlterField(
            model_name='itemtexturevariant',
            name='availability',
            field=custom.models.fields.ChoiceArrayField(
                base_field=models.CharField(
                    blank=True,
                    choices=[
                        ['ShelfType:0_Material:0', 'ShelfType:0_Material:0'],
                        ['ShelfType:0_Material:1', 'ShelfType:0_Material:1'],
                        ['ShelfType:0_Material:3', 'ShelfType:0_Material:3'],
                        ['ShelfType:0_Material:4', 'ShelfType:0_Material:4'],
                        ['ShelfType:0_Material:5', 'ShelfType:0_Material:5'],
                        ['ShelfType:0_Material:6', 'ShelfType:0_Material:6'],
                        ['ShelfType:0_Material:7', 'ShelfType:0_Material:7'],
                        ['ShelfType:0_Material:8', 'ShelfType:0_Material:8'],
                        ['ShelfType:0_Material:9', 'ShelfType:0_Material:9'],
                        ['ShelfType:0_Material:10', 'ShelfType:0_Material:10'],
                        ['ShelfType:1_Material:0', 'ShelfType:1_Material:0'],
                        ['ShelfType:1_Material:1', 'ShelfType:1_Material:1'],
                        ['ShelfType:1_Material:2', 'ShelfType:1_Material:2'],
                        ['ShelfType:1_Material:3', 'ShelfType:1_Material:3'],
                        ['ShelfType:1_Material:4', 'ShelfType:1_Material:4'],
                        ['ShelfType:1_Material:6', 'ShelfType:1_Material:6'],
                        ['ShelfType:1_Material:7', 'ShelfType:1_Material:7'],
                        ['ShelfType:1_Material:8', 'ShelfType:1_Material:8'],
                        ['ShelfType:1_Material:9', 'ShelfType:1_Material:9'],
                        ['ShelfType:1_Material:10', 'ShelfType:1_Material:10'],
                        ['ShelfType:1_Material:11', 'ShelfType:1_Material:11'],
                        ['ShelfType:1_Material:12', 'ShelfType:1_Material:12'],
                        ['ShelfType:1_Material:13', 'ShelfType:1_Material:13'],
                        ['ShelfType:1_Material:14', 'ShelfType:1_Material:14'],
                        ['ShelfType:1_Material:15', 'ShelfType:1_Material:15'],
                        ['ShelfType:1_Material:16', 'ShelfType:1_Material:16'],
                        ['ShelfType:1_Material:17', 'ShelfType:1_Material:17'],
                        ['ShelfType:1_Material:18', 'ShelfType:1_Material:18'],
                        ['ShelfType:2_Material:0', 'ShelfType:2_Material:0'],
                        ['ShelfType:2_Material:1', 'ShelfType:2_Material:1'],
                        ['ShelfType:3_Material:0', 'ShelfType:3_Material:0'],
                        ['ShelfType:3_Material:1', 'ShelfType:3_Material:1'],
                        ['ShelfType:3_Material:2', 'ShelfType:3_Material:2'],
                        ['ShelfType:3_Material:3', 'ShelfType:3_Material:3'],
                        ['ShelfType:3_Material:4', 'ShelfType:3_Material:4'],
                        ['ShelfType:3_Material:5', 'ShelfType:3_Material:5'],
                        ['ShelfType:3_Material:6', 'ShelfType:3_Material:6'],
                        ['ShelfType:3_Material:7', 'ShelfType:3_Material:7'],
                        ['ShelfType:3_Material:8', 'ShelfType:3_Material:8'],
                        ['ShelfType:3_Material:9', 'ShelfType:3_Material:9'],
                        ['ShelfType:3_Material:10', 'ShelfType:3_Material:10'],
                        ['ShelfType:3_Material:11', 'ShelfType:3_Material:11'],
                        ['ShelfType:3_Material:12', 'ShelfType:3_Material:12'],
                        ['ShelfType:3_Material:13', 'ShelfType:3_Material:13'],
                        ['ShelfType:3_Material:14', 'ShelfType:3_Material:14'],
                        ['ShelfType:3_Material:15', 'ShelfType:3_Material:15'],
                        ['ShelfType:3_Material:16', 'ShelfType:3_Material:16'],
                        ['ShelfType:3_Material:17', 'ShelfType:3_Material:17'],
                        ['ShelfType:3_Material:18', 'ShelfType:3_Material:18'],
                        ['ShelfType:3_Material:19', 'ShelfType:3_Material:19'],
                        ['ShelfType:3_Material:20', 'ShelfType:3_Material:20'],
                        ['ShelfType:4_Material:0', 'ShelfType:4_Material:0'],
                        ['ShelfType:4_Material:1', 'ShelfType:4_Material:1'],
                        ['ShelfType:4_Material:2', 'ShelfType:4_Material:2'],
                        ['ShelfType:4_Material:3', 'ShelfType:4_Material:3'],
                        ['ShelfType:4_Material:4', 'ShelfType:4_Material:4'],
                        ['ShelfType:4_Material:5', 'ShelfType:4_Material:5'],
                        ['ShelfType:4_Material:6', 'ShelfType:4_Material:6'],
                        ['ShelfType:4_Material:7', 'ShelfType:4_Material:7'],
                        ['ShelfType:4_Material:8', 'ShelfType:4_Material:8'],
                        ['ShelfType:4_Material:9', 'ShelfType:4_Material:9'],
                        ['ShelfType:4_Material:10', 'ShelfType:4_Material:10'],
                        ['ShelfType:4_Material:11', 'ShelfType:4_Material:11'],
                    ],
                    max_length=25,
                ),
                blank=True,
                default=list,
                size=None,
            ),
        ),
    ]
