# Generated by Django 4.1.8 on 2023-07-21 12:08

from django.db import (
    migrations,
    models,
)

import custom.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('items_for_render', '0016_add_new_T01_and_T02_colors'),
    ]

    operations = [
        migrations.AlterField(
            model_name='item',
            name='categories',
            field=custom.models.fields.ChoiceArrayField(
                base_field=models.CharField(
                    blank=True,
                    choices=[
                        ('bookcase', 'Bookcase'),
                        ('sideboard', 'Sideboard'),
                        ('tv-stand', 'Tv Stand'),
                        ('shoerack', 'Shoerack'),
                        ('wallstorage', 'Wall Storage'),
                        ('chest-of-drawers', 'Chest Of Drawers'),
                        ('vinyl_storage', 'Vinyl Storage'),
                    ],
                    max_length=25,
                ),
                blank=True,
                default=list,
                size=None,
            ),
        ),
        migrations.AlterField(
            model_name='item',
            name='corner_to_snap',
            field=models.CharField(
                choices=[
                    ('bottom_center', 'Bottom center'),
                    ('bottom_left', 'Bottom left'),
                    ('bottom_right', 'Bottom right'),
                    ('top_center', 'Top center'),
                    ('top_left', 'Top left'),
                    ('top_right', 'Top right'),
                ],
                default='bottom_center',
                max_length=50,
            ),
        ),
        migrations.AlterField(
            model_name='itemgroup',
            name='categories',
            field=custom.models.fields.ChoiceArrayField(
                base_field=models.CharField(
                    blank=True,
                    choices=[
                        ('bookcase', 'Bookcase'),
                        ('sideboard', 'Sideboard'),
                        ('tv-stand', 'Tv Stand'),
                        ('shoerack', 'Shoerack'),
                        ('wallstorage', 'Wall Storage'),
                        ('chest-of-drawers', 'Chest Of Drawers'),
                        ('vinyl_storage', 'Vinyl Storage'),
                    ],
                    max_length=25,
                ),
                blank=True,
                default=list,
                size=None,
            ),
        ),
    ]
