from rest_framework import serializers

from items_for_render.models import (
    Item,
    ItemGroup,
    ItemTextureVariant,
    Strategy,
    StrategyGroupRule,
)


class ItemGroupSerializer(serializers.ModelSerializer):
    class Meta:
        model = ItemGroup
        fields = (
            'id',
            'name',
            'items',
            'categories',
        )


class ItemTextureSerializer(serializers.ModelSerializer):
    class Meta:
        model = ItemTextureVariant
        fields = (
            'id',
            'name',
            'texture_file',
            'is_transparent',
            'availability',
        )


class ItemSerializer(serializers.ModelSerializer):
    textures = ItemTextureSerializer(many=True, required=True)
    snapping = serializers.ReadOnlyField(source='get_snapping_display')

    def create(self, validated_data):
        textures_data = validated_data.pop('textures')
        item = Item.objects.create(**validated_data)
        ItemTextureVariant.objects.bulk_create(
            ItemTextureVariant(item=item, **texture_data)
            for texture_data in textures_data
        )
        return item

    class Meta:
        model = Item
        fields = (
            'id',
            'textures',
            'snapping',
            'name',
            'enabled',
            'created_at',
            'model',
            'shadow_model',
            'shadow_texture',
            'categories',
            'scale_x_min',
            'scale_x_max',
            'scale_y_min',
            'scale_y_max',
            'scale_z_min',
            'scale_z_max',
            'default_x',
            'default_y',
            'default_z',
            'max_y',
            'min_y',
            'margin_min_left',
            'margin_min_right',
            'margin_min_top',
            'can_be_on_top_horizontal',
            'outside',
            'corner_to_snap',
            'snapping_corner_offset_x',
            'snapping_corner_offset_y',
            'snapping_corner_offset_z',
        )
        depth = 1


class StrategyGroupRuleSerializer(serializers.ModelSerializer):
    class Meta:
        model = StrategyGroupRule
        fields = (
            'id',
            'description',
            'min_number_of_items',
            'max_number_of_items',
            'proposed_number_of_items',
            'item_group',
        )


class StrategySerializer(serializers.ModelSerializer):
    rules = StrategyGroupRuleSerializer(many=True)
    number_of_items_approach = serializers.ReadOnlyField(
        source='get_number_of_items_approach_display'
    )
    free_space_selection_approach = serializers.ReadOnlyField(
        source='get_free_space_selection_approach_display'
    )

    class Meta:
        model = Strategy
        fields = (
            'id',
            'rules',
            'number_of_items_approach',
            'free_space_selection_approach',
            'name',
            'description',
            'number_of_items',
        )
        depth = 1
