from rest_framework.response import Response
from rest_framework.views import APIView

from items_for_render.models import (
    Item,
    ItemGroup,
    Strategy,
)
from items_for_render.serializers import (
    ItemGroupSerializer,
    ItemSerializer,
    StrategySerializer,
)


class ItemsList(APIView):
    def get(self, request):
        additional_items = Item.objects.filter(enabled=True)
        all_groups = ItemGroup.objects.all()
        all_strategies = Strategy.objects.all()
        response = {
            'items': ItemSerializer(additional_items, many=True).data,
            'groups': ItemGroupSerializer(all_groups, many=True).data,
            'strategies': StrategySerializer(all_strategies, many=True).data,
        }
        return Response(response)
