# This file contains compatibility utils for migration from text-based <PERSON><PERSON><PERSON><PERSON> to
# postgres native <PERSON><PERSON><PERSON><PERSON>. It should be removed when dependent migrations are squashed

import json

from django.db import models


migration_sql = 'ALTER TABLE "{0}" ALTER COLUMN {1} TYPE JSONB USING {1}::JSONB'
migration_reverse_sql = 'ALTER TABLE "{0}" ALTER COLUMN {1} TYPE TEXT USING {1}::TEXT'


class MigrationJSONDecoder(json.JSONDecoder):
    """Handle JSON stored as text or jsonb in PostgreSQL."""

    def decode(self, obj, *args, **kwargs):
        obj = super().decode(obj, *args, **kwargs)
        return self.post_decode(obj)

    @classmethod
    def post_decode(cls, obj):
        if isinstance(obj, str):
            obj = json.loads(obj)
        return obj


class MigrationJ<PERSON>NField(models.JSONField):
    """
    Handle migration from jsonfield.<PERSON><PERSON><PERSON><PERSON> to django native <PERSON><PERSON><PERSON><PERSON>.

    This is our own compat field that allows us to remove jsonfield lib without
    having to squash migrations. We just replace occurrences of jsonfield.<PERSON><PERSON><PERSON><PERSON>
    in migrations with this field which works with both ways of writing json.

    See: https://tech.polyconseil.fr/migrating-from-json-to-jsonb.html
    """

    def __init__(self, **kwargs):
        self.decoder = MigrationJSONDecoder
        super().__init__(**kwargs)

    def from_db_value(self, value, _expression, _connection, _context):
        return self.decoder.post_decode(value)
