from __future__ import unicode_literals

import django.contrib.postgres.fields.jsonb
import django.db.models.deletion
import django.utils.timezone

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('contenttypes', '0002_remove_content_type_name'),
    ]

    operations = [
        migrations.CreateModel(
            name='EarlyAccessMail',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('email', models.CharField(max_length=500)),
                ('origin', models.TextField()),
                ('ip', models.TextField(blank=True, null=True)),
                ('enabled', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('token', models.TextField(blank=True, null=True)),
                ('usage_counter', models.IntegerField(default=0)),
            ],
            options={
                'verbose_name': 'Early access entry',
                'verbose_name_plural': 'Early access entries',
            },
        ),
        migrations.CreateModel(
            name='MailChimpTaskRetry',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('scheduled_at', models.DateTimeField(db_index=True, null=True)),
                (
                    'status',
                    models.IntegerField(
                        choices=[(1, 'To Retry'), (10, 'Sent'), (100, 'Failed')],
                        default=1,
                    ),
                ),
                ('retry', models.IntegerField(default=5)),
                ('kwargs', django.contrib.postgres.fields.jsonb.JSONField(blank=True)),
                (
                    'fail_info',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, default=None, null=True
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(
                        db_index=True,
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name='Created at',
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='MailingFlowSettings',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'flow_designation',
                    models.CharField(db_index=True, max_length=128, unique=True),
                ),
                ('active', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='MailingFlowStatus',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('time', models.DateTimeField(auto_now_add=True, db_index=True)),
                ('flow_designation', models.CharField(db_index=True, max_length=64)),
                ('object_id', models.PositiveIntegerField()),
                (
                    'status',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (2, 'failure'),
                            (3, 'success'),
                            (4, 'goal'),
                            (5, 'object gone'),
                        ],
                        db_index=True,
                    ),
                ),
                (
                    'email_address',
                    models.EmailField(
                        blank=True, db_index=True, max_length=254, null=True
                    ),
                ),
                (
                    'email_language',
                    models.CharField(
                        choices=[
                            ('en', 'English'),
                            ('de', 'Deutsch'),
                            ('fr', 'Français'),
                        ],
                        db_index=True,
                        max_length=2,
                        null=True,
                    ),
                ),
                (
                    'template_class',
                    models.CharField(db_index=True, max_length=64, null=True),
                ),
                ('subject', models.CharField(blank=True, max_length=256, null=True)),
                (
                    'opens',
                    models.PositiveSmallIntegerField(
                        blank=True, db_index=True, null=True
                    ),
                ),
                (
                    'clicks',
                    models.PositiveSmallIntegerField(
                        blank=True, db_index=True, null=True
                    ),
                ),
                ('version', models.PositiveIntegerField(db_index=True, default=0)),
                (
                    'additional_data',
                    django.contrib.postgres.fields.jsonb.JSONField(
                        blank=True, null=True
                    ),
                ),
                (
                    'content_type',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='contenttypes.ContentType',
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='MailingSchedule',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('flow_designation', models.CharField(db_index=True, max_length=64)),
                ('scheduled_at', models.DateTimeField()),
                (
                    'status',
                    models.CharField(
                        choices=[
                            ('waiting', 'Waiting'),
                            ('processing', 'Processing'),
                            ('sent', 'Sent'),
                            ('revoked', 'Revoked'),
                        ],
                        default='waiting',
                        max_length=10,
                    ),
                ),
                (
                    'origin',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='mailing.MailingFlowStatus',
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='MailStatistics',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('created_at', models.DateTimeField(db_index=True)),
                ('bounces', models.IntegerField()),
                ('forwards', models.IntegerField()),
                ('opens', models.IntegerField()),
                ('clicks', models.IntegerField()),
                ('facebook_likes', models.IntegerField()),
            ],
            options={
                'verbose_name': 'Mail statistics',
                'verbose_name_plural': 'Mail statistics',
            },
        ),
        migrations.CreateModel(
            name='MandrillRejectReportHistory',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('last_record_time', models.DateTimeField()),
            ],
        ),
        migrations.CreateModel(
            name='MandrillReportHistory',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('for_date', models.DateField()),
                ('report_id', models.CharField(max_length=128)),
                ('is_imported', models.BooleanField(default=False)),
            ],
        ),
        migrations.CreateModel(
            name='MandrillTagReportHistory',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('tag', models.CharField(max_length=64)),
                ('last_record_time', models.DateTimeField()),
            ],
        ),
        migrations.CreateModel(
            name='NewsletterMail',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('email', models.CharField(max_length=500)),
                ('origin', models.TextField()),
                ('ip', models.TextField(blank=True, null=True)),
                (
                    'event',
                    models.IntegerField(
                        choices=[
                            (1, 'first betatests'),
                            (2, 'mvp'),
                            (3, 'waiting for iphone'),
                            (4, 'send me the app link'),
                            (5, 'new page, new newsletters'),
                            (6, 'Amoreli popup'),
                        ],
                        default=5,
                    ),
                ),
                ('enabled', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('unsub_token', models.TextField(blank=True, null=True)),
            ],
            options={
                'verbose_name': 'Newsletter entry',
                'verbose_name_plural': 'Newsletter entries',
            },
        ),
        migrations.CreateModel(
            name='ProductDeliveredBlacklistedEmail',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('email', models.EmailField(max_length=254)),
            ],
        ),
        migrations.CreateModel(
            name='RetargetingBlacklist',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('email', models.EmailField(max_length=200, unique=True)),
                ('source', models.CharField(blank=True, max_length=200, null=True)),
                ('date_added', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
