{% extends "admin/change_list.html" %}{% load static %}

{% block extrastyle %}
    {{ block.super }}
    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">
    <style>
        .small-table td {
            padding: 10px;
        }

        @media print {
            .table td,
            .table th {
                background-color: inherit !important;
            }

            .table-striped tr:nth-of-type(odd) {
                background-color: #E6E6E6 !important;
                -webkit-print-color-adjust: exact;
            }

            .table-striped td:nth-of-type(odd) {
                border-right: 2px solid !important;
                -webkit-print-color-adjust: exact;
            }
        }

        .table-small-grinder td {
            line-height: 0.5 !important;
        }

        .table-small-grinder .higher td {
            line-height: 1.2 !important;
        }
{#        .datetimeshortcuts:nth-of-type(2n) {#}
{#            display: none;#}
{#        }#}

    </style>
{% endblock %}

{% block content %}
    {{ block.super }}
    {% if mfs_statistics and mfs_statistics|length > 0 %}<div class="module" style="float:left; width: 100%">
        {% for flow_designation, templates in mfs_statistics.items %}
            {% for template, languages in templates.items %}
                    <div class="col-md-4">
                        <table class="table table-bordered table-small-grinder table-striped">
                            <tr class="higher" style="font-weight: bold; border-bottom: 2px solid;">
                                <td class="col-md-5">{{ template|default_if_none:flow_designation }}</td>
                                <td>Sent</td>
                                <td>Opened</td>
                                <td>Clicked</td>
                                <td>Goals</td>
                                <td>Emails #</td>
                            </tr>
                            {% for language, row in languages.items %}
                                <tr>
                                    <td>{{ language }}</td>
                                    <td>{{ row.sent }}</td>
                                    <td>{{ row.opened }}</td>
                                    <td>{{ row.clicked }}</td>
                                    <td>{{ row.goals }}</td>
                                    <td>{{ row.emails_count }}</td>
                                </tr>
                            {% endfor %}
                        </table>
                    </div>
                {% endfor %}
                <br style="clear:both"/>
            {% endfor %}
    </div>{% endif %}
{% endblock %}
