from django.contrib import admin
from django.urls import reverse
from django.utils.html import format_html

from admin_customization.admin import ButtonActionsBaseAdmin
from material_recovery.exporters import MaterialRecoveryPricingFactorExporter
from material_recovery.importers import MaterialRecoveryPricingFactorImporter
from material_recovery.models import (
    MaterialRecoveryPricingFactor,
    MaterialRecoveryProduct,
    MaterialRecoveryReport,
    MaterialRecoverySampleBox,
    MaterialRecoveryWarehouse,
    ShelfMarket,
)
from production_margins.admin_mixins import (
    CSVExportActionMixin,
    CSVImportActionMixin,
)


class MaterialRecoveryWithReportLinkAdminMixin:
    def recovery_report_link(self, obj):
        if obj.recovery_report is None:
            return '-'

        link = reverse(
            'admin:material_recovery_materialrecoveryreport_change',
            args=[obj.recovery_report.pk],
        )
        return format_html('<a href="{}">{}</a>', link, obj.recovery_report)


class MaterialRecoveryProductAdmin(
    MaterialRecoveryWithReportLinkAdminMixin,
    admin.ModelAdmin,
):
    list_display = ('id', 'recovery_report_link', 'recovered_at', 'manufactor')
    raw_id_fields = ('recovery_report',)


class MaterialRecoverySampleBoxAdmin(
    MaterialRecoveryWithReportLinkAdminMixin,
    admin.ModelAdmin,
):
    list_display = ('id', 'amount', 'color', 'recovery_report_link', 'created_at')
    raw_id_fields = ('recovery_report',)


class ShelfMarketAdmin(MaterialRecoveryWithReportLinkAdminMixin, admin.ModelAdmin):
    list_display = (
        'id',
        'product',
        'recovery_report_link',
        'created_at',
    )
    raw_id_fields = ('recovery_report',)


class MaterialRecoveryReportAdmin(admin.ModelAdmin):
    date_hierarchy = 'created_at'
    list_filter = ('report_type',)
    list_display = (
        'id',
        'admin_report',
        'manufactor_report',
        'created_at',
        'report_type',
    )

    search_fields = ('id',)


class MaterialRecoveryWarehouseAdmin(admin.ModelAdmin):
    list_filter = (
        'type',
        'color',
    )
    list_display = (
        'id',
        'codename',
        'color',
        'type',
        'amount',
        'updated_at',
    )


class MaterialRecoveryPricingFactorAdmin(
    CSVExportActionMixin,
    CSVImportActionMixin,
    ButtonActionsBaseAdmin,
):
    model = MaterialRecoveryPricingFactor

    list_filter = (
        'type',
        'color',
    )

    list_display = (
        'id',
        'codename',
        'color',
        'type',
        'price',
        'updated_at',
    )

    search_fields = (
        'codename',
        'color',
    )

    actions = ('export_to_csv',)

    button_actions = ('import_from_csv',)

    @admin.action(description='Export selected items as CSV')
    def export_to_csv(self, request, queryset):
        return self.export_to_csv_file(
            request,
            queryset=queryset,
            filename='Material_Recovery_Pricing_Factors',
            exporter_class=MaterialRecoveryPricingFactorExporter,
        )

    @admin.action(description='Import from CSV')
    def import_from_csv(self, request):
        return self.import_csv_file_action(
            request,
            MaterialRecoveryPricingFactorImporter,
        )


admin.site.register(MaterialRecoveryWarehouse, MaterialRecoveryWarehouseAdmin)
admin.site.register(MaterialRecoveryProduct, MaterialRecoveryProductAdmin)
admin.site.register(MaterialRecoverySampleBox, MaterialRecoverySampleBoxAdmin)
admin.site.register(ShelfMarket, ShelfMarketAdmin)
admin.site.register(MaterialRecoveryReport, MaterialRecoveryReportAdmin)
admin.site.register(MaterialRecoveryPricingFactor, MaterialRecoveryPricingFactorAdmin)
