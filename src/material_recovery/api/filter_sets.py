import django_filters

from producers.models import Product


class NumberInFilter(django_filters.BaseInFilter, django_filters.NumberFilter):
    """
    Number Filter with "in" expression sending comma separated string
    ?id=1,2,3
    """


class ProductMaterialRecoveryFilterSet(django_filters.FilterSet):
    ids = NumberInFilter(field_name='id', lookup_expr='in')

    class Meta:
        model = Product
        fields = ('id', 'is_recovered')
