from typing import (
    Dict,
    List,
)

from rest_framework import serializers
from rest_framework.relations import PrimaryKeyRelatedField
from rest_framework.validators import UniqueValidator

from material_recovery.constants import SAMPLE_BOX_COLOR_TO_MATERIAL_CODENAMES
from material_recovery.exceptions import (
    MaterialRecoveryWarehouseLowAvailabilityException,
)
from material_recovery.extractors import (
    MaterialsExtractor,
    ProductElementsExtractor,
)
from material_recovery.models import (
    MaterialRecoveryProduct,
    MaterialRecoveryReport,
    MaterialRecoverySampleBox,
    MaterialRecoveryWarehouse,
    ShelfMarket,
)
from producers.errors import SerializationMissingError
from producers.models import Product
from production_margins.data_management.serializers.fields import (
    ManyRelatedFieldWithCommaSeparatedValue,
)


class ManyRelatedFieldWithCommaSeparatedQueryParams(
    ManyRelatedFieldWithCommaSeparatedValue
):
    def to_internal_value(self, data):
        splitted_data = data[0].split(',')
        return super(
            ManyRelatedFieldWithCommaSeparatedQueryParams, self
        ).to_internal_value(splitted_data)


class PrimaryKeyRelatedFieldWithContextQueryset(serializers.PrimaryKeyRelatedField):
    def get_queryset(self):
        return self.context.get('queryset')


class BulkIdsParamSerializer(serializers.Serializer):
    ids = ManyRelatedFieldWithCommaSeparatedQueryParams(
        representation_field='ids',
        child_relation=PrimaryKeyRelatedFieldWithContextQueryset(),
    )


def serialize_elements(elements_by_name_with_size: dict):
    return [
        ElementSerializer({'name': name_with_size, 'amount': amount}).data
        for name_with_size, amount in elements_by_name_with_size.items()
    ]


class ProductColorNameSerializer(serializers.Serializer):
    name = serializers.CharField(max_length=255)
    value = serializers.CharField(max_length=255)

    @classmethod
    def from_choices(cls):
        return cls.sorted_colors(SAMPLE_BOX_COLOR_TO_MATERIAL_CODENAMES.keys())

    @classmethod
    def sorted_colors(cls, colors):
        return cls(
            [{'name': name, 'value': name} for name in sorted(colors)],
            many=True,
        )


class MaterialRecoveryReportSerializer(serializers.ModelSerializer):
    class Meta:
        model = MaterialRecoveryReport
        fields = ('id', 'get_report_type_display', 'manufactor_report', 'created_at')


class MaterialRecoverySampleBoxSerializer(serializers.ModelSerializer):
    recovery_report = MaterialRecoveryReportSerializer(read_only=True)

    class Meta:
        model = MaterialRecoverySampleBox
        fields = ('id', 'amount', 'color', 'created_at', 'recovery_report')


class ProductMaterialRecoverySerializer(serializers.ModelSerializer):
    manufactor = serializers.CharField(source='manufactor.name', default='')
    color = serializers.ReadOnlyField(source='color_with_shelf_name')
    carrier = serializers.SerializerMethodField()
    elements = serializers.SerializerMethodField()
    recovery_report = MaterialRecoveryReportSerializer()
    depth = serializers.CharField(source='cached_depth')

    class Meta:
        model = Product
        fields = (
            'id',
            'manufactor',
            'color',
            'depth',
            'recovered_at',
            'created_at',
            'carrier',
            'elements',
            'recovery_report',
        )

    def get_carrier(self, obj):
        return obj.get_carrier_name()

    def get_elements(self, obj: 'Product') -> dict:
        try:
            serialization = obj.get_serialized_product_info()
        except SerializationMissingError:
            return RecoverableElementsSerializers().data

        elements_extractor = ProductElementsExtractor(product=obj)
        product_elements = serialization['item']['elements']
        (
            supports,
            verticals,
        ) = elements_extractor.extract_verticals_and_supports(product_elements)

        material_extractor = MaterialsExtractor()
        materials = serialization['materials']
        recovered_materials = (
            material_extractor.extract_fittings_semiproducts_packaging(materials)
        )

        return RecoverableElementsSerializers(
            {
                'verticals': serialize_elements(verticals),
                'supports': serialize_elements(supports),
                'fittings': serialize_elements(recovered_materials),
            }
        ).data


class ElementSerializer(serializers.Serializer):
    name = serializers.CharField()
    amount = serializers.IntegerField()


class RecoverableElementsSerializers(serializers.Serializer):
    verticals = ElementSerializer(many=True)
    supports = ElementSerializer(many=True)
    fittings = ElementSerializer(many=True)


class CreateShelfMarketSerializer(serializers.ModelSerializer):
    product = PrimaryKeyRelatedField(
        queryset=Product.objects.exclude(is_recovered=True),
        validators=[UniqueValidator(queryset=ShelfMarket.objects.all())],
    )

    class Meta:
        model = ShelfMarket
        fields = ('product', 'quality_factor')


class ShelfMarketSerializer(serializers.ModelSerializer):
    recovery_report = MaterialRecoveryReportSerializer(read_only=True)
    product = ProductMaterialRecoverySerializer(read_only=True)

    class Meta:
        model = ShelfMarket
        fields = ('id', 'product', 'quality_factor', 'created_at', 'recovery_report')


class CreateProductMaterialRecoveryReportListSerializer(serializers.ListSerializer):
    def create(self, validated_data: List[Dict]):
        report = MaterialRecoveryReport.from_product_elements(validated_data)
        report.save()

        for recovered_product in validated_data:
            product = recovered_product['product_id']
            elements = recovered_product['elements']
            MaterialRecoveryWarehouse.objects.update_or_create_by_product(
                product,
                elements,
            )
            product.recovery_report = report
            product.save()
        return report


class MaterialRecoverySerializer(serializers.ModelSerializer):
    class Meta:
        model = MaterialRecoveryProduct
        fields = ('id',)


class CreateProductMaterialRecoveryReportSerializer(serializers.Serializer):
    elements = ElementSerializer(many=True)
    product_id = PrimaryKeyRelatedField(queryset=MaterialRecoveryProduct.objects.all())

    class Meta:
        fields = ('elements', 'product_id')
        list_serializer_class = CreateProductMaterialRecoveryReportListSerializer


class CreateSampleBoxMaterialRecoveryReportListSerializer(serializers.ListSerializer):
    def create(self, validated_data):
        report = MaterialRecoveryReport.from_sample_boxes(validated_data)
        report.save()

        for recovered_sample in validated_data:
            sample_box = recovered_sample['sample_box_id']
            MaterialRecoveryWarehouse.objects.update_or_create_by_sample_box(sample_box)
            sample_box.recovery_report = report
            sample_box.save()
        return report


class CreateSampleBoxMaterialRecoveryReportSerializer(serializers.Serializer):
    sample_box_id = PrimaryKeyRelatedField(
        queryset=MaterialRecoverySampleBox.objects.all()
    )

    class Meta:
        fields = ('sample_box_id',)
        list_serializer_class = CreateSampleBoxMaterialRecoveryReportListSerializer


class CreateShelfMarketReportListSerializer(serializers.ListSerializer):
    def create(self, validated_data):
        report = MaterialRecoveryReport.from_shelf_market(validated_data)
        report.save()
        for shelf_market_by_id in validated_data:
            shelf_market = shelf_market_by_id['shelf_market_id']
            shelf_market.recovery_report = report
            shelf_market.save()
        return report


class CreateShelfMarketReportSerializer(serializers.Serializer):
    shelf_market_id = PrimaryKeyRelatedField(queryset=ShelfMarket.objects.all())

    class Meta:
        fields = ('shelf_market_id',)
        list_serializer_class = CreateShelfMarketReportListSerializer


class MaterialRecoveryWarehouseSerializer(serializers.ModelSerializer):
    class Meta:
        model = MaterialRecoveryWarehouse
        fields = ('id', 'codename', 'color', 'type', 'amount')


class DecreaseAmountSerializerListSerializer(serializers.ListSerializer):
    def save(self, **kwargs):
        amount = self.validated_data[0]['amount']

        report = MaterialRecoveryReport.from_warehouse(self.validated_data)
        report.save()

        for warehouse_by_id in self.validated_data:
            warehouse = warehouse_by_id['id']
            try:
                warehouse.decrease_amount(amount)
            except MaterialRecoveryWarehouseLowAvailabilityException:
                warehouse.amount = 0
            warehouse.recovery_report = report
            warehouse.save()
        return report


class DecreaseAmountSerializer(serializers.Serializer):
    id = PrimaryKeyRelatedField(queryset=MaterialRecoveryWarehouse.objects.all())
    amount = serializers.IntegerField()

    class Meta:
        fields = ('id', 'amount')
        list_serializer_class = DecreaseAmountSerializerListSerializer
