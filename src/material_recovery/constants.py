from decimal import Decimal

from custom.enums import (
    Type01Color,
    Type02Color,
    Type03Color,
    VeneerType01Color,
)

SHELF_MARKET_PRICING_IN_PLN = {
    'T': Decimal('100.00'),
    'Ih': Decimal('10.00'),
    'Iv': <PERSON><PERSON><PERSON>('10.00'),
    'Px': <PERSON><PERSON><PERSON>('40.00'),
    'Pz': <PERSON><PERSON><PERSON>('40.00'),
    'Pe': <PERSON><PERSON><PERSON>('40.00'),
    'Pf': Decimal('40.00'),
    'Pb': Decimal('40.00'),
    'Ll': Decimal('20.00'),
    'G': Decimal('20.00'),
    'D': {
        'A': Decimal('50.00'),
        'B': Decimal('50.00'),
        'C': Decimal('50.00'),
        'D': Decimal('120.00'),
        'E': Decimal('120.00'),
        'F': Decimal('120.00'),
        'G': Decimal('120.00'),
    },
    'T1': Decimal('300.00'),
    'T2': Decimal('150.00'),
    'F1': Decimal('300.00'),
}

ELEMENTS_FITTINGS_AND_SEMIPRODUCTS_INCLUDED = [
    'fitting_hinge_black_cover-cap-hinge',
    'fitting_hinge_black_door-bluemotion',
    'fitting_hinge_black_door-spring',
    'fitting_hinge_black_mounting-plate',
    'fitting_slide_indaux_left-rail-drawer-270',
    'fitting_slide_indaux_left-rail-drawer-350',
    'fitting_slide_indaux_right-rail-drawer-270',
    'fitting_slide_indaux_right-rail-drawer-350',
    'fitting_slide_indaux_drawer-locking-mechanism',
    'packaging_other_assembly_plywood-tool-board',
    'semiproduct_cut-out_DTR_plywood-tool-board',
    'semiproduct_cut-out_INX_plywood-tool-board',
    'semiproduct_cut-out_MPL_plywood-tool-board',
    'semiproduct_cut-out_NOV_plywood-tool-board',
    'semiproduct_cut-out_S93_plywood-tool-board',
    'tool_board',
    'packaging_other_orange_plywood-tool-wrench',
    'semiproduct_cut-out_DTR_plywood-tool-wrench',
    'semiproduct_cut-out_INX_plywood-tool-wrench',
    'semiproduct_cut-out_MPL_plywood-tool-wrench',
    'semiproduct_cut-out_NOV_plywood-tool-wrench',
    'tool_wrench',
]

TOOL_BOARD_AND_WRENCH_CODENAME_TO_NAME = {
    # 'deseczka montażowa'
    'packaging_other_assembly_plywood-tool-board': 'tool_board',
    'semiproduct_cut-out_DTR_plywood-tool-board': 'tool_board',
    'semiproduct_cut-out_INX_plywood-tool-board': 'tool_board',
    'semiproduct_cut-out_MPL_plywood-tool-board': 'tool_board',
    'semiproduct_cut-out_NOV_plywood-tool-board': 'tool_board',
    'semiproduct_cut-out_S93_plywood-tool-board': 'tool_board',
    # 'klucz do podnoszenia'
    'packaging_other_orange_plywood-tool-wrench': 'tool_wrench',
    'semiproduct_cut-out_DTR_plywood-tool-wrench': 'tool_wrench',
    'semiproduct_cut-out_INX_plywood-tool-wrench': 'tool_wrench',
    'semiproduct_cut-out_MPL_plywood-tool-wrench': 'tool_wrench',
    'semiproduct_cut-out_NOV_plywood-tool-wrench': 'tool_wrench',
}

SAMPLE_BOX_COLOR_TO_MATERIAL_CODENAMES = {
    Type01Color.WHITE.color_name: 'material_plywood_HPL-white_18',
    Type01Color.BLACK.color_name: 'material_plywood_HPL-black_18',
    Type01Color.GREY.color_name: 'material_plywood_HPL-gray_18',
    Type01Color.RED.color_name: 'material_plywood_classic-red_18',
    Type01Color.YELLOW.color_name: 'material_plywood_yellow_18',
    Type01Color.DUSTY_PINK.color_name: 'material_plywood_dusty-pink_18',
    Type02Color.WHITE.color_name: 'material_chipboard_melamine-snow-white_18',
    Type02Color.TERRACOTTA.color_name: 'material_chipboard_melamine-ceramic-red_18',
    Type02Color.MIDNIGHT_BLUE.color_name: 'material_chipboard_melamine-indigo-blue_18',
    Type02Color.SAND.color_name: 'material_chipboard_melamine-sand-beige_18',
    Type02Color.MATTE_BLACK.color_name: 'material_chipboard_melamine-black-mat_18',
    Type02Color.SKY_BLUE.color_name: 'material_chipboard_melamine-blue_18',
    Type02Color.BURGUNDY.color_name: 'material_chipboard_melamine-burgundy_18',
    Type02Color.COTTON.color_name: 'material_chipboard_melamine-cotton_18',
    Type02Color.GRAY.color_name: 'material_chipboard_melamine-gray_18',
    Type02Color.DARK_GRAY.color_name: 'material_chipboard_melamine-gray_18',
    Type02Color.MUSTARD_YELLOW.color_name: 'material_chipboard_melamine-sand-beige_18',
    Type03Color.WHITE.color_name: 'material_chipboard_melamine-white_18',
    Type03Color.BEIGE.color_name: 'material_chipboard_melamine-cashmere_18',
    Type03Color.GRAPHITE.color_name: 'material_chipboard_melamine-graphite_18',
    Type03Color.BEIGE_PINK.color_name: 'material_chipboard_melamine-pink_18',
    VeneerType01Color.ASH.color_name: 'material_chipboard_veneer-ash_18',
    VeneerType01Color.OAK.color_name: 'material_chipboard_veneer-oak_18',
    VeneerType01Color.DARK_OAK.color_name: 'material_chipboard_veneer-dark-oak_18',
    Type01Color.BLUE.color_name: 'material_plywood_cobalt-blue_19',
    Type01Color.DARK_BROWN.color_name: 'material_plywood_dark-brown_19',
    Type02Color.REISINGERS_PINK.color_name: (
        'material_chipboard_melamine-reisinger-pink_18'
    ),
    Type02Color.SAGE_GREEN.color_name: 'material_chipboard_melamine-sage-green_18',
    Type02Color.STONE_GRAY.color_name: 'material_chipboard_melamine-stone-gray_18',
    Type02Color.WALNUT.color_name: 'material_chipboard_melamine-stone-gray_18',
}
