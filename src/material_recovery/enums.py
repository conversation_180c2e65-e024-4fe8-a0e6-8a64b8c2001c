import enum

from django.db import models

from custom.enums import ChoicesMixin


class MaterialRecoveryReportTypeEnum(models.IntegerChoices):
    ELEMENTS = 1
    SAMPLES = 2
    SHELF_MARKET = 3
    WAREHOUSE = 4


class MaterialRecoveryWarehouseTypeEnum(ChoicesMixin, enum.StrEnum):
    SAMPLES = 'samples'
    ELEMENTS = 'elements'
    FITTINGS = 'fittings'
    WAREHOUSE = 'warehouse'


class MaterialRecoveryPricingFactorType(models.TextChoices):
    ELEMENTS = 'elements', 'Elements'
    FITTINGS = 'fittings', 'Fittings'
