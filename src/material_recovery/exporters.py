from rest_framework import serializers

from material_recovery.models import MaterialRecoveryPricingFactor
from production_margins.data_management.exporters import BaseSerializedItemsExporter

MATERIAL_RECOVERY_PRICING_FACTOR_EXPORT_COLUMNS: list[str] = [
    'id',
    'codename',
    'color',
    'type',
    'price',
]


class MaterialRecoveryPricingFactorSerializer(serializers.ModelSerializer):
    class Meta:
        model = MaterialRecoveryPricingFactor
        fields = MATERIAL_RECOVERY_PRICING_FACTOR_EXPORT_COLUMNS


class MaterialRecoveryPricingFactorExporter(BaseSerializedItemsExporter):
    dict_export_fields = MATERIAL_RECOVERY_PRICING_FACTOR_EXPORT_COLUMNS[1:]
    dict_export_key = MATERIAL_RECOVERY_PRICING_FACTOR_EXPORT_COLUMNS[0]
    serializer = MaterialRecoveryPricingFactorSerializer
    export_item_prefetch_related = ()

    @property
    def default_export_queryset(self):
        return MaterialRecoveryPricingFactor.objects.all()
