from collections import defaultdict

from material_recovery.constants import (
    ELEMENTS_FITTINGS_AND_SEMIPRODUCTS_INCLUDED,
    TOOL_BOARD_AND_WRENCH_CODENAME_TO_NAME,
)


def is_recoverable_fitting(name: str) -> bool:
    return name in ELEMENTS_FITTINGS_AND_SEMIPRODUCTS_INCLUDED


def element_type_by_name(name: str) -> str:
    return 'fittings' if is_recoverable_fitting(name) else 'elements'


class ProductElementsExtractor:
    VERTICAL_ELEM_TYPE = 'V'
    SUPPORT_ELEM_TYPE = 'S'

    VERTICAL_ELEM_NAME = 'vertical'
    SUPPORT_ELEM_NAME = 'support'

    def __init__(self, product):
        self.product = product

    def extract_verticals_and_supports(self, product_elements) -> [dict, dict]:
        elements_verticals = defaultdict(int)
        elements_support = defaultdict(int)

        for element in product_elements:
            if self.is_not_extractable(element):
                continue

            if self.is_not_drilled_vertical(element):
                element_name_with_size = self.element_vertical_name(element)
                elements_verticals[element_name_with_size] += 1

            if self.is_support(element):
                element_name_with_size = self.element_support_name(element)
                elements_support[element_name_with_size] += 1
        return elements_support, elements_verticals

    def is_not_extractable(self, element):
        return self.elem_type(element) not in {'V', 'S'} or (
            self.product.is_complaint() and not element.get('complaint', False)
        )

    def element_support_name(self, element):
        return f'{self.SUPPORT_ELEM_NAME}_{self.get_elem_height(element)}'

    def element_vertical_name(self, element):
        element_depth = element.get('depth', element.get('row_depth', 0) // 100)
        element_height = self.get_elem_height(element)
        return f'{self.VERTICAL_ELEM_NAME}_{element_depth}_{element_height}'

    def get_elem_height(self, element):
        return element.get('height_name', '')

    def elem_type(self, element):
        return element.get('elem_type')

    def is_not_drilled_vertical(self, element):
        is_element_drilled = element.get('cnc_info', []) != []
        return (
            self.elem_type(element) == self.VERTICAL_ELEM_TYPE
            and not is_element_drilled
        )

    def is_support(self, element):
        return self.elem_type(element) == self.SUPPORT_ELEM_TYPE


class MaterialsExtractor:
    def extract_fittings_semiproducts_packaging(self, materials: dict) -> dict:
        fittings = materials['material_data']
        semiproducts = materials['semiproducts_data']
        packaging = materials['packaging_data']

        return {
            **self.extract_fittings_or_semiproducts(fittings),
            **self.extract_fittings_or_semiproducts(semiproducts),
            **self.extract_fittings_or_semiproducts(packaging),
        }

    def extract_fittings_or_semiproducts(self, product_elements: dict) -> dict:
        recoverable_elements = defaultdict(int)

        for element_name, usage_unit_cost in product_elements.items():
            element_name = TOOL_BOARD_AND_WRENCH_CODENAME_TO_NAME.get(
                element_name, element_name
            )

            if is_recoverable_fitting(element_name):
                recoverable_elements[element_name] += usage_unit_cost['usage']
        return recoverable_elements
