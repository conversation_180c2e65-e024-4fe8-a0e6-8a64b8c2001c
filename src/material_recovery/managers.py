from typing import (
    TYPE_CHECKING,
    Dict,
    List,
)

from django.db import models
from django.db.models import F

from material_recovery.extractors import element_type_by_name

if TYPE_CHECKING:
    from material_recovery.models import MaterialRecoverySampleBox
    from producers.models import Product


class MaterialRecoveryProductManager(models.Manager):
    def get_queryset(self):
        queryset = super().get_queryset()
        return queryset.filter(is_recovered=True)


class MaterialRecoveryWarehouseManager(models.Manager):
    def update_or_create_by_product(self, product: 'Product', elements: List[Dict]):
        for element in elements:
            element_type = element_type_by_name(element['name'])
            color = product.color_with_shelf_name if element_type == 'elements' else ''
            defaults = {'amount': F('amount') + element['amount']}
            if not self.filter(
                codename=element['name'],
                type=element_type,
                color=color,
            ).exists():
                defaults = {'amount': element['amount']}
            self.update_or_create(
                codename=element['name'],
                type=element_type,
                color=color,
                defaults=defaults,
            )

    def update_or_create_by_sample_box(self, sample_box: 'MaterialRecoverySampleBox'):
        color = sample_box.color
        defaults = {'amount': F('amount') + sample_box.amount}

        if not self.filter(type='samples', color=color).exists():
            defaults = {'amount': sample_box.amount}

        self.update_or_create(
            type='samples',
            color=color,
            defaults=defaults,
        )
