# Generated by Django 3.2.15 on 2022-10-19 13:37

import django.core.serializers.json
import django.db.models.deletion

from django.db import (
    migrations,
    models,
)

import custom.models.fields


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('producers', '0001_squashed_0039_product_has_plus_feature'),
    ]

    operations = [
        migrations.CreateModel(
            name='MaterialRecoveryReport',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(auto_now_add=True, help_text='Creation date'),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, help_text='Update date'),
                ),
                (
                    'report_type',
                    models.IntegerField(
                        blank=True,
                        choices=[
                            (1, 'Elements'),
                            (2, 'Samples'),
                            (3, 'Shelf Market'),
                            (4, 'Warehouse'),
                        ],
                        default=None,
                        null=True,
                    ),
                ),
                (
                    'manufactor_report',
                    custom.models.fields.FileFieldWithHash(
                        blank=True,
                        max_length=150,
                        null=True,
                        upload_to='producers/product_material_recovery_reports/%Y/%m',
                    ),
                ),
                (
                    'admin_report',
                    custom.models.fields.FileFieldWithHash(
                        blank=True,
                        max_length=150,
                        null=True,
                        upload_to='producers/product_material_recovery_reports/%Y/%m',
                    ),
                ),
                (
                    'product_data',
                    models.JSONField(
                        blank=True,
                        default=list,
                        encoder=django.core.serializers.json.DjangoJSONEncoder,
                    ),
                ),
                (
                    'sample_box_data',
                    models.JSONField(
                        blank=True,
                        default=list,
                        encoder=django.core.serializers.json.DjangoJSONEncoder,
                    ),
                ),
                (
                    'shelf_market_data',
                    models.JSONField(
                        blank=True,
                        default=list,
                        encoder=django.core.serializers.json.DjangoJSONEncoder,
                    ),
                ),
                (
                    'warehouse_data',
                    models.JSONField(
                        blank=True,
                        default=list,
                        encoder=django.core.serializers.json.DjangoJSONEncoder,
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated_at',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='MaterialRecoveryProduct',
            fields=[],
            options={
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('producers.product',),
        ),
        migrations.CreateModel(
            name='ShelfMarket',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(auto_now_add=True, help_text='Creation date'),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, help_text='Update date'),
                ),
                ('quality_factor', models.DecimalField(decimal_places=1, max_digits=2)),
                (
                    'product',
                    models.OneToOneField(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.CASCADE,
                        to='producers.product',
                    ),
                ),
                (
                    'recovery_report',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='material_recovery.materialrecoveryreport',
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated_at',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='MaterialRecoveryWarehouse',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(auto_now_add=True, help_text='Creation date'),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, help_text='Update date'),
                ),
                ('codename', models.CharField(blank=True, max_length=255)),
                ('color', models.CharField(blank=True, max_length=255)),
                (
                    'type',
                    models.CharField(
                        choices=[
                            ('samples', 'SAMPLES'),
                            ('elements', 'ELEMENTS'),
                            ('fittings', 'FITTINGS'),
                            ('warehouse', 'WAREHOUSE'),
                        ],
                        max_length=255,
                    ),
                ),
                ('amount', models.IntegerField(default=0)),
                (
                    'recovery_report',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='material_recovery.materialrecoveryreport',
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated_at',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='MaterialRecoverySampleBox',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(auto_now_add=True, help_text='Creation date'),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, help_text='Update date'),
                ),
                ('amount', models.IntegerField()),
                (
                    'color',
                    models.CharField(
                        choices=[
                            ('T01_WHITE', 'T01_WHITE'),
                            ('T01_BLACK', 'T01_BLACK'),
                            ('T01_GREY', 'T01_GREY'),
                            ('T01_RED', 'T01_RED'),
                            ('T01_YELLOW', 'T01_YELLOW'),
                            ('T01_DUSTY_PINK', 'T01_DUSTY_PINK'),
                            ('T02_WHITE', 'T02_WHITE'),
                            ('T02_TERRACOTTA', 'T02_TERRACOTTA'),
                            ('T02_MIDNIGHT_BLUE', 'T02_MIDNIGHT_BLUE'),
                            ('T02_SAND', 'T02_SAND'),
                            ('T02_MATTE_BLACK', 'T02_MATTE_BLACK'),
                            ('T02_SKY_BLUE', 'T02_SKY_BLUE'),
                            ('T02_BURGUNDY', 'T02_BURGUNDY'),
                            ('T02_COTTON', 'T02_COTTON'),
                            ('T02_GRAY', 'T02_GRAY'),
                            ('T02_DARK_GRAY', 'T02_DARK_GRAY'),
                            ('T02_MUSTARD_YELLOW', 'T02_MUSTARD_YELLOW'),
                            ('T03_WHITE', 'T03_WHITE'),
                            ('T03_BEIGE', 'T03_BEIGE'),
                            ('T03_GRAPHITE', 'T03_GRAPHITE'),
                            ('T03_BEIGE_PINK', 'T03_BEIGE_PINK'),
                            ('T01_ASH', 'T01_ASH'),
                            ('T01_OAK', 'T01_OAK'),
                        ],
                        max_length=255,
                    ),
                ),
                (
                    'recovery_report',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='material_recovery.materialrecoveryreport',
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated_at',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='MaterialRecoveryPricingFactor',
            fields=[
                (
                    'id',
                    models.BigAutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(auto_now_add=True, help_text='Creation date'),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, help_text='Update date'),
                ),
                ('codename', models.CharField(max_length=255)),
                ('color', models.CharField(blank=True, max_length=255)),
                (
                    'type',
                    models.CharField(
                        choices=[('elements', 'Elements'), ('fittings', 'Fittings')],
                        max_length=50,
                    ),
                ),
                (
                    'price',
                    models.DecimalField(
                        decimal_places=2,
                        max_digits=12,
                        verbose_name='Element cost (PLN)',
                    ),
                ),
            ],
            options={
                'unique_together': {('codename', 'color', 'type')},
            },
        ),
    ]
