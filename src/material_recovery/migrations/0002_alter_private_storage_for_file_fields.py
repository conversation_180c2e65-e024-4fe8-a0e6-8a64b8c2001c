# Generated by Django 3.2.16 on 2022-12-15 13:30

import django.core.files.storage

from django.db import migrations

import custom.models.fields


class Migration(migrations.Migration):

    dependencies = [
        ('material_recovery', '0001_initial'),
    ]

    operations = [
        migrations.AlterField(
            model_name='materialrecoveryreport',
            name='admin_report',
            field=custom.models.fields.FileFieldWithHash(
                blank=True,
                max_length=150,
                null=True,
                storage=django.core.files.storage.FileSystemStorage(),
                upload_to='producers/product_material_recovery_reports/%Y/%m',
            ),
        ),
        migrations.AlterField(
            model_name='materialrecoveryreport',
            name='manufactor_report',
            field=custom.models.fields.FileFieldWithHash(
                blank=True,
                max_length=150,
                null=True,
                storage=django.core.files.storage.FileSystemStorage(),
                upload_to='producers/product_material_recovery_reports/%Y/%m',
            ),
        ),
    ]
