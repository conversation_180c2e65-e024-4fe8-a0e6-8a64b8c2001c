# Generated by Django 4.1.9 on 2024-02-28 16:20

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('material_recovery', '0003_alter_materialrecoverysamplebox_color'),
    ]

    operations = [
        migrations.AlterField(
            model_name='materialrecoverysamplebox',
            name='color',
            field=models.CharField(
                choices=[
                    ('T01_WHITE', 'T01_WHITE'),
                    ('T01_BLACK', 'T01_BLACK'),
                    ('T01_GREY', 'T01_GREY'),
                    ('T01_RED', 'T01_RED'),
                    ('T01_YELLOW', 'T01_YELLOW'),
                    ('T01_DUSTY_PINK', 'T01_DUSTY_PINK'),
                    ('T02_WHITE', 'T02_WHITE'),
                    ('T02_TERRACOTTA', 'T02_TERRACOTTA'),
                    ('T02_MIDNIGHT_BLUE', 'T02_MIDNIGHT_BLUE'),
                    ('T02_SAND', 'T02_SAND'),
                    ('T02_MATTE_BLACK', 'T02_MATTE_BLACK'),
                    ('T02_SKY_BLUE', 'T02_SKY_BLUE'),
                    ('T02_BURGUNDY', 'T02_BURGUNDY'),
                    ('T02_COTTON', 'T02_COTTON'),
                    ('T02_GRAY', 'T02_GRAY'),
                    ('T02_DARK_GRAY', 'T02_DARK_GRAY'),
                    ('T02_MUSTARD_YELLOW', 'T02_MUSTARD_YELLOW'),
                    ('T03_WHITE', 'T03_WHITE'),
                    ('T03_BEIGE', 'T03_BEIGE'),
                    ('T03_GRAPHITE', 'T03_GRAPHITE'),
                    ('T03_BEIGE_PINK', 'T03_BEIGE_PINK'),
                    ('T01_ASH', 'T01_ASH'),
                    ('T01_OAK', 'T01_OAK'),
                    ('T01_DARK_OAK', 'T01_DARK_OAK'),
                    ('T01_BLUE', 'T01_BLUE'),
                    ('T01_DARK_BROWN', 'T01_DARK_BROWN'),
                    ('T02_REISINGERS_PINK', 'T02_REISINGERS_PINK'),
                    ('T02_SAGE_GREEN', 'T02_SAGE_GREEN'),
                    ('T02_STONE_GRAY', 'T02_STONE_GRAY'),
                    ('T02_WALNUT', 'T02_WALNUT'),
                ],
                max_length=255,
            ),
        ),
    ]
