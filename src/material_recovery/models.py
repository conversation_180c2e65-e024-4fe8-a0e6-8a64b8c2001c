from typing import (
    Dict,
    List,
)

from django.conf import settings
from django.core.serializers.json import DjangoJSONEncoder
from django.db import (
    models,
    transaction,
)
from django.utils import timezone

from cstm_be.media_storage import private_media_storage
from custom.models import (
    FileFieldWithHash,
    Timestampable,
)
from mailing.templates import ShelfMarketReportCreatedInfoMail
from material_recovery.constants import SAMPLE_BOX_COLOR_TO_MATERIAL_CODENAMES
from material_recovery.enums import (
    MaterialRecoveryPricingFactorType,
    MaterialRecoveryReportTypeEnum,
    MaterialRecoveryWarehouseTypeEnum,
)
from material_recovery.exceptions import (
    MaterialRecoveryWarehouseLowAvailabilityException,
)
from material_recovery.managers import (
    MaterialRecoveryProductManager,
    MaterialRecoveryWarehouseManager,
)
from material_recovery.reports_product_recovery import (
    create_product_recovery_csv_admin_report,
    create_product_recovery_csv_manufactor_report,
)
from material_recovery.reports_sample_box import (
    create_sample_box_csv_admin_report,
    create_sample_box_csv_manufactor_report,
)
from material_recovery.reports_shelf_market import (
    create_shelf_market_csv_admin_report,
    create_shelf_market_csv_manufactor_report,
)
from material_recovery.reports_warehouse import (
    create_warehouse_csv_admin_report,
    create_warehouse_csv_manufactor_report,
)
from producers.models import Product


class MaterialRecoveryProduct(Product):
    objects = MaterialRecoveryProductManager()

    class Meta:
        proxy = True


class MaterialRecoverySampleBox(Timestampable):
    amount = models.IntegerField()
    color = models.CharField(
        max_length=255,
        choices=[(name, name) for name in SAMPLE_BOX_COLOR_TO_MATERIAL_CODENAMES],
    )

    recovery_report = models.ForeignKey(
        'material_recovery.MaterialRecoveryReport',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )


class ShelfMarket(Timestampable):
    product = models.OneToOneField(
        'producers.Product',
        on_delete=models.CASCADE,
        null=True,
        blank=True,
    )
    quality_factor = models.DecimalField(max_digits=2, decimal_places=1)
    recovery_report = models.ForeignKey(
        'material_recovery.MaterialRecoveryReport',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )


class MaterialRecoveryReport(Timestampable):
    report_type = models.IntegerField(
        choices=MaterialRecoveryReportTypeEnum.choices,
        default=None,
        null=True,
        blank=True,
    )
    manufactor_report = FileFieldWithHash(
        upload_to='producers/product_material_recovery_reports/%Y/%m',
        storage=private_media_storage,
        max_length=150,
        null=True,
        blank=True,
    )
    admin_report = FileFieldWithHash(
        upload_to='producers/product_material_recovery_reports/%Y/%m',
        max_length=150,
        storage=private_media_storage,
        null=True,
        blank=True,
    )
    product_data = models.JSONField(
        blank=True,
        default=list,
        encoder=DjangoJSONEncoder,
    )
    sample_box_data = models.JSONField(
        blank=True,
        default=list,
        encoder=DjangoJSONEncoder,
    )
    shelf_market_data = models.JSONField(
        blank=True,
        default=list,
        encoder=DjangoJSONEncoder,
    )
    warehouse_data = models.JSONField(
        blank=True,
        default=list,
        encoder=DjangoJSONEncoder,
    )

    @classmethod
    def get_report_name(cls, report: 'MaterialRecoveryReport') -> str:
        now = timezone.now().strftime('%Y-%m-%d-%H:%M:%S')
        return f'{report.get_report_type_display()}-{now}.csv'

    @classmethod
    @transaction.atomic
    def from_product_elements(
        cls, elements_by_product: List[Dict]
    ) -> 'MaterialRecoveryReport':
        from material_recovery.api.serializers import (
            CreateProductMaterialRecoveryReportSerializer,
        )

        report = cls.objects.create(
            report_type=MaterialRecoveryReportTypeEnum.ELEMENTS,
            product_data=[
                CreateProductMaterialRecoveryReportSerializer(product).data
                for product in elements_by_product
            ],
        )
        report_name = cls.get_report_name(report)
        report.manufactor_report.save(
            report_name,
            create_product_recovery_csv_manufactor_report(report, elements_by_product),
        )
        report.admin_report.save(
            report_name,
            create_product_recovery_csv_admin_report(report, elements_by_product),
        )
        return report

    @classmethod
    @transaction.atomic
    def from_sample_boxes(cls, samples_by_id: List[Dict]) -> 'MaterialRecoveryReport':
        from material_recovery.api.serializers import (
            CreateSampleBoxMaterialRecoveryReportSerializer,
        )

        report = cls.objects.create(
            report_type=MaterialRecoveryReportTypeEnum.SAMPLES,
            sample_box_data=[
                CreateSampleBoxMaterialRecoveryReportSerializer(sample_box).data
                for sample_box in samples_by_id
            ],
        )
        report_name = cls.get_report_name(report)
        report.manufactor_report.save(
            report_name,
            create_sample_box_csv_manufactor_report(report, samples_by_id),
        )
        report.admin_report.save(
            report_name,
            create_sample_box_csv_admin_report(report, samples_by_id),
        )
        return report

    @classmethod
    @transaction.atomic
    def from_shelf_market(
        cls, shelf_markets_by_id: List[Dict]
    ) -> 'MaterialRecoveryReport':
        from material_recovery.api.serializers import CreateShelfMarketReportSerializer

        report = cls.objects.create(
            report_type=MaterialRecoveryReportTypeEnum.SHELF_MARKET,
            shelf_market_data=[
                CreateShelfMarketReportSerializer(shelf_market).data
                for shelf_market in shelf_markets_by_id
            ],
        )
        report_name = cls.get_report_name(report)
        report.manufactor_report.save(
            report_name,
            create_shelf_market_csv_manufactor_report(report, shelf_markets_by_id),
        )
        report.admin_report.save(
            report_name,
            create_shelf_market_csv_admin_report(report, shelf_markets_by_id),
        )
        cls.send_email_shelf_market_report_created(report, shelf_markets_by_id)
        return report

    @classmethod
    @transaction.atomic
    def from_warehouse(cls, warehouses_by_id: List[Dict]) -> 'MaterialRecoveryReport':
        from material_recovery.api.serializers import DecreaseAmountSerializer

        report = cls.objects.create(
            report_type=MaterialRecoveryReportTypeEnum.WAREHOUSE,
            warehouse_data=[
                DecreaseAmountSerializer(warehouse).data
                for warehouse in warehouses_by_id
            ],
        )
        report_name = cls.get_report_name(report)
        report.manufactor_report.save(
            report_name,
            create_warehouse_csv_manufactor_report(report, warehouses_by_id),
        )
        report.admin_report.save(
            report_name,
            create_warehouse_csv_admin_report(report, warehouses_by_id),
        )
        return report

    @classmethod
    def send_email_shelf_market_report_created(
        cls, report: 'MaterialRecoveryReport', shelf_market_by_ids: List[Dict]
    ) -> None:
        data_html = {
            'report_id': report.id,
            'product_count': len(shelf_market_by_ids),
            'product_ids': ', '.join(
                [
                    str(shelf_market['shelf_market_id'].product_id)
                    for shelf_market in shelf_market_by_ids
                ]
            ),
        }
        for name, email in settings.SHELF_MARKET_REPORT_CREATED_RECIPIENTS:
            mail = ShelfMarketReportCreatedInfoMail(
                email,
                data_html=data_html,
                topic=f'Shelf market report has been created: {report.id}',
            )
            mail.send()


class MaterialRecoveryWarehouse(Timestampable):
    codename = models.CharField(blank=True, max_length=255)
    color = models.CharField(max_length=255, blank=True)

    type = models.CharField(
        max_length=255, choices=MaterialRecoveryWarehouseTypeEnum.choices()
    )

    amount = models.IntegerField(default=0)

    recovery_report = models.ForeignKey(
        'material_recovery.MaterialRecoveryReport',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )

    objects = MaterialRecoveryWarehouseManager()

    def decrease_amount(self, amount: int) -> None:
        if amount > self.amount:
            raise MaterialRecoveryWarehouseLowAvailabilityException
        self.amount -= amount


class MaterialRecoveryPricingFactor(Timestampable):
    codename = models.CharField(max_length=255)
    color = models.CharField(max_length=255, blank=True)
    type = models.CharField(
        max_length=50, choices=MaterialRecoveryPricingFactorType.choices
    )
    price = models.DecimalField(
        max_digits=12,
        decimal_places=2,
        verbose_name='Element cost (PLN)',
    )

    def __str__(self):
        return f'{self.codename} {self.color}'

    class Meta:
        unique_together = ('codename', 'color', 'type')
