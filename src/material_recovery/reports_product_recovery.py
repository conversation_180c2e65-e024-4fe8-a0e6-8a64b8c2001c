import csv

from collections import defaultdict
from decimal import Decimal
from io import String<PERSON>
from typing import (
    TYPE_CHECKING,
    Dict,
    List,
)

from django.core.files.base import ContentFile

from material_recovery.enums import MaterialRecoveryPricingFactorType
from material_recovery.extractors import is_recoverable_fitting
from material_recovery.reports import (
    RecoveredItemWithCost,
    buffer_to_csv_file,
    calculate_recovered_total_waste_recycling,
    format_decimal_with_comma,
)

if TYPE_CHECKING:
    from material_recovery.models import MaterialRecoveryReport
    from producers.models import Product


def group_by_carriers(elements_by_product: List[Dict]):
    products_by_carrier = defaultdict(int)
    for elements in elements_by_product:
        products_by_carrier[elements['product_id'].get_carrier_name()] += 1
    return products_by_carrier


def group_by_color_and_name(elements_by_product: List[Dict]):
    from material_recovery.models import MaterialRecoveryPricingFactor

    elements_by_color_and_name = defaultdict(
        lambda: defaultdict(
            lambda: RecoveredItemWithCost(amount=0, cost=Decimal('0.0'))
        )
    )
    fittings_by_name = defaultdict(
        lambda: RecoveredItemWithCost(amount=0, cost=Decimal('0.0'))
    )

    for elements in elements_by_product:
        product = elements['product_id']
        for element in elements['elements']:
            name = element['name']
            amount = element['amount']
            if is_recoverable_fitting(name):
                pricing_factor = MaterialRecoveryPricingFactor.objects.get(
                    codename=name, type=MaterialRecoveryPricingFactorType.FITTINGS
                )
                recovered = fittings_by_name[name]
            else:
                pricing_factor = MaterialRecoveryPricingFactor.objects.get(
                    codename=name,
                    color=product.color_with_shelf_name,
                    type=MaterialRecoveryPricingFactorType.ELEMENTS,
                )
                recovered = elements_by_color_and_name[product.color_with_shelf_name][
                    name
                ]
            recovered.amount += amount
            recovered.cost += pricing_factor.price * amount
    return elements_by_color_and_name, fittings_by_name


def create_product_recovery_csv_manufactor_report(
    report: 'MaterialRecoveryReport', elements_by_product: List[Dict]
):
    csv_buffer = StringIO()
    csv_writer = csv.writer(csv_buffer, delimiter=';')
    csv_writer.writerow([f'Raport nr {report.pk}'])
    csv_writer.writerow(['Data utworzenia', report.created_at.strftime('%Y-%m-%d')])

    csv_writer.writerow(['ID regałów:'])
    for elements in elements_by_product:
        csv_writer.writerow([elements['product_id'].id])

    elements_by_color_and_name, fittings_by_name = group_by_color_and_name(
        elements_by_product
    )

    for color, elements in elements_by_color_and_name.items():
        csv_writer.writerow([])
        csv_writer.writerow(['Kolor:', color])
        csv_writer.writerow([])
        csv_writer.writerow(['Elementy:', '', 'Ilość'])
        for element_name, element_item in elements.items():
            csv_writer.writerow([element_name, '', element_item.amount])

    if fittings_by_name:
        csv_writer.writerow([])
        csv_writer.writerow(['Okucia'])
        csv_writer.writerow([])
        csv_writer.writerow(['Codename', '', 'Ilość'])

        for fitting_name, recovered_item in fittings_by_name.items():
            csv_writer.writerow([fitting_name, '', recovered_item.amount])

    return buffer_to_csv_file(csv_buffer)


def total_production_cost(product: 'Product'):
    product_info = product.get_serialized_product_info()
    material_categories = product_info['margins']['cogs_dict_mat_categories']
    return sum(
        [
            material_categories['materials'],
            material_categories['semiproducts'],
            material_categories['services'],
        ]
    )


def count_total_production_cost(elements_by_product: List[Dict]) -> Decimal:
    total_cost = sum(
        [
            Decimal(total_production_cost(elements['product_id']))
            for elements in elements_by_product
        ]
    )
    return round(total_cost, 2)


def create_product_recovery_csv_admin_report(
    report: 'MaterialRecoveryReport', elements_by_product: List[Dict]
) -> ContentFile:
    csv_buffer = StringIO()
    csv_writer = csv.writer(csv_buffer, delimiter=';')
    csv_writer.writerow([f'Raport nr {report.pk}'])
    csv_writer.writerow(['Data utworzenia', report.created_at.strftime('%Y-%m-%d')])

    csv_writer.writerow(['ID regałów:'])
    for elements in elements_by_product:
        csv_writer.writerow([elements['product_id'].id])
    csv_writer.writerow([])

    elements_by_color_and_name, fittings_by_name = group_by_color_and_name(
        elements_by_product
    )

    total_production_cost = count_total_production_cost(elements_by_product)
    total_waste_recycling = calculate_recovered_total_waste_recycling(
        elements_by_color_and_name, fittings_by_name
    )

    csv_writer.writerow(
        ['Koszt produkcyjny regałów', format_decimal_with_comma(total_production_cost)]
    )
    csv_writer.writerow(
        ['Zysk z odzysku', format_decimal_with_comma(total_waste_recycling)]
    )
    csv_writer.writerow(
        [
            'Bilans kosztów',
            format_decimal_with_comma(total_production_cost - total_waste_recycling),
        ]
    )
    csv_writer.writerow([])

    csv_writer.writerow(['Typy wysyłki', '', 'Ilość'])
    by_carriers = group_by_carriers(elements_by_product)
    for carrier_name, amount in by_carriers.items():
        csv_writer.writerow([carrier_name, '', amount])

    csv_writer.writerow([])

    for color, elements in elements_by_color_and_name.items():
        csv_writer.writerow(['Kolor:', color])
        csv_writer.writerow([])
        csv_writer.writerow(['Elementy:', '', 'Ilość'])
        for element_name, recovered_item in elements.items():
            csv_writer.writerow(
                [
                    element_name,
                    '',
                    recovered_item.amount,
                    format_decimal_with_comma(recovered_item.cost),
                ]
            )

    if fittings_by_name:
        csv_writer.writerow([])
        csv_writer.writerow(['Okucia'])
        csv_writer.writerow([])
        csv_writer.writerow(['Codename:', '', 'Ilość'])

        for fitting_name, recovered_item in fittings_by_name.items():
            csv_writer.writerow(
                [
                    fitting_name,
                    '',
                    recovered_item.amount,
                    format_decimal_with_comma(recovered_item.cost),
                ]
            )

    return buffer_to_csv_file(csv_buffer)
