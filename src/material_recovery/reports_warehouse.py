import csv

from collections import defaultdict
from decimal import Decimal
from io import String<PERSON>
from typing import (
    TYPE_CHECKING,
    Dict,
    List,
)

from django.core.files.base import ContentFile

from material_recovery.enums import MaterialRecoveryPricingFactorType
from material_recovery.extractors import is_recoverable_fitting
from material_recovery.reports import (
    RecoveredItemWithCost,
    buffer_to_csv_file,
    calculate_recovered_total_waste_recycling,
    format_decimal_with_comma,
)
from material_recovery.reports_sample_box import (
    calculate_samples_total_waste_recycling,
    count_waste_recycling,
)

if TYPE_CHECKING:
    from material_recovery.models import MaterialRecoveryReport


def group_by_color_and_name(warehouses_by_id: List[Dict]):
    from material_recovery.models import MaterialRecoveryPricingFactor

    elements_by_color_and_name = defaultdict(
        lambda: defaultdict(
            lambda: RecoveredItemWithCost(amount=0, cost=Decimal('0.0'))
        )
    )
    fittings_by_name = defaultdict(
        lambda: RecoveredItemWithCost(amount=0, cost=Decimal('0.0'))
    )
    samples_by_color = defaultdict(
        lambda: RecoveredItemWithCost(amount=0, cost=Decimal('0.0'))
    )

    for element in warehouses_by_id:
        material_warehouse = element['id']
        amount = element['amount']
        name = material_warehouse.codename

        if not name:
            samples_by_color[material_warehouse.color].amount += amount
            samples_by_color[material_warehouse.color].cost += count_waste_recycling(
                amount, material_warehouse.color
            )
        elif is_recoverable_fitting(name):
            pricing_factor = MaterialRecoveryPricingFactor.objects.get(
                codename=name, type=MaterialRecoveryPricingFactorType.FITTINGS
            )
            fittings_by_name[name].amount += amount
            fittings_by_name[name].cost += pricing_factor.price * amount
        else:
            pricing_factor = MaterialRecoveryPricingFactor.objects.get(
                codename=name,
                color=material_warehouse.color,
                type=MaterialRecoveryPricingFactorType.ELEMENTS,
            )
            elements_by_color_and_name[material_warehouse.color][name].amount += amount
            elements_by_color_and_name[material_warehouse.color][name].cost += (
                pricing_factor.price * amount
            )
    return elements_by_color_and_name, fittings_by_name, samples_by_color


def create_warehouse_csv_manufactor_report(
    report: 'MaterialRecoveryReport', warehouses_by_id: List[Dict]
):
    csv_buffer = StringIO()
    csv_writer = csv.writer(csv_buffer, delimiter=';')
    csv_writer.writerow([f'Raport nr {report.pk}'])
    csv_writer.writerow(['Data utworzenia', report.created_at.strftime('%Y-%m-%d')])

    (
        elements_by_color_and_name,
        fittings_by_name,
        samples_by_color,
    ) = group_by_color_and_name(warehouses_by_id)

    for color, elements in elements_by_color_and_name.items():
        csv_writer.writerow([])
        csv_writer.writerow(['Kolor:', color])
        csv_writer.writerow([])
        csv_writer.writerow(['Elementy:', '', 'Ilość'])
        for element_name, element_item in elements.items():
            csv_writer.writerow([element_name, '', element_item.amount])

    if fittings_by_name:
        csv_writer.writerow([])
        csv_writer.writerow(['Okucia'])
        csv_writer.writerow([])
        csv_writer.writerow(['Codename', '', 'Ilość'])

        for fitting_name, recovered_item in fittings_by_name.items():
            csv_writer.writerow([fitting_name, '', recovered_item.amount])

    if samples_by_color:
        headers = ['LP', 'Kolor', 'Ilość sampli']
        csv_writer.writerow(headers)

        for idx, (color, samples) in enumerate(samples_by_color.items(), 1):
            csv_writer.writerow([idx, color, samples.amount])

    return buffer_to_csv_file(csv_buffer)


def create_warehouse_csv_admin_report(
    report: 'MaterialRecoveryReport', warehouses_by_id: List[Dict]
) -> ContentFile:
    csv_buffer = StringIO()
    csv_writer = csv.writer(csv_buffer, delimiter=';')
    csv_writer.writerow([f'Raport nr {report.pk}'])
    csv_writer.writerow(['Data utworzenia', report.created_at.strftime('%Y-%m-%d')])

    (
        elements_by_color_and_name,
        fittings_by_name,
        samples_by_color,
    ) = group_by_color_and_name(warehouses_by_id)

    total_waste_recycling = calculate_recovered_total_waste_recycling(
        elements_by_color_and_name, fittings_by_name
    ) + calculate_samples_total_waste_recycling(samples_by_color)

    csv_writer.writerow(
        ['Zysk z odzysku', format_decimal_with_comma(total_waste_recycling)]
    )
    csv_writer.writerow([])

    for color, elements in elements_by_color_and_name.items():
        csv_writer.writerow(['Kolor:', color])
        csv_writer.writerow([])
        csv_writer.writerow(['Elementy:', '', 'Ilość'])
        for element_name, recovered_item in elements.items():
            csv_writer.writerow(
                [
                    element_name,
                    '',
                    recovered_item.amount,
                    format_decimal_with_comma(recovered_item.cost),
                ]
            )

    if fittings_by_name:
        csv_writer.writerow([])
        csv_writer.writerow(['Okucia'])
        csv_writer.writerow([])
        csv_writer.writerow(['Codename:', '', 'Ilość'])

        for fitting_name, recovered_item in fittings_by_name.items():
            csv_writer.writerow(
                [
                    fitting_name,
                    '',
                    recovered_item.amount,
                    format_decimal_with_comma(recovered_item.cost),
                ]
            )
    if samples_by_color:
        headers = ['LP', 'Kolor', 'Ilość sampli']
        csv_writer.writerow(headers)

        for idx, (color, samples) in enumerate(samples_by_color.items(), 1):
            csv_writer.writerow([idx, color, samples.amount])
    return buffer_to_csv_file(csv_buffer)
