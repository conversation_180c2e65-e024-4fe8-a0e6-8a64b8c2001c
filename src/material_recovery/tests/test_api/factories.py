import factory.fuzzy

from factory import fuzzy

from custom.enums import Type01Color
from material_recovery.enums import MaterialRecoveryPricingFactorType


class MaterialRecoveryPricingFactorFactory(factory.django.DjangoModelFactory):
    codename = fuzzy.FuzzyText()
    color = Type01Color.GREY.color_name
    type = MaterialRecoveryPricingFactorType.ELEMENTS.value
    price = fuzzy.FuzzyDecimal(low=10, high=100)

    class Meta:
        model = 'material_recovery.MaterialRecoveryPricingFactor'
