from django.contrib.auth.mixins import LoginRequiredMixin
from django.contrib.auth.views import LoginView
from django.http import HttpResponseRedirect
from django.urls import reverse
from django.views.generic import TemplateView

from material_recovery import permissions


class MaterialRecoveryProducerLoginView(LoginView):
    template_name = 'recovery_producer_login.html'

    def get_success_url(self):
        return reverse('recovery_producer_index')


class MaterialRecoveryProducerSPAView(LoginRequiredMixin, TemplateView):
    permission_classes = (permissions.MaterialRecoveryPanelPermission,)
    template_name = 'recovery_materials/app.html'

    def get(self, request, *args, **kwargs):
        for permission_class in self.permission_classes:
            if not permission_class().has_permission(request, self):
                return HttpResponseRedirect(reverse('recovery_producer_logout'))
        return super().get(request, *args, **kwargs)
