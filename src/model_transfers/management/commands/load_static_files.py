from django.core.management.base import BaseCommand

from model_transfers.data_managers import CstmLoadManager


class Command(BaseCommand):
    help = 'Load static files into storage.'

    def add_arguments(self, parser):
        parser.add_argument('--directory', type=str, default='fixtures')

    def handle(self, **options):
        manager = CstmLoadManager(
            directory=options['directory'],
        )
        manager.populate_storage_files(manager.static_dir_path)
