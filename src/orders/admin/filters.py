from django.contrib import admin
from django.contrib.contenttypes.models import ContentType
from django.db.models import (
    Exists,
    OuterRef,
    Q,
)

from rangefilter.filters import DateRangeFilter

from custom.enums import (
    Furniture,
    ShelfType,
)
from gallery.enums import FurnitureCategory
from gallery.models import Sotty
from invoice.choices import InvoiceStatus
from orders.enums import (
    OrderStatus,
    OrderType,
)
from orders.internal_api.clients import LogisticOrderAPIClient
from orders.models import Order
from orders.tasks import get_delivered_klarna_orders_for_automatic_capture
from payments.choices import KlarnaStatus
from payments.constants import KLARNA_PAYMENT_METHODS


class SellChannelFilter(admin.SimpleListFilter):
    title = 'sell channel'
    parameter_name = 'sell_channel'

    def lookups(self, request, model_admin):
        return (
            (
                'web',
                'web',
            ),
            (
                'app',
                'app',
            ),
        )

    def queryset(self, request, queryset):
        if self.value() in [
            'web',
            'app',
        ]:
            ids = []
            for order in queryset:
                if order.get_sell_channel() == self.value():
                    ids.append(order.id)
            if len(ids) > 0:
                return queryset.filter(id__in=ids)


class ReturningCustomersFilter(DateRangeFilter):
    def __init__(self, field, request, params, model, model_admin, field_path):
        field_path = 'returning'
        super(ReturningCustomersFilter, self).__init__(
            field, request, params, model, model_admin, field_path
        )
        self.title = 'returning customers'

    def queryset(self, request, queryset):
        if self.form.is_valid():
            validated_data = dict(self.form.cleaned_data.items())
            if validated_data:
                query_filter = self._make_query_filter(request, validated_data)
                paid_at_max_date = query_filter.get('paid_at__gte', None)
                if paid_at_max_date:
                    previous_orders = Order.objects.filter(
                        email=OuterRef('email'), paid_at__lt=paid_at_max_date
                    )
                    queryset = queryset.annotate(
                        previous_exists=Exists(previous_orders)
                    ).filter(previous_exists=True)
                return queryset.filter(**query_filter)
        return queryset

    def _make_query_filter(self, request, validated_data):
        query_filter = super(ReturningCustomersFilter, self)._make_query_filter(
            request, validated_data
        )
        for suffix in ['gte', 'lte']:
            try:
                query_filter['paid_at__{}'.format(suffix)] = query_filter.pop(
                    '{}__{}'.format(self.field_path, suffix)
                )
            except KeyError:
                pass
        return query_filter


class OrderShelfTypeFilter(admin.SimpleListFilter):
    title = 'shelf type'
    parameter_name = 'shelf_type'

    def lookups(self, request, model_admin):
        return ShelfType.choices_active()

    def queryset(self, request, queryset):
        if not self.value() or not self.value().isdigit():
            return queryset
        shelf_type = ShelfType(int(self.value()))
        filter_kwargs = {
            f'items__{shelf_type.furniture_type.value}__shelf_type': shelf_type.value,
        }
        # 'shelf_type' doesn't exist on the Sotty model in the DB;
        # apply workaround for filtering.
        if shelf_type.furniture_type.value == Furniture.sotty.value:
            sotty_content_type = ContentType.objects.get_for_model(Sotty)
            filter_kwargs = {
                'items__content_type': sotty_content_type,
            }
        return queryset.filter(**filter_kwargs)


class OrderShelfCategoryFilter(admin.SimpleListFilter):
    title = 'Category'
    parameter_name = 'furniture_category'

    def lookups(self, request, model_admin):
        return FurnitureCategory.valid_choices()

    def queryset(self, request, queryset):
        category = self.value()
        if not category:
            return queryset
        return queryset.filter(
            Q(items__jetty__shelf_category=category)
            | Q(items__watty__shelf_category=category)
        )


class OrderKlarnaPaymentFilter(admin.SimpleListFilter):
    title = 'delivered klarna filters'
    parameter_name = 'delivered_klarna_filters'

    def lookups(self, request, model_admin):
        return (
            (0, 'Delivered klarna orders'),
            (1, 'Without capture'),
            (2, 'Success klarna capture'),
            (3, 'Failed klarna capture'),
            (4, 'Failed sending klarna capture'),
            (5, 'Orders for automatic capture'),
            (6, 'Orders without normal invoice'),
            (7, 'Orders sent, not delivered'),
        )

    def queryset(self, request, queryset):
        queryset_functions_dict = {
            '0': self.klarna_delivered_orders,
            '1': self.klarna_delivered_orders_without_capture,
            '2': self.klarna_delivered_orders_with_success_capture,
            '3': self.klarna_delivered_orders_with_failed_capture,
            '4': self.klarna_delivered_orders_with_failed_capture_attempt,
            '5': get_delivered_klarna_orders_for_automatic_capture,
            '6': self.klarna_delivered_orders_without_invoice,
            '7': self.klarna_sent_not_delivered_orders,
        }
        queryset_function = queryset_functions_dict.get(self.value(), None)
        return queryset_function(queryset) if queryset_function else queryset

    @classmethod
    def klarna_delivered_orders(cls, queryset):
        logistic_order_api_client = LogisticOrderAPIClient()
        order_ids = logistic_order_api_client.filter_delivered_orders()

        return queryset.filter(
            chosen_payment_method__in=KLARNA_PAYMENT_METHODS,
            paid_at__isnull=False,
            id__in=order_ids,
            status=OrderStatus.DELIVERED,
            order_type=OrderType.CUSTOMER,
        )

    def klarna_delivered_orders_without_capture(self, queryset):
        return (
            self.klarna_delivered_orders(queryset)
            .exclude(
                transactions__payment_method__in=KLARNA_PAYMENT_METHODS,
                transactions__klarna_captures__isnull=False,
            )
            .distinct()
        )

    def klarna_delivered_orders_with_success_capture(self, queryset):
        success_capture = Q(
            transactions__payment_method__in=KLARNA_PAYMENT_METHODS,
            transactions__klarna_captures__status=KlarnaStatus.SUCCESS,
        )
        return self.klarna_delivered_orders(queryset).filter(success_capture).distinct()

    def klarna_delivered_orders_with_failed_capture(self, queryset):
        failed_capture = Q(
            transactions__payment_method__in=KLARNA_PAYMENT_METHODS,
            transactions__klarna_captures__isnull=False,
        ) & ~Q(
            transactions__klarna_captures__status=KlarnaStatus.SUCCESS,
        )
        return self.klarna_delivered_orders(queryset).filter(failed_capture).distinct()

    def klarna_delivered_orders_with_failed_capture_attempt(self, queryset):
        return (
            self.klarna_delivered_orders(queryset)
            .exclude(
                transactions__payment_method__in=KLARNA_PAYMENT_METHODS,
                transactions__klarna_captures__isnull=False,
            )
            .filter(klarna_capture_attempt_date__isnull=False)
            .distinct()
        )

    def klarna_delivered_orders_without_invoice(self, queryset):
        return self.klarna_delivered_orders(queryset).exclude(
            invoice__status=InvoiceStatus.ENABLED
        )

    @classmethod
    def klarna_sent_not_delivered_orders(cls, queryset):
        logistic_order_api_client = LogisticOrderAPIClient()
        order_ids = logistic_order_api_client.filter_sent_not_delivered_orders()

        return queryset.filter(
            chosen_payment_method__in=KLARNA_PAYMENT_METHODS,
            paid_at__isnull=False,
            status__in=[
                OrderStatus.DELIVERED,
                OrderStatus.SHIPPED,
                OrderStatus.TO_BE_SHIPPED,
            ],
            order_type=OrderType.CUSTOMER,
            id__in=order_ids,
        )


class UserProfileFilter(admin.SimpleListFilter):
    title = 'User type'
    parameter_name = 'user_type'

    def lookups(self, request, model_admin):
        return (
            ('guestweb', 'only guest web'),
            ('guestmobile', 'only guest mobile'),
            ('registerweb', 'only registered web'),
            ('registermobile', 'only registered mobile'),
            ('registered', 'only registered'),
            ('guests', 'only guests'),
        )

    def queryset(self, request, queryset):
        filter_map = {
            'guestweb': Q(
                owner__profile__user_type=3, owner__profile__registration_source=0
            ),
            'guestmobile': (
                Q(owner__profile__user_type=3)
                & ~Q(owner__profile__registration_source=0)
            ),
            'registerweb': Q(
                owner__profile__user_type=2, owner__profile__registration_source=0
            ),
            'registermobile': (
                Q(owner__profile__user_type=2)
                & ~Q(owner__profile__registration_source=0)
            ),
            'registered': Q(owner__profile__user_type=2),
            'guests': Q(owner__profile__user_type=3),
        }
        if self.value() in filter_map:
            return queryset.filter(filter_map[self.value()])
        return queryset


class NoItemsFilter(admin.SimpleListFilter):
    title = 'No items / empty'
    parameter_name = 'empty'

    def lookups(self, request, model_admin):
        return (
            ('empty', 'Empty'),
            ('not_empty', 'Not empty'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'empty':
            return queryset.filter(total_price=0)
        if self.value() == 'not_empty':
            return queryset.filter(total_price__gt=0)


class StatusPaidFilter(admin.SimpleListFilter):
    title = 'status paid'
    parameter_name = 'status_paid'

    def lookups(self, request, model_admin):
        return (
            (1, 'paid'),
            (2, 'not paid'),
        )

    def queryset(self, request, queryset):
        if not self.value():
            return queryset
        if self.value() == 1:
            return queryset.filter(paid_at__isnull=False)
        return queryset.filter(paid_at__isnull=True)


class EmptyCartFilter(admin.SimpleListFilter):
    # Human-readable title which will be displayed in the
    # right admin sidebar just above the filter options.
    title = 'Empty order/cart'

    # Parameter for the filter that will be used in the URL query.
    parameter_name = 'emptycart'

    def lookups(self, request, model_admin):
        """
        Returns a list of tuples. The first element in each
        tuple is the coded value for the option that will
        appear in the URL query. The second element is the
        human-readable name for the option that will appear
        in the right sidebar.
        """
        return (
            ('emptycart', 'empty'),
            ('notemptycart', 'not empty'),
        )

    def queryset(self, request, queryset):
        """
        Returns the filtered queryset based on the value
        provided in the query string and retrievable via
        `self.value()`.
        """
        # Compare the requested value (either '80s' or '90s')
        # to decide how to filter the queryset.
        if self.value() == 'emptycart':
            return queryset.filter(total_price=0)
        if self.value() == 'notemptycart':
            return queryset.filter(total_price__gt=0)


class WattyFilter(admin.SimpleListFilter):
    title = 'Watty filter'

    # Parameter for the filter that will be used in the URL query.
    parameter_name = 'watty'

    def lookups(self, request, model_admin):
        """
        Returns a list of tuples. The first element in each
        tuple is the coded value for the option that will
        appear in the URL query. The second element is the
        human-readable name for the option that will appear
        in the right sidebar.
        """
        return (('only_watty', 'Watty orders'),)

    def queryset(self, request, queryset):
        """
        Returns the filtered queryset based on the value
        provided in the query string and retrievable via
        `self.value()`.
        """
        if self.value() == 'only_watty':
            watty = ContentType.objects.get(model='watty')
            return queryset.filter(items__content_type=watty)
        return queryset
