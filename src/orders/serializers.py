import logging
import operator
import re

from datetime import (
    date,
    datetime,
)
from decimal import Decimal
from typing import Optional
from urllib.parse import (
    urlencode,
    urljoin,
)

from django.conf import settings
from django.contrib.contenttypes.models import ContentType
from django.urls import (
    reverse,
    translate_url,
)

from rest_framework import serializers
from rest_framework.exceptions import ValidationError
from rest_framework_dataclasses.serializers import DataclassSerializer

from carts.serializers import (
    CartItemSerializer,
    CartSerializer,
    MailingCartItemSerializer,
    MailingCartSerializer,
)
from checkout.services.region_restrictions import RegionRestrictionService
from checkout.vat.validation import vat_validator
from custom.enums import (
    Furniture,
    LanguageEnum,
)
from custom.internal_api.dto import (
    AssemblyServiceDTO,
    DeliveryTimeFrameProposalDTO,
    LogisticOrderDTO,
    ToBeShippedEmail24DTO,
)
from custom.logistic_enums import AsMaxStatusEnum
from custom.templatetags.defaulttags import file_url
from custom.utils.python2_specific import SanitizedModelSerializer
from gallery.enums import (
    FurnitureStatusEnum,
    SellableItemContentTypes,
)
from gallery.models import (
    <PERSON>y,
    SampleBox,
    Watty,
)
from gallery.serializers import (
    JettySerializer,
    SampleBoxSerializer,
    WattySerializer,
)
from gallery.serializers.furniture.mailing import MailingSellableItemSerializer
from gallery.services.copy_furniture import copy_furniture
from gallery.services.prices_for_serializers import (
    get_region_price_in_euro,
    get_region_price_with_discount,
    get_region_price_with_discount_in_euro,
)
from orders.enums import OrderStatus
from orders.models import (
    Order,
    OrderItem,
    OrderStatusCheckHistory,
)
from orders.services.status_description import get_status_description
from pricing_v3.serializers import PricingSerializer
from producers.choices import ProductStatus
from producers.models import Product
from promotions.utils import strikethrough_promo
from regions.serializers import RegionSerializer
from regions.utils import reverse_with_region
from user_profile.models import LoginAccessToken

logger = logging.getLogger('orders')


class OrderItemGenericField(serializers.Serializer):
    FURNITURE_CLASSES = {
        'jetty': {'serializer': JettySerializer, 'model': Jetty},
        'sample_box': {'serializer': SampleBoxSerializer, 'model': SampleBox},
        'watty': {'serializer': WattySerializer, 'model': Watty},
    }

    def to_internal_value(self, data):
        if data.get('type') in list(self.FURNITURE_CLASSES.keys()):
            if data.get('id', None) is not None:
                try:
                    item = self.FURNITURE_CLASSES[data.get('type')][
                        'model'
                    ].objects.get(pk=data.get('id'), owner=self.context['request'].user)
                    serializer = self.FURNITURE_CLASSES[data.get('type')]['serializer'](
                        item, data=data
                    )
                except self.FURNITURE_CLASSES[data.get('type')]['model'].DoesNotExist:
                    serializer = self.FURNITURE_CLASSES[data.get('type')]['serializer'](
                        data=data
                    )
            else:
                serializer = self.FURNITURE_CLASSES[data.get('type')]['serializer'](
                    data=data
                )
        else:
            return None
        if serializer.is_valid(raise_exception=True):
            resp = serializer.save(owner=self.context['request'].user)
            return resp
        return None

    def to_representation(self, value):
        related_orderitem = value
        for key, item in list(self.FURNITURE_CLASSES.items()):
            if related_orderitem.__class__ == item['model']:
                serializer = item['serializer'](related_orderitem, context=self.context)
                return serializer.data
        return None  # error?


class ContentTypeCustomField(serializers.Field):
    def to_representation(self, obj):
        return obj.model

    def to_internal_value(self, data):
        return ContentType.objects.get(
            app_label='gallery', model=(data.encode('ascii', 'ignore'))
        )


class BaseOrderItemSerializer(serializers.ModelSerializer):
    furniture_type = serializers.ReadOnlyField(source='get_furniture_type')
    order_item = OrderItemGenericField()

    region = RegionSerializer(read_only=True, source='get_region')
    region_price = serializers.DecimalField(
        max_digits=12, decimal_places=2, required=False, default=0
    )
    region_price_net = serializers.DecimalField(
        max_digits=12, decimal_places=2, required=False, default=0
    )
    region_assembly_price = serializers.DecimalField(
        max_digits=12, decimal_places=2, required=False, default=0
    )
    region_delivery_price = serializers.DecimalField(
        max_digits=12, decimal_places=2, required=False, default=0
    )
    region_vat_amount = serializers.DecimalField(
        max_digits=12, decimal_places=2, required=False, default=0
    )

    def create(self, validated_attrs):
        return OrderItem.objects.create(**validated_attrs)

    class Meta:
        model = OrderItem
        fields = (
            'id',
            'furniture_type',
            'order_item',
            'region',
            'region_price',
            'region_price_net',
            'region_assembly_price',
            'region_delivery_price',
            'region_vat_amount',
            'deleted',
            'price',
            'price_net',
            'product_name',
            'invoice_product_name',
            'assembly_price',
            'free_assembly_service',
            'delivery_price',
            'vat_amount',
            'created_at',
            'quantity',
            'exported_to_big_query',
            'free_return',
        )


class OrderItemSerializer(BaseOrderItemSerializer):
    region_price_with_discount = serializers.SerializerMethodField()
    region_price_in_euro = serializers.SerializerMethodField()
    region_price_with_discount_in_euro = serializers.SerializerMethodField()

    class Meta(BaseOrderItemSerializer.Meta):
        fields = (
            *BaseOrderItemSerializer.Meta.fields,
            'region_price_with_discount',
            'region_price_in_euro',
            'region_price_with_discount_in_euro',
        )

    def get_region_price_with_discount(self, obj) -> Decimal:
        return get_region_price_with_discount(
            obj.order_item,
            obj.order.region,
            promotion=strikethrough_promo(obj.order.region),
        )

    def get_region_price_in_euro(self, obj) -> Decimal:
        return get_region_price_in_euro(
            obj.order_item,
            self.context['currency_rate'],
            obj.order.region,
        )

    def get_region_price_with_discount_in_euro(self, obj) -> Decimal:
        return get_region_price_with_discount_in_euro(
            obj.order_item,
            self.context['currency_rate'],
            obj.order.region,
            strikethrough_promo(obj.order.region),
        )


class BaseOrderSerializer(SanitizedModelSerializer):
    items = OrderItemSerializer(many=True)

    status_text = serializers.CharField(source='get_status_display', read_only=True)
    status_previous_text = serializers.CharField(
        source='get_status_previous_display', read_only=True
    )

    region = RegionSerializer(read_only=True, source='get_region')
    region_total_price = serializers.DecimalField(
        max_digits=12, decimal_places=2, required=False, default=0
    )
    region_total_price_net = serializers.DecimalField(
        max_digits=12, decimal_places=2, required=False, default=0
    )
    region_promo_amount = serializers.DecimalField(
        max_digits=12, decimal_places=2, required=False, default=0
    )
    region_promo_amount_net = serializers.DecimalField(
        max_digits=12, decimal_places=2, required=False, default=0
    )

    delivery_time_in_weeks = serializers.ReadOnlyField(
        source='get_longest_delivery_time_in_weeks_as_string'
    )
    invoice_address_used = serializers.BooleanField(
        source='different_billing_address',
        read_only=True,
    )

    def create(self, validated_attrs):
        items_attrs = validated_attrs.pop('items')
        order = Order.objects.create(**validated_attrs)

        for item in items_attrs:
            OrderItem.objects.create(order=order, **item)
        return order

    vatregex = re.compile('^((at)?U[0-9]{8}|(de)?[0-9]{9}|(pl)?[0-9]{10})$')

    def validate_vat(self, value):
        if not value or not self.vatregex.match(value):
            return value
        if not vat_validator(value):
            raise serializers.ValidationError('Eu vat is inactive or invalid')
        return value

    def validate_invoice_vat(self, value):
        if not value or not self.vatregex.match(value):
            return value
        if not vat_validator(value):
            raise serializers.ValidationError('Eu vat is inactive or invalid')
        return value

    class Meta:
        model = Order
        fields = (
            'id',
            'items',
            'status_text',
            'status_previous_text',
            'region',
            'region_total_price',
            'region_total_price_net',
            'region_promo_amount',
            'region_promo_amount_net',
            'delivery_time_in_weeks',
            'switch_status',
            'source_total_price',
            'source_total_price_net',
            'source_region_total_price',
            'source_region_total_price_net',
            'cached_items_type',
            'status',
            'status_previous',
            'sent_invoice_date',
            'order_pretty_id',
            'package_tracking',
            'paymant_status_history',
            'total_price',
            'total_price_net',
            'vat_type',
            'assembly',
            'token',
            'created_at',
            'updated_at',
            'estimated_delivery_time',
            'estimated_delivery_time_log',
            'first_item_added_at',
            'items_changed_at',
            'price_updated_at',
            'placed_at',
            'paid_at',
            'settled_at',
            'payable_booking_date',
            'email',
            'first_name',
            'last_name',
            'street_address_1',
            'street_address_2',
            'company_name',
            'city',
            'postal_code',
            'country',
            'country_area',
            'phone',
            'vat',
            'notes',
            'hard_parking',
            'above_3rd_floor',
            'no_elevator',
            'invoice_for_company',
            'invoice_address_used',
            'invoice_company_name',
            'invoice_first_name',
            'invoice_last_name',
            'invoice_email',
            'invoice_vat',
            'invoice_street_address_1',
            'invoice_street_address_2',
            'invoice_city',
            'invoice_postal_code',
            'invoice_country',
            'invoice_country_area',
            'chosen_payment_method',
            'additional_text',
            'promo_text',
            'promo_amount',
            'promo_amount_net',
            'region_vat',
            'order_placed_email_sent',
            'showed_confirmation',
            'klarna_email_notification_for_accounting',
            'klarna_capture_attempt_date',
            'ab_tests',
            'cs_notes',
            'source_order_item',
            'target_order_item',
            'used_promo_config',
        )


class OrderSerializer(BaseOrderSerializer):
    suborders = BaseOrderSerializer(many=True, allow_null=True, required=False)

    class Meta:
        model = Order
        fields = (
            *BaseOrderSerializer.Meta.fields,
            'suborders',
        )


class EcommerceOrderItemSerializer(CartItemSerializer):
    class Meta:
        model = OrderItem
        fields = CartItemSerializer.Meta.fields


class EcommerceOrderSerializer(CartSerializer):
    """On confirmation page we need to show order data the same as cart data."""

    order_items = EcommerceOrderItemSerializer(many=True, source='material_items')
    cart_id = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = (
            'cart_id',
            'order_items',
            'region_name',
            'cart_items_count',
            'cart_used_promo',
            'cart_used_assembly',
            'promo_code_name',
            'has_assembly_possible',
            'has_old_sofa_collection',
            'order_pricing',
            'has_lighting',
            'library_items',
            'signed_in',
            'sale_enabled',
            'shelf_items_count',
            'has_t03',
            'has_t03_samples',
            'cart_ribbon_enabled',
            't03_available',
            'chosen_payment_method',
            'placed_at',
        )

    @staticmethod
    def get_cart_id(obj: Order) -> int | None:
        if cart := obj.get_cart():
            return cart.id


class OrderRetoolSerializer(BaseOrderSerializer):
    items = BaseOrderItemSerializer(many=True)
    assembly_price = serializers.CharField(source='get_assembly_price_display')

    class Meta:
        model = Order
        fields = (
            'id',
            'items',
            'status_text',
            'status_previous_text',
            'region',
            'region_total_price',
            'region_total_price_net',
            'region_promo_amount',
            'region_promo_amount_net',
            'delivery_time_in_weeks',
            'source_total_price',
            'source_total_price_net',
            'source_region_total_price',
            'source_region_total_price_net',
            'cached_items_type',
            'status',
            'status_previous',
            'sent_invoice_date',
            'order_pretty_id',
            'total_price',
            'total_price_net',
            'vat_type',
            'assembly',
            'created_at',
            'updated_at',
            'first_item_added_at',
            'items_changed_at',
            'price_updated_at',
            'placed_at',
            'paid_at',
            'settled_at',
            'email',
            'first_name',
            'last_name',
            'street_address_1',
            'street_address_2',
            'company_name',
            'city',
            'postal_code',
            'country',
            'country_area',
            'phone',
            'vat',
            'notes',
            'invoice_for_company',
            'invoice_address_used',
            'invoice_company_name',
            'invoice_first_name',
            'invoice_last_name',
            'invoice_email',
            'invoice_vat',
            'invoice_street_address_1',
            'invoice_street_address_2',
            'invoice_city',
            'invoice_postal_code',
            'invoice_country',
            'invoice_country_area',
            'chosen_payment_method',
            'additional_text',
            'promo_text',
            'promo_amount',
            'promo_amount_net',
            'region_vat',
            'cs_notes',
            'used_promo_config',
            'assembly_price',
        )

    def save(self, **kwargs):
        instance = super().save(**kwargs)
        cart = instance.cart
        if not cart:
            return instance
        # align vat number
        cart.vat = instance.vat
        cart.save(update_fields=['vat'])
        return instance


class OrderItemFullSerializer(serializers.ModelSerializer):
    class Meta:
        fields = '__all__'
        model = OrderItem

    content_type_id = serializers.ReadOnlyField(source='content_type.id')
    order_id = serializers.ReadOnlyField(source='order.id')
    region_id = serializers.ReadOnlyField(source='region.id')


class OrderFullSerializer(serializers.ModelSerializer):
    class Meta:
        fields = '__all__'
        model = Order

    items = serializers.SerializerMethodField()
    owner_id = serializers.ReadOnlyField(source='owner.id')
    parent_order_id = serializers.ReadOnlyField(source='parent_order.id')
    region_id = serializers.ReadOnlyField(source='region.id')

    def get_items(self, obj):
        items = obj.items.order_by('id')
        return OrderItemFullSerializer(items, many=True).data


class CheckOrderStatusFiltersSerializer(serializers.ModelSerializer):
    order = serializers.CharField(source='id', write_only=True)
    postal_code = serializers.CharField(write_only=True)
    status = serializers.SerializerMethodField()
    order_status_code = serializers.IntegerField(source='status', read_only=True)

    default_error_code = 'incorrect_order_data'
    allowed_statuses = [0, 1, 2, 3, 4, 5, 6, 7]

    def validate_order(self, value):
        order_id = Order.deserialize_order_number(value)
        if not order_id:
            raise ValidationError(code=self.default_error_code)
        return order_id

    def get_status(self, instance):
        status = instance.status
        delivery_dates = [instance.estimated_delivery_time]
        if status not in self.allowed_statuses:
            return get_status_description('no_order', instance.id, empty_lines=False)
        return get_status_description(
            instance.status,
            instance.id,
            delivery_date=max(delivery_dates),
            empty_lines=False,
            order=instance,
        )

    class Meta:
        model = Order
        fields = ('order', 'status', 'postal_code', 'order_status_code')


class CheckOrderStatusSerializer(CheckOrderStatusFiltersSerializer):
    email = serializers.EmailField(required=False)
    order = serializers.CharField(write_only=True)

    def validate_order(self, value):
        return value

    class Meta:
        model = Order
        fields = CheckOrderStatusFiltersSerializer.Meta.fields + ('email',)


class CheckOrderStatusHistorySerializer(serializers.ModelSerializer):
    created_at = serializers.DateTimeField(read_only=True)

    class Meta:
        model = OrderStatusCheckHistory
        fields = (
            'created_at',
            'order',
            'postal_code',
            'email',
            'order_status',
            'success',
        )


class PackageSerializer(serializers.Serializer):
    size = serializers.SerializerMethodField()
    weight = serializers.SerializerMethodField()

    class Meta:
        fields = (
            'size',
            'wieght',
        )

    def get_size(self, obj):
        return f'{obj.dim_x}x{obj.dim_y}x{obj.dim_z} mm'

    def get_weight(self, obj):
        return f'{round(obj.weight, 2)} kg'


class OrderItemStatusAssemblyServiceSerializer(DataclassSerializer):
    class Meta:
        dataclass = AssemblyServiceDTO
        fields = ('status', 'accepted_date')


def can_see_tracking_url(user, order):
    return user.is_authenticated and (user == order.owner or user.is_superuser)


class OrderItemStatusDeliveryTimeFrameSelectedSlotSerializer(
    serializers.ModelSerializer
):
    start_hour = serializers.TimeField(format='%H:%M', read_only=True)
    end_hour = serializers.TimeField(format='%H:%M', read_only=True)

    class Meta:
        dataclass = AssemblyServiceDTO
        fields = ('status', 'accepted_date')


class OrderItemStatusDeliveryTimeFrameProposalSerializer(DataclassSerializer):
    url = serializers.SerializerMethodField()

    class Meta:
        dataclass = DeliveryTimeFrameProposalDTO
        fields = (
            'url',
            'status',
            'is_confirmed',
            'selected_dates',
            'accepted_date',
        )

    def get_url(self, obj):
        user = self.context['request'].user
        order = self.context['order']
        if not can_see_tracking_url(user, order):
            return ''
        return reverse('dtf-proposal', args=[obj.id])


class OrderItemStatusTrackingInfoSerializer(DataclassSerializer):
    url = serializers.SerializerMethodField()

    class Meta:
        dataclass = LogisticOrderDTO
        fields = ('tracking_number', 'url')

    def get_url(self, obj):
        user = self.context['request'].user
        order = self.context['order']
        if not can_see_tracking_url(user, order):
            return ''

        if obj.carrier is None:
            return ''

        carrier = obj.carrier.lower().strip()
        if carrier == 'tnt':
            return (
                f'https://www.tnt.com/express/en_gc/site/shipping-tools/'
                f'tracking.html?searchType=con&cons={obj.tracking_number}'
            )
        elif carrier == 'dpd':
            return f'https://tracking.dpd.de/parcelstatus?query={obj.tracking_number}'
        elif carrier == 'ups':
            return (
                f'https://wwwapps.ups.com/WebTracking/track?'
                f'loc={obj.tracking_number}&HTMLVersion=5.0&'
            )
        elif carrier == 'fedex':
            return (
                f'https://www.fedex.com/fedextrack/?action=track&'
                f'tracknumbers={obj.tracking_number}'
            )
        return ''


class OrderItemStatusToBeShippedEmail24Serializer(DataclassSerializer):
    url = serializers.SerializerMethodField()

    class Meta:
        dataclass = ToBeShippedEmail24DTO
        fields = (
            'status',
            'url',
        )

    def get_url(self, obj):
        user = self.context['request'].user
        order = self.context['order']
        if not can_see_tracking_url(user, order):
            return ''
        return obj.url


class OrderItemStatusSerializer(serializers.ModelSerializer):
    is_delayed = serializers.SerializerMethodField()
    is_dedicated_transport = serializers.SerializerMethodField()
    is_email24 = serializers.SerializerMethodField()

    calendar_week = serializers.SerializerMethodField()
    color = serializers.SerializerMethodField()
    delivery_date = serializers.SerializerMethodField()
    delivery_range_start = serializers.SerializerMethodField()
    delivery_range_end = serializers.SerializerMethodField()
    dimensions = serializers.SerializerMethodField()
    furniture_category = serializers.SerializerMethodField()
    furniture_type = serializers.SerializerMethodField()
    instruction = serializers.SerializerMethodField()
    packaging = serializers.SerializerMethodField()
    preview = serializers.SerializerMethodField()
    shelf_type = serializers.SerializerMethodField()
    product_status = serializers.SerializerMethodField()
    status = serializers.SerializerMethodField()
    tracking_info = serializers.SerializerMethodField()

    assembly_service = serializers.SerializerMethodField()
    delivery_time_frames = serializers.SerializerMethodField()
    email24 = serializers.SerializerMethodField()
    is_assembly_service_mix_no_assembly = serializers.SerializerMethodField()
    estimated_production_date = serializers.SerializerMethodField()
    product_id = serializers.SerializerMethodField()
    as_max_status = serializers.SerializerMethodField()
    as_max_planning_date = serializers.SerializerMethodField()
    is_complaint = serializers.BooleanField(source='order.is_complaint', read_only=True)

    class Meta:
        model = OrderItem
        fields = (
            'id',
            'is_assembly',
            'is_delayed',
            'is_dedicated_transport',
            'is_email24',
            'calendar_week',
            'color',
            'quantity',
            'delivery_date',
            'delivery_range_start',
            'delivery_range_end',
            'dimensions',
            'furniture_category',
            'furniture_type',
            'instruction',
            'packaging',
            'preview',
            'shelf_type',
            'status',
            'product_status',
            'tracking_info',
            'assembly_service',
            'delivery_time_frames',
            'as_max_status',
            'as_max_planning_date',
            'is_complaint',
            'email24',
            'is_assembly_service_mix_no_assembly',
            'estimated_production_date',
            'product_id',
        )

    def get_is_delayed(self, obj):
        return obj.order.is_production_delayed()

    def get_is_dedicated_transport(self, obj):
        logistic_order = obj.get_logistic_order()
        if logistic_order is None:
            return False
        return logistic_order.dedicated_transport is not None

    def get_is_email24(self, obj):
        logistic_order = obj.get_logistic_order()
        if logistic_order is None:
            return False
        return bool(logistic_order.to_be_shipped_email24)

    def get_dimensions(self, obj):
        dimensions = [
            str(dimension['value'])
            for dimension in obj.order_item.get_dimensions().get_dimensions_list()
        ]
        return '{} cm'.format(' x '.join(dimensions))

    def get_estimated_production_date(self, obj):
        # product = obj.get_product()
        # if product and product.status in [
        #     ProductStatus.ASSIGNED_TO_PRODUCTION,
        #     ProductStatus.IN_PRODUCTION,
        # ]:
        #     return product.estimated_production_date.isoformat()
        return (
            obj.order.estimated_delivery_time.date().isoformat()
            if obj.order.estimated_delivery_time
            else ''
        )

    def get_delivery_date(self, obj):
        logistic_order = obj.get_logistic_order()
        if logistic_order is None:
            return None
        return (
            logistic_order.delivered_date.isoformat()
            if logistic_order.delivered_date
            else None
        )

    def get_calendar_week(self, obj):
        delivery_date_start = obj.delivery_range[0]
        if not delivery_date_start:
            return None
        return delivery_date_start.isocalendar().week

    def get_delivery_range_start(self, obj):
        return obj.delivery_range[0]

    def get_delivery_range_end(self, obj):
        return obj.delivery_range[1]

    def get_color(self, obj):
        order_item = obj.order_item
        if order_item.get_furniture_type() == Furniture.sample_box.value:
            return None
        return order_item.color.translated_color

    def get_shelf_type(self, obj):
        order_item = obj.order_item
        if order_item.get_furniture_type() == Furniture.sample_box.value:
            return order_item.box_variant.variant_type
        return order_item.shelf_type

    def get_status(self, obj):
        if obj.order.status in [
            OrderStatus.CANCELLED.value,
            OrderStatus.DRAFT.value,
            OrderStatus.PAYMENT_PENDING.value,
            OrderStatus.PAYMENT_FAILED.value,
        ]:
            return obj.order.status
        logistic_order = obj.get_logistic_order()
        if obj.is_samplebox():
            if logistic_order.delivered_date:
                if logistic_order.delivered_date >= date.today():
                    return OrderStatus.SHIPPED.value
                else:
                    return OrderStatus.DELIVERED.value
            if logistic_order.to_be_shipped:
                return OrderStatus.TO_BE_SHIPPED.value
            if logistic_order.sent_to_customer:
                return OrderStatus.SHIPPED.value
            return obj.order.status

        if obj.is_assembly() or obj.order.is_assembly_service_mix():
            assembly_mix = (
                logistic_order.assembly_service is not None and obj.is_assembly()
            ) or obj.order.is_assembly_service_mix()
            if (obj.order.status == OrderStatus.SHIPPED and assembly_mix) or (
                obj.order.status == OrderStatus.DELIVERED
                and not logistic_order.sent_to_customer
            ):
                return OrderStatus.TO_BE_SHIPPED.value
            elif (
                obj.order.status == OrderStatus.DELIVERED
                and self.get_delivery_date(obj) >= date.today().isoformat()
            ):
                return OrderStatus.SHIPPED.value

        product = obj.get_product()
        if product is None:
            return obj.order.status

        PRODUCT_TO_ORDER_STATUS = {
            ProductStatus.NEW.value: OrderStatus.IN_PRODUCTION.value,
            ProductStatus.ASSIGNED_TO_PRODUCTION.value: OrderStatus.IN_PRODUCTION.value,
            ProductStatus.IN_PRODUCTION.value: OrderStatus.IN_PRODUCTION.value,
            ProductStatus.TO_BE_SHIPPED.value: OrderStatus.TO_BE_SHIPPED.value,
            ProductStatus.SENT_TO_CUSTOMER.value: OrderStatus.SHIPPED.value,
            ProductStatus.SENT_TO_WAREHOUSE.value: OrderStatus.SHIPPED.value,
            ProductStatus.DELIVERED_TO_CUSTOMER.value: OrderStatus.DELIVERED.value,
        }

        return PRODUCT_TO_ORDER_STATUS.get(product.status, obj.order.status)

    def get_product_status(self, obj):
        product = obj.get_product()
        if not product:
            return None
        return product.status

    def get_is_assembly_service_mix_no_assembly(self, obj):
        return obj.order.is_assembly_service_mix() and not obj.is_assembly()

    def get_furniture_category(self, obj):
        order_item = obj.order_item
        if order_item.get_furniture_type() == Furniture.sample_box.value:
            return None
        return order_item.furniture_category

    def get_furniture_type(self, obj):
        return obj.order_item.get_furniture_type()

    def get_preview(self, obj):
        return file_url(obj.order_item.preview)

    def get_packaging(self, obj):
        product = obj.get_product()
        if not product:
            return []
        return PackageSerializer(product.get_packaging(), many=True).data

    def get_instruction(self, obj):
        product = obj.get_product()
        if not product:
            return None
        if product.details.instruction:
            return product.details.instruction.url

    def get_assembly_service(self, obj):
        logistic_order = obj.get_logistic_order()
        if not logistic_order:
            return None
        assembly_service = logistic_order.assembly_service
        if not assembly_service:
            return None
        return OrderItemStatusAssemblyServiceSerializer(assembly_service).data

    def get_delivery_time_frames(self, obj):
        logistic_order = obj.get_logistic_order()
        if not logistic_order or not logistic_order.dtf_proposals:
            return None
        dtf = sorted(logistic_order.dtf_proposals, key=operator.attrgetter('id'))[-1]
        return OrderItemStatusDeliveryTimeFrameProposalSerializer(
            dtf, context=self.context
        ).data

    def get_as_max_status(self, obj):
        logistic_order = obj.get_logistic_order()
        if not logistic_order or not logistic_order.as_max_status:
            return AsMaxStatusEnum.EMPTY.value
        return logistic_order.as_max_status

    def get_as_max_planning_date(self, obj):
        logistic_order = obj.get_logistic_order()
        if not logistic_order or not logistic_order.as_max_status_history:
            return ''
        sorted_history = sorted(
            logistic_order.as_max_status_history,
            key=operator.attrgetter('changed_at'),
            reverse=True,
        )
        return sorted_history[0].planned_start_date

    def get_email24(self, obj):
        logistic_order = obj.get_logistic_order()
        if not logistic_order or not logistic_order.to_be_shipped_email24:
            return None
        email24 = sorted(
            logistic_order.to_be_shipped_email24, key=operator.attrgetter('id')
        )[-1]
        return OrderItemStatusToBeShippedEmail24Serializer(
            email24, context=self.context
        ).data

    def get_tracking_info(self, obj):
        logistic_order = obj.get_logistic_order()
        if not logistic_order:
            return None

        dtf = logistic_order.dtf_proposals
        assembly_service = logistic_order.assembly_service
        if dtf or assembly_service is not None:
            return None

        return OrderItemStatusTrackingInfoSerializer(
            logistic_order, context=self.context
        ).data

    @staticmethod
    def get_product_id(obj) -> Optional[int]:
        if product := obj.product_set.exclude(status=ProductStatus.ABORTED).last():
            return product.id


class OrderStatusSerializer(serializers.ModelSerializer):
    items = OrderItemStatusSerializer(source='material_items', many=True)
    status_paid = serializers.SerializerMethodField()

    def get_status_paid(self, obj):
        return True if obj.paid_at else False

    class Meta:
        model = Order
        fields = ('items', 'status_paid', 'is_complaint', 'is_assembly_service_mix')


class EstimatedDeliveryTimeBigQuerySerializer(serializers.ModelSerializer):
    order_id = serializers.IntegerField(source='id')
    estimated_delivery_date = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = (
            'order_id',
            'updated_at',
            'estimated_delivery_date',
        )

    @staticmethod
    def get_estimated_delivery_date(obj):
        return obj.estimated_delivery_time.strftime('%Y-%m-%d')


class GTMPurchaseEventOrderOwnerSerializer(serializers.ModelSerializer):
    em = serializers.CharField(source='email')
    ph = serializers.CharField(source='phone')
    fbp = serializers.SerializerMethodField(method_name='get_fbp_cookie')
    fbc = serializers.SerializerMethodField(method_name='get_fbc_cookie')
    external_id = serializers.SerializerMethodField()
    client_ip_address = serializers.SerializerMethodField()
    client_user_agent = serializers.SerializerMethodField()
    zp = serializers.SerializerMethodField(method_name='get_zip_code')
    country = serializers.CharField(source='get_country_short')

    class Meta:
        model = Order
        fields = (
            'em',
            'ph',
            'fbp',
            'fbc',
            'external_id',
            'client_ip_address',
            'client_user_agent',
            'zp',
            'country',
        )

    def get_zip_code(self, obj: Order) -> Optional[str]:
        return obj.postal_code.replace('-', '') if obj.postal_code else None

    def get_fbp_cookie(self, _) -> Optional[str]:
        return self.context['request'].COOKIES.get('_fbp', None)

    def get_fbc_cookie(self, _) -> Optional[str]:
        return self.context['request'].COOKIES.get('_fbc', None)

    def get_external_id(self, value: Order) -> Optional[str]:
        return f'user-{value.owner_id}'

    def get_client_ip_address(self, _) -> Optional[str]:
        request = self.context['request']
        return (
            request.META.get('REMOTE_ADDR', '')
            or request.META.get('HTTP_X_FORWARDED_FOR', '').split(',')[-1].strip()
        )

    def get_client_user_agent(self, _):
        return self.context['request'].META.get('HTTP_USER_AGENT', None)


class GTMPurchaseEventOrderItemSerializer(serializers.ModelSerializer):
    id = serializers.IntegerField(source='object_id')
    quantity = serializers.IntegerField(default=1)
    variant = serializers.CharField(source='order_item.get_variant')

    class Meta:
        model = OrderItem
        fields = (
            'id',
            'quantity',
            'variant',
        )


class GTMPurchaseEventCustomDataSerializer(serializers.ModelSerializer):
    currency = serializers.CharField(max_length=3, source='region.currency.code')
    value = serializers.DecimalField(
        max_digits=None,
        decimal_places=2,
        source='total_price_net',
    )
    order_id = serializers.CharField(source='id')
    quantity = serializers.SerializerMethodField()
    contents = GTMPurchaseEventOrderItemSerializer(many=True, source='items')

    class Meta:
        model = Order
        fields = (
            'currency',
            'value',
            'order_id',
            'quantity',
            'contents',
        )

    @staticmethod
    def get_quantity(obj):
        return obj.items.count()


class GTMPurchaseEventSerializer(serializers.ModelSerializer):
    """
    Main serializer for serverside GTM purchase event.
    """

    event_name = serializers.CharField(default='Purchase')
    event_time = serializers.SerializerMethodField()
    event_id = serializers.SerializerMethodField()
    event_source_url = serializers.SerializerMethodField()
    action_source = serializers.CharField(default='website')
    user_data = serializers.SerializerMethodField()
    custom_data = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = (
            'event_name',
            'event_time',
            'event_id',
            'event_source_url',
            'action_source',
            'user_data',
            'custom_data',
        )

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.event_timestamp = int(datetime.now().timestamp())

    def get_event_time(self, obj):
        return self.event_timestamp

    def get_event_id(self, obj):
        return obj.id

    @staticmethod
    def get_event_source_url(obj):
        return urljoin(settings.SITE_URL, f'confirmation/{obj.id}')

    def get_user_data(self, obj):
        # circular import
        from payments.services.gtm_purchase_event_data_handler import (
            GTMPurchaseEventDataHandler,
        )

        return GTMPurchaseEventDataHandler(obj).get_data()

    @staticmethod
    def get_custom_data(obj):
        return GTMPurchaseEventCustomDataSerializer(obj).data


class MailingOrderItemSerializer(MailingCartItemSerializer):
    sellable_item = MailingSellableItemSerializer()
    assembly_manual_link = serializers.SerializerMethodField()
    disassembly_manual_link = serializers.SerializerMethodField()
    product_id = serializers.SerializerMethodField()

    class Meta:
        model = OrderItem
        fields = (
            'sellable_item',
            'region_price_display',
            'assembly_manual_link',
            'disassembly_manual_link',
            'product_id',
        )

    def get_assembly_manual_link(self, obj) -> Optional[str]:
        if obj.content_type.model != SellableItemContentTypes.JETTY:
            return None

        if product := self._get_product(obj):
            lat = LoginAccessToken.get_or_create_for_user(obj.parent.owner)
            manual_url = reverse('assembly-manual-redirect', args=(product.id,))
            return f'{manual_url}?lat={lat}'

    @staticmethod
    def get_disassembly_manual_link(obj) -> Optional[str]:
        if obj.content_type.model != SellableItemContentTypes.JETTY:
            return None

        return LanguageEnum(
            obj.parent.owner.profile.language
        ).get_disassembly_manual_link(obj.sellable_item.height)

    def get_product_id(self, obj) -> Optional[int]:
        if product := self._get_product(obj):
            return product.id

    @staticmethod
    def _get_product(obj) -> Optional[Product]:
        return obj.product_set.exclude(status=ProductStatus.ABORTED).last()


class OrderAddressFormSerializer(serializers.ModelSerializer):
    class Meta:
        model = Order
        fields = (
            'email',
            'first_name',
            'last_name',
            'street_address_1',
            'street_address_2',
            'city',
            'phone_prefix',
            'phone',
            'postal_code',
            'country',
            'floor_number',
            'no_elevator',
            'notes',
            'vat',
            'invoice_company_name',
            'invoice_first_name',
            'invoice_last_name',
            'invoice_street_address_1',
            'invoice_postal_code',
            'invoice_city',
            'invoice_country',
            'company_name',
        )

    def update(self, instance: Order, validated_data: dict) -> Order:
        instance = super().update(instance, validated_data)
        instance.copy_address_to_profile()
        instance.save()
        return instance

    def validate_postal_code(self, value: str) -> str:
        if not value:
            return value

        region_restriction_service = RegionRestrictionService(
            postal_code=value, region=self.instance.region
        )
        if region_restriction_service.is_outside_of_tax_union():
            raise ValidationError(
                f'Postal code {value} belongs to the special territory of EU.'
                'We do not deliver there',
            )
        if (
            self.instance.has_s01
            and region_restriction_service.is_sofa_delivery_unavailable()
        ):
            raise ValidationError(f'We do not deliver sofas to your address, {value}')

        return value


class MailingOrderSummarySerializer(MailingCartSerializer):
    items = MailingOrderItemSerializer(many=True)
    pricing = PricingSerializer(source='*')
    estimated_production_date = serializers.SerializerMethodField()
    estimated_production_year = serializers.SerializerMethodField()
    estimated_production_date_range = serializers.CharField(
        source='production_range_formatted',
    )
    estimated_delivery_date = serializers.SerializerMethodField()
    estimated_delivery_date_range = serializers.CharField(
        source='delivery_range_formatted',
    )
    order_status_url = serializers.SerializerMethodField()

    class Meta:
        model = Order
        fields = (
            'status',
            'items',
            'items_count',
            'total_price_display',
            'pricing',
            'currency',
            'first_name',
            'last_name',
            'street_address_1',
            'street_address_2',
            'company_name',
            'city',
            'postal_code',
            'country',
            'country_area',
            'phone',
            'invoice_for_company',
            'invoice_company_name',
            'invoice_first_name',
            'invoice_last_name',
            'invoice_email',
            'invoice_vat',
            'invoice_street_address_1',
            'invoice_street_address_2',
            'invoice_city',
            'invoice_postal_code',
            'invoice_country',
            'invoice_country_area',
            'chosen_payment_method',
            'estimated_production_date',
            'estimated_production_year',
            'estimated_production_date_range',
            'estimated_delivery_date',
            'estimated_delivery_date_range',
            'order_status_url',
        )

    @staticmethod
    def get_estimated_production_date(obj) -> str:
        return obj.production_range_formatted.split(' - ')[-1]

    def get_estimated_production_year(self, obj) -> int:
        date_formatted = self.get_estimated_production_date(obj)
        date_object = datetime.strptime(date_formatted, '%d.%m.%Y')
        return date_object.year

    @staticmethod
    def get_estimated_delivery_date(obj) -> str:
        return obj.delivery_range_formatted.split(' - ')[-1]

    def get_order_status_url(self, obj) -> str:
        base_url = urljoin(
            settings.SITE_URL,
            reverse_with_region(
                'front-contact',
                self.context['region'].country.code.lower(),
            ),
        )
        query = urlencode(
            {
                'topic': 'order_status',
                'order_id': obj.id,
                'postal_code': obj.postal_code,
            }
        )
        language = self._get_language_from_owner_profile(obj)
        return translate_url(f'{base_url}?{query}', language)

    @staticmethod
    def _get_language_from_owner_profile(obj) -> str:
        return obj.owner.profile.language


class MailingPurchaseOrderItemSerializer(OrderItemStatusSerializer):
    class Meta:
        model = OrderItem
        fields = (
            'region_price',
            'furniture_type',
            'furniture_category',
            'shelf_type',
            'color',
        )


class RetoolCopyItemSerializer(serializers.Serializer):
    """
    Retool is an external service used to build GUI. This serializer is a part of B2B
    Panel, that allows to create and proceed business orders.
    """

    object_id = serializers.IntegerField()
    furniture_type = serializers.ChoiceField(choices=('jetty', 'watty'))
    quantity = serializers.IntegerField(default=1)

    def save(self, order: Order):
        model_class = (
            Jetty if self.validated_data['furniture_type'] == 'jetty' else Watty
        )
        item_to_copy = model_class.objects.get(id=self.validated_data['object_id'])
        return copy_furniture(
            old_furniture=item_to_copy,
            owner=order.owner,
            new_furniture_status=FurnitureStatusEnum.DRAFT,
        )
