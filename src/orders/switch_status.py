import enum
import logging

from collections import namedtuple

from django.utils import (
    timezone,
    translation,
)

from custom.enums import (
    ChoicesMixin,
    Furniture,
)
from customer_service.enums import CSCorrectionRequestType
from gallery.models import (
    <PERSON>y,
    SampleBox,
    <PERSON>tty,
    <PERSON><PERSON>,
)
from invoice.choices import (
    InvoiceItemType,
    InvoiceStatus,
)
from invoice.enums import InvoiceItemTag

Price = namedtuple('Price', ['brutto', 'netto'])


logger = logging.getLogger('cstm')


def can_order_item_be_switched(order_item_to_remove):
    return not order_item_to_remove.product_is_batched()


def should_recalculate_order_again(options):
    return (
        options['should_add_assembly']
        or options['without_assembly_price_change']
        or options['without_shelf_price_change']
    )


def with_added_assembly(options):
    return (
        not options['without_assembly_price_change'] and options['should_add_assembly']
    )


def with_edited_assembly(options):
    return (
        not options['without_assembly_price_change'] and options['should_edit_assembly']
    )


def is_order_promo_inactive(order):
    return order.used_promo and not order.used_promo.is_active()


class InvoiceCorrector:
    def __init__(self, order):
        self.order = order
        self.source_invoice = (
            order.invoice_set.exclude(
                status__in=[InvoiceStatus.PROFORMA, InvoiceStatus.CORRECTING_DRAFT]
            )
            .order_by('-issued_at')
            .first()
        )

    def __enter__(self):
        from invoice.models import Invoice

        translation.activate(self.order.owner.profile.language)
        self.target_invoice = Invoice.objects.create(
            pretty_id='DRAFT',
            order=self.order,
            sell_at=self.source_invoice.sell_at,
            issued_at=timezone.now(),
            currency_symbol=self.order.get_region().currency.symbol,
        )
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        self.target_invoice.delete()

    def should_correction_amount_gross_be_updated_from_invoice(
        self, correction_invoice_total_value
    ):
        return (
            correction_invoice_total_value
            != self.order.region_total_price_after_switch_diff.brutto
        )

    def create_correction_request(self, options, by):
        from customer_service.correction_request_strategies import (
            CorrectionRequestSwitchStrategy,
        )
        from customer_service.models import CSCorrectionRequest

        (
            added_invoice_items,
            deleted_invoice_items,
        ) = self.prepare_deleted_and_added_invoice_items(options)

        correction_request = CSCorrectionRequest.objects.create(
            invoice=self.source_invoice,
            issuer=by,
            type_cs=CSCorrectionRequestType.TYPE_SWITCH,
            tag=InvoiceItemTag.cs_correction_status_to_invoice_tag_mapper(
                CSCorrectionRequestType.TYPE_SWITCH
            ),
            correction_amount_gross=(
                -self.order.region_total_price_after_switch_diff.brutto
            ),
        )
        correction_request.added_invoice_items.set(added_invoice_items)
        correction_request.deleted_invoice_items.set(deleted_invoice_items)
        correction_request_strategy = CorrectionRequestSwitchStrategy(
            correction_request
        )
        if self.is_absolute_promo_used():
            correction_invoice = correction_request_strategy.prepare_correcting_draft_for_switch_with_absolute_voucher(  # noqa: E501
                target_invoice=self.target_invoice
            )
        else:
            correction_invoice = (
                correction_request_strategy.prepare_correcting_draft_for_switch()
            )

        correction_invoice_total_value = correction_invoice.to_diff_dict()[
            'total_value'
        ]
        if self.should_correction_amount_gross_be_updated_from_invoice(
            correction_invoice_total_value
        ):
            correction_request.correction_amount_gross = -correction_invoice_total_value
            logger.warning(
                'Difference between invoice and order during switch: '
                'Invoice: %s, '
                'Diff: %s '
                'Order: %s, '
                'Diff: %s',
                correction_invoice.pk,
                correction_invoice_total_value,
                self.order.pk,
                self.order.region_total_price_after_switch_diff.brutto,
                exc_info=True,
            )
            correction_request.save(update_fields=['correction_amount_gross'])
        return correction_request.correction_amount_gross

    def is_absolute_promo_used(self):
        return self.order.used_promo and self.order.used_promo.is_absolute()

    def prepare_deleted_and_added_invoice_items(self, options):
        deleted_invoice_items = self.source_invoice.invoice_items.filter(
            order_item=self.order.source_order_item,
            item_type__in=[
                InvoiceItemType.ITEM,
                InvoiceItemType.DELIVERY,
                InvoiceItemType.SERVICE,
            ],
        )
        added_invoice_items = self.target_invoice.invoice_items.filter(
            order_item=self.order.target_order_item,
            item_type__in=[
                InvoiceItemType.ITEM,
                InvoiceItemType.DELIVERY,
                InvoiceItemType.SERVICE,
            ],
        )
        if with_added_assembly(options):
            added_invoice_items |= self.added_assembly_invoice_items()
        elif with_edited_assembly(options):
            deleted_invoice_items |= self.deleted_assembly_invoice_items()
            added_invoice_items |= self.added_assembly_invoice_items()
        return added_invoice_items, deleted_invoice_items

    def deleted_assembly_invoice_items(self):
        return self.source_invoice.invoice_items.filter(
            item_type=InvoiceItemType.ASSEMBLY
        )

    def added_assembly_invoice_items(self):
        return self.target_invoice.invoice_items.filter(
            item_type=InvoiceItemType.ASSEMBLY
        )


@enum.unique
class SwitchStatus(ChoicesMixin, enum.Enum):
    BLANK = 'blank'
    ITEM_REPLACEMENT = 'item_replacement'
    COST_RECALCULATION = 'cost_recalculation'
    WAITING_FOR_ADDITIONAL_PAYMENT = 'waiting_for_additional_payment'
    COMPLETED = 'completed'
    CANCELLED = 'cancelled'


def furniture_type_to_model(furniture_type):
    FURNITURE_TYPE_TO_MODEL = {
        Furniture.jetty.value: Jetty,
        Furniture.watty.value: Watty,
        Furniture.sample_box.value: SampleBox,
        Furniture.sotty.value: Sotty,
    }
    return FURNITURE_TYPE_TO_MODEL[furniture_type]
