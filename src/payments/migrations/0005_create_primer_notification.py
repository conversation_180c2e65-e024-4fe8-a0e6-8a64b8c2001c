# Generated by Django 3.2.14 on 2022-09-28 10:31

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('payments', '0004_alter_status_field'),
    ]

    operations = [
        migrations.CreateModel(
            name='PrimerNotification',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('event_type', models.CharField(max_length=255)),
                ('date', models.DateTimeField()),
                ('notification_config_id', models.Char<PERSON>ield(max_length=255)),
                ('notification_config_description', models.Char<PERSON>ield(max_length=255)),
                ('version', models.CharField(max_length=7)),
                ('payment_id', models.Char<PERSON>ield(max_length=255)),
                ('payment_date', models.DateTimeField()),
                ('payment_amount', models.Integer<PERSON>ield()),
                ('payment_currency_code', models.CharField(max_length=3)),
                ('payment_customer_id', models.CharField(max_length=255)),
                ('payment_order_id', models.Char<PERSON>ield(max_length=255, unique=True)),
                ('code', models.Char<PERSON><PERSON>(max_length=255)),
                ('payment_method_token', models.CharField(max_length=255)),
                ('payment_method_analytics_id', models.CharField(max_length=255)),
                ('payment_method_type', models.CharField(max_length=255)),
                ('payment_method_data', models.JSONField()),
                (
                    'three_ds_secure_authentication_response_code',
                    models.CharField(max_length=255),
                ),
                ('processor', models.JSONField()),
            ],
        ),
        migrations.AddField(
            model_name='transaction',
            name='primer_notification',
            field=models.ForeignKey(
                blank=True,
                default=None,
                null=True,
                on_delete=django.db.models.deletion.CASCADE,
                related_name='transactions',
                to='payments.primernotification',
            ),
        ),
    ]
