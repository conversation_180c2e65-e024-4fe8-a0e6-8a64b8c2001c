import typing

from decimal import Decimal
from typing import Optional

from django.conf import settings

from custom.enums import ShelfType
from custom.models import GlobalSettings

from . import (
    base_prices,
    coefficients,
    elements,
    margins,
    traits,
    value_based,
)

if typing.TYPE_CHECKING:
    from gallery.models import Jetty
    from pricing_v3.models import PricingVersion


def calculate_elements_price(
    jetty: 'Jetty',
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    return {
        'horizontals': elements.calculate_horizontals_price(
            jetty.horizontals,
            price_coefficients,
        ),
        'verticals': elements.calculate_verticals_price(
            jetty.verticals,
            price_coefficients,
        ),
        'supports': elements.calculate_supports_price(
            jetty.supports,
            jetty.backs,
            price_coefficients,
        ),
        'backs': elements.calculate_backs_price(jetty.backs, price_coefficients),
        'doors': elements.calculate_doors_price(jetty.doors, price_coefficients),
        'drawers': elements.calculate_jetty_drawers_price(
            jetty.drawers, price_coefficients
        ),
        'long_legs': elements.calculate_long_legs_price(
            jetty.long_legs,
            price_coefficients,
        ),
        'plinth': elements.calculate_plinth_price(
            jetty.plinth,
            jetty.width,
            price_coefficients,
        ),
        'inserts': elements.calculate_inserts_price(jetty.inserts, price_coefficients),
        'cable_management': elements.calculate_cable_managements_price(
            jetty.cable_management,
            price_coefficients,
        ),
        'desk_beams': elements.calculate_deskbeam_price(
            jetty.desk_beams,
            price_coefficients,
        ),
    }


def calculate_traits_price(
    jetty: 'Jetty',
    price_sum: Decimal,
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    return {
        'shelf_type': traits.calculate_shelf_type_price(
            price_sum,
            jetty.shelf_type,
            price_coefficients,
        ),
        'material': traits.calculate_material_price(
            price_sum,
            jetty.shelf_type,
            jetty.material,
            price_coefficients,
        ),
        'depth': traits.calculate_depth_price(
            price_sum,
            jetty.depth,
            price_coefficients,
        ),
        'category': traits.calculate_category_price(
            price_sum,
            jetty.furniture_category,
            price_coefficients,
        ),
    }


def calculate_margins(
    jetty: 'Jetty',
    price_sum: Decimal,
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    return {
        'margin_base': margins.calculate_jetty_base_margin(
            price_sum, price_coefficients
        ),
        'margin_additional': margins.calculate_additional_small_furniture_margin(
            jetty.width,
            jetty.height,
            price_coefficients,
        ),
    }


def calculate_value_based_increase(
    jetty: 'Jetty',
    price_sum: Decimal,
    price_coefficients: dict[str, Decimal],
) -> dict[str, Decimal]:
    all_vbps = {
        'ceramic_red': value_based.calculate_ceramic_red_value_increase(
            price_sum,
            jetty.shelf_type,
            jetty.material,
            price_coefficients,
        ),
        'width_300': value_based.calculate_width_value_increase(
            price_sum,
            jetty.shelf_type,
            jetty.width,
            price_coefficients,
        ),
        'height_250': value_based.calculate_height_value_increase(
            price_sum,
            jetty.shelf_type,
            jetty.height,
            price_coefficients,
        ),
        'depth_400': value_based.calculate_depth_value_increase(
            price_sum,
            jetty.shelf_type,
            jetty.depth,
            price_coefficients,
        ),
    }
    # If the shelf is wide, high and terracota, it would have a 50% price increase
    # - let's prohibit that and only take the biggest value based increase.
    corrected_vbps = {key: 0 for key in all_vbps.keys()}
    max_key = max(all_vbps, key=all_vbps.get)
    corrected_vbps[max_key] = all_vbps[max_key]
    return corrected_vbps


def calculate_additional_increase(
    jetty: 'Jetty',
    jetty_price: Decimal,
    price_coefficients: dict[str, Decimal],
):
    if jetty.shelf_type == ShelfType.TYPE01:
        return jetty_price * price_coefficients['type_01_additional_increase']
    if jetty.shelf_type == ShelfType.TYPE02:
        return jetty_price * price_coefficients['type_02_additional_increase']
    if jetty.shelf_type == ShelfType.VENEER_TYPE01:
        return jetty_price * price_coefficients['type_01v_additional_increase']
    return 0


def prepare_price(
    jetty: 'Jetty', region_name: str, pricing_version: 'PricingVersion' = None
) -> dict:
    """
    Prepares the pricing dict.

    Pricing dict includes all the components of the price, for easier analysis.
    Args:
        jetty: Jetty for which the price is calculated
        region_name: Region name to calculate coefficients
        pricing_version: Optional pricing version. Could be used to recalculate price
                based on past coefficients

    Returns: Dictionary with the price split.

    """
    if pricing_version is None:
        price_coefficients = coefficients.get_coefficients(region_name)
    else:
        price_coefficients = pricing_version.coefficients

    base_jetty_price = base_prices.calculate_base_jetty_price(jetty, price_coefficients)
    jetty_price = sum(base_jetty_price.values())

    elements_price = calculate_elements_price(jetty, price_coefficients)
    jetty_price += sum(elements_price.values())

    traits_price = calculate_traits_price(jetty, jetty_price, price_coefficients)
    jetty_price += sum(traits_price.values())

    margins_price = calculate_margins(jetty, jetty_price, price_coefficients)
    jetty_price += sum(margins_price.values())

    logistic_price = base_prices.calculate_jetty_logistics_price(
        jetty, price_coefficients
    )
    jetty_price += sum(logistic_price.values())

    value_based_increase = calculate_value_based_increase(
        jetty, jetty_price, price_coefficients
    )
    jetty_price += sum(value_based_increase.values())

    additional_increase = calculate_additional_increase(
        jetty,
        jetty_price,
        price_coefficients,
    )

    jetty_price += additional_increase

    return {
        'jetty_price_gross': jetty_price * Decimal(settings.POLISH_VAT_FACTOR),
        **base_jetty_price,
        **elements_price,
        **traits_price,
        **margins_price,
        **value_based_increase,
        **logistic_price,
    }


def get_pricing_dict(jetty: 'Jetty', region_name: str, in_pln: bool) -> dict:
    pricing_dict = prepare_price(jetty, region_name=region_name)

    price_in_pln = pricing_dict['jetty_price_gross']
    if in_pln:
        final_price = price_in_pln
    else:
        final_price = price_in_pln / GlobalSettings.factor_euro()
    pricing_dict['total_rounded_gross_regional'] = int(final_price)
    return pricing_dict


def calculate_price(
    jetty: 'Jetty',
    region_name: Optional[str],
    pricing_version: 'PricingVersion' = None,
) -> int:
    pricing_dict = prepare_price(jetty, region_name, pricing_version)
    euro_price_gross = pricing_dict['jetty_price_gross'] / GlobalSettings.factor_euro()
    return int(euro_price_gross)
