import typing

from collections import defaultdict
from decimal import Decimal

from custom.enums import Axis

if typing.TYPE_CHECKING:
    from gallery.models import <PERSON><PERSON> = dict[str, int]
ElementsList = list[Element]

MAX_DOOR_HEIGHT_FOR_TREX = Decimal('0.382')


def compute_length(element: Element, axis: Axis) -> Decimal:
    """Compute element length along an axis and convert it to meters."""
    return abs(Decimal(element[f'{axis}2']) - Decimal(element[f'{axis}1'])) / 1000


def sum_lengths(elements: ElementsList, axis: Axis) -> Decimal:
    return sum(compute_length(element, axis) for element in elements)


def remove_supports_overlapping_with_backs(supports, backs):
    """In case when backs and supports take up the same space,
    only backs will be produced.
    So let's remove the supports.
    """
    backs_by_minimal_y = defaultdict(list)
    for back in backs:
        back_y_min = min(back['y1'], back['y2'])
        backs_by_minimal_y[back_y_min].append(back)

    new_supports = []
    for support in supports:
        support_y_min = min(support['y1'], support['y2'])

        found_overlapping_back = False
        for back in backs_by_minimal_y[support_y_min]:
            back_x_min = min(back['x1'], back['x2'])
            back_x_max = max(back['x1'], back['x2'])
            if (
                back_x_min <= support['x1'] <= back_x_max
                and back_x_min <= support['x2'] <= back_x_max
            ):
                found_overlapping_back = True
                break
        if not found_overlapping_back:
            new_supports.append(support)

    return new_supports


def calculate_horizontals_price(
    horizontals: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Horizontals price is based only on their width (assume depth==320)."""
    count, length_sum = get_horizontals_count_and_length_sum(horizontals)
    return (
        length_sum * price_coefficients['horizontal_length']
        + count * price_coefficients['horizontal_unit']
    )


def get_horizontals_count_and_length_sum(horizontals: list) -> tuple[int, Decimal]:
    count = len(horizontals)
    length_sum = Decimal('0')
    for horizontal in horizontals:
        horizontal_length = compute_length(horizontal, Axis.X)
        if horizontal_length >= Decimal('2.3'):
            # add "fake joint" - increase the price gradually, so when the actual joints
            # appear (at width=2.4m) we won't have a sudden increase in price
            # simplest way to do it is to introduce a part of a horizontal,
            # so now if the horizontal is 2.35m long, it'll count as 1.5 horizontals.
            count += (horizontal_length - Decimal('2.3')) / Decimal('0.1')
        length_sum += horizontal_length
    return count, length_sum


def calculate_deskbeam_price(
    desk_beams: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Desk beam aluminum profile price is based on it's size"""
    if not desk_beams:
        return Decimal()
    length = sum_lengths(desk_beams, axis=Axis.X)
    return (
        length * price_coefficients['desk_beam_length']
        + price_coefficients['desk_beam_unit']
    )


def calculate_verticals_price(
    verticals: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Verticals price is based only on their height (assume depth==320)."""
    length = sum_lengths(verticals, axis=Axis.Y)
    count = len(verticals)
    return (
        length * price_coefficients['vertical_length']
        + count * price_coefficients['vertical_unit']
    )


def calculate_supports_price(
    supports: ElementsList,
    backs: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Supports price is based only on their height (constant width==125).
    Sometimes the supports are overlapping with backs (i.e. backs were enabled
    but supports weren't removed from the Jetty even though they won't be produced.
    So we need to clean them first - remove the supports that won't be produced.
    """
    supports = remove_supports_overlapping_with_backs(supports, backs)
    length = sum_lengths(supports, axis=Axis.Y)
    count = len(supports)
    return (
        length * price_coefficients['support_length']
        + count * price_coefficients['support_unit']
    )


def calculate_backs_price(
    backs: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Backs price is based on both width and height."""
    price = Decimal(0)
    for back in backs:
        width = compute_length(back, axis=Axis.X)
        height = compute_length(back, axis=Axis.Y)
        perim = 2 * (width + height)
        price += (
            perim * price_coefficients['backs_perimeter']
            + price_coefficients['backs_unit']
        )
    return price


def calculate_doors_price(
    doors: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Doors price is based on both width and height."""
    price = Decimal(0)
    for door in doors:
        width = compute_length(door, axis=Axis.X)
        height = compute_length(door, axis=Axis.Y)
        perim = 2 * (width + height)
        door_price = (
            perim * price_coefficients['doors_perimeter']
            + price_coefficients['doors_unit']
        )
        if height > MAX_DOOR_HEIGHT_FOR_TREX:
            door_price *= price_coefficients['raptor_factor']
        price += door_price
    return price


def calculate_jetty_drawers_price(
    drawers: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Drawers price is based on the width and height of their front
    (assume depth==320).
    """
    price = Decimal(0)
    for drawer in drawers:
        width = compute_length(drawer, axis=Axis.X)
        height = compute_length(drawer, axis=Axis.Y)
        front_perim = 2 * (width + height)
        price += (
            front_perim * price_coefficients['drawers_perimeter']
            + price_coefficients['drawers_unit']
        )
    return price


def calculate_long_legs_price(
    long_legs: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Long legs price is constant."""
    return (
        len(long_legs)
        * price_coefficients['long_leg_unit']
        * price_coefficients['raptor_factor']
    )


def calculate_plinth_price(
    plinth: ElementsList,
    jetty_width: int,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Plinth price is based on the Jetty width (assume depth==320)."""
    if plinth:
        width_in_m = Decimal(jetty_width / 1000)
        return (
            width_in_m
            * price_coefficients['plinth_length']
            * price_coefficients['raptor_factor']
        )
    return Decimal(0)


def calculate_inserts_price(
    inserts: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Inserts price is based on their width
    (horizontal inserts, subtype ``h`` or ``t``)
    or height (vertical inserts, subtype ``v``) (assume depth==320).
    """
    price = Decimal(0)
    for insert in inserts:
        if insert['subtype'] in {'h', 't'}:
            length = compute_length(insert, Axis.X)
        else:
            length = compute_length(insert, Axis.Y)
        price += (
            length * price_coefficients['insert_length']
            + price_coefficients['insert_unit']
        )
    return price * price_coefficients['raptor_factor']


def calculate_cable_managements_price(
    cable_managements: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Cable management price is constant."""
    return (
        len(cable_managements)
        * price_coefficients['cable_management_unit']
        * price_coefficients['raptor_factor']
    )


def calculate_bars_price(
    bars: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Bars (Watty element) price is based on their length."""
    regular_bars = [bar for bar in bars if bar.get('subtype', '') != 'z']
    cross_bars = [bar for bar in bars if bar.get('subtype', '') == 'z']
    regular_bar_length = sum_lengths(regular_bars, Axis.X)
    return (
        regular_bar_length * price_coefficients['bar_length']
        + price_coefficients['bar_unit'] * len(regular_bars)
        + price_coefficients['crossbar_unit'] * len(cross_bars)
    )


def calculate_slabs_price(
    slabs: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Slabs (Watty element) price is based on their length."""
    length = sum_lengths(slabs, Axis.X)
    length_price = length * price_coefficients['slab_length']
    unit_price = len(slabs) * price_coefficients['slab_unit']
    return length_price + unit_price


def calculate_watty_drawers_price(
    drawers: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Drawers in Watty have a standard height,
    so their price is based on their width.
    """
    internal_drawers = [drawer for drawer in drawers if drawer['subtype'] in {'i', 'b'}]
    external_drawers = [drawer for drawer in drawers if drawer['subtype'] == 'e']
    return _calculate_drawers_price(
        internal_drawers,
        price_coefficients['watty_drawer_length'],
        price_coefficients['watty_internal_drawer_unit'],
    ) + _calculate_drawers_price(
        external_drawers,
        price_coefficients['watty_drawer_length'],
        price_coefficients['watty_external_drawer_unit'],
    )


def _calculate_drawers_price(
    drawers: ElementsList,
    length_coefficient: Decimal,
    count_coefficient: Decimal,
):
    length = sum_lengths(drawers, Axis.X)
    length_price = length * length_coefficient
    unit_price = count_coefficient * len(drawers)
    return length_price + unit_price


def calculate_walls_price(
    walls: ElementsList,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Two outermost walls are included in the watty's frame (base price)."""
    number_of_inner_walls = len(set(wall['x1'] for wall in walls)) - 2
    return number_of_inner_walls * price_coefficients['wall_unit']


def calculate_extra_height_price(
    height: int,
    width: int,
    price_coefficients: dict[str, Decimal],
) -> Decimal:
    """Only for heights over 2.37m."""
    if height < 2370:
        return Decimal('0')
    if height == 2370:
        return _calculate_extra_height_price(
            height,
            width,
            price_coefficients['watty_additional_height_length'],
            price_coefficients['watty_additional_height_height'],
            price_coefficients['watty_additional_height_unit'],
        )
    return _calculate_extra_height_price(
        height,
        width,
        price_coefficients['watty_height+_length'],
        price_coefficients['watty_height+_height'],
        price_coefficients['watty_height+_unit'],
    )


def _calculate_extra_height_price(
    height: int,
    width: int,
    additional_width_coefficient: Decimal,
    additional_height_coefficient: Decimal,
    additional_height_count_coefficient: Decimal,
):
    width_m = Decimal(width) / 1000
    extra_height_m = Decimal(height - 2000) / 1000
    return (
        width_m * additional_width_coefficient
        + extra_height_m * additional_height_coefficient
        + additional_height_count_coefficient
    )


def get_number_of_modules_with_lighting(lighting):
    return len(set((light['x1'], light['x2']) for light in lighting))


def calculate_lighting_price(
    lighting: ElementsList,
    price_coefficients: dict[str, Decimal],
):
    number_of_modules_with_light = get_number_of_modules_with_lighting(lighting)
    modules_price = number_of_modules_with_light * price_coefficients['light_module']
    length = sum_lengths(lighting, Axis.X)
    length_price = length * price_coefficients['light_length']
    unit_price = len(lighting) * price_coefficients['light_unit']
    return modules_price + length_price + unit_price


class Type13ElementsPriceCalculator:
    def __init__(self, watty: 'Watty', price_coefficients: dict) -> None:
        self.watty = watty
        self._pc = price_coefficients

    @property
    def is_made_from_plywood(self) -> bool:
        """We have no info about actual material in watty,
        that's why we use color to differentiate plywood from chipboard.
        """
        return self.watty.color.is_plywood_color

    def calculate_walls_price(self) -> Decimal:
        walls = self.watty.walls
        if not walls:
            return Decimal()
        height_m = Decimal(self.watty.height) / 1000
        depth_m = Decimal(self.watty.depth) / 1000
        area_coefficient = (
            self._pc['type13_t01_wall_area']
            if self.is_made_from_plywood
            else self._pc['type13_t02_wall_area']
        )
        return (
            len(walls) * self._pc['type13_wall_unit']
            + height_m * depth_m * len(walls) * area_coefficient
        )

    def calculate_slabs_price(self) -> Decimal:
        slabs = self.watty.slabs
        if not slabs:
            return Decimal()
        slab_length = sum_lengths(slabs, Axis.X)
        depth = Decimal(self.watty.depth) / 1000
        return (
            len(slabs) * self._pc['type13_slab_unit']
            + slab_length * depth * self._pc['type13_slab_area']
        )

    def calculate_doors_price(self) -> Decimal:
        doors = self.watty.doors
        if not doors:
            return Decimal()
        sum_width = sum_lengths(doors, Axis.X)
        sum_height = sum_lengths(doors, Axis.Y)
        return (
            len(doors) * self._pc['type13_door_unit']
            + (sum_width + sum_height) * 2 * self._pc['type13_door_perimeter']
        )

    def calculate_backs_price(self) -> Decimal:
        backs = self.watty.backs
        if not backs:
            return Decimal()
        width = Decimal(self.watty.width) / 1000
        height = Decimal(self.watty.height) / 1000
        return (
            len(backs) * self._pc['type13_backs_unit']
            + width * height * self._pc['type13_backs_area']
        )

    def calculate_bars_price(self) -> Decimal:
        regular_bars = [bar for bar in self.watty.bars if bar['subtype'] != 'z']
        cross_bars = [bar for bar in self.watty.bars if bar['subtype'] == 'z']
        if regular_bars or cross_bars:
            return (
                len(regular_bars) * self._pc['type13_bar_unit']
                + len(cross_bars) * self._pc['type13_crossbar_unit']
                + sum_lengths(regular_bars, Axis.X) * self._pc['type13_bar_length']
            )
        return Decimal()

    def calculate_drawers_price(self) -> Decimal:
        # note: internal in context of t13 means behind doors
        drawers = self.watty.drawers
        internal_drawers = [drawer for drawer in drawers if drawer['subtype'] == 'd']
        external_drawers = [
            drawer for drawer in drawers if drawer['subtype'] in {'i', 'b'}
        ]
        internal_drawers_length = sum_lengths(internal_drawers, Axis.X)
        external_drawers_length = sum_lengths(external_drawers, Axis.X)

        internal_drawers_price = (
            len(internal_drawers) * self._pc['type13_internal_drawer_unit']
            + internal_drawers_length * self._pc['type13_drawer_width']
        )

        external_drawers_price = (
            len(external_drawers) * self._pc['type13_external_drawer_unit']
            + external_drawers_length * self._pc['type13_drawer_width']
        )

        return internal_drawers_price + external_drawers_price
