from dataclasses import (
    dataclass,
    fields,
)
from datetime import datetime
from decimal import (
    ROUND_HALF_UP,
    Decimal,
)
from typing import (
    TYPE_CHECKING,
    Generic,
    Optional,
    TypeVar,
    Union,
)

from regions.mixins import RegionCalculationsObject

if TYPE_CHECKING:
    from carts.models import (
        Cart,
        CartItem,
    )
    from orders.models import (
        Order,
        OrderItem,
    )
    from vouchers.models import Voucher


TInstance = TypeVar('TInstance', bound=Union['CartItem', 'OrderItem'])
TInstanceParent = TypeVar('TInstanceParent', bound=Union['Cart', 'Order'])


@dataclass
class ItemPriceCalculatedValues:
    region_price: Decimal  # how much customer will pay
    region_price_net: Decimal
    region_vat_amount: Decimal
    region_assembly_price: Decimal
    region_delivery_price: Decimal

    price: Decimal  # how much company will earn in EUR
    price_net: Decimal
    vat_amount: Decimal
    assembly_price: Decimal
    delivery_price: Decimal


class ItemPriceCalculatorBase(Generic[TInstance, TInstanceParent]):
    parent_field_lookup: str

    def __init__(
        self,
        instance: TInstance,
        rco: Optional['RegionCalculationsObject'] = None,
    ) -> None:
        self.instance = instance
        self.parent: TInstanceParent = getattr(
            instance,
            self.parent_field_lookup,
        )
        self.region = self.parent.get_region(dont_use_cache=True)
        self.country = self.region.get_country()

        self.rco = rco or RegionCalculationsObject(region=self.region)

    def calculate(
        self,
        vat_rate: Decimal = Decimal('0.23'),
        with_save: bool = True,
        for_datetime: Optional[datetime] = None,
    ) -> TInstance:
        calculated_values = self._calculate_values(vat_rate, for_datetime)
        self._set_values(calculated_values, with_save)
        return self.instance

    def _calculate_values(
        self,
        vat_rate: Decimal,
        for_datetime: Optional[datetime],
    ) -> ItemPriceCalculatedValues:

        # main price components
        base_price = self._get_base_price(for_datetime)
        region_price = self._get_region_price(base_price)
        income_price = self._get_income_price(region_price)

        region_price_net = self._get_net_price(region_price, vat_rate)
        income_price_net = self._get_net_price(income_price, vat_rate)

        region_vat_amount = self._get_vat_amount(region_price, region_price_net)
        income_vat_amount = self._get_vat_amount(income_price, income_price_net)

        if self.parent.is_vat_exempt:
            region_vat_amount = Decimal('0')
            income_vat_amount = Decimal('0')

            if self.parent.is_from_non_eu_country:
                region_price_net = region_price
                income_price = self._round_up_to_integer(income_price)
                income_price_net = income_price
            else:
                region_price = region_price_net
                income_price_net = self._round_up_to_integer(income_price_net)
                income_price = income_price_net

        # additional price components
        delivery_price = self._get_delivery_price(vat_rate)
        region_delivery_price = self._get_region_delivery_price(delivery_price)
        income_delivery_price = self._get_income_delivery_price(region_delivery_price)
        base_assembly_price = self._get_assembly_price(vat_rate, for_datetime)
        region_assembly_price = self._get_region_assembly_price(base_assembly_price)
        income_assembly_price = self._get_income_assembly_price(region_assembly_price)

        return ItemPriceCalculatedValues(
            price=income_price,
            price_net=income_price_net,
            vat_amount=income_vat_amount,
            region_price=region_price,
            region_price_net=region_price_net,
            region_vat_amount=region_vat_amount,
            assembly_price=income_assembly_price,
            region_assembly_price=region_assembly_price,
            delivery_price=income_delivery_price,
            region_delivery_price=region_delivery_price,
        )

    def _set_values(
        self,
        calculated_values: ItemPriceCalculatedValues,
        with_save: bool,
    ) -> None:
        self.instance.price = calculated_values.price
        self.instance.price_net = calculated_values.price_net
        self.instance.region_price = calculated_values.region_price
        self.instance.region_price_net = calculated_values.region_price_net
        self.instance.assembly_price = calculated_values.assembly_price
        self.instance.region_assembly_price = calculated_values.region_assembly_price
        self.instance.delivery_price = calculated_values.delivery_price
        self.instance.region_delivery_price = calculated_values.region_delivery_price
        self.instance.vat_amount = calculated_values.vat_amount
        self.instance.region_vat_amount = calculated_values.region_vat_amount

        if with_save:
            self.instance.save(
                update_fields=[
                    field.name for field in fields(ItemPriceCalculatedValues)
                ]
            )

    def _get_assembly_price(
        self,
        vat_rate: Decimal,
        for_datetime: Optional[datetime] = None,
    ) -> Decimal:
        if self.instance.is_service:
            return Decimal('0')

        assembly_price = self.instance.assembly_price
        if not self.instance.free_assembly_service:
            assembly_price = self.instance.sellable_item.get_assembly_price(
                region=self.region,
                for_datetime=for_datetime,
            )
        if self.parent.is_vat_exempt and not self.parent.is_from_non_eu_country:
            assembly_price = self._get_net_price(assembly_price, vat_rate)

        return assembly_price

    def _get_region_assembly_price(self, assembly_price: Decimal) -> Decimal:
        return self.rco.calculate_regionalized(assembly_price)

    def _get_income_assembly_price(self, region_assembly_price: Decimal) -> Decimal:
        return self.rco.calculate_base(region_assembly_price) * self.rco.region_rate

    def _get_delivery_price(self, vat_rate: Decimal) -> Decimal:
        delivery_price = self.instance.sellable_item.get_delivery_price(
            region=self.region,
        )
        if self.parent.is_vat_exempt and not self.parent.is_from_non_eu_country:
            delivery_price = self._get_net_price(delivery_price, vat_rate)

        return delivery_price

    def _get_region_delivery_price(self, delivery_price: Decimal) -> Decimal:
        return self.rco.calculate_regionalized(delivery_price)

    def _get_income_delivery_price(self, region_delivery_price: Decimal) -> Decimal:
        return self.rco.calculate_base(region_delivery_price) * self.rco.region_rate

    def _get_base_price(self, for_datetime: datetime) -> Decimal:
        """Returns shelf price from pricing in euro with polish vat added (so gross)."""
        if self.instance.is_service:
            return self.instance.sellable_item.price

        return self.instance.sellable_item.get_shelf_price_as_number(
            region=self.region,
            for_datetime=for_datetime,
        )

    def _get_region_price(self, base_price: Decimal) -> Decimal:
        return self._round_up_to_integer(self.rco.calculate_regionalized(base_price))

    def _get_income_price(self, region_price: Decimal) -> Decimal:
        return self.rco.calculate_base(region_price) * self.rco.region_rate

    def _get_net_price(self, gross_price: Decimal, vat_rate: Decimal) -> Decimal:
        net_price = gross_price / (1 + vat_rate)
        return self._round_up_to_hundredth(net_price)

    def _get_vat_amount(self, gross_price: Decimal, net_price: Decimal) -> Decimal:
        vat_amount = gross_price - net_price
        return self._round_up_to_hundredth(vat_amount)

    @staticmethod
    def _round_up_to_hundredth(value: Decimal) -> Decimal:
        return Decimal(value).quantize(Decimal('.01'), rounding=ROUND_HALF_UP)

    @staticmethod
    def _round_up_to_integer(value: Decimal) -> Decimal:
        return Decimal(value).quantize(Decimal('1'), rounding=ROUND_HALF_UP)

    def calculate_absolute_promo_price(self, rate: Decimal) -> None:
        self.instance.region_promo_value = self._round_up_to_hundredth(
            value=self.instance.region_price * rate
        )
        # Because absolute promos do not apply to delivery We need to reset this field
        # In case a global promo was applied before the absolute promo on the cart.
        self.instance.region_delivery_promo_value = 0

    def calculate_percentage_promo_price(self, voucher: 'Voucher') -> None:
        # delivery promo fields
        region_delivery_price_with_discount = self._round_up_to_integer(
            voucher.get_delivery_price_with_discount(
                self.instance.region_delivery_price
            )
        )
        region_delivery_promo_value = (
            self.instance.region_delivery_price - region_delivery_price_with_discount
        )
        self.instance.region_delivery_promo_value = region_delivery_promo_value
        # region_promo_value
        region_price_after_voucher = voucher.calculate_price_for_furniture(
            self.instance.sellable_item, self.instance.region_price
        )
        region_promo_value = self._round_up_to_hundredth(
            value=(self.instance.region_price - region_price_after_voucher),
        )
        self.instance.region_promo_value = region_promo_value


class OrderItemPriceCalculator(ItemPriceCalculatorBase['OrderItem', 'Order']):
    parent_field_lookup = 'order'


class CartItemPriceCalculator(ItemPriceCalculatorBase['CartItem', 'Cart']):
    parent_field_lookup = 'cart'
