from unittest.mock import patch

import pytest

from pricing_v3.models import SamplePriceSettings


@pytest.mark.django_db
class TestPriceAbstractModel:
    @pytest.mark.parametrize('factory_name', ['order_factory', 'cart_factory'])
    def test_get_base_total_value_returns_zero_when_total_price_is_none(
        self,
        factory_name,
        request,
    ):
        model = request.getfixturevalue(factory_name)()
        model.total_price = None
        assert model.get_base_total_value() == 0

    @pytest.mark.parametrize('factory_name', ['order_factory', 'cart_factory'])
    def test_get_base_total_price_net_returns_zero_when_total_price_is_none(
        self,
        factory_name,
        request,
    ):
        model = request.getfixturevalue(factory_name)()
        model.total_price_net = None
        assert model.get_base_total_value_net() == 0

    @pytest.mark.parametrize('factory_name', ['order_factory', 'cart_factory'])
    def test_get_total_value_returns_region_total_price(
        self,
        factory_name,
        request,
    ):
        model = request.getfixturevalue(factory_name)()
        model.region_total_price = 100
        assert model.get_total_value() == 100

    @pytest.mark.parametrize('factory_name', ['order_factory', 'cart_factory'])
    def test_get_total_value_returns_base_total_value_if_not_region_total_price(
        self,
        factory_name,
        request,
    ):
        model = request.getfixturevalue(factory_name)()
        model.total_price = 100
        model.region_total_price = None
        base_total_value = model.get_base_total_value()
        assert model.get_total_value() == base_total_value

    @pytest.mark.parametrize('factory_name', ['order_factory', 'cart_factory'])
    def test_get_total_value_net_returns_region_total_price(
        self,
        factory_name,
        request,
    ):
        model = request.getfixturevalue(factory_name)()
        model.region_total_price_net = 100
        assert model.get_total_value_net() == 100

    @pytest.mark.parametrize('factory_name', ['order_factory', 'cart_factory'])
    def test_get_total_value_net_returns_base_total_value_if_not_region_total_price(
        self,
        factory_name,
        request,
    ):
        model = request.getfixturevalue(factory_name)()
        model.total_price_net = 100
        model.region_total_price_net = None
        base_total_value_net = model.get_base_total_value_net()
        assert model.get_total_value_net() == base_total_value_net

    @pytest.mark.parametrize('factory_name', ['order_factory', 'cart_factory'])
    def test_get_base_vat_value(
        self,
        factory_name,
        request,
    ):
        model = request.getfixturevalue(factory_name)()
        model.total_price = 100
        model.total_price_net = 60
        assert model.get_base_vat_value() == 40

    @pytest.mark.parametrize('factory_name', ['order_factory', 'cart_factory'])
    def test_get_vat_value(
        self,
        factory_name,
        request,
    ):
        model = request.getfixturevalue(factory_name)()
        model.region_total_price = 100
        model.region_total_price_net = 60
        assert model.get_vat_value() == 40

    @pytest.mark.parametrize('factory_name', ['order_factory', 'cart_factory'])
    def test_get_promo_amount_returns_zero_when_total_price_is_none(
        self,
        factory_name,
        request,
    ):
        model = request.getfixturevalue(factory_name)()
        model.promo_amount = None
        assert model.get_base_promo_amount() == 0

    @pytest.mark.parametrize('factory_name', ['order_factory', 'cart_factory'])
    def test_get_base_promo_amount_net_returns_zero_when_total_price_is_none(
        self,
        factory_name,
        request,
    ):
        model = request.getfixturevalue(factory_name)()
        model.promo_amount_net = None
        assert model.get_base_promo_amount_net() == 0

    @pytest.mark.parametrize('factory_name', ['order_factory', 'cart_factory'])
    def test_clear_promo_amounts(
        self,
        factory_name,
        request,
    ):
        model = request.getfixturevalue(factory_name)()
        model.promo_amount = 30
        model.promo_amount_net = 20
        model.region_promo_amount = 50
        model.region_promo_amount_net = 40

        model.clear_promo_amounts()

        assert model.promo_amount == 0
        assert model.promo_amount_net == 0
        assert model.region_promo_amount == 0
        assert model.region_promo_amount_net == 0

    @pytest.mark.parametrize('factory_name', ['order_factory', 'cart_factory'])
    def test_get_total_price_number_before_discount_when_no_items(
        self,
        factory_name,
        request,
    ):
        model = request.getfixturevalue(factory_name)()
        model.items.all().delete()
        model.save()
        model.refresh_from_db()
        assert model.get_total_price_number_before_discount() == 0

    @pytest.mark.parametrize('factory_name', ['order_factory', 'cart_factory'])
    def test_get_total_price_number_before_discount_no_assembly(
        self,
        factory_name,
        request,
    ):
        model = request.getfixturevalue(factory_name)()
        model.total_price = 100
        model.region_promo_amount = 50
        model.region_total_price = 100

        assert model.get_total_price_number_before_discount() == 150

    @pytest.mark.parametrize('factory_name', ['order_factory', 'cart_factory'])
    def test_get_total_price_number_before_discount_with_assembly(
        self,
        factory_name,
        request,
    ):
        model = request.getfixturevalue(factory_name)(
            total_price=100,
            region_promo_amount=50,
            region_total_price=100,
            assembly=True,
            items__assembly_price=10,
        )

        assert model.get_total_price_number_before_discount() == 130

    @pytest.mark.parametrize('factory_name', ['order_factory', 'cart_factory'])
    def test_get_total_price_number_before_discount(
        self,
        factory_name,
        request,
    ):
        model = request.getfixturevalue(factory_name)()
        model.total_price = 100
        model.region_promo_amount = 50
        model.region_total_price = 100

        assert model.get_total_price_number_before_discount() == 150

    @pytest.mark.parametrize('factory_name', ['order_factory', 'cart_factory'])
    def test_get_total_price_number(
        self,
        factory_name,
        request,
    ):
        model = request.getfixturevalue(factory_name)()
        model.total_price = 10
        model.region_total_price = 100

        assert model.get_total_price_number() == 100

    @pytest.mark.parametrize('factory_name', ['order_factory', 'cart_factory'])
    @patch('pricing_v3.services.price_calculators.OrderPriceCalculator.calculate')
    @patch('pricing_v3.services.price_calculators.CartPriceCalculator.calculate')
    def test_get_total_price_number_calls_price_calculator_if_total_price_is_none(
        self,
        cart_price_calculator_mock,
        order_price_calculator_mock,
        factory_name,
        request,
    ):
        model = request.getfixturevalue(factory_name)()
        model.total_price = None

        model.get_total_price_number()

        if factory_name == 'order_factory':
            assert order_price_calculator_mock.called
        else:
            assert cart_price_calculator_mock.called

    @pytest.mark.parametrize('factory_name', ['order_factory', 'cart_factory'])
    def test_get_assembly_price(
        self,
        factory_name,
        request,
    ):
        model = request.getfixturevalue(factory_name)()
        item_1, item_2 = model.items.all()

        item_1.region_assembly_price = 10
        item_1.free_assembly_service = False
        item_1.with_assembly = True
        item_1.save()

        item_2.region_assembly_price = 10
        item_2.free_assembly_service = True
        item_2.with_assembly = True
        item_2.save()

        assert model.get_assembly_price() == 10


@pytest.mark.django_db
class TestItemPriceAbstractModel:
    @pytest.mark.parametrize(
        'factory_name',
        ['order_item_factory', 'cart_item_factory'],
    )
    def test_get_price_number_returns_price_when_region_price_is_zero(
        self,
        factory_name,
        request,
    ):
        model = request.getfixturevalue(factory_name)()
        model.region_price = 0
        model.price = 10
        assert model.get_price_number() == 10

    @pytest.mark.parametrize(
        'factory_name',
        ['order_item_factory', 'cart_item_factory'],
    )
    def test_get_price_net_number_returns_price_net_when_region_price_net_is_zero(
        self,
        factory_name,
        request,
    ):
        model = request.getfixturevalue(factory_name)()
        model.region_price_net = 0
        model.price_net = 10
        assert model.get_price_net_number() == 10

    @pytest.mark.parametrize(
        'factory_name',
        ['order_item_factory', 'cart_item_factory'],
    )
    def test_get_delivery_price_returns_delivery_price_when_region_delivery_price_is_zero(
        self,
        factory_name,
        request,
    ):
        model = request.getfixturevalue(factory_name)()
        model.region_delivery_price = 0
        model.delivery_price = 10
        assert model.get_delivery_price() == 10

    @pytest.mark.parametrize(
        'factory_name',
        ['order_item_factory', 'cart_item_factory'],
    )
    def test_get_assembly_price_returns_assembly_price_when_region_assembly_price_is_zero(
        self,
        factory_name,
        request,
    ):
        model = request.getfixturevalue(factory_name)()
        model.region_assembly_price = 0
        model.assembly_price = 10
        assert model.get_assembly_price() == 10

    @pytest.mark.parametrize(
        'factory_name',
        ['order_item_factory', 'cart_item_factory'],
    )
    def test_get_vat_amount_returns_vat_amount_when_region_vat_amount_is_zero(
        self,
        factory_name,
        request,
    ):
        model = request.getfixturevalue(factory_name)()
        model.region_vat_amount = 0
        model.vat_amount = 10
        assert model.get_vat_amount() == 10


@pytest.mark.django_db
class TestSamplePriceSettings:
    @pytest.fixture(autouse=True)
    def setup_test(self):
        SamplePriceSettings.objects.all().delete()

    def test_get_price_returns_sale_price_when_promo_active(self):
        """Test that _get_price returns sale price when promo is active"""
        SamplePriceSettings.objects.create(
            storage_sample_price=3,
            storage_sample_sale_price=2,
            storage_sample_promo_active=True,
        )

        price = SamplePriceSettings._get_price(
            'storage_sample_price',
            'storage_sample_sale_price',
            'storage_sample_promo_active',
        )

        assert price == 2  # Sale price

    def test_get_price_returns_base_price_when_promo_inactive(self):
        """Test that _get_price returns base price when promo is inactive"""
        SamplePriceSettings.objects.create(
            storage_sample_price=3,
            storage_sample_sale_price=2,
            storage_sample_promo_active=False,
        )

        price = SamplePriceSettings._get_price(
            'storage_sample_price',
            'storage_sample_sale_price',
            'storage_sample_promo_active',
        )

        assert price == 3  # Base price

    # Wooden sample tests
    def test_get_storage_sample_price_in_euro_with_promo_active(self):
        """Test get_storage_sample_price_in_euro returns sale price when promo is active"""
        SamplePriceSettings.objects.create(
            storage_sample_price=3,
            storage_sample_sale_price=2,
            storage_sample_promo_active=True,
        )

        price = SamplePriceSettings.get_storage_sample_price_in_euro()
        assert price == 2  # Sale price

    def test_get_storage_sample_price_in_euro_with_promo_inactive(self):
        """Test get_storage_sample_price_in_euro returns base price when promo is inactive"""
        SamplePriceSettings.objects.create(
            storage_sample_price=3,
            storage_sample_sale_price=2,
            storage_sample_promo_active=False,
        )

        price = SamplePriceSettings.get_storage_sample_price_in_euro()
        assert price == 3  # Base price

    def test_get_storage_sample_base_price(self):
        """Test get_storage_sample_base_price returns the base price"""
        SamplePriceSettings.objects.create(
            storage_sample_price=3,
            storage_sample_sale_price=2,
        )

        price = SamplePriceSettings.get_storage_sample_base_price()
        assert price == 3

    def test_get_storage_sample_sale_price(self):
        """Test get_storage_sample_sale_price returns the sale price"""
        SamplePriceSettings.objects.create(
            storage_sample_price=3,
            storage_sample_sale_price=2,
        )

        price = SamplePriceSettings.get_storage_sample_sale_price()
        assert price == 2

    def test_is_storage_sample_promo_active_returns_true(self):
        """Test is_storage_sample_promo_active returns True when promo is active"""
        SamplePriceSettings.objects.create(
            storage_sample_promo_active=True,
        )

        is_active = SamplePriceSettings.is_storage_sample_promo_active()
        assert is_active is True

    def test_is_storage_sample_promo_active_returns_false(self):
        """Test is_storage_sample_promo_active returns False when promo is inactive"""
        SamplePriceSettings.objects.create(
            storage_sample_promo_active=False,
        )

        is_active = SamplePriceSettings.is_storage_sample_promo_active()
        assert is_active is False

    # Sofa sample tests
    def test_get_sofa_sample_price_in_euro_with_promo_active(self):
        """Test get_sofa_sample_price_in_euro returns sale price when promo is active"""
        SamplePriceSettings.objects.create(
            sofa_sample_price=2,
            sofa_sample_sale_price=1,
            sofa_sample_promo_active=True,
        )

        price = SamplePriceSettings.get_sofa_sample_price_in_euro()
        assert price == 1  # Sale price

    def test_get_sofa_sample_price_in_euro_with_promo_inactive(self):
        """Test get_sofa_sample_price_in_euro returns base price when promo is inactive"""
        SamplePriceSettings.objects.create(
            sofa_sample_price=2,
            sofa_sample_sale_price=1,
            sofa_sample_promo_active=False,
        )

        price = SamplePriceSettings.get_sofa_sample_price_in_euro()
        assert price == 2  # Base price

    def test_get_sofa_sample_base_price(self):
        """Test get_sofa_sample_base_price returns the base price"""
        SamplePriceSettings.objects.create(
            sofa_sample_price=2,
        )

        price = SamplePriceSettings.get_sofa_sample_base_price()
        assert price == 2

    def test_get_sofa_sample_sale_price(self):
        """Test get_sofa_sample_sale_price returns the sale price"""
        SamplePriceSettings.objects.create(
            sofa_sample_sale_price=1,
        )

        price = SamplePriceSettings.get_sofa_sample_sale_price()
        assert price == 1

    def test_is_sofa_sample_promo_active_returns_true(self):
        """Test is_sofa_sample_promo_active returns True when promo is active"""
        SamplePriceSettings.objects.create(
            sofa_sample_promo_active=True,
        )

        is_active = SamplePriceSettings.is_sofa_sample_promo_active()
        assert is_active is True

    def test_is_sofa_sample_promo_active_returns_false(self):
        """Test is_sofa_sample_promo_active returns False when promo is inactive"""
        SamplePriceSettings.objects.create(
            sofa_sample_promo_active=False,
        )

        is_active = SamplePriceSettings.is_sofa_sample_promo_active()
        assert is_active is False

    def test_save_calls_clear_related_cache(self):
        """Test save method calls clear_related_cache"""
        with patch(
            'pricing_v3.models.models.SamplePriceSettings.clear_related_cache'
        ) as mock_clear_cache:
            SamplePriceSettings.objects.create()
            mock_clear_cache.assert_called_once()
