<svg baseProfile="full" height="2100" version="1.1" width="2970"
     xmlns="http://www.w3.org/2000/svg" xmlns:ev="http://www.w3.org/2001/xml-events"
     xmlns:xlink="http://www.w3.org/1999/xlink">
    <defs/>
    <g class="default" id="0">
        <rect fill="white" fill-opacity="1.0" height="2100" width="2970" x="0" y="0"/>
    </g>
    <g class="default" id="1">
        <text alignment-baseline="baseline" fill="grey" font-size="30" font-family="Messina Sans"
              text-anchor="middle" x="2905" y="2035">{{ svg_data['version'] }}
        </text>
        <text alignment-baseline="middle" fill="black" font-size="35" font-family="Messina Sans"
              x="2025" y="1975">{{ svg_data['url'] }}
        </text>
    </g>
    <g class="default" id="qrcode">
        {%- for rect in svg_data['qrcode'] -%}
        <rect fill="{{ rect['fill'] }}" fill-opacity="1.0" height="10" width="10" x="{{ 2020 + rect['x'] }}" y="{{ 1445 + rect['y'] }}"/>
        {%- endfor -%}
    </g>
</svg>
