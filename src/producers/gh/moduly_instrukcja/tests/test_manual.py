import json

from importlib import resources

import pytest

from producers.gh.moduly_glowne.ivy_production import IvyProduction
from producers.gh.moduly_instrukcja.Ivy_instrukcja import Instrukcja
from producers.gh.moduly_instrukcja.Ivy_pages import PageAssembly
from producers.gh.moduly_instrukcja.manual_manager import VerticalSolo

TEST_ID = 37567


def load_data_from_json(json_name, json_folder_name='.'):
    with resources.open_text(json_folder_name, json_name) as file_input:
        json_data = json.load(file_input)
    return json_data


@pytest.fixture(params=range(27))  # FIXME somehow get this number from input?
def setup_test_data(request):
    manual_expected_json = load_data_from_json(
        f'item_{TEST_ID}_{request.param}.json',
        'expected',
    )
    with resources.open_text(
        'expected',
        f'item_{TEST_ID}_{request.param}.svg',
    ) as file_input:
        manual_expected_svg = file_input.readlines()

    return {
        'json': manual_expected_json,
        'svg': manual_expected_svg,
        'index': request.param,
    }


@pytest.fixture
def get_ivy_production():
    production_data = load_data_from_json(
        f'item_{TEST_ID}.json',
        'sample_files',
    )

    return IvyProduction(production_data)


@pytest.mark.parametrize('product_id', [37705, 37567, 37655])
def test_manual_assembly_steps(product_id):
    production_data = load_data_from_json(
        f'item_{product_id}.json',
        'sample_files',
    )

    ivy_production = IvyProduction(production_data)
    assembly_types = PageAssembly().get_assembly_types(
        ivy_production,
        shelf_module=0,
    )
    assembly_types_expected = {
        37655: [['H'], ['V'], ['H'], ['B']],
        37567: [['H', 'V'], ['H'], ['B', 'S'], ['V'], ['H'], ['B']],
        37705: [['H', 'V'], ['H'], ['B'], ['V'], ['H'], ['B']],
    }
    assert assembly_types == assembly_types_expected[product_id], product_id


@pytest.mark.parametrize('product_id', [37705, 37655, 37655])
def test_manual_shelf_flow_data(product_id):
    production_data = load_data_from_json(
        f'item_{product_id}.json',
        'sample_files',
    )

    ivy_production = IvyProduction(production_data)
    manual_shelf_flow = Instrukcja(
        ivy_production,
        user_name='user_name',
    ).get_shelf_flow_data()

    shelf_flow_expected = {
        37567: {
            'has_doors': True,
            'has_drawers': True,
            'has_inserts': False,
            'has_hafele_hinges': False,
            'module_zero': [],
            'modules': [1, 2],
            'modules_amount': 2,
            'rows_type': 1,
            'shelf_type': 0,
            'fixing': 4,
            'fixing_raptor': True,
            'long_legs_modules': set(),
            'has_plinth': False,
            'assembly_style': 1,
            'has_high_doors': True,
            'is_desk': False,
            'desk_solo_vertical': VerticalSolo.DOES_NOT_EXIST,
            'is_charlie': False,
        },
        37655: {
            'has_doors': True,
            'has_drawers': False,
            'has_inserts': True,
            'has_hafele_hinges': False,
            'module_zero': [
                'B',
                'B',
                'L',
                'L',
                'L',
                'L',
            ],
            'modules': [1, 2, 3],
            'modules_amount': 3,
            'rows_type': 1,
            'shelf_type': 1,
            'fixing': 0,
            'fixing_raptor': True,
            'long_legs_modules': {0, 1, 2, 3},
            'has_plinth': False,
            'assembly_style': 1,
            'has_high_doors': True,
            'is_desk': False,
            'desk_solo_vertical': VerticalSolo.DOES_NOT_EXIST,
            'is_charlie': False,
        },
        37705: {
            'has_doors': True,
            'has_drawers': True,
            'has_inserts': True,
            'has_hafele_hinges': False,
            'module_zero': [
                'B',
                'B',
                'L',
                'L',
                'L',
                'L',
                'L',
                'L',
                'L',
                'L',
                'L',
                'L',
                'Px',
            ],
            'modules': [1, 2],
            'modules_amount': 2,
            'rows_type': 1,
            'shelf_type': 0,
            'fixing': 0,
            'fixing_raptor': True,
            'long_legs_modules': set(),
            'has_plinth': True,
            'assembly_style': 1,
            'has_high_doors': True,
            'is_desk': False,
            'desk_solo_vertical': VerticalSolo.DOES_NOT_EXIST,
            'is_charlie': False,
        },
    }

    assert manual_shelf_flow._asdict() == shelf_flow_expected[product_id]
