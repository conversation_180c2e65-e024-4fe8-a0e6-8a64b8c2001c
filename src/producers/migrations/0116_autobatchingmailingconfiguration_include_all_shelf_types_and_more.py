# Generated by Django 4.1.13 on 2025-02-11 13:16

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('producers', '0115_productdetailsjetty_production_drawings'),
    ]

    operations = [
        migrations.AddField(
            model_name='autobatchingmailingconfiguration',
            name='include_all_shelf_types',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='autobatchingmailingconfiguration',
            name='mail_subject',
            field=models.CharField(
                blank=True,
                default='',
                help_text=(
                    'Include all key information here - ',
                    'this will override the auto-generated subject.',
                ),
                max_length=255,
            ),
        ),
        migrations.AlterField(
            model_name='autobatchingmailingconfiguration',
            name='shelf_type',
            field=models.PositiveSmallIntegerField(
                blank=True,
                choices=[
                    (0, 'TYPE01'),
                    (1, 'TYPE02'),
                    (2, 'VENEER TYPE01'),
                    (3, 'TYPE03'),
                    (4, 'TYPE13'),
                    (5, 'VENEER TYPE13'),
                    (6, 'TYPE23'),
                    (7, 'TYPE24'),
                    (8, 'TYPE25'),
                    (10, 'SOFA TYPE01'),
                ],
                default=None,
                null=True,
            ),
        ),
    ]
