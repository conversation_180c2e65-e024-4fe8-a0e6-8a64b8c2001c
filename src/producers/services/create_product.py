import logging

from custom.enums import Furniture
from orders.utils import add_order_context
from producers.errors import SerializationError
from producers.internal_api.events import (
    ProductCreatedEvent,
    ProductDetailCreatedEvent,
)
from producers.models import Product
from producers.models_split.product_details import (
    ProductDetailsJetty,
    ProductDetailsSotty,
    ProductDetailsWatty,
)

logger = logging.getLogger('producers')


class CreateProductFromOrderItem:
    def __init__(self, order_item):
        self.order_item = order_item
        self.order = order_item.order

    def create(self, logistic_order_id):
        product = Product.objects.create(
            notes=self.order.order_notes,
            order=self.order,
            order_item=self.order_item,
            logistic_order=logistic_order_id,
            order_item_serialized=self.order_item.serialize_furniture(),
            cached_product_type=self.order_item.content_type.model,
        )
        product.set_priority_from_order()

        product.serialization_updater.update_product_serialization()
        if product.order_item.content_type.model == Furniture.jetty.value:
            self.create_product_details_jetty(product)
        elif product.order_item.content_type.model == Furniture.watty.value:
            self.create_product_details_watty(product)
        elif product.order_item.content_type.model == Furniture.sotty.value:
            self.create_product_details_sotty(product)
        ProductCreatedEvent(product)

    def create_product_details_watty(self, product):
        try:
            serialization = (
                product.get_serialization()
            )  # new place for first serialization
            if not serialization.item:
                raise SerializationError('Serialization failed with no items')

            pdw, created = ProductDetailsWatty.objects.get_or_create(product=product)
            pdw.generate_connections_zip()
            pdw.generate_front_view()
            pdw.generate_packaging_instruction()
            pdw.generate_production_drawings()
            if created:
                pdw.save()
                ProductDetailCreatedEvent(pdw.product)
        except Exception:
            logger.exception(
                add_order_context(
                    'Error while getting serizaliation for watty',
                    order=self.order,
                ),
                extra={'order_id': self.order.id},
            )

    def create_product_details_sotty(self, product):
        try:
            serialization = (
                product.get_serialization()
            )  # new place for first serialization
            if not serialization.item:
                raise SerializationError('Serialization failed with no items')

            pdw, created = ProductDetailsSotty.objects.get_or_create(product=product)
            pdw.generate_front_view()
            pdw.generate_labels_packaging()
            if created:
                pdw.save()
                ProductDetailCreatedEvent(pdw.product)
        except Exception:
            logger.exception(
                add_order_context(
                    'Error while getting serizaliation for watty',
                    order=self.order,
                ),
                extra={'order_id': self.order.id},
            )

    def create_product_details_jetty(self, product):

        try:
            serialization = (
                product.get_jetty_serialized()
            )  # new place for first serialization
            if not serialization.item:
                raise SerializationError('Serialization failed with no items')

            pdj, created = ProductDetailsJetty.objects.get_or_create(product=product)
            pdj.generate_front_view()
            pdj.generate_packaging_instruction()
            pdj.generate_connections_zip()
            pdj.generate_connections_dxf()
            pdj.generate_horizontals_pdf()
            pdj.generate_production_drawings()
            if created:
                pdj.save()
                ProductDetailCreatedEvent(pdj.product)
        except Exception:
            logger.exception(
                add_order_context(
                    'Error while process_to_production',
                    order=self.order,
                ),
                extra={'order_id': self.order.id},
            )
