/*Style opisujace elementry graficzne do eksportu do csv*/

.default {
  fill: None;
  stroke: black;
  stroke-width: 1;
  font-family: arial;
}
.default_txt {
  fill: black;
  stroke: None;
  stroke-width: 1;
  font-family: arial;
}
.example {
  fill: red;
  stroke: red;
  stroke-width: 1;
  font-size: 30px;
  font-family: arial;
}
/* Dla rysunku frotu */
.front_horizontalsVerticals {
  fill: None;
  stroke: black;
  stroke-width: 1;
  font-family: arial;
}
.front_horizontals {
  fill: None;
  stroke: black;
  stroke-width: 1;
  font-family: arial;
}
.front_Verticals {
  fill: None;
  stroke: black;
  stroke-width: 1;
  font-family: arial;
}
.front_doors {
  fill: None;
  stroke: black;
  stroke-width: 1;
  font-family: arial;
  stroke-dasharray: 10;
}
.front_drawers {
  fill: None;
  stroke: black;
  stroke-width: 1;
  font-family: arial;
  stroke-dasharray: 10;
}
.front_backs {
  fill: grey;
  fill-opacity: 0.1;
  stroke: black;
  stroke-width: 1.2;
  font-family: arial;
}
.front_supports {
  fill: grey;
  fill-opacity: 0.15;
  stroke: black;
  stroke-width: 2;
  font-family: arial;
}
.front_labels {
  stroke: black;
  fill: black;
  font-size: 65px;
  font-family: arial;
}
.front_dimension {
  stroke: black;
  stroke-width: 2;
  fill: black;
  font-size: 45px;
  font-family: arial;
}
/* Dla rysunku zawartosci paczki */
.paczki_tabLines {
  fill: None;
  stroke: black;
  stroke-width: 2;
  font-family: arial;
}
.paczki_tabLinesthin {
  fill: None;
  stroke: grey;
  stroke-width: 1;
  font-family: arial;
}
.paczki_tabText {
  fill: black;
  text-anchor: middle;
  stroke: None;
  font: bold 100% arial;
  text-shadow: 0 1px 2px rgba(0,0,0,.2);
}
.paczki_headText {
  fill: grey;
  stroke: None;
  font-family: arial;
}
.paczki_levels {
  fill: grey;
  stroke: None;
  font-family: arial;
}
.paczki_fill {
  fill: grey;
  fill-opacity: 0.5;
  stroke: black;
  font-family: arial;
}
