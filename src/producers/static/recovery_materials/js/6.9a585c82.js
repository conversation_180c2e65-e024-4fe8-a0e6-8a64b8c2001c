(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([[6],{"2a4d":function(e,t,a){},5096:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("q-page",{staticClass:"q-pa-lg"},[a("div",{staticClass:"q-gutter-md row"},[a("q-select",{staticClass:"warehouse-select",attrs:{options:e.tableTypeOptions,label:"Typ elementów w magazynie"},model:{value:e.tableType,callback:function(t){e.tableType=t},expression:"tableType"}}),a("q-select",{staticClass:"warehouse-select",attrs:{clearable:"",options:e.availableColors,label:"Kolor elementów"},model:{value:e.selectedColor,callback:function(t){e.selectedColor=t},expression:"selectedColor"}}),a("q-btn",{staticClass:"q-mt-lg bg-indigo-9 text-white",attrs:{disable:!e.tableType},on:{click:e.fetchData}},[e._v("\n      Szukaj elementów\n    ")])],1),e.isFetched?a("BaseRecoveryMaterialsTable",e._b({},"BaseRecoveryMaterialsTable",{selection:"single",title:e.tableType.value,fetchData:e.fetchData,tableData:e.tableData,tableButtons:e.tableButtons,loading:e.loading,columns:e.columnsForTableType[e.tableType.value]},!1)):e._e(),a("q-dialog",{model:{value:e.decreaseAmount,callback:function(t){e.decreaseAmount=t},expression:"decreaseAmount"}},[a("div",{staticClass:"modal-wrapper q-pa-lg bg-white"},[a("DecreaseAmountFromWarehouse",e._b({},"DecreaseAmountFromWarehouse",{closeModal:e.closeModal,warehouseElementsIds:e.selectedIds},!1))],1)])],1)},s=[],n=(a("ddb0"),a("63f9")),o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("section",[a("div",{staticClass:"text-h6"},[e._v("\n    Zdejmij ze stanu\n  ")]),a("q-input",{attrs:{label:"Ilość do zdjęcia",type:"number",min:"1"},model:{value:e.amount,callback:function(t){e.amount=t},expression:"amount"}}),a("q-btn",{staticClass:"q-mt-lg bg-indigo-9 text-white",attrs:{label:"Zapisz",loading:e.loading},on:{click:e.decreaseAmountFromWarehouse}})],1)},i=[],r=(a("a79d"),{name:"DecreaseAmountFromWarehouse",props:{closeModal:{type:Function,required:!0},warehouseElementsIds:{type:Array,required:!0}},data(){return{loading:!1,amount:null}},methods:{preparePayload(){return this.warehouseElementsIds.reduce(((e,t)=>(e.push({id:t,amount:parseInt(this.amount,10)}),e)),[])},decreaseAmountFromWarehouse(){this.loading=!0,this.$axios.post("/api/v1/product_material_recovery/warehouse/decrease_amount/",this.preparePayload()).then((()=>{this.$q.notify({message:"Zapisano",type:"positive"}),this.closeModal()})).catch((e=>{this.$q.notify({message:`Nastąpił błąd - ${e.message}`,type:"negative"})})).finally((()=>{this.loading=!1}))}}}),c=r,d=a("2877"),u=a("27f9"),m=a("9c40"),p=a("eebe"),h=a.n(p),b=Object(d["a"])(c,o,i,!1,null,null,null),y=b.exports;h()(b,"components",{QInput:u["a"],QBtn:m["a"]});var g={name:"Warehouse",components:{DecreaseAmountFromWarehouse:y,BaseRecoveryMaterialsTable:n["a"]},data(){return{selectedIds:[],decreaseAmount:!1,tableButtons:[{label:"Zdejmij ze stanu",icon:"remove",handleClick:e=>{this.decreaseAmount=!0,this.selectedIds=e},enableOnOnlySelectedItems:!0}],maxAvailableValues:{},loading:!0,tableData:[],isFetched:!1,tableType:null,tableTypeOptions:[{label:"Elementy",value:"elements"},{label:"Okucia",value:"fittings"},{label:"Sample",value:"samples"}],availableColors:[],selectedColor:null,columnsForTableType:{elements:[{name:"codename",align:"center",label:"codename",field:({codename:e})=>e,style:"width: 400px",headerStyle:"width: 400px"},{name:"amount",align:"center",label:"Ilość",field:({amount:e})=>e,style:"width: 400px",headerStyle:"width: 400px"}],fittings:[{name:"codename",align:"center",label:"codename",field:({codename:e})=>e,style:"width: 400px",headerStyle:"width: 400px"},{name:"amount",align:"center",label:"Ilość",field:({amount:e})=>e,style:"width: 400px",headerStyle:"width: 400px"}],samples:[{name:"color",align:"center",label:"kolor",field:({color:e})=>e,style:"width: 400px",headerStyle:"width: 400px"},{name:"amount",align:"center",label:"Ilość",field:({amount:e})=>e,style:"width: 400px",headerStyle:"width: 400px"}]}}},created(){this.getAvailableColors()},methods:{closeModal(){this.decreaseAmount=!1},getMaxValuesForElements(e){return e.reduce(((e,t)=>(e[t.id]=t.amount,e)),[])},fetchData(){this.loading=!0,this.$axios.get(`/api/v1/product_material_recovery/warehouse?type=${this.tableType.value}&color=${this.selectedColor&&this.selectedColor.value||""}`).then((e=>{this.tableData=e.data,this.loading=!1,this.isFetched=!0,this.maxAvailableValues=this.getMaxValuesForElements(e.data)})).catch((e=>{this.$q.notify({message:`Nastąpił błąd - ${e.message}`,type:"negative"})}))},getAvailableColors(){this.$axios.get("/api/v1/product_material_recovery/product_material_recovery_configuration/").then((e=>{this.availableColors=[...e.data.color_options.map((({name:e,value:t})=>({label:e,value:t})))]})).catch((e=>{this.$q.notify({message:`Nastąpił błąd - ${e.message}`,type:"negative"})}))}}},f=g,v=(a("f3e1"),a("9989")),w=a("ddd8"),x=a("24e8"),q=Object(d["a"])(f,l,s,!1,null,"61b9ca78",null);t["default"]=q.exports;h()(q,"components",{QPage:v["a"],QSelect:w["a"],QBtn:m["a"],QDialog:x["a"]})},"63f9":function(e,t,a){"use strict";var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("q-table",{staticClass:"sticky-header-table",attrs:{title:e.title,"row-key":"id",data:e.tableData,columns:e.columns,loading:e.loading,"loading-label":"Pobieranie danych...","rows-per-page-label":"Ilość na stronie:","rows-per-page-options":[25,10,50,100,0],selection:"multiple",selected:e.selected,flat:"",bordered:"",separator:"cell"},on:{"update:selected":function(t){e.selected=t}},scopedSlots:e._u([{key:"top",fn:function(){return e._l(e.tableButtons,(function(t,l){return a("q-btn",{key:"table-btn-"+l,attrs:{label:t.label,icon:t.icon,disable:t.disableFunction&&t.disableFunction(e.selected)||t.enableOnOnlySelectedItems&&(e.loading||0===e.selected.length),flat:"",dense:"",color:"primary"},on:{click:function(a){t.handleClick(e.getSelectedIds())}}})}))},proxy:!0},e._l(e.$scopedSlots,(function(t,a){return{key:a,fn:function(t){return[e._t(a,null,null,t)]}}}))],null,!0)})},s=[],n={name:"BaseRecoveryMaterialsTable",props:{title:{type:String,required:!0},columns:{type:Array,required:!0},fetchData:{type:Function,required:!0},tableButtons:{type:Array,required:!1,default:()=>[]},loading:{type:Boolean,required:!1,default:!1},tableData:{type:Array,required:!1,default:()=>[]},selection:{type:String,required:!1,default:"multiple"}},data(){return{pagination:{rowsPerPage:250},selected:[],filter:""}},created(){this.fetchData()},methods:{getSelectedIds(){return this.selected.map((e=>e.id))},resetSelection(){this.selected=[]}}},o=n,i=a("2877"),r=a("eaac"),c=a("9c40"),d=a("eebe"),u=a.n(d),m=Object(i["a"])(o,l,s,!1,null,null,null);t["a"]=m.exports;u()(m,"components",{QTable:r["a"],QBtn:c["a"]})},f3e1:function(e,t,a){"use strict";a("2a4d")}}]);