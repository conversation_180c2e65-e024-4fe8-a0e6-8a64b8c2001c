{% load i18n static humanize%}<!DOCTYPE html>
{% get_current_language as LANGUAGE_CODE %}{% get_current_language_bidi as LANGUAGE_BIDI %}

<html lang="{{ LANGUAGE_CODE|default:"en-us" }}" {% if LANGUAGE_BIDI %}dir="rtl"{% endif %}>
<head>
    <title>{% block title %}{% endblock %}</title>
    <link rel="stylesheet" type="text/css" href="{% block stylesheet %}{% static "admin/css/base.css" %}{% endblock %}" />
    {% block extrastyle %}{% endblock %}
    {% if LANGUAGE_BIDI %}<link rel="stylesheet" type="text/css" href="{% block stylesheet_rtl %}{% static "admin/css/rtl.css" %}{% endblock %}" />{% endif %}
    <script type="text/javascript">window.__admin_media_prefix__ = "{% filter escapejs %}{% static "admin/" %}{% endfilter %}";</script>
    <script type="text/javascript">window.__admin_utc_offset__ = "{% filter escapejs %}{% now "Z" %}{% endfilter %}";</script>

    <script src="//ajax.googleapis.com/ajax/libs/jquery/2.2.0/jquery.min.js" type="text/javascript"></script>

    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">

    <!-- MetisMenu CSS -->
    <link href="{% static 'css/plugins/metisMenu/metisMenu.min.css' %}" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'css/sb-admin-2.css' %}" rel="stylesheet">

    <!-- Morris Charts CSS -->
    <link href="{% static 'css/plugins/morris.css' %}" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="{% static 'font-awesome-4.1.0/css/font-awesome.min.css' %}" rel="stylesheet" type="text/css">
    <link rel="stylesheet" type="text/css" href="{% static 'css/dc.min.css' %}"/>

    <style>
        #panel-refresh .panel-heading { border: 1px solid; cursor: pointer;}
        #panel-refresh .panel-heading:hover { background-color: #F5F5F5; }
        #panel-refresh-icon { position: relative; top: 10px; }

        .typical-chart{
            height: 400px;
        }
        .navbar-default{
            position: fixed;
            width: 100%;
            margin: 0 0 405px 0;
        }
        @media(min-width:768px) {
            #page-wrapper {
                position: inherit;
                margin: 0 0 0 250px;
                padding: 52px 0 0 30px;/* for fixed menu*/ /*padding: 0 30px;*/
                border-left: 1px solid #e7e7e7;
            }
        }
        #page-wrapper {
            padding: 0 15px;
            min-height: 568px;
            background-color: #fff;
            padding-top: 101px;
        }
    </style>

    <script type="application/javascript">

        function reduceAdd(p, v) {
            v.rows.forEach (function(val, idx) {
                p[val] = (p[val] || 0) + 1; //increment counts
            });
            return p;
        }

        function reduceRemove(p, v) {
            v.rows.forEach (function(val, idx) {
                p[val] = (p[val] || 0) - 1; //decrement counts
            });
            return p;

        }

        function reduceInitial() {
            return {};
        }

        function statusSelect(val) {

            var value = val.value;
            if(value != 'null') {
                cstm_report.statusPieChart.filterAll();
                cstm_report.statusPieChart.filter(value);
            } else {
                cstm_report.statusPieChart.filterAll();
            }
            dc.redrawAll();
        }

        function presetSelect(val) {
            var value = val.value;
            console.log('value', value);
            if(value != 'null') {
                cstm_report.presetPieChart.filterAll();
                cstm_report.presetPieChart.filter(value);
                dc.redrawAll();
            } else {
                cstm_report.presetPieChart.filterAll();
            }
            dc.redrawAll();
        }

        $(document).ready(function() {
            window.cstm_report = {};

            var ndx = crossfilter(json_for_all);

            var productionActualDim = ndx.dimension(function (d) {
                return +d.production_price_actual_shelfs;
            });

            var productionActualGroup = productionActualDim.group().reduceCount();

            productionActual_list = _.map(json_for_all, function (x) {
                return x.production_price_actual_shelfs;
            });


            cstm_report.PriceBarChart = dc.barChart("#price-stacked-chart")
                    .dimension(productionActualDim)
                    .group(productionActualGroup, "1", function(d){return d["production_price_actual_shelfs"]})
                    .x(d3.scale.linear().domain([0,2000]))
                    .brushOn(false)
                    .clipPadding(10)
                    .elasticY(true);
            cstm_report.PriceBarChart.stack(productionActualGroup).stack(productionActualGroup, 'noga', function(d){return d["total_price_actual_shelfs"]});
            dc.renderAll();


            /// reset all filters

            $('.filterAll').on('click', function () {
                for (var property in window.cstm_report) {
                    if (window.cstm_report.hasOwnProperty(property)) {
                        window.cstm_report[property].filterAll();
                    }
                }
                dc.redrawAll();
                $('#filterListContainer').remove();
            });

            $(document).on('click', '.button-remove-filter', function(e){
                e.preventDefault();
                var parent = $(this).parent().parent();
                console.log('parent', parent);
                console.log('parent', parent.data('chart'));
                for(property in window.cstm_report) {
                    if(window.cstm_report[property].__dcFlag__ == parent.data('chart')) {
                        window.cstm_report[property].filterAll();
                    }
                }
                dc.redrawAll();
                parent.remove();
            });


            // list all filters applied
            var properties = ['patternPieChart', 'patternHistChart',  'widthPieChart', 'widthHistChart', 'heigthHistChart', 'heightPieChart', 'depthHistChart', 'depthPieChart', 'rowsHistChart', 'rowsPieChart', 'materialHistChart', 'materialPieChart', 'propertyHistChart', 'propertyPieChart', 'horizontalsHistChart', 'horizontalsPieChart', 'verticalsHistChart', 'verticalsPieChart', 'supportsHistChart', 'supportsPieChart', 'rowsHeightHistChart', 'rowsHeightPieChart' ];

            for(var i=0; i<properties.length; i++) {
                if (window.cstm_report.hasOwnProperty(properties[i])) {
                    window.cstm_report[properties[i]].on('filtered.monitor', function (chart, filter) {
                        console.log('chart', chart);
                        console.log('active filter', filter);
                        if($('#filterList').length == 0) {
                            $('#filterListContainer').append('Filter List <ul id="filterList"></ul>');
                        };
                        var filterType;
                        switch(chart.__dcFlag__) {
                            case 1:
                                filterType = 'patternPieChart';
                                break;
                            case 2:
                                filterType = 'pattern';
                                break;
                            case 3:
                                filterType = 'width';
                                break;
                            case 4:
                                filterType = 'widthPieChart';
                                break;
                            case 5:
                                filterType = 'heightPieChart';
                                break;
                            case 6:
                                filterType = 'height';
                                break;
                            case 7:
                                filterType = 'depht';
                                break;
                            case 8:
                                filterType = 'dephtPieChart';
                                break;
                            case 9:
                                filterType = 'rows';
                                break;
                            case 10:
                                filterType = 'rowsPieChart';
                                break;
                            case 11:
                                filterType = 'material';
                                break;
                            case 12:
                                filterType = 'materialPieChart';
                                break;
                            case 13:
                                filterType = 'property';
                                break;
                            case 14:
                                filterType = 'propertyPieChart';
                                break;
                            case 15:
                                filterType = 'horizontals';
                                break;
                            case 16:
                                filterType = 'horizontalsPieChart';
                                break;
                            case 17:
                                filterType = 'verticals';
                                break;
                            case 18:
                                filterType = 'verticalsPieChart';
                                break;
                            case 19:
                                filterType = 'supports';
                                break;
                            case 20:
                                filterType = 'supportsPieChart';
                                break;
                            case 21:
                                filterType = 'rowsHeight';
                                break;
                            case 22:
                                filterType = 'rowsHeightPieChart';
                                break;
                        };
                        if($('#'+filterType).length == 0) {
                            $('#filterList').append('<li id="'+filterType+'" data-chart="'+chart.__dcFlag__+'"><p>'+filterType+'<button type="button" class="btn btn-default btn-xs button-remove-filter" style="margin-left: 5px;"><span class="glyphicon glyphicon-remove" aria-hidden="true"></span>Remove</button></p></li>');
                        };
                        if($('#'+filterType).find('span[data-value="'+filter+'"]').length > 0){
                            $('#'+filterType).find('span[data-value="'+filter+'"]').remove();
                            if($('#'+filterType+'> span').length == 0) {
                                $('#'+filterType).remove();
                            }
                        } else {
                            $('#'+filterType).append('<span data-value='+filter+'>'+filter+', </span>');
                        };
                    });
                }
            }



        });


        window.json_for_all = {{ json_for_all|safe }}

    </script>
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
        <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
        <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->
    <style>
        {% if not IS_PRODUCTION %}
        #page-wrapper{
            background: #ccc;
        }
        #header{
            background: black;
            color: #ffffff;
        }
        {% endif %}
    </style>
</head>


<body class="{% if is_popup %}popup {% endif %}{% block bodyclass %}{% endblock %}">

<!-- Container -->
<div id="container">

    {% if not is_popup %}
        <!-- Header -->
        <div id="header">
            <div id="branding">
                {% block branding %}{% endblock %}
            </div>
            {% block usertools %}
                {% if has_permission %}
                    <div id="user-tools">
                        {% block welcome-msg %}
                            {% trans 'Welcome,' %}
                            <strong>{% firstof user.get_short_name user.get_username %}</strong>.
                        {% endblock %}
                        {% block userlinks %}
                            {% if site_url %}
                                <a href="{{ site_url }}">{% trans 'View site' %}</a> /
                            {% endif %}
                            {% if user.is_active and user.is_staff %}
                                {% url 'django-admindocs-docroot' as docsroot %}
                                {% if docsroot %}
                                    <a href="{{ docsroot }}">{% trans 'Documentation' %}</a> /
                                {% endif %}
                            {% endif %}
                            {% if user.has_usable_password %}
                                <a href="{% url 'admin:password_change' %}">{% trans 'Change password' %}</a> /
                            {% endif %}
                            <a href="{% url 'admin:logout' %}">{% trans 'Log out' %}</a>
                        {% endblock %}
                    </div>
                {% endif %}
            {% endblock %}
            {% block nav-global %}{% endblock %}
        </div>
        <!-- END Header -->
        {% block breadcrumbs %}
            <div class="breadcrumbs">
                <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
                {% if title %} &rsaquo; {{ title }}{% endif %}
            </div>
        {% endblock %}
    {% endif %}

    <!-- Content -->
    <div id="content" class="{% block coltype %}colM{% endblock %}">
        <div id="wrapper">
            <nav class="navbar navbar-default" role="navigation" style="margin-bottom: 0; width: 240px;">
                <div class="navbar-header">
                    <button type="button" class="navbar-toggle" data-toggle="collapse" data-target=".navbar-collapse">
                        <span class="sr-only">Toggle navigation</span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                        <span class="icon-bar"></span>
                    </button>
                    <a class="navbar-brand" href="/admin/">Dashboard</a>
                </div>
                <!-- /.navbar-header -->


                <div class="navbar-default sidebar" role="navigation" style="width: 240px;">
                    <div class="sidebar-nav navbar-collapse">
                        <ul class="nav" id="side-menu">
                            <li>
                                <a class="active" href="{% url "admin:report_ivy" %}"><i class="fa fa-dashboard fa-fw"></i> Main ivy report</a>
                            </li>
                            <li>
                                <a class="active" href="{% url "admin:report_sold_ivy" %}"><i class="fa fa-dashboard fa-fw"></i> Sold Ivy report</a>
                            </li>
                            <li>
                                <a href="{% url "admin:kpis" %}"><i class="fa fa-table fa-fw"></i> Kpis</a>
                            </li>

                            <li>
                                <a href="{% url "admin:orders_order_changelist" %}"><i class="fa fa-table fa-fw"></i> Move to dimensions</a>
                            </li>
                            <li>
                                <a href="#" class="filterAll"><i class="fa fa-dashboard fa-fw"></i> Reset all filters</a>
                            </li>
                            <li><span>Furniture status</span>
                                <select id="id_furniture_status" name="furniture_status" onchange="statusSelect(this)">
                                    <option value="null" selected="selected">---------</option>
                                    <option value="0">Cart</option>
                                    <option value="1">Wishlist</option>
                                    <option value="2">Ordered</option>
                                    <option value="3">Shared</option>
                                    <option value="4">Special</option>
                                </select>
                            </li>
                            <li><span>Preset</span>
                                <select id="id_furniture_preset" name="furniture_preset" onchange="presetSelect(this)">
                                    <option value="null" selected="selected">---------</option>
                                    <option value="true">True</option>
                                    <option value="false">False</option>
                                </select>
                            </li>
                            <li id="filterListContainer">

                            </li>
                        </ul>
                    </div>
                    <!-- /.sidebar-collapse -->
                </div>
                <!-- /.navbar-static-side -->
            </nav>


            <div id="page-wrapper">
                <div class="row">
                </div>

                <div class="row">
                    <h2>Item cost chart</h2>
                    <div class="col-md-6 typical-chart" id="price-stacked-chart">
                        <div class="reset" style="visibility: hidden;">selected: <span class="filter"></span>
                            <a href="javascript:cstm_report.horizontalsPieChart.filterAll();dc.redrawAll();">reset</a>
                        </div>
                    </div>
                    <div class="col-md-6 typical-chart" id="horizontals-hist-chart">
                        <div class="reset" style="visibility: hidden;">range: <span class="filter"></span>
                            <a href="javascript:cstm_report.horizontalsHistChart.filterAll();dc.redrawAll();">reset</a>
                        </div>
                    </div>
                </div>
                <div class="row">
                    <h2>Production cost chart</h2>
                    <div class="col-md-6 typical-chart" id="verticals-pie-chart">
                        <div class="reset" style="visibility: hidden;">selected: <span class="filter"></span>
                            <a href="javascript:cstm_report.verticalsPieChart.filterAll();dc.redrawAll();">reset</a>
                        </div>
                    </div>
                    <div class="col-md-6 typical-chart" id="verticals-hist-chart">
                        <div class="reset" style="visibility: hidden;">range: <span class="filter"></span>
                            <a href="javascript:cstm_report.verticalsHistChart.filterAll();dc.redrawAll();">reset</a>
                        </div>
                    </div>
                </div>



                <div class="row">
                    <div>
                        <h2> Based on {{ number_of_items }} items.</h2>
                    </div>
                </div>
            </div></div>

        <script src="{% static 'js/lodash.js' %}"></script>
        <script src="{% static 'js/d3.v3.min.js' %}"></script>
        <script type="text/javascript" src="{% static 'js/crossfilter.js' %}"></script>
        <script type="text/javascript" src="{% static 'js/dc.js' %}"></script>
        <br class="clear" />
    </div>
    <!-- END Content -->

    {% block footer %}<div id="footer"></div>{% endblock %}
</div>
<!-- END Container -->

</body>
</html>
