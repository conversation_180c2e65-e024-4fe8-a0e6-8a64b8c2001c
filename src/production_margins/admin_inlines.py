from django.contrib import admin

from production_margins.models import (
    CustomPricingFactor,
    CustomPricingFactorChangeRequestComment,
    MaterialInfoAttachment,
)


class CustomPricingFactorInline(admin.TabularInline):
    model = CustomPricingFactor
    extra = 0
    raw_id_fields = ('pricing_factor_item', 'manufacturer_code')


class CustomPricingFactorChangeRequestCommentInline(admin.TabularInline):
    model = CustomPricingFactorChangeRequestComment
    extra = 0
    raw_id_fields = ('author',)

    def formfield_for_foreignkey(self, db_field, request, **kwargs):
        if db_field.name == 'author':
            kwargs['initial'] = request.user.pk
            kwargs['disabled'] = True
        return super().formfield_for_foreignkey(db_field, request, **kwargs)


class MaterialInfoAttachmentInline(admin.TabularInline):
    model = MaterialInfoAttachment
    extra = 1
    can_delete = False
    ordering = ('document_type', 'id')
