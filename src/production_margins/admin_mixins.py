import io

from datetime import datetime
from typing import Type

from django.contrib import (
    admin,
    messages,
)
from django.db.models import QuerySet
from django.http import (
    HttpRequest,
    HttpResponse,
    HttpResponseRedirect,
)

from admin_customization.forms import UploadFileForm
from custom.admin_action import admin_action_with_form
from production_margins.data_management.exporters import BaseSerializedItemsExporter
from production_margins.data_management.importers import BaseImporter
from production_margins.forms import ChooseDateForm


class CSVImportActionMixin:
    @classmethod
    def import_csv_file_action(
        cls,
        request: HttpRequest,
        importer_class: Type[BaseImporter],
    ) -> HttpResponse:
        """Use an importer class to upload and process a CSV file."""
        form_class = UploadFileForm
        success_function = cls.process_csv_file
        success_function_kwargs = {'importer_class': importer_class}
        return admin_action_with_form(
            modeladmin=cls,
            request=request,
            queryset=None,
            form_class=form_class,
            success_function=success_function,
            success_function_kwargs=success_function_kwargs,
        )

    @staticmethod
    def process_csv_file(
        modeladmin,
        request: HttpRequest,
        queryset: QuerySet,
        form: UploadFileForm,
        importer_class: Type[BaseImporter],
    ) -> HttpResponseRedirect:
        """Use process uploaded CSV file with an importer class."""
        csv_file = form.files.get('file')
        decoded_file = csv_file.read().decode(importer_class.encoding)
        importer = importer_class.from_csv(io.StringIO(decoded_file))
        importer.save_valid_items()
        if importer.valid_items:
            valid_items_list = importer.report_items('valid_items')
            messages.success(
                request,
                f'Items imported: {valid_items_list}',
            )
        if importer.duplicated_items:
            duplicated_items_list = importer.report_items('duplicated_items')
            messages.warning(
                request,
                f'Found duplicated items (ignored): {duplicated_items_list}',
            )
        if importer.conflicting_items:
            conflicting_items_list = importer.report_items('conflicting_items')
            messages.error(
                request,
                f'Found conflicting items (ignored): {conflicting_items_list}',
            )
        if importer.invalid_items:
            invalid_items_list = importer.report_items('invalid_items')
            first_error = importer.invalid_items[0].errors
            messages.error(
                request,
                f'Found invalid items (ignored): {invalid_items_list}',
            )
            messages.error(
                request,
                f'First invalid item error: {first_error}',
            )
        if importer.non_existing_items:
            non_existing_items_list = importer.non_existing_items
            messages.error(
                request,
                f'Found non existing items: {non_existing_items_list}',
            )
        return HttpResponseRedirect('../')


class CSVExportActionMixin:
    @staticmethod
    def export_to_csv_file(
        request: HttpRequest,
        queryset: QuerySet,
        filename: str,
        exporter_class: Type[BaseSerializedItemsExporter],
        **exporter_kwargs,
    ) -> HttpResponse:
        """Use an exporter to prepare and download a CSV file."""
        exporter = exporter_class(queryset, **exporter_kwargs)
        response = HttpResponse(content_type='text/csv')
        response['Content-Disposition'] = 'attachment; filename={}_{}.csv'.format(
            filename,
            datetime.today().strftime('%y%m%d_%H%M'),
        )
        response.write(exporter.to_csv())
        return response


class BulkUpdateDatesMixin:
    actions = ['bulk_update_dates_action']

    @admin.action(description='Bulk update dates for selected items')
    def bulk_update_dates_action(self, request, queryset):
        return admin_action_with_form(
            modeladmin=self,
            request=request,
            queryset=queryset,
            form_class=ChooseDateForm,
            success_function=self.bulk_update_dates,
            success_function_kwargs={},
        )

    @staticmethod
    def bulk_update_dates(
        modeladmin,
        request: HttpRequest,
        queryset: QuerySet,
        form: ChooseDateForm,
    ) -> None:
        date_to = form.cleaned_data['date_to']
        date_from = form.cleaned_data['date_from']
        if date_to:
            queryset.update(date_to=date_to)
        if date_from:
            queryset.update(date_from=date_from)
