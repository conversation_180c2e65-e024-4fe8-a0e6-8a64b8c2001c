from django.contrib.admin import AdminSite
from django.urls import path

from production_margins.views import (
    CodenamesReportView,
    EcoTaxReportView,
    ElectroTaxReportView,
)


class ProductionMarginsAdminPage(AdminSite):
    def get_production_margins_admin_urls(self):
        return [
            path(
                'codenames_report/',
                self.admin_view(CodenamesReportView.as_view()),
                name='codenames-report',
            ),
            path(
                'ecotax_report/',
                self.admin_view(EcoTaxReportView.as_view()),
                name='ecotax-report',
            ),
            path(
                'electrotax_report/',
                self.admin_view(ElectroTaxReportView.as_view()),
                name='electrotax-report',
            ),
        ]


urlpatterns = ProductionMarginsAdminPage().get_production_margins_admin_urls()
