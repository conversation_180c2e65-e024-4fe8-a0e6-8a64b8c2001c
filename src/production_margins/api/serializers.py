from rest_framework import serializers

from production_margins.models import (
    ElementsOrder,
    GalaStockLevelMaterial,
)


class ElementsOrderSerializer(serializers.ModelSerializer):
    class Meta:
        model = ElementsOrder
        fields = ('id', 'generated_at', 'batches', 'deleted', 'new_report')


class GalaStockLevelMaterialSerializer(serializers.ModelSerializer):
    class Meta:
        model = GalaStockLevelMaterial
        fields = [
            'index',
            'invoice',
            'warehouse',
            'quantity',
            'entry_date',
            'defect',
            'defect_description',
            'supplier',
        ]

    def get_unique_together_validators(self):
        """Overriding method to disable unique together checks"""
        return []
