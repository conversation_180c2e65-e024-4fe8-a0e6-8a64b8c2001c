from django.db.models import Q

from rest_framework import status
from rest_framework.authentication import TokenAuthentication
from rest_framework.exceptions import ValidationError
from rest_framework.generics import (
    GenericAPIView,
    ListAPIView,
    get_object_or_404,
)
from rest_framework.response import Response
from rest_framework.settings import api_settings
from rest_framework.views import APIView

from producers.api.pagination import ProducerPagePagination
from producers.api.permissions import (
    ProducerPanelPermission,
    ProducerPanelWarehousePermission,
)
from producers.models import Manufactor
from production_margins.api.serializers import (
    ElementsOrderSerializer,
    GalaStockLevelMaterialSerializer,
)
from production_margins.models import (
    ElementsOrder,
    GalaStockLevelMaterial,
)
from production_margins.utils import elements_order_file_as_json


class ElementsOrderListAPIView(ListAPIView):
    permission_classes = [ProducerPanelPermission | ProducerPanelWarehousePermission]
    pagination_class = ProducerPagePagination
    authentication_classes = [
        *api_settings.DEFAULT_AUTHENTICATION_CLASSES,
        TokenAuthentication,
    ]
    serializer_class = ElementsOrderSerializer

    def get_queryset(self):
        manufactor = Manufactor.objects.filter(
            Q(owner=self.request.user) | Q(accounts__user=self.request.user)
        ).first()
        return (
            ElementsOrder.objects.filter(batches__manufactor=manufactor)
            .order_by('-id')
            .distinct()
        )


class ElementsOrderFileAPIView(GenericAPIView):
    permission_classes = [ProducerPanelPermission]
    authentication_classes = [
        *api_settings.DEFAULT_AUTHENTICATION_CLASSES,
        TokenAuthentication,
    ]

    def get(self, request, pk, *args, **kwargs):
        instance = get_object_or_404(self.get_queryset(), pk=pk)
        return Response(
            data=elements_order_file_as_json(instance.order_file),
            content_type='application/json',
        )

    def get_queryset(self):
        manufactor = Manufactor.objects.filter(
            Q(owner=self.request.user) | Q(accounts__user=self.request.user)
        ).first()
        return ElementsOrder.objects.filter(batches__manufactor=manufactor).distinct()


class GalaStockLevelMaterialApiView(APIView):
    permission_classes = (ProducerPanelPermission,)
    authentication_classes = (TokenAuthentication,)

    def post(self, request, *args, **kwargs):
        if 'data' not in request.data:
            raise ValidationError(
                'Wrong format. Expected form: {"data": [{}, {}, ...]}'
            )
        serializer = GalaStockLevelMaterialSerializer(
            data=request.data['data'], many=True
        )
        serializer.is_valid(raise_exception=True)
        results = {'created': 0, 'updated': 0}
        for item in serializer.validated_data:
            index = item.pop('index')
            invoice = item.pop('invoice')
            _, created = GalaStockLevelMaterial.objects.update_or_create(
                index=index, invoice=invoice, defaults=item
            )
            results['created' if created else 'updated'] += 1

        return Response(status=status.HTTP_201_CREATED, data=results)
