from django.db import models


class ReportDataType(models.IntegerChoices):
    MONTHLY = 1, 'Data from a given month'
    PRODUCT_IDS = 2, 'List of product ids'


class CodenamesRecalculationChoice(models.IntegerChoices):
    ORIGINAL = 1
    CURRENT = 2


class PricingFactorItemCategory(models.IntegerChoices):
    FITTING = 1
    MATERIAL = 2
    PACKAGING = 3
    SEMIPRODUCT = 4
    SERVICE = 5
    PHANTOM = 6
    COVER = 7

    @classmethod
    def from_str(cls, category_str):
        category_map = {
            'fitting': cls.FITTING,
            'material': cls.MATERIAL,
            'packaging': cls.PACKAGING,
            'semiproduct': cls.SEMIPRODUCT,
            'service': cls.SERVICE,
            'cover': cls.COVER,
        }
        return category_map.get(category_str, cls.FITTING)


class ReportingMaterialType(models.TextChoices):
    PAPER = 'paper'
    PLASTIC = 'plastic'
    BEAVERBOARD = 'beaverboard'
    NOT_REPORTABLE = 'n/a'


class MeasurementUnit(models.IntegerChoices):
    PIECES = 1, 'pc'
    LINEAR_METER = 2, 'lm'
    SQUARE_METER = 3, 'm2'
    SET = 4, 'set'
    METER = 5, 'm'
    KILOGRAM = 6, 'kg'
    LITER = 7, 'l'

    @classmethod
    def from_str(cls, measurement_unit_str):
        measurement_unit_map = {
            'szt': cls.PIECES,
            'kpl': cls.SET,
            'szy': cls.PIECES,
            'szt.': cls.PIECES,
            'mb': cls.LINEAR_METER,
            'm^2': cls.SQUARE_METER,
            'm2': cls.SQUARE_METER,
            'l': cls.LITER,
            'kg': cls.KILOGRAM,
        }
        return measurement_unit_map.get(measurement_unit_str, cls.PIECES)

    @classmethod
    def from_english_str(cls, measurement_unit_str):
        measurement_unit_map = {
            'pc': cls.PIECES,
            'lm': cls.LINEAR_METER,
            'm2': cls.SQUARE_METER,
            'set': cls.SET,
            'm': cls.METER,
            'kg': cls.KILOGRAM,
            'l': cls.LITER,
        }
        return measurement_unit_map.get(measurement_unit_str, cls.PIECES)

    def to_polish(self):
        return self.measurement_unit_map.get(self, 'szt')

    def to_polish_admin_display_only(self) -> str:
        measurement_unit_map = self.measurement_unit_map
        measurement_unit_map[self.METER] = 'm'
        measurement_unit_map[self.SQUARE_METER] = 'm\u00b2'
        return measurement_unit_map.get(self, 'szt')

    @property
    def measurement_unit_map(self) -> dict:
        return {
            self.PIECES: 'szt',
            self.SET: 'kpl',
            self.LINEAR_METER: 'mb',
            self.SQUARE_METER: 'm^2',
            self.LITER: 'l',
            self.KILOGRAM: 'kg',
        }


class PricingFactorNorms(models.IntegerChoices):
    NORM_v1 = 1, 'NORM_v1'
    NORM_v2 = 2, 'NORM_v2'


class PricingFactorStatus(models.IntegerChoices):
    DRAFT = 0, 'DRAFT'
    PLANNING = 1, 'PLANNING'
    REPORTING = 2, 'REPORTING'
    AUTO = 3, 'AUTO'
    USED_IN_PRODUCTION = 4, 'USED_IN_PRODUCTION'
    SERIALIZATION_VERSIONING = 5, 'SERIALIZATION_VERSIONING'


class MaterialInfoAttachmentType(models.IntegerChoices):
    ANY = 0, 'Any'
    PICTURE = 1, 'Picture'
    CERTIFICATION = 2, 'Certification'
    TECHNICAL_SKETCH = 3, 'Technical Sketch'
    TECHNICAL_CARD = 4, 'Technical Card'
    REACH = 5, 'REACH'
    ROHS = 6, 'ROHS'
    WOOD_SOURCE = 7, 'Wood Source'
    FORMALDEHYDE = 8, 'Formaldehyde'
    GOLDEN_SAMPLE = 9, 'Golden Sample'
