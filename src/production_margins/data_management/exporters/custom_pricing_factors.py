from production_margins.data_management.exporters.base_exporter import (
    BaseSerializedItemsExporter,
)
from production_margins.data_management.serializers.custom_pricing_factors import (
    CUSTOM_PRICING_FACTORS_EXPORT_COLUMNS,
    CustomPricingFactorsExportSerializer,
)
from production_margins.models import CustomPricingFactor


class CustomPricingFactorsExporter(BaseSerializedItemsExporter):
    dict_export_fields = CUSTOM_PRICING_FACTORS_EXPORT_COLUMNS[1:]
    dict_export_key = CUSTOM_PRICING_FACTORS_EXPORT_COLUMNS[0]
    serializer = CustomPricingFactorsExportSerializer
    export_item_prefetch_related = ('pricing_factor_item',)

    @property
    def default_export_queryset(self):
        return CustomPricingFactor.objects.all()
