from production_margins.data_management.exporters.base_exporter import (
    BaseSerializedItemsExporter,
)
from production_margins.data_management.serializers.element_managed_info import (
    ELEMENT_MANAGED_INFO_EXPORT_COLUMNS,
    ElementManagedInfoExportSerializer,
)
from production_margins.models import ElementManagedInfo


class ElementManagedInfoExporter(BaseSerializedItemsExporter):
    dict_export_fields = ELEMENT_MANAGED_INFO_EXPORT_COLUMNS[1:]
    dict_export_key = ELEMENT_MANAGED_INFO_EXPORT_COLUMNS[0]
    serializer = ElementManagedInfoExportSerializer
    export_item_prefetch_related = ('pricing_factor_item', 'manufactor')

    @property
    def default_export_queryset(self):
        return ElementManagedInfo.objects.all()
