from production_margins.data_management.exporters import BaseSerializedItemsExporter
from production_margins.data_management.serializers.manufacturer_code import (
    MANUFACTURER_CODE_EXPORT_COLUMNS,
    ManufacturerCodeSerializer,
)
from production_margins.models import ManufacturerCode


class ManufacturerCodeExporter(BaseSerializedItemsExporter):
    dict_export_fields = MANUFACTURER_CODE_EXPORT_COLUMNS[1:]
    dict_export_key = MANUFACTURER_CODE_EXPORT_COLUMNS[0]
    serializer = ManufacturerCodeSerializer
    export_item_prefetch_related = []

    @property
    def default_export_queryset(self):
        return ManufacturerCode.objects.all()
