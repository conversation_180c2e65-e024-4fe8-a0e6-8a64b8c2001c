from production_margins.data_management.exporters.base_exporter import (
    BaseSerializedItemsExporter,
)
from production_margins.data_management.serializers.material_management_cost import (
    MATERIAL_MANAGEMENT_COST_EXPORT_COLUMNS,
    MaterialManagementCostExportSerializer,
)
from production_margins.models import MaterialManagementCost


class MaterialManagementCostExporter(BaseSerializedItemsExporter):
    dict_export_fields = MATERIAL_MANAGEMENT_COST_EXPORT_COLUMNS[1:]
    dict_export_key = MATERIAL_MANAGEMENT_COST_EXPORT_COLUMNS[0]
    serializer = MaterialManagementCostExportSerializer
    export_item_prefetch_related = ('manufactor',)

    @property
    def default_export_queryset(self):
        return MaterialManagementCost.objects.all()
