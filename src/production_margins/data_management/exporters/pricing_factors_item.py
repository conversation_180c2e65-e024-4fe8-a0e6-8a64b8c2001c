from collections import defaultdict

from production_margins.data_management.exporters.base_exporter import (
    BaseSerializedItemsExporter,
)
from production_margins.data_management.serializers.pricing_factors_item import (
    PRICING_FACTOR_ITEM_CPF_EXPORT_COLUMNS,
    PRICING_FACTOR_ITEM_EXPORT_COLUMNS,
    PRICING_FACTOR_ITEM_PF_EXPORT_COLUMNS,
    PricingFactorItemCPFColumnsExportSerializer,
    PricingFactorItemExportSerializer,
    PricingFactorItemPFDataExportSerializer,
)
from production_margins.models import PricingFactorItem


class PricingFactorItemCPFColumnsExporter(BaseSerializedItemsExporter):
    """
    Exports PFI with additional columns with financial data from CPF.

    Adds two columns per manufacturer with price and loss factors overrides from
    currently active Custom Pricing Factors.
    """

    dict_export_key = PRICING_FACTOR_ITEM_CPF_EXPORT_COLUMNS[0]
    serializer = PricingFactorItemCPFColumnsExportSerializer
    export_item_prefetch_related = ('custompricingfactor_set',)

    @property
    def dict_export_fields(self):
        return [
            *PRICING_FACTOR_ITEM_CPF_EXPORT_COLUMNS[1:],
            *self.serializer.fields_generated_per_manufacturer(),
        ]

    @property
    def default_export_queryset(self):
        return PricingFactorItem.objects.all()


class PricingFactorItemPFDataExporter(BaseSerializedItemsExporter):
    """Exports PFI data with fields to reconstruct a correct Pricing Factors object."""

    dict_export_fields = PRICING_FACTOR_ITEM_PF_EXPORT_COLUMNS[1:]
    dict_export_key = 'codename'
    serializer = PricingFactorItemPFDataExportSerializer
    export_item_prefetch_related = ()

    @property
    def default_export_queryset(self):
        return PricingFactorItem.objects.all()

    def to_dict(self) -> dict:
        """This is a much more sensible format used by CSTM Pricing Factors model."""
        exported_data = defaultdict(dict)
        for export_item in self.export_items_serialized:
            export_key = export_item[self.dict_export_key]
            for field_name in self.dict_export_fields:
                exported_data[field_name][export_key] = export_item[field_name]
        return dict(exported_data)


class PricingFactorItemExporter(BaseSerializedItemsExporter):
    """Exports PFI data with fields to reconstruct a correct Pricing Factors object."""

    dict_export_fields = PRICING_FACTOR_ITEM_EXPORT_COLUMNS[1:]
    dict_export_key = PRICING_FACTOR_ITEM_EXPORT_COLUMNS[0]
    serializer = PricingFactorItemExportSerializer
    export_item_prefetch_related = ()

    @property
    def default_export_queryset(self):
        return PricingFactorItem.objects.all()
