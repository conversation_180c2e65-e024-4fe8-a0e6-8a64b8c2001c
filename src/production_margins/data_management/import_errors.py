from rest_framework import serializers


class DuplicatedItemValidationError(serializers.ValidationError):
    """Instances with duplicated data may be ignored on import."""

    default_code = 'duplicate'


class ConflictingItemValidationError(serializers.ValidationError):
    """Instances with conflicting data should be rejected on import."""

    default_code = 'conflict'


class WrongDatesRangeValidationError(serializers.ValidationError):
    """If `date_from` is greater than `date_to` error should be raised."""

    default_code = 'incorrect_range'
