import csv

from io import <PERSON><PERSON>
from typing import (
    ClassVar,
    Literal,
    Type,
)

from rest_framework import serializers

from production_margins.data_management.import_errors import (
    ConflictingItemValidationError,
    DuplicatedItemValidationError,
)

ItemCategoriesLiteral = Literal[
    'valid_items',
    'invalid_items',
    'duplicated_items',
    'conflicting_items',
]


class BaseImporter:
    encoding = 'utf-8'
    serializer = ClassVar[Type[serializers.ModelSerializer]]
    dict_export_key = ClassVar[str]

    def __init__(self, data):
        self.valid_items = []
        self.invalid_items = []
        self.duplicated_items = []
        self.conflicting_items = []
        self.non_existing_items = []

        self._data = data

    def _serialize_and_register(self, entry) -> serializers.ModelSerializer:
        """Serialize and register entry according to its validation status."""
        serialized_entry = self.serializer(data=entry)
        try:
            serialized_entry.is_valid(raise_exception=True)
        except serializers.ValidationError as validation_error:
            self._register_invalid_entry(
                serialized_entry=serialized_entry,
                error_codes=validation_error.get_codes(),
            )
        else:
            self.valid_items.append(serialized_entry)
        return serialized_entry

    def _register_invalid_entry(self, serialized_entry, error_codes) -> None:
        """Register entry as conflicting, duplicated or invalid."""
        non_field_errors = error_codes.get('non_field_errors', {})
        if ConflictingItemValidationError.default_code in non_field_errors:
            self.conflicting_items.append(serialized_entry)
        elif DuplicatedItemValidationError.default_code in non_field_errors:
            self.duplicated_items.append(serialized_entry)
        else:
            self.invalid_items.append(serialized_entry)

    def save_valid_items(self) -> None:
        """Serialize items one by one and save them if validated."""
        for entry in self._data:
            serialized_item = self._serialize_and_register(entry)
            if serialized_item and serialized_item.is_valid():
                serialized_item.save()

    @classmethod
    def from_csv(cls, csv_file: StringIO):
        reader = csv.DictReader(csv_file)
        return cls(list(reader))

    def report_items(self, items_category: ItemCategoriesLiteral) -> str:
        """List valid or invalid items by value in their key column."""
        items_list = getattr(self, items_category)
        return ', '.join(
            item.initial_data.get(self.dict_export_key, '') for item in items_list
        )
