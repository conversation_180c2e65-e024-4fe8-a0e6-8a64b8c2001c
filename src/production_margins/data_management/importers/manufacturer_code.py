from rest_framework import serializers

from production_margins.data_management.importers import BaseImporter
from production_margins.data_management.serializers.manufacturer_code import (
    MANUFACTURER_CODE_EXPORT_COLUMNS,
    ManufacturerCodeSerializer,
)


class ManufacturerCodeImporter(BaseImporter):
    dict_export_key = MANUFACTURER_CODE_EXPORT_COLUMNS[0]
    serializer = ManufacturerCodeSerializer

    def _serialize_and_register(self, entry) -> serializers.ModelSerializer:
        pricing_factor_items = entry['pricing_factor_item'].split()
        entry['pricing_factor_item'] = pricing_factor_items
        return super()._serialize_and_register(entry)
