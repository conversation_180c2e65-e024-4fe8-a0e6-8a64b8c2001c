from decimal import Decimal

from production_margins.choices import (
    MeasurementUnit,
    PricingFactorItemCategory,
)
from production_margins.data_management.importers import BaseImporter
from production_margins.data_management.serializers.pricing_factors_item import (
    PRICING_FACTOR_ITEM_EXPORT_COLUMNS,
    PricingFactorItemExportSerializer,
)
from production_margins.models import (
    PricingFactorItem,
    PricingFactors,
)


def float_multiplier_to_percentage(value=None) -> Decimal:
    if not value:
        return Decimal()
    return (Decimal(str(value)) - 1) * 100


def percentage_to_float_multiplier(value=None) -> float:
    if not value:
        return float(1)
    return float((value + 100) / 100)


class LegacyPricingFactorItemImporter:
    """Creates Pricing Factor Item objects based on active Pricing Factors."""

    @staticmethod
    def get_pricing_factor_dict():
        active_pricing_factor = PricingFactors.get_solo()
        pricing_factor_df = active_pricing_factor.df
        return pricing_factor_df.to_dict(orient='index')

    @staticmethod
    def pricing_factor_item_exists(codename):
        return PricingFactorItem.objects.filter(codename=codename).exists()

    @staticmethod
    def get_pricing_factor_item(codename, data):
        return PricingFactorItem(
            codename=codename,
            category=PricingFactorItemCategory.from_str(
                data['C1_category'],
            ),
            measurement_unit=MeasurementUnit.from_str(
                data['unit'],
            ),
            price=data['price_per_unit'],
            loss_factor=float_multiplier_to_percentage(data['loss_factor']),
            weight=data['weight_per_unit'],
            thickness=data['thickness'],
            length=data['length'],
            description=data['polish_desc'],
            conversion=data.get('conversion'),
        )

    @staticmethod
    def update_pricing_factor_item(codename, data):
        pricing_factor_item = PricingFactorItem.objects.filter(
            codename=codename,
        ).last()

        fields_to_check = (
            ('price_per_unit', 'price', float),
            ('weight_per_unit', 'weight', float),
            ('loss_factor', 'loss_factor', percentage_to_float_multiplier),
            ('polish_desc', 'description', str),
            ('conversion', 'conversion', str),
            ('thickness', 'thickness', int),
            ('length', 'length', int),
        )
        update_fields = []
        for column_name, field_name, cast_type in fields_to_check:
            current_value = getattr(pricing_factor_item, field_name)
            if current_value is None:
                current_value = cast_type()
            else:
                current_value = cast_type(current_value)
            if current_value != data.get(column_name):
                setattr(pricing_factor_item, field_name, data.get(column_name))
                update_fields.append(field_name)

        if update_fields:
            pricing_factor_item.save(update_fields=update_fields)

    def load(self):
        pricing_factor_items = []
        for codename, data in self.get_pricing_factor_dict().items():
            if self.pricing_factor_item_exists(codename):
                self.update_pricing_factor_item(codename, data)
            else:
                pricing_factor_items.append(
                    self.get_pricing_factor_item(codename, data)
                )

        if pricing_factor_items:
            PricingFactorItem.objects.bulk_create(
                pricing_factor_items,
                ignore_conflicts=True,
            )

        return len(pricing_factor_items)


class PricingFactorItemsImporter(BaseImporter):
    serializer = PricingFactorItemExportSerializer
    dict_export_key = PRICING_FACTOR_ITEM_EXPORT_COLUMNS[0]
