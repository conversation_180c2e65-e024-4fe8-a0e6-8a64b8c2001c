from decimal import Decimal

from django.db.models import (
    Avg,
    QuerySet,
)

from rest_framework import serializers

from production_margins.choices import MeasurementUnit
from production_margins.models import (
    CustomPricingFactor,
    MaterialManagementCost,
    PricingFactorItem,
)

AVERAGE_PRICING_FACTORS_EXPORT_COLUMNS: list[str] = [
    'codename',
    'unit',
    'length',
    'thickness',
    'loss_factor',
    'price_per_unit',
    'weight_per_unit',
    'management_cost',
]
ADDITIONAL_PRICING_FACTORS_EXPORT_COLUMNS: list[str] = [
    'description',
    'conversion',
]


class AveragePricingFactorExportSerializer(serializers.ModelSerializer):
    price_per_unit = serializers.SerializerMethodField()
    loss_factor = serializers.SerializerMethodField()
    management_cost = serializers.SerializerMethodField()
    unit = serializers.SerializerMethodField()
    weight_per_unit = serializers.DecimalField(
        source='weight',
        decimal_places=3,
        max_digits=8,
    )

    class Meta:
        model = PricingFactorItem
        fields = [
            *AVERAGE_PRICING_FACTORS_EXPORT_COLUMNS,
            *ADDITIONAL_PRICING_FACTORS_EXPORT_COLUMNS,
        ]

    def _get_custom_pricing_factors(
        self,
        exported_item: PricingFactorItem,
    ) -> QuerySet[CustomPricingFactor]:
        for_date = self.context.pop('for_date', None)
        if for_date:
            return exported_item.custom_pricing_factor_valid_for_date
        return exported_item.custom_pricing_factor_valid_for_today

    def get_price_per_unit(self, exported_item: PricingFactorItem) -> Decimal:
        custom_pricing_factors = self._get_custom_pricing_factors(exported_item)
        if custom_pricing_factors:
            # TODO: ask accounting if we take Avg or Max
            price_avg = Decimal(
                sum(cpf.price for cpf in custom_pricing_factors)
                / len(custom_pricing_factors)
            )
            return price_avg.quantize(Decimal('.001'))
        return exported_item.price

    def get_loss_factor(self, exported_item: PricingFactorItem) -> Decimal:
        custom_pricing_factors = exported_item.custom_pricing_factor_valid_for_today
        loss_factor_percentage = exported_item.loss_factor
        if custom_pricing_factors:
            # TODO: ask accounting if we take Avg or Max
            loss_factor_percentage = Decimal(
                sum(cpf.loss_factor for cpf in custom_pricing_factors)
                / len(custom_pricing_factors)
            )
        loss_factor = loss_factor_percentage / Decimal(100) + Decimal(1)
        return loss_factor.quantize(Decimal('.001'))

    def get_management_cost(self, exported_item: PricingFactorItem) -> Decimal:
        manufactor_ids = [
            item.manufactor_id
            for item in exported_item.elementmanagedinfo_valid_for_today
        ]
        if manufactor_ids:
            # TODO: ask accounting if we take Avg or Max
            management_cost = (
                MaterialManagementCost.objects.valid_for_today()
                .filter(manufactor_id__in=manufactor_ids)
                .aggregate(management_cost_avg=Avg('management_cost'))[
                    'management_cost_avg'
                ]
            )
            if management_cost:
                return management_cost.quantize(Decimal('.001'))
        return Decimal('0.00')

    def get_unit(self, exported_item: PricingFactorItem) -> str:
        return MeasurementUnit(
            exported_item.measurement_unit,
        ).to_polish()
