from rest_framework import serializers
from rest_framework.exceptions import ValidationError

from producers.models import Manufactor
from production_margins.data_management.serializers.fields import (
    DateFieldWithEmptyString,
)
from production_margins.data_management.serializers.validators import (
    UniqueForDateRangeValidator,
)
from production_margins.models import (
    CustomPricingFactor,
    ManufacturerCode,
    PricingFactorItem,
)

CUSTOM_PRICING_FACTORS_EXPORT_COLUMNS: list[str] = [
    'id',
    'codename',
    'manufacturer',
    'price',
    'loss_factor',
    'manufacturer_code',
    'date_from',
    'date_to',
    'description',
    'conversion',
    'note',
]


class CustomPricingFactorsExportSerializer(serializers.ModelSerializer):
    codename = serializers.SlugRelatedField(
        queryset=PricingFactorItem.objects.all(),
        source='pricing_factor_item',
        slug_field='codename',
    )
    manufacturer = serializers.SlugRelatedField(
        queryset=Manufactor.objects.all(),
        source='manufactor',
        slug_field='name',
    )
    description = serializers.CharField(
        source='pricing_factor_item.description',
        read_only=True,
    )
    conversion = serializers.CharField(
        source='pricing_factor_item.conversion',
        read_only=True,
    )
    manufacturer_code = serializers.SlugRelatedField(
        queryset=ManufacturerCode.objects.all(),
        slug_field='code',
        allow_null=True,
        required=False,
    )
    date_to = DateFieldWithEmptyString(allow_null=True, required=False)

    class Meta:
        model = CustomPricingFactor
        fields = CUSTOM_PRICING_FACTORS_EXPORT_COLUMNS
        validators = [
            UniqueForDateRangeValidator(
                model_class=CustomPricingFactor,
                lookup_fields=('pricing_factor_item', 'manufactor'),
                value_fields=('price', 'loss_factor'),
            ),
        ]


class CustomPricingFactorsImportSerializer(serializers.ModelSerializer):
    codename = serializers.SlugRelatedField(
        queryset=PricingFactorItem.objects.all(),
        source='pricing_factor_item',
        slug_field='codename',
    )
    manufacturer = serializers.SlugRelatedField(
        queryset=Manufactor.objects.all(),
        source='manufactor',
        slug_field='name',
    )
    description = serializers.CharField(
        source='pricing_factor_item.description',
        read_only=True,
    )
    conversion = serializers.CharField(
        source='pricing_factor_item.conversion',
        read_only=True,
    )
    manufacturer_code = serializers.SlugRelatedField(
        queryset=ManufacturerCode.objects.all(),
        slug_field='code',
        allow_null=True,
        required=False,
    )
    date_to = DateFieldWithEmptyString(allow_null=True, required=False)

    class Meta:
        model = CustomPricingFactor
        fields = CUSTOM_PRICING_FACTORS_EXPORT_COLUMNS
        validators = [
            UniqueForDateRangeValidator(
                model_class=CustomPricingFactor,
                lookup_fields=('pricing_factor_item', 'manufactor'),
                value_fields=('price', 'loss_factor'),
            ),
        ]

    def validate_manufacturer_code(self, value):
        """
        Used for CSV import, when it's too late to validate on `save`.

        Validate that the manufacturer code in this CPF is already assigned
          to this CPF's PFI.
        """
        if not value:
            return value
        manufacturer_code_codenames = value.pricing_factor_item.values_list(
            'codename',
            flat=True,
        )
        codename = self.get_initial()['codename']
        if codename not in manufacturer_code_codenames:
            raise ValidationError(f'Incorrect manufacturer code for {codename}')
        return value
