from rest_framework import serializers

from producers.models import Manufactor
from production_margins.data_management.serializers.fields import (
    DateFieldWithEmptyString,
)
from production_margins.data_management.serializers.validators import (
    UniqueForDateRangeValidator,
)
from production_margins.models import (
    ElementManagedInfo,
    PricingFactorItem,
)

ELEMENT_MANAGED_INFO_EXPORT_COLUMNS: list[str] = [
    'id',
    'codename',
    'managed_by_us',
    'date_from',
    'date_to',
    'manufacturer',
]


class ElementManagedInfoExportSerializer(serializers.ModelSerializer):
    codename = serializers.SlugRelatedField(
        queryset=PricingFactorItem.objects.all(),
        source='pricing_factor_item',
        slug_field='codename',
    )
    manufacturer = serializers.SlugRelatedField(
        queryset=Manufactor.objects.all(),
        source='manufactor',
        slug_field='name',
    )
    date_to = DateFieldWithEmptyString(allow_null=True, required=False)

    class Meta:
        model = ElementManagedInfo
        fields = ELEMENT_MANAGED_INFO_EXPORT_COLUMNS
        validators = [
            UniqueForDateRangeValidator(
                model_class=ElementManagedInfo,
                lookup_fields=('pricing_factor_item', 'manufactor'),
                value_fields=('managed_by_us',),
            ),
        ]
