from rest_framework import serializers


class DateFieldWithEmptyString(serializers.DateField):
    def to_internal_value(self, value):
        if value == '':
            return None
        return super().to_internal_value(value)


class ManyRelatedFieldWithCommaSeparatedValue(serializers.ManyRelatedField):
    def __init__(
        self,
        representation_field='pk',
        child_relation=None,
        *args,
        **kwargs,
    ):
        self.representation_field = representation_field
        super().__init__(child_relation=child_relation, *args, **kwargs)

    def to_representation(self, iterable):
        return ','.join(
            str(value)
            for value in iterable.values_list(
                self.representation_field,
                flat=True,
            )
        )


class ManyRelatedFieldWithSpaceSeparatedValue(serializers.ManyRelatedField):
    def __init__(
        self,
        representation_field='pk',
        child_relation=None,
        *args,
        **kwargs,
    ):
        self.representation_field = representation_field
        super().__init__(child_relation=child_relation, *args, **kwargs)

    def to_representation(self, iterable):
        return ' '.join(
            str(value)
            for value in iterable.values_list(
                self.representation_field,
                flat=True,
            )
        )
