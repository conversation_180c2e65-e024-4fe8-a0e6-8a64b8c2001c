from rest_framework import serializers
from rest_framework.relations import SlugRelatedField

from production_margins.data_management.serializers.fields import (
    ManyRelatedFieldWithSpaceSeparatedValue,
)
from production_margins.models import (
    ManufacturerCode,
    PricingFactorItem,
)

MANUFACTURER_CODE_EXPORT_COLUMNS: list[str] = [
    'id',
    'code',
    'name',
    'pricing_factor_item',
    'carbon_footprint',
]


class ManufacturerCodeSerializer(serializers.ModelSerializer):
    pricing_factor_item = ManyRelatedFieldWithSpaceSeparatedValue(
        representation_field='codename',
        child_relation=SlugRelatedField(
            slug_field='codename',
            queryset=PricingFactorItem.objects.all(),
        ),
    )

    class Meta:
        model = ManufacturerCode
        fields = MANUFACTURER_CODE_EXPORT_COLUMNS
