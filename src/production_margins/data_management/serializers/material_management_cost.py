from rest_framework import serializers

from producers.models import Manufactor
from production_margins.data_management.serializers.fields import (
    DateFieldWithEmptyString,
)
from production_margins.data_management.serializers.validators import (
    UniqueForDateRangeValidator,
)
from production_margins.models import MaterialManagementCost

MATERIAL_MANAGEMENT_COST_EXPORT_COLUMNS: list[str] = [
    'id',
    'management_cost',
    'date_from',
    'date_to',
    'manufacturer',
]


class MaterialManagementCostExportSerializer(serializers.ModelSerializer):
    manufacturer = serializers.SlugRelatedField(
        queryset=Manufactor.objects.all(),
        source='manufactor',
        slug_field='name',
    )
    date_to = DateFieldWithEmptyString(allow_null=True, required=False)

    class Meta:
        model = MaterialManagementCost
        fields = MATERIAL_MANAGEMENT_COST_EXPORT_COLUMNS
        validators = [
            UniqueForDateRangeValidator(
                model_class=MaterialManagementCost,
                lookup_fields=('manufactor',),
                value_fields=('management_cost',),
            ),
        ]
