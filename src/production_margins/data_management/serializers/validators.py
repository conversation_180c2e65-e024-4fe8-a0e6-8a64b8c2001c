from typing import Type

from django.db.models import (
    Model,
    Q,
)

from production_margins.data_management.import_errors import (
    ConflictingItemValidationError,
    DuplicatedItemValidationError,
    WrongDatesRangeValidationError,
)


class UniqueForDateRangeValidator:
    """
    Validates if the serialized item does not overlap with an existing item in db.

    If an item with overlapping date range and matching lookup fields exists,
    checks if value fields' content match and raises either
    DuplicatedItemValidationError or ConflictingItemValidationError.
    """

    def __init__(
        self,
        model_class: Type[Model],
        lookup_fields=None,
        value_fields=None,
        exclude_params=None,
    ):
        self.model_class = model_class
        self._lookup_fields = set(lookup_fields or ())
        self._value_fields = value_fields or ()
        self._exclude_params = exclude_params or {}

    def __call__(self, data):
        overlapping_items = self._get_overlapping_items(data)
        if not self._are_dates_correct(data):
            raise WrongDatesRangeValidationError()
        elif overlapping_items and self._is_duplicate(overlapping_items.first(), data):
            raise DuplicatedItemValidationError()
        elif overlapping_items:
            raise ConflictingItemValidationError()
        return data

    def _are_dates_correct(self, data):
        date_from = data.get('date_from')
        date_to = data.get('date_to')
        if date_from and date_to:
            return date_from <= date_to
        return True

    def _get_overlapping_items(self, data):
        query_params = {
            field: data.get(field.split('__')[0])
            for field in self._lookup_fields
            if data.get(field.split('__')[0]) is not None
        }
        overlapping_dates_query = self._get_dates_overlapping_query(data)

        return (
            self.model_class.objects.filter(**query_params)
            .filter(overlapping_dates_query)
            .exclude(**self._exclude_params)
        )

    def _get_dates_overlapping_query(self, data):
        query = Q()
        if data.get('date_to'):
            query &= Q(date_from__lte=data.get('date_to'))
        if data.get('date_from'):
            query &= Q(date_to__gte=data.get('date_from')) | Q(date_to__isnull=True)
        return query

    def _is_duplicate(self, overlapping_item, data):
        return all(
            getattr(overlapping_item, field) == data.get(field)
            for field in self._value_fields
        )
