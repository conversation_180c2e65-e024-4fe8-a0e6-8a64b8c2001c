import numpy as np
import pandas as pd

from production_margins.models import PricingFactorItem


def reporting_material_from_pfi() -> pd.DataFrame:
    pfis = PricingFactorItem.objects.filter(material_type__isnull=False)
    df = pd.DataFrame(
        list(pfis.values('codename', 'material_type')),
    )
    df.rename(columns={'material_type': 'material'}, inplace=True)
    return df


def _custom_loss_factor(row):
    """
    Custom loss factor for codenames.

    We do not count the actual size of the cardboard box ("z-fold" codename)
     used to pack the package, but the entire used cardboard,
     so we subtract the average "z-fold" codenames waste
     so as not to pay more ecotax than necessary.
    """
    z_fold_avg_loss_factor = 0.189
    if 'z-fold' in row['codename']:
        return 1 - z_fold_avg_loss_factor
    return 1


def calculate_ecotax(
    sales_report: pd.DataFrame,
    pricing_factors: pd.DataFrame,
) -> pd.DataFrame:
    codename_materials = reporting_material_from_pfi()
    conversion_coefficients = pricing_factors['weight_per_unit']
    ecotax_data = pd.merge(codename_materials, sales_report, how='inner')

    # multiply by correct coefficient to get correct weight
    ecotax_data['result_quantity'] = ecotax_data.apply(
        lambda row: (
            row['summary_quantity']
            * conversion_coefficients[row['codename']]
            * _custom_loss_factor(row)
        ),
        axis=1,
    )
    # get only useful cols
    ecotax_data = ecotax_data[
        ['country', 'material', 'product_id', 'sold_price', 'result_quantity']
    ]

    # Sum quantity for same category in given furniture
    result_df = ecotax_data.groupby(
        ['country', 'material', 'sold_price', 'product_id'],
        as_index=False,
    )['result_quantity'].sum()
    # Aggregate information about used materials per country
    result_df = result_df.groupby(['country', 'material'], as_index=False,).agg(
        {
            'product_id': np.size,
            'sold_price': np.sum,
            'result_quantity': np.sum,
        }
    )
    # Pivot to move material information from row to column
    pivoted_df = result_df.pivot(
        index=['country'],
        columns='material',
        values='result_quantity',
    )
    pivoted_df = pivoted_df.reset_index()
    result_df = pd.merge(pivoted_df, result_df, how='left')
    result_df = (
        result_df.sort_values('product_id', ascending=False)
        .drop_duplicates('country')
        .sort_index()
    )

    result_df.rename(
        columns={'product_id': 'count', 'sold_price': 'sum_price'},
        inplace=True,
    )
    result_df.drop(columns=['result_quantity'], inplace=True)

    return result_df
