import datetime

import numpy as np

from celery.utils.log import get_task_logger

from orders.enums import OrderType
from orders.internal_api.clients import LogisticOrderAPIClient
from producers.models import Product
from production_margins.codenames_report import generate_codenames_report
from production_margins.exceptions import CodenamesReportError

task_logger = get_task_logger('celery_task')

ELECTRO_CODENAMES = [
    'fitting_electric_wire_led-strip-9-6',
    'fitting_electric_wire_led-strip-4-8',
    'fitting_electric_wire_extension-cord-2m',
    'fitting_electric_wire_extension-cord-1m',
    'fitting_electric_wire_extension-cord-0-5m',
    'fitting_electric_wire_reed-switch-set',
    'fitting_electric_wire_reed-switch',
    'fitting_electric_distributor_6-slot',
    'fitting_electric_power-supply_24V-20W',
    'fitting_electric_power-supply_24V-40W',
    'fitting_electric_power-supply_24V-90W',
    'fitting_electric_wire_Y-connector',
    'fitting_electric_wire_UK-plug-2m',
    'fitting_electric_wire_EU-plug-2m',
    'fitting_electric_wire_extension-cc-cord-2m',
    'fitting_electric_led_cover',
    'material_blank_white_led-profile',
    'material_blank_cashmere_led-profile',
    'material_blank_graphite_led-profile',
    'material_blank_pink_led-profile',
    'material_blank_sage-green_led-profile',
    'material_blank_misty-blue_led-profile',
    'material_blank_stone-gray_led-profile',
    'fitting_electric_magnet_neodymium-13x10x5',
]


def calculate_electrotax(
    start_date: datetime.date,
    end_date: datetime.date,
) -> tuple:
    logistic_order_api_client = LogisticOrderAPIClient()
    # NOTE: using eco_tax here, as it's the same way to filter logistic orders
    #  in ecotax and electrotax
    order_ids = logistic_order_api_client.filter_orders_for_eco_tax(
        start_date, end_date
    )
    products = (
        Product.objects.filter(
            order_id__in=order_ids,
            has_lighting=True,
        )
        .exclude(order__order_type=OrderType.COMPLAINT)
        .prefetch_related(
            'order_item',
            'order_item__order_item',
            'order_item__region',
            'product_status_history',
            'product_details_jetty',
            'product_details_watty',
        )
        .select_related(
            'order',
        )
    )
    try:
        codenames_report, products_with_errors = generate_codenames_report(
            products=products,
            is_original_serialization=True,
            is_aggregated=False,
        )
    except AttributeError as ex:
        task_logger.error(
            'Margins report: every product out of %s has failed',
            len(products),
        )
        raise CodenamesReportError(
            'Every product has failed while generating '
            + 'codenames_report for electrotax report',
        ) from ex

    electro_report = codenames_report[codenames_report.codename.isin(ELECTRO_CODENAMES)]
    electro_report = electro_report[['country', 'codename', 'codename_weight']]
    grouped_electro = electro_report.groupby(
        ['country', 'codename'],
        as_index=True,
    ).agg({'codename_weight': np.sum})
    return grouped_electro, products_with_errors
