import enum

from custom.enums import ChoicesMixin


class ElementsOrderType(ChoicesMixin, enum.IntEnum):
    NEW_ORDER = 1
    COMPLAINT = 2
    COMPLAINT_MANUFACTOR = 3
    INVOICE_TO_ORDER = 4
    S93_PACKAGING = 5


STANDARD_ELEMENTS_ORDER_TYPES = [
    ElementsOrderType.NEW_ORDER.value,
    ElementsOrderType.COMPLAINT.value,
]


class PricingFactorItemStatus(ChoicesMixin, enum.Enum):
    ARCHIVED = 'ARCHIVED'
    ACTIVE = 'ACTIVE'


class MaterialInfoAttachmentState(enum.IntEnum):
    VALID = 0
    OPTIONAL = -1
    SOON_TO_EXPIRE = -2
    EXPIRED = -3
    MISSING = -4

    def details(self):
        return [
            # (color, title),
            ('green', 'Attachment present and valid.'),
            ('gray', 'Attachment is optional.'),
            ('purple', 'Attachment will expire soon.'),
            ('yellow', 'Attachment is already expired.'),
            ('red', 'Missing attachment!'),
        ][-self.value]
