import io
import tempfile

from collections import OrderedDict
from typing import List

from django.db.models import Value

from openpyxl.workbook import Workbook

from production_margins.choices import MeasurementUnit
from production_margins.utils import auto_adjust_columns_on_worksheet


class CustomPricingFactorXLSXReport:
    headers = (
        'index',
        'codename',
        'polska nazwa',
        'J.m.',
        '<PERSON>to waluta domy<PERSON>lna',
        'Wal<PERSON>',
        'date_from',
        'date_to',
        'Producent',
        'notatka',
    )

    def __init__(self, queryset, filename):
        self.filename = filename
        self.queryset = queryset

        self._init_workbook()

    def _init_workbook(self):
        self.workbook = Workbook()
        self.sheet = self.workbook.active
        self.sheet.title = 'Custom pricing factor report'

    def _append_data(self):
        self.sheet.append(self.headers)

        for row_data in self.data:
            self.sheet.append(row_data)

    def _format_cpf_list(self, cpf_data_list) -> List:
        for cpf in cpf_data_list:
            for field_name, pricing_factor_value in cpf.items():
                if field_name == 'pricing_factor_item__measurement_unit':
                    cpf[field_name] = MeasurementUnit(
                        pricing_factor_value
                    ).to_polish_admin_display_only()

        return [list(cpf.values()) for cpf in cpf_data_list]

    def _order_queryset_with_keys(self, key_list, cpf_queryset) -> List:
        result_cpf_list = []
        for cpf in cpf_queryset:
            ordered = OrderedDict((key, cpf[key]) for key in key_list)
            result_cpf_list.append(ordered)
        return result_cpf_list

    @property
    def data(self):
        key_list = (
            'manufacturer_code__code',
            'pricing_factor_item__codename',
            'manufacturer_code__name',
            'pricing_factor_item__measurement_unit',
            'price',
            'currency',
            'date_from',
            'date_to',
            'manufactor__name',
            'note',
        )

        cpf_ordered_list = self._order_queryset_with_keys(
            key_list, self.queryset.annotate(currency=Value('PLN')).values(*key_list)
        )

        return self._format_cpf_list(cpf_ordered_list)

    def generate_report(self):
        self._append_data()
        auto_adjust_columns_on_worksheet(self.sheet)

        with tempfile.NamedTemporaryFile() as f:
            self.workbook.save(f.name)
            f.seek(0)
            return io.BytesIO(f.read())
