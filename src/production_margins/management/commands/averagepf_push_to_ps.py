from django.core.management import BaseCommand

from producers.production_system_utils.client import ProductionSystemClient
from production_margins.data_management.exporters import AveragePricingFactorsExporter


class Command(BaseCommand):
    def handle(self, *args, **options):
        """Sends a request with APF data to Price Factors create endpoint in PS."""
        average_pricing_factors_dict = AveragePricingFactorsExporter().to_dict()
        with ProductionSystemClient(suppress_errors=True) as ps_client:
            ps_client.price_factors_create(
                pricing_factors_serialized_data=average_pricing_factors_dict,
            )
