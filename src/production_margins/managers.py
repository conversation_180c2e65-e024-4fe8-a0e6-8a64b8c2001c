from datetime import datetime

from django.db import models
from django.db.models import Q

from producers.enums import CustomPricingFactorChangeRequestStatusEnum


class ElementsOrderManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(deleted=False)

    def all_orders(self):
        return self._queryset_class(model=self.model, using=self._db, hints=self._hints)


class ElementsOrderHistoryManager(models.Manager):
    def get_queryset(self):
        return super().get_queryset().filter(deleted=True)


class CustomPricingFactorChangeRequestManager(models.Manager):
    @staticmethod
    def accept_change_request(obj):
        if not obj.can_be_accepted():
            return False, f'Change request {obj.request_number} cannot be accepted'

        obj.update_and_create_custom_pricing_factor()
        obj.status = CustomPricingFactorChangeRequestStatusEnum.ACCEPTED.value
        obj.save()
        return True, None

    def generate_request_number(self, user):
        from producers.models import Manufactor

        manufacturer = Manufactor.objects.filter(
            Q(owner=user) | Q(accounts__user=user)
        ).first()

        if not manufacturer:
            raise ValueError('No manufacturer found for the given user.')

        current_date = datetime.now()
        manufacturer_name = manufacturer.name.replace(' ', '_').upper()

        request_number_prefix = (
            f'{current_date.year}/{current_date.month}/'
            f'{current_date.day}/{manufacturer_name}/'
        )

        last_request_number = (
            self.model.objects.filter(request_number__startswith=request_number_prefix)
            .values_list('request_number', flat=True)
            .last()
        )

        last_request_number = (
            int(last_request_number.split('/')[-1]) + 1 if last_request_number else 1
        )

        return f'{request_number_prefix}{last_request_number}'
