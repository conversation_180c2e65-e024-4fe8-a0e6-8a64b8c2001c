# Generated by Django 4.1.9 on 2023-10-27 16:30

import django.core.files.storage
import django.core.validators
import django.db.models.deletion
import django.utils.timezone

from django.conf import settings
from django.db import (
    migrations,
    models,
)

import custom.models.fields
import producers.choices
import producers.enums


class Migration(migrations.Migration):

    replaces = [
        ('production_margins', '0001_initial'),
        ('production_margins', '0002_add_cash_flow_related_models'),
        ('production_margins', '0003_added_new_fields_to_cash_flow_feature'),
        ('production_margins', '0004_add_date_fields_to_management_model'),
        ('production_margins', '0005_django31_jsonfield'),
        ('production_margins', '0006_elementsorder_total_cost'),
        ('production_margins', '0007_new_update_to_dir'),
        ('production_margins', '0008_extend_file_name_with_hash'),
        ('production_margins', '0009_add_elements_order_history'),
        ('production_margins', '0010_remove_groupproduct'),
        ('production_margins', '0011_elementsorder_manufactor_fault'),
        ('production_margins', '0011_alter_elementmanagedinfo_date_to'),
        ('production_margins', '0012_merge_20220311_1457'),
        ('production_margins', '0013_elementsorder_guilty_manufactor'),
        ('production_margins', '0014_create_pricing_factor_item'),
        ('production_margins', '0015_match_price_factor_item'),
        ('production_margins', '0016_disallow_nulls_pfi_related_objects'),
        ('production_margins', '0017_add_description_fields_to_pfi'),
        ('production_margins', '0018_add_pricingfactoritem_codename_validation'),
        ('production_margins', '0019_nullable_date_to'),
        ('production_margins', '0020_standarize_loss_factor_to_percentage_value'),
        ('production_margins', '0021_alter_custompricingfactor_price'),
        ('production_margins', '0022_unique_pricingfactoritem_codename'),
        ('production_margins', '0023_elementsorder_service_cost'),
        ('production_margins', '0024_add_manufacturer_code'),
        ('production_margins', '0025_alter_elementsorder_order_file'),
        ('production_margins', '0026_add_pfi_to_manufacturer_code'),
        ('production_margins', '0027_alter_custompricingfactor_price'),
        ('production_margins', '0028_elementsorder_invoice_number'),
        ('production_margins', '0029_alter_elementsorder_invoice_number'),
        ('production_margins', '0030_elementsorder_file_status'),
        ('production_margins', '0031_pricingfactoritem_archival'),
        ('production_margins', '0032_manufacturercode_carbon_footprint'),
    ]

    initial = True

    dependencies = [
        ('producers', '0001_squashed_0039_product_has_plus_feature'),
        ('gallery', '0001_initial'),
        (
            'producers',
            '0041_remove_priority_complaint_for_products_squashed_0091_alter_manufactor_invoice_company_name_and_more',  # noqa E501
        ),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='PricingFactors',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(max_length=50)),
                ('description', models.TextField(blank=True, null=True)),
                ('data', models.JSONField(blank=True, null=True)),
                (
                    'norm',
                    models.IntegerField(
                        choices=[(1, 'NORM_v1'), (2, 'NORM_v2')], default=2
                    ),
                ),
                ('active', models.BooleanField(default=False)),
                (
                    'activated_at',
                    models.DateTimeField(
                        blank=True, null=True, verbose_name='Factors takes effect at'
                    ),
                ),
                (
                    'deactivated_at',
                    models.DateTimeField(
                        blank=True, null=True, verbose_name='Factors takes effect till'
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(
                        db_index=True,
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name='Created at',
                    ),
                ),
                (
                    'updated_at',
                    models.DateTimeField(
                        blank=True, null=True, verbose_name='Updated at'
                    ),
                ),
                (
                    'status',
                    models.IntegerField(
                        choices=[
                            (0, 'DRAFT'),
                            (1, 'PLANNING'),
                            (2, 'REPORTING'),
                            (3, 'AUTO'),
                            (4, 'USED_IN_PRODUCTION'),
                            (5, 'SERIALIZATION_VERSIONING'),
                        ],
                        default=0,
                    ),
                ),
                (
                    'created_by',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                'verbose_name': 'Price Factors',
                'ordering': ('created_at',),
            },
        ),
        migrations.CreateModel(
            name='GroupProduct',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('name', models.CharField(max_length=150)),
                ('description', models.TextField(blank=True)),
                (
                    'objects_type',
                    models.IntegerField(
                        choices=[(0, 'Jetty'), (1, 'Product')], default=0
                    ),
                ),
                ('date_lt', models.DateField(blank=True, null=True)),
                ('date_gte', models.DateField(blank=True, null=True)),
                ('reporting_data_f01', models.JSONField(blank=True, null=True)),
                ('reporting_data_t01', models.JSONField(blank=True, null=True)),
                ('reporting_data_t02', models.JSONField(blank=True, null=True)),
                ('amount_f01', models.IntegerField(blank=True, null=True)),
                ('amount_t01', models.IntegerField(blank=True, null=True)),
                ('amount_t02', models.IntegerField(blank=True, null=True)),
                ('gmv', models.FloatField(blank=True, null=True)),
                ('gmv_f01', models.FloatField(blank=True, null=True)),
                ('gmv_f01_sandbox', models.FloatField(blank=True, null=True)),
                ('gmv_sandbox', models.FloatField(blank=True, null=True)),
                ('gmv_t01', models.FloatField(blank=True, null=True)),
                ('gmv_t01_sandbox', models.FloatField(blank=True, null=True)),
                ('gmv_t02', models.FloatField(blank=True, null=True)),
                ('gmv_t02_sandbox', models.FloatField(blank=True, null=True)),
                ('gmv_from_fixed_eur', models.FloatField(blank=True, null=True)),
                (
                    'gmv_from_fixed_eur_sandbox',
                    models.FloatField(blank=True, null=True),
                ),
                ('nmvv', models.FloatField(blank=True, null=True)),
                ('nmvv_sandbox', models.FloatField(blank=True, null=True)),
                ('nmvv_atypical_orders', models.FloatField(blank=True, null=True)),
                ('cogs', models.FloatField(blank=True, null=True)),
                ('logs', models.FloatField(blank=True, null=True)),
                ('logs_sandbox', models.FloatField(blank=True, null=True)),
                ('vogs', models.FloatField(blank=True, null=True)),
                ('m1', models.FloatField(blank=True, null=True)),
                ('m1_f01', models.FloatField(blank=True, null=True)),
                ('m1_f01_sandbox', models.FloatField(blank=True, null=True)),
                ('m1_sandbox', models.FloatField(blank=True, null=True)),
                ('m1_t01', models.FloatField(blank=True, null=True)),
                ('m1_t01_sandbox', models.FloatField(blank=True, null=True)),
                ('m1_t02', models.FloatField(blank=True, null=True)),
                ('m1_t02_sandbox', models.FloatField(blank=True, null=True)),
                ('m2', models.FloatField(blank=True, null=True)),
                ('m2_f01', models.FloatField(blank=True, null=True)),
                ('m2_f01_sandbox', models.FloatField(blank=True, null=True)),
                ('m2_sandbox', models.FloatField(blank=True, null=True)),
                ('m2_t01', models.FloatField(blank=True, null=True)),
                ('m2_t01_sandbox', models.FloatField(blank=True, null=True)),
                ('m2_t02', models.FloatField(blank=True, null=True)),
                ('m2_t02_sandbox', models.FloatField(blank=True, null=True)),
                (
                    'group_type',
                    models.IntegerField(
                        choices=[
                            (0, 'CUSTOM'),
                            (1, 'DAY'),
                            (2, 'WEEK'),
                            (3, 'MONTH'),
                            (4, 'QUARTER'),
                            (5, 'YEAR'),
                            (6, 'JOINED'),
                            (7, 'BATCH'),
                            (8, 'LAST_7'),
                            (9, 'LAST_28'),
                        ],
                        default=0,
                    ),
                ),
                ('date_start', models.DateField(blank=True, null=True)),
                ('date_end', models.DateField(blank=True, null=True)),
                (
                    'status',
                    models.IntegerField(
                        choices=[
                            (0, 'DRAFT'),
                            (1, 'PLANNING'),
                            (2, 'REPORTING'),
                            (3, 'AUTO'),
                            (4, 'USED_IN_PRODUCTION'),
                            (5, 'SERIALIZATION_VERSIONING'),
                        ],
                        default=0,
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(
                        db_index=True,
                        default=django.utils.timezone.now,
                        editable=False,
                        verbose_name='Created at',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Groups product',
                'ordering': ('created_at',),
            },
        ),
        migrations.CreateModel(
            name='ProductGroupSummary',
            fields=[],
            options={
                'verbose_name': 'ProductGroup Summary',
                'verbose_name_plural': 'Product Group Summary',
                'proxy': True,
                'indexes': [],
            },
            bases=('production_margins.groupproduct',),
        ),
        migrations.CreateModel(
            name='CustomPricingFactor',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('codename', models.CharField(max_length=255)),
                (
                    'price',
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=12,
                        null=True,
                        verbose_name='Element cost (PLN)',
                    ),
                ),
                (
                    'loss_factor',
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=5,
                        null=True,
                        verbose_name='Element loss factor (Percentage)',
                    ),
                ),
                ('date_from', models.DateField()),
                ('date_to', models.DateField()),
                (
                    'manufactor',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='producers.manufactor',
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='ElementManagedInfo',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('codename', models.CharField(max_length=255)),
                ('managed_by_us', models.BooleanField(default=False)),
                ('date_from', models.DateField(default=django.utils.timezone.now)),
                ('date_to', models.DateField(blank=True, null=True)),
                (
                    'manufactor',
                    models.ForeignKey(
                        default=1,
                        on_delete=django.db.models.deletion.CASCADE,
                        to='producers.manufactor',
                    ),
                ),
            ],
            options={
                'verbose_name': 'Element managed info',
                'verbose_name_plural': 'Element managed info',
            },
        ),
        migrations.CreateModel(
            name='MaterialManagementCost',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'management_cost',
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=5,
                        null=True,
                        verbose_name='Element loss factor (Percentage)',
                    ),
                ),
                (
                    'manufactor',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='producers.manufactor',
                    ),
                ),
                ('date_from', models.DateField(default=django.utils.timezone.now)),
                ('date_to', models.DateField(blank=True, null=True)),
            ],
        ),
        migrations.CreateModel(
            name='ElementsOrder',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'order_file',
                    custom.models.fields.FileFieldWithHash(
                        storage=django.core.files.storage.FileSystemStorage(),
                        upload_to='production_margins/elements_order/%Y/%m',
                    ),
                ),
                ('batches', models.ManyToManyField(to='producers.productbatch')),
                ('generated_at', models.DateTimeField(auto_now=True)),
                (
                    'total_cost',
                    models.DecimalField(decimal_places=2, default=0, max_digits=16),
                ),
                ('deleted', models.BooleanField(default=False)),
                (
                    'new_report',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='production_margins.elementsorder',
                    ),
                ),
                ('manufactor_fault', models.BooleanField(default=False)),
                (
                    'guilty_manufactor',
                    models.ForeignKey(
                        blank=True,
                        null=True,
                        on_delete=django.db.models.deletion.SET_NULL,
                        to='producers.manufactor',
                    ),
                ),
                (
                    'service_cost',
                    models.DecimalField(decimal_places=2, default=0, max_digits=16),
                ),
                ('invoice_number', models.CharField(blank=True, max_length=100)),
                (
                    'file_status',
                    models.IntegerField(
                        choices=[
                            (0, 'INITIAL'),
                            (1, 'DOWNLOADED'),
                            (2, 'RECALCULATED_AFTER_DOWNLOAD'),
                        ],
                        default=producers.choices.ProductionFileStatus['INITIAL'],
                    ),
                ),
            ],
        ),
        migrations.CreateModel(
            name='ElementsOrderHistory',
            fields=[],
            options={
                'verbose_name_plural': 'Elements order histories',
                'proxy': True,
                'indexes': [],
                'constraints': [],
            },
            bases=('production_margins.elementsorder',),
        ),
        migrations.DeleteModel(
            name='ProductGroupSummary',
        ),
        migrations.CreateModel(
            name='PricingFactorItem',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(auto_now_add=True, help_text='Creation date'),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, help_text='Update date'),
                ),
                (
                    'codename',
                    models.CharField(
                        max_length=255,
                        unique=True,
                        validators=[
                            django.core.validators.RegexValidator(
                                message='A codename must consist of four parts divided by "_" sign.',  # noqa
                                regex='^[^_]+_[^_]+_[^_]+_[^_]+$',
                            )
                        ],
                    ),
                ),
                (
                    'category',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, 'Fitting'),
                            (2, 'Material'),
                            (3, 'Packaging'),
                            (4, 'Semiproduct'),
                            (5, 'Service'),
                        ]
                    ),
                ),
                (
                    'weight',
                    models.DecimalField(
                        blank=True,
                        decimal_places=3,
                        max_digits=10,
                        null=True,
                        verbose_name='weight per unit (kg)',
                    ),
                ),
                (
                    'thickness',
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name='thickness (m^(-5))'
                    ),
                ),
                (
                    'length',
                    models.PositiveIntegerField(
                        blank=True, null=True, verbose_name='length (m^(-5))'
                    ),
                ),
                (
                    'measurement_unit',
                    models.PositiveSmallIntegerField(
                        choices=[
                            (1, 'pc'),
                            (2, 'lm'),
                            (3, 'm2'),
                            (4, 'set'),
                            (5, 'm'),
                            (6, 'kg'),
                            (7, 'l'),
                        ]
                    ),
                ),
                (
                    'price',
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=12,
                        null=True,
                        verbose_name='item cost (PLN)',
                    ),
                ),
                (
                    'loss_factor',
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=5,
                        null=True,
                        verbose_name='Item loss factor (Percentage)',
                    ),
                ),
                (
                    'conversion',
                    models.TextField(
                        blank=True, null=True, verbose_name='how to count it'
                    ),
                ),
                (
                    'description',
                    models.TextField(
                        blank=True, null=True, verbose_name='item description'
                    ),
                ),
                ('archived_at', models.DateField(blank=True, null=True)),
                (
                    'status',
                    models.CharField(
                        choices=[('ARCHIVED', 'ARCHIVED'), ('ACTIVE', 'ACTIVE')],
                        default='ACTIVE',
                        max_length=10,
                    ),
                ),
            ],
            options={
                'verbose_name': 'pricing factor item',
                'verbose_name_plural': 'pricing factor items',
            },
        ),
        migrations.CreateModel(
            name='PricingFactorItemImport',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(auto_now_add=True, help_text='Creation date'),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, help_text='Update date'),
                ),
                ('created_count', models.PositiveSmallIntegerField()),
                (
                    'created_by',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
            ],
            options={
                'verbose_name': 'pricing factor item import',
                'verbose_name_plural': 'pricing factor item imports',
            },
        ),
        migrations.AddField(
            model_name='custompricingfactor',
            name='pricing_factor_item',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                to='production_margins.pricingfactoritem',
            ),
        ),
        migrations.AddField(
            model_name='elementmanagedinfo',
            name='pricing_factor_item',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to='production_margins.pricingfactoritem',
            ),
        ),
        migrations.RemoveField(
            model_name='custompricingfactor',
            name='codename',
        ),
        migrations.RemoveField(
            model_name='elementmanagedinfo',
            name='codename',
        ),
        migrations.AlterField(
            model_name='custompricingfactor',
            name='date_to',
            field=models.DateField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='custompricingfactor',
            name='price',
            field=models.DecimalField(
                blank=True,
                decimal_places=5,
                max_digits=12,
                null=True,
                verbose_name='Element cost (PLN)',
            ),
        ),
        migrations.CreateModel(
            name='ManufacturerCode',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                ('code', models.CharField(max_length=20, unique=True)),
                ('name', models.CharField(max_length=255)),
                (
                    'pricing_factor_item',
                    models.ManyToManyField(
                        blank=True, to='production_margins.pricingfactoritem'
                    ),
                ),
                (
                    'carbon_footprint',
                    models.DecimalField(
                        blank=True,
                        decimal_places=2,
                        max_digits=12,
                        null=True,
                        verbose_name='Carbon footprint [t]',
                    ),
                ),
            ],
        ),
        migrations.AddField(
            model_name='custompricingfactor',
            name='note',
            field=models.TextField(blank=True, default=''),
        ),
        migrations.AddField(
            model_name='custompricingfactor',
            name='manufacturer_code',
            field=models.ForeignKey(
                blank=True,
                default=None,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to='production_margins.manufacturercode',
            ),
        ),
        migrations.AlterField(
            model_name='custompricingfactor',
            name='price',
            field=models.DecimalField(
                decimal_places=5,
                max_digits=12,
                null=True,
                verbose_name='Element cost (PLN)',
            ),
        ),
        migrations.DeleteModel(
            name='GroupProduct',
        ),
    ]
