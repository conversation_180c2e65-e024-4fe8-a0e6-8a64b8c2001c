# Generated by Django 4.1.9 on 2023-12-07 11:30

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('producers', '0093_add_brachio_ppv'),
        ('production_margins', '0001_squashed_0032_manufacturercode_carbon_footprint'),
    ]

    operations = [
        migrations.AlterField(
            model_name='elementmanagedinfo',
            name='date_from',
            field=models.DateField(),
        ),
        migrations.AlterField(
            model_name='elementmanagedinfo',
            name='manufactor',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE, to='producers.manufactor'
            ),
        ),
        migrations.AlterField(
            model_name='materialmanagementcost',
            name='date_from',
            field=models.DateField(),
        ),
    ]
