# Generated by Django 4.1.9 on 2024-04-12 09:07

import django.contrib.postgres.fields

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('production_margins', '0034_alter_custompricingfactor_loss_factor_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='elementsorder',
            name='invoice_to_order',
            field=models.BooleanField(default=False),
        ),
        migrations.AddField(
            model_name='elementsorder',
            name='meblepl_products',
            field=django.contrib.postgres.fields.ArrayField(
                base_field=models.IntegerField(), default=list, size=None
            ),
        ),
    ]
