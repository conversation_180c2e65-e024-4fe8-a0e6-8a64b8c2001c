# Generated by Django 4.1.9 on 2024-04-15 21:55

import django.core.files.storage
import django.db.models.deletion

from django.conf import settings
from django.db import (
    migrations,
    models,
)

import custom.models.fields


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('production_margins', '0035_elementsorder_invoice_to_order_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='CustomPricingFactorChangeRequest',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(auto_now_add=True, help_text='Creation date'),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, help_text='Update date'),
                ),
                ('request_number', models.CharField(max_length=20)),
                (
                    'status',
                    models.IntegerField(
                        choices=[
                            (0, 'NEW'),
                            (1, 'ACCEPTED'),
                            (2, 'REJECTED'),
                            (3, 'CHANGES_REQUESTED'),
                        ],
                        default=0,
                    ),
                ),
                (
                    'price',
                    models.DecimalField(
                        decimal_places=5,
                        max_digits=12,
                        null=True,
                        verbose_name='New element cost (PLN)',
                    ),
                ),
                (
                    'manufacturer_code',
                    models.CharField(
                        blank=True, max_length=20, verbose_name='New manufacturer code'
                    ),
                ),
                ('date_from', models.DateField()),
            ],
            options={
                'get_latest_by': 'updated_at',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='CustomPricingFactorChangeRequestAttachment',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(auto_now_add=True, help_text='Creation date'),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, help_text='Update date'),
                ),
                (
                    'file',
                    custom.models.fields.FileFieldWithHash(
                        blank=True,
                        max_length=150,
                        storage=django.core.files.storage.FileSystemStorage(),
                        upload_to='production_margins/custom_pricing_factor_change_request/%Y/%m',  # noqa: E501
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated_at',
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='CustomPricingFactorChangeRequestComment',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'created_at',
                    models.DateTimeField(auto_now_add=True, help_text='Creation date'),
                ),
                (
                    'updated_at',
                    models.DateTimeField(auto_now=True, help_text='Update date'),
                ),
                ('text', models.CharField(blank=True, max_length=255)),
                (
                    'author',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to=settings.AUTH_USER_MODEL,
                    ),
                ),
                (
                    'custom_pricing_factor_change_request',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.CASCADE,
                        to='production_margins.custompricingfactorchangerequest',
                    ),
                ),
            ],
            options={
                'get_latest_by': 'updated_at',
                'abstract': False,
            },
        ),
        migrations.AddField(
            model_name='custompricingfactorchangerequest',
            name='attachment',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                to='production_margins.custompricingfactorchangerequestattachment',
            ),
        ),
        migrations.AddField(
            model_name='custompricingfactorchangerequest',
            name='custom_pricing_factor',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.CASCADE,
                to='production_margins.custompricingfactor',
            ),
        ),
    ]
