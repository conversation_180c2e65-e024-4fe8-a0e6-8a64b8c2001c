# Generated by Django 4.1.9 on 2024-05-21 13:54

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('production_margins', '0036_custompricingfactorchangerequest_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='pricingfactoritem',
            name='material_type',
            field=models.CharField(
                blank=True,
                choices=[
                    ('paper', 'Paper'),
                    ('plastic', 'Plastic'),
                    ('beaverboard', 'Beaverboard'),
                    ('n/a', 'Not Reportable'),
                ],
                help_text='Material type for ecotax reporting purposes',
                max_length=20,
                null=True,
            ),
        ),
    ]
