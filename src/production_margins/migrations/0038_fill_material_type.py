# Generated by Django 4.1.9 on 2024-05-21 14:14
import csv

from django.conf import settings
from django.db import migrations

from production_margins.models import PricingFactorItem


def fill_material_type(apps, schema_editor):
    with open(
        f'{settings.ROOT_PATH}/production_margins/ecotax/codename_materials.csv'
    ) as f:
        materials_data = csv.reader(f, delimiter=',')
        next(materials_data, None)  # skip header
        for row in materials_data:
            PricingFactorItem.objects.filter(codename=row[0]).update(
                material_type=row[1]
            )


class Migration(migrations.Migration):

    dependencies = [
        ('production_margins', '0037_pricingfactoritem_material_type'),
    ]

    operations = [
        migrations.RunPython(
            fill_material_type,
            migrations.RunPython.noop,
            elidable=True,
        )
    ]
