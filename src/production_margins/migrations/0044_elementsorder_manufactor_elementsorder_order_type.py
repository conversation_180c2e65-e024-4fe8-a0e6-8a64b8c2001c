# Generated by Django 4.1.13 on 2025-02-05 15:51

import django.db.models.deletion

from django.db import (
    migrations,
    models,
)
from django.db.models import (
    Case,
    IntegerField,
    Q,
    Value,
    When,
)

from producers.choices import BatchType
from producers.models import Manufactor
from production_margins.enums import ElementsOrderType


def populate_new_fields(apps, schema_editor):
    ElementsOrder = apps.get_model('production_margins', 'ElementsOrder')
    ManufactorModel = apps.get_model('producers', 'Manufactor')
    queryset = ElementsOrder.objects.annotate(
        calculated_order_type=Case(
            When(
                Q(manufactor_fault=True)
                & Q(batches__batch_type__exact=BatchType.COMPLAINTS),
                then=Value(ElementsOrderType.COMPLAINT_MANUFACTOR.value),
            ),
            When(
                batches__batch_type__exact=BatchType.COMPLAINTS,
                then=Value(ElementsOrderType.COMPLAINT.value),
            ),
            When(
                invoice_to_order=True,
                then=Value(ElementsOrderType.INVOICE_TO_ORDER.value),
            ),
            default=Value(ElementsOrderType.NEW_ORDER.value),
            output_field=IntegerField(),
        ),
    ).distinct()
    try:
        meblepl_manufactor = ManufactorModel.objects.get(name=Manufactor.MEBLE_PL)
    except ManufactorModel.DoesNotExist:
        meblepl_manufactor = None
    for element_order in queryset:
        element_order.order_type = element_order.calculated_order_type
        if element_order.order_type == ElementsOrderType.COMPLAINT_MANUFACTOR.value:
            element_order.manufactor = element_order.guilty_manufactor
        elif element_order.order_type == ElementsOrderType.INVOICE_TO_ORDER.value:
            element_order.manufactor = meblepl_manufactor
        elif element_order.batches.exists():
            element_order.manufactor = element_order.batches.first().manufactor

        element_order.save(update_fields=['order_type', 'manufactor'])


class Migration(migrations.Migration):

    dependencies = [
        ('producers', '0114_autobatchingmailingconfiguration_attach_nesting_desk_beam'),
        ('production_margins', '0043_telmexmagazinecode'),
    ]

    operations = [
        migrations.AddField(
            model_name='elementsorder',
            name='manufactor',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='elements_orders',
                to='producers.manufactor',
            ),
        ),
        migrations.AddField(
            model_name='elementsorder',
            name='order_type',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (1, 'NEW_ORDER'),
                    (2, 'COMPLAINT'),
                    (3, 'COMPLAINT_MANUFACTOR'),
                    (4, 'INVOICE_TO_ORDER'),
                    (5, 'S93_PACKAGING'),
                ],
                default=1,
            ),
        ),
        migrations.RunPython(populate_new_fields, migrations.RunPython.noop),
    ]
