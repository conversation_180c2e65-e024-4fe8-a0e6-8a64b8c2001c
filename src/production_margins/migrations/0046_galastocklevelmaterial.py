# Generated by Django 4.1.13 on 2025-03-27 15:57

from django.db import (
    migrations,
    models,
)


class Migration(migrations.Migration):

    dependencies = [
        ('production_margins', '0045_alter_pricingfactoritem_category'),
    ]

    operations = [
        migrations.CreateModel(
            name='GalaStockLevelMaterial',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'index',
                    models.Char<PERSON>ield(max_length=50, verbose_name='Material index'),
                ),
                (
                    'invoice',
                    models.CharField(max_length=100, verbose_name='Invoice number'),
                ),
                (
                    'warehouse',
                    models.CharField(max_length=50, verbose_name='Storage location'),
                ),
                (
                    'quantity',
                    models.DecimalField(
                        decimal_places=2, max_digits=10, verbose_name='Current stock'
                    ),
                ),
                ('entry_date', models.DateTimeField(verbose_name='Date of entry')),
                ('defect', models.BooleanField(default=False, verbose_name='Defect')),
                (
                    'defect_description',
                    models.CharField(
                        blank=True, max_length=255, verbose_name='Defect description'
                    ),
                ),
                (
                    'supplier',
                    models.CharField(
                        blank=True, max_length=50, verbose_name='Supplier code'
                    ),
                ),
            ],
            options={
                'verbose_name': 'Gala stock level material',
                'verbose_name_plural': 'Gala stock level materials',
                'unique_together': {('index', 'invoice')},
            },
        ),
    ]
