# Generated by Django 4.1.13 on 2025-06-25 14:46

import custom.models.fields
import django.core.files.storage
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('production_margins', '0046_galastocklevelmaterial'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='pricingfactors',
            options={
                'ordering': ('created_at',),
                'verbose_name': 'price factors',
                'verbose_name_plural': 'price factors',
            },
        ),
        migrations.AlterField(
            model_name='custompricingfactor',
            name='manufacturer_code',
            field=models.ForeignKey(
                blank=True,
                default=None,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='custom_pricing_factors',
                to='production_margins.manufacturercode',
            ),
        ),
        migrations.CreateModel(
            name='MaterialInfoAttachment',
            fields=[
                (
                    'id',
                    models.AutoField(
                        auto_created=True,
                        primary_key=True,
                        serialize=False,
                        verbose_name='ID',
                    ),
                ),
                (
                    'document_type',
                    models.IntegerField(
                        choices=[
                            (0, 'Any'),
                            (1, 'Picture'),
                            (2, 'Certification'),
                            (3, 'Technical Sketch'),
                            (4, 'Technical Card'),
                            (5, 'REACH'),
                            (6, 'ROHS'),
                            (7, 'Wood Source'),
                            (8, 'Formaldehyde'),
                            (9, 'Golden Sample'),
                        ],
                        default=0,
                    ),
                ),
                ('valid_from', models.DateField()),
                ('valid_to', models.DateField(blank=True, null=True)),
                ('optional', models.BooleanField(default=False)),
                (
                    'file',
                    custom.models.fields.FileFieldWithHash(
                        blank=True,
                        storage=django.core.files.storage.FileSystemStorage(),
                        upload_to='production_margins/material_info/%Y/%m',
                    ),
                ),
                (
                    'manufacturer_code',
                    models.ForeignKey(
                        on_delete=django.db.models.deletion.PROTECT,
                        related_name='attachments',
                        to='production_margins.manufacturercode',
                        to_field='code',
                    ),
                ),
            ],
        ),
    ]
