import datetime
import json
import logging

from copy import deepcopy

from django.conf import settings
from django.contrib.postgres.fields import <PERSON>rrayField
from django.core.exceptions import ValidationError
from django.core.validators import (
    MaxValueValidator,
    RegexValidator,
)
from django.db import (
    models,
    transaction,
)
from django.utils import timezone
from django.utils.translation import gettext_lazy as _

import pandas as pd

from rest_framework import serializers

from cstm_be.media_storage import private_media_storage
from custom.models import (
    FileFieldWithHash,
    Timestampable,
)
from custom.models.managers import DateRangeQuerySet
from custom.utils.decorators import cache_function
from producers.choices import ProductionFileStatus
from producers.enums import CustomPricingFactorChangeRequestStatusEnum
from production_margins.choices import (
    MaterialInfoAttachmentType,
    MeasurementUnit,
    PricingFactorItemCategory,
    PricingFactorNorms,
    PricingFactorStatus,
    ReportingMaterialType,
)
from production_margins.data_management.serializers.validators import (
    UniqueForDateRangeValidator,
)
from production_margins.enums import (
    STANDARD_ELEMENTS_ORDER_TYPES,
    ElementsOrderType,
    PricingFactorItemStatus,
)
from production_margins.managers import (
    CustomPricingFactorChangeRequestManager,
    ElementsOrderHistoryManager,
    ElementsOrderManager,
)

logger = logging.getLogger('cstm')

REQUIRED_COLUMNS = [
    'unit',
    'polish_desc',  # drewtur_desc -> polish_desc,
    'drewtur_erp_code',
    'weight_per_unit',
    'thickness',
    'length',
    'C1_category',
]  # category -> C1_category, 'ivy_raw_material_cat' -> 'C1_category`


class PricingFactors(models.Model):
    name = models.CharField(max_length=50)
    description = models.TextField(blank=True, null=True)
    data = models.JSONField(blank=True, null=True)
    norm = models.IntegerField(
        choices=PricingFactorNorms.choices, default=PricingFactorNorms.NORM_v2
    )

    active = models.BooleanField(default=False)
    activated_at = models.DateTimeField(
        'Factors takes effect at', blank=True, null=True
    )
    deactivated_at = models.DateTimeField(
        'Factors takes effect till', blank=True, null=True
    )

    created_at = models.DateTimeField(
        'Created at', default=timezone.now, editable=False, db_index=True
    )
    updated_at = models.DateTimeField('Updated at', blank=True, null=True)
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
    )

    status = models.IntegerField(
        choices=PricingFactorStatus.choices, default=PricingFactorStatus.DRAFT
    )

    class Meta(object):
        ordering = ('created_at',)
        verbose_name = 'price factors'
        verbose_name_plural = 'price factors'

    def __str__(self):
        return 'Price Factor {} - updated {}'.format(self.name, self.updated_at)

    # ------ DATA ACCESS --------------------------------------------------------------

    @staticmethod
    @cache_function(cache_period=600)
    def get_solo():
        return PricingFactors.objects.filter(active=True).first()

    @staticmethod
    @cache_function(cache_period=600)
    def get_pricing_factors_by_date(date):
        return (
            PricingFactors.objects.filter(activated_at__lte=date)
            .filter(
                models.Q(deactivated_at__gt=date)
                | models.Q(deactivated_at__isnull=True)
            )
            .first()
        )

    @property
    def df(self):
        from production_margins.utils import PricingFactorArchiveRemoval

        df = self._get_pricing_factors_as_df()
        df = self.df_sorter(df)
        archive_removal = PricingFactorArchiveRemoval()
        df = archive_removal.remove_from_data_frame(df)
        return df

    def set_as_active(self):
        now = timezone.now()
        # -- Deaktywuje wszystkie aktualnie aktywne z wyłączeniem aktualnego
        for obj in PricingFactors.objects.filter(active=True):
            if obj.id == self.id:
                continue
            obj.active = False
            obj.deactivated_at = now
            obj.save()
        # -- Aktywuj aktualny obiekt
        if self.activated_at is None:
            self.activated_at = now
        self.active = True
        self.status = 4
        self.save()
        return self

    # ------ HELPERS ------------------------------------------------------------------

    @classmethod
    def pricing_factors_df_validation(cls, pricing_factors_df):
        # Sprawdzenie obecnosci wymaganych kolumn
        if not set(REQUIRED_COLUMNS).issubset(pricing_factors_df.columns):
            return False
        return True

    @staticmethod
    def df_sorter(df):
        # Kolejnosc kolumn oraz kolejność codenames
        columns_order = [
            'ivy_raw_material_cat',
            'category',
            'unit',
            'usage',
            'price_per_unit',
            'total',
            'loss_factor',
            'weight_per_unit',
            'thickness',
            'length',
            'cashflow_name_1',
            'cashflow_price_1',
            'cashflow_name_2',
            'cashflow_price_2',
            'polish_desc',
            'drewtur_erp_code',
            'old_codename',
        ]
        sorter_list = ['ivy_raw_material_cat', 'category', 'codename']

        # Sortowanie DF
        df.index.name = 'codename'
        df = df.reset_index()
        sorter_list = [c for c in sorter_list if c in df.columns]
        df = df.sort_values(sorter_list).set_index('codename')
        df = df[
            [c for c in columns_order if c in df.columns]
            + [c for c in df.columns if c not in columns_order]
        ]
        return df

    @staticmethod
    def df_multiindexer(df, rows=True, columns=True, sort=False):
        # -- Definicje multiindexów
        index_cat = ['ivy_raw_material_cat', 'category', 'codename']
        col_cat = dict(
            ivy_raw_material_cat='categories',
            category='categories',
            unit='attributes',
            price_per_unit='attributes',
            loss_factor='attributes',
            weight_per_unit='attributes',
            thickness='attributes',
            length='attributes',
            cashflow_name_1='cashflow',
            cashflow_price_1='cashflow',
            cashflow_name_2='cashflow',
            cashflow_price_2='cashflow',
            polish_desc='drewtur',
            drewtur_erp_code='drewtur',
            old_codename='drewtur',
            total='_main',
            usage='_main',
        )
        # -- Modyfikacja df
        if rows:
            df = df.reset_index()
            df = df.set_index([c for c in index_cat if c in df.columns])

        if columns:
            columns = [(col_cat.get(col, 'other'), col) for col in df.columns]
            df.columns = pd.MultiIndex.from_tuples(columns)
        if sort:
            df = df.sort_index(axis=0).sort_index(axis=1)
        return df

    def _get_pricing_factors_as_df(self):
        if self.data is None:
            return pd.DataFrame()
        data = json.loads(self.data)
        pricing_factors_df = pd.DataFrame(data=data)
        return pricing_factors_df


class PricingFactorItemImport(Timestampable):
    """
    Class represent pricing factor items import
    """

    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.PROTECT,
    )
    created_count = models.PositiveSmallIntegerField()

    class Meta:
        verbose_name = 'pricing factor item import'
        verbose_name_plural = 'pricing factor item imports'

    def __str__(self):
        return (
            f'Imported objects: {self.created_count} '
            f'({self.created_at} by {self.created_by})'
        )


class PricingFactorItem(Timestampable):
    """
    Class represent pricing factor item
    """

    codename = models.CharField(
        max_length=255,
        validators=[
            RegexValidator(
                regex='^[^_]+_[^_]+_[^_]+_[^_]+$',
                message='A codename must consist of four parts divided by "_" sign.',
            )
        ],
        unique=True,
    )
    category = models.PositiveSmallIntegerField(
        choices=PricingFactorItemCategory.choices,
    )
    material_type = models.CharField(
        max_length=20,
        choices=ReportingMaterialType.choices,
        help_text='Material type for ecotax reporting purposes',
        blank=True,
        null=True,
    )
    weight = models.DecimalField(
        verbose_name='weight per unit (kg)',
        max_digits=10,
        decimal_places=3,
        blank=True,
        null=True,
        validators=[MaxValueValidator(40)],
    )
    thickness = models.PositiveIntegerField(
        verbose_name='thickness (m^(-5))',
        blank=True,
        null=True,
    )
    length = models.PositiveIntegerField(
        verbose_name='length (m^(-5))',
        blank=True,
        null=True,
    )
    measurement_unit = models.PositiveSmallIntegerField(
        choices=MeasurementUnit.choices,
    )
    price = models.DecimalField(
        verbose_name='item cost (PLN)',
        max_digits=12,
        decimal_places=2,
        blank=True,
        null=True,
    )
    loss_factor = models.DecimalField(
        verbose_name='Item loss factor (Percentage)',
        max_digits=5,
        decimal_places=2,
        default=0,
    )
    description = models.TextField(
        verbose_name='item description',
        blank=True,
        null=True,
    )
    conversion = models.TextField(
        verbose_name='how to count it',
        blank=True,
        null=True,
    )
    archived_at = models.DateField(blank=True, null=True)

    status = models.CharField(
        choices=PricingFactorItemStatus.choices(),
        max_length=10,
        default=PricingFactorItemStatus.ACTIVE.value,
    )

    class Meta:
        verbose_name = 'pricing factor item'
        verbose_name_plural = 'pricing factor items'

    def __str__(self):
        return self.codename

    def save(self, *args, **kwargs):
        if self.status == PricingFactorItemStatus.ARCHIVED.value:
            self.archived_at = timezone.now()
        else:
            self.archived_at = None
        super().save(*args, **kwargs)

    @property
    def category_display(self):
        return self.get_category_display()

    @property
    def measurement_unit_display(self):
        return self.get_measurement_unit_display()

    @property
    def c1_category(self):
        return self.codename.split('_')[0]

    @property
    def c2_type(self):
        return self.codename.split('_')[1]

    @property
    def c3_version(self):
        return self.codename.split('_')[2]

    @property
    def c4_description(self):
        return self.codename.split('_')[3]


class ManufacturerCode(models.Model):
    code = models.CharField(unique=True, max_length=20)
    name = models.CharField(max_length=255)
    pricing_factor_item = models.ManyToManyField(
        PricingFactorItem,
        blank=True,
    )
    carbon_footprint = models.DecimalField(
        'Carbon footprint [t]', max_digits=12, decimal_places=2, blank=True, null=True
    )

    def __str__(self):
        return self.code


class CustomPricingFactor(models.Model):
    pricing_factor_item = models.ForeignKey(
        'production_margins.PricingFactorItem',
        on_delete=models.PROTECT,
    )
    manufactor = models.ForeignKey('producers.Manufactor', on_delete=models.CASCADE)
    price = models.DecimalField(
        max_digits=12,
        decimal_places=5,
        null=True,
        verbose_name='Element cost (PLN)',
    )
    loss_factor = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        default=0,
        verbose_name='Element loss factor (Percentage)',
    )
    manufacturer_code = models.ForeignKey(
        ManufacturerCode,
        on_delete=models.SET_NULL,
        null=True,
        default=None,
        blank=True,
        related_name='custom_pricing_factors',
    )
    date_from = models.DateField()
    date_to = models.DateField(null=True, blank=True)
    note = models.TextField(blank=True, default='')

    objects = DateRangeQuerySet.as_manager()

    @property
    def codename(self):
        return self.pricing_factor_item.codename

    def clean(self):
        validator = UniqueForDateRangeValidator(
            model_class=CustomPricingFactor,
            lookup_fields=('pricing_factor_item', 'manufactor'),
            exclude_params={'pk': self.pk},
        )
        data = {
            'date_from': self.date_from,
            'pricing_factor_item': self.pricing_factor_item,
            'manufactor': self.manufactor,
        }
        if self.date_to:
            data['date_to'] = self.date_to
        try:
            validator(data)
        except serializers.ValidationError as error:
            raise ValidationError(error.default_code) from error

        if self.manufacturer_code and self.pricing_factor_item:
            manufacturer_codes = self.pricing_factor_item.manufacturercode_set.all()
            if self.manufacturer_code not in manufacturer_codes:
                raise ValidationError(
                    {
                        'pricing_factor_item': _(
                            'This PFI does not support selected Manufacturer Code. '
                            'Available options are: '
                            f'{", ".join(str(mc) for mc in manufacturer_codes)}'
                        ),
                    }
                )

    def save(self, *args, **kwargs):
        self.full_clean()
        super().save(*args, **kwargs)


class CustomPricingFactorChangeRequest(Timestampable):
    request_number = models.CharField(max_length=120)
    custom_pricing_factor = models.ForeignKey(
        CustomPricingFactor,
        on_delete=models.CASCADE,
    )
    status = models.IntegerField(
        choices=CustomPricingFactorChangeRequestStatusEnum.choices(),
        default=CustomPricingFactorChangeRequestStatusEnum.NEW.value,
    )
    price = models.DecimalField(
        max_digits=12,
        decimal_places=5,
        null=True,
        verbose_name='New element cost (PLN)',
    )
    manufacturer_code = models.CharField(
        max_length=20,
        verbose_name='New manufacturer code',
        blank=True,
    )
    date_from = models.DateField()
    attachment = models.ForeignKey(
        'production_margins.CustomPricingFactorChangeRequestAttachment',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )

    objects = CustomPricingFactorChangeRequestManager()

    def can_be_accepted(self):
        """
        Determines if the object can be accepted.
        An object can be accepted if it is new and there are no other active requests,
        or if its 'date_from' is newer than all other active requests.
        """
        return (
            self.status == CustomPricingFactorChangeRequestStatusEnum.NEW.value
            and self.is_newest_or_only_active_request()
            and self.can_update_custom_pricing_factor_with_selected_date()
        )

    def is_newest_or_only_active_request(self):
        other_active_requests = CustomPricingFactorChangeRequest.objects.filter(
            custom_pricing_factor=self.custom_pricing_factor,
            status=CustomPricingFactorChangeRequestStatusEnum.NEW.value,
        ).exclude(pk=self.pk)
        if not other_active_requests:
            return True
        newest_date_from = min(
            other_active_requests.values_list('date_from', flat=True)
        )
        return self.date_from < newest_date_from

    def can_update_custom_pricing_factor_with_selected_date(self):
        latest_factor = self.get_latest_active_custom_pricing_factor()

        is_start_valid = self.date_from > latest_factor.date_from
        is_end_valid = (latest_factor.date_to is None) or (
            latest_factor.date_to + datetime.timedelta(days=1) >= self.date_from
        )

        return is_start_valid and is_end_valid

    @transaction.atomic
    def update_and_create_custom_pricing_factor(
        self,
    ):
        latest_custom_pricing_factor = self.get_latest_active_custom_pricing_factor()
        self.update_latest_custom_pricing_factor(
            latest_custom_pricing_factor, self.date_from
        )
        new_custom_pricing_factor = self.create_new_custom_pricing_factor(
            latest_custom_pricing_factor,
            self.date_from,
            self.price,
            manufacturer_code=self.manufacturer_code,
        )
        new_custom_pricing_factor.save()

    def get_latest_active_custom_pricing_factor(self):
        today = datetime.date.today()
        active_element_managed_info = ElementManagedInfo.objects.filter(
            (models.Q(date_to__isnull=True) | models.Q(date_to__gte=today))
            & models.Q(date_from__lte=today),
            pricing_factor_item_id=models.OuterRef('pricing_factor_item_id'),
            manufactor=self.custom_pricing_factor.manufactor,
        )
        return (
            self.custom_pricing_factor.pricing_factor_item.custompricingfactor_set.annotate(  # noqa E501
                has_active_element_managed_info=models.Exists(
                    active_element_managed_info
                ),
            )
            .filter(
                (models.Q(date_to__isnull=True) | models.Q(date_to__gte=today)),
                manufactor=self.custom_pricing_factor.manufactor,
                has_active_element_managed_info=True,
            )
            .order_by(models.F('date_to').asc(nulls_last=True))
            .last()
        )

    @staticmethod
    def update_latest_custom_pricing_factor(pricing_factor, date_from):
        if not pricing_factor.date_to:
            pricing_factor.date_to = date_from - datetime.timedelta(days=1)
            pricing_factor.save(update_fields=['date_to'])

    @staticmethod
    def create_new_custom_pricing_factor(
        old_pricing_factor, date_from, price, manufacturer_code
    ):
        new_pricing_factor = deepcopy(old_pricing_factor)
        new_pricing_factor.pk = None
        new_pricing_factor.date_from = date_from
        new_pricing_factor.date_to = None
        new_pricing_factor.price = price
        new_pricing_factor.manufacturer_code = ManufacturerCode.objects.get(
            code=manufacturer_code
        )
        return new_pricing_factor

    @staticmethod
    def is_manufacturer_code_changed(old_code, new_code):
        return old_code != new_code

    @staticmethod
    def create_new_manufacturer_code(old_code, new_code):
        new_manufacturer_code = deepcopy(old_code)
        new_manufacturer_code.pk = None
        new_manufacturer_code.code = new_code
        return new_manufacturer_code


class CustomPricingFactorChangeRequestAttachment(Timestampable):
    file = FileFieldWithHash(
        upload_to='production_margins/custom_pricing_factor_change_request/%Y/%m',
        storage=private_media_storage,
        blank=True,
        max_length=150,
    )


class CustomPricingFactorChangeRequestComment(Timestampable):
    text = models.CharField(max_length=255, blank=True)
    custom_pricing_factor_change_request = models.ForeignKey(
        CustomPricingFactorChangeRequest,
        on_delete=models.CASCADE,
    )
    author = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
    )


class ElementManagedInfo(models.Model):
    pricing_factor_item = models.ForeignKey(
        'production_margins.PricingFactorItem',
        on_delete=models.CASCADE,
    )
    managed_by_us = models.BooleanField(default=False)
    date_from = models.DateField()
    date_to = models.DateField(null=True, blank=True)
    manufactor = models.ForeignKey('producers.Manufactor', on_delete=models.CASCADE)

    objects = DateRangeQuerySet.as_manager()

    class Meta:
        verbose_name = 'Element managed info'
        verbose_name_plural = 'Element managed info'

    @property
    def codename(self):
        return self.pricing_factor_item.codename


class ElementsOrder(models.Model):
    order_file = FileFieldWithHash(
        upload_to='production_margins/elements_order/%Y/%m',
        storage=private_media_storage,
    )
    batches = models.ManyToManyField('producers.ProductBatch')
    total_cost = models.DecimalField(
        max_digits=16,
        decimal_places=2,
        default=0,
    )
    service_cost = models.DecimalField(
        max_digits=16,
        decimal_places=2,
        default=0,
    )
    generated_at = models.DateTimeField(auto_now=True)
    deleted = models.BooleanField(default=False)
    new_report = models.ForeignKey(
        'production_margins.ElementsOrder',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
    )
    guilty_manufactor = models.ForeignKey(
        'producers.Manufactor',
        null=True,
        blank=True,
        on_delete=models.SET_NULL,
    )
    manufactor = models.ForeignKey(
        'producers.Manufactor',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='elements_orders',
    )
    meblepl_products = ArrayField(models.IntegerField(), default=list)
    invoice_number = models.CharField(max_length=100, blank=True)
    file_status = models.IntegerField(
        choices=ProductionFileStatus.choices,
        default=ProductionFileStatus.INITIAL,
    )
    order_type = models.PositiveSmallIntegerField(
        choices=ElementsOrderType.choices(),
        default=ElementsOrderType.NEW_ORDER.value,
    )

    # FIXME: remove after successful migration
    invoice_to_order = models.BooleanField(default=False)
    manufactor_fault = models.BooleanField(default=False)

    objects = ElementsOrderManager()

    def __str__(self):
        return (
            f'{self.pk} '
            f'{self.generated_at.date()} '
            f'{"Man Fault" if self.is_manufactor_fault else ""}'
        )

    @property
    def is_manufactor_fault(self):
        return self.order_type == ElementsOrderType.COMPLAINT_MANUFACTOR

    @property
    def is_standard_type(self):
        return self.order_type in STANDARD_ELEMENTS_ORDER_TYPES

    @property
    def is_s93_packaging(self):
        return self.order_type == ElementsOrderType.S93_PACKAGING


class ElementsOrderHistory(ElementsOrder):
    objects = ElementsOrderHistoryManager()

    class Meta:
        verbose_name_plural = 'Elements order histories'
        proxy = True


class MaterialManagementCost(models.Model):
    management_cost = models.DecimalField(
        max_digits=5,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name='Element loss factor (Percentage)',
    )
    manufactor = models.ForeignKey('producers.Manufactor', on_delete=models.CASCADE)
    date_from = models.DateField()
    date_to = models.DateField(null=True, blank=True)

    objects = DateRangeQuerySet.as_manager()


class MaterialInfoAttachment(models.Model):
    manufacturer_code = models.ForeignKey(
        ManufacturerCode,
        to_field='code',
        on_delete=models.PROTECT,
        related_name='attachments',
    )
    document_type = models.IntegerField(
        choices=MaterialInfoAttachmentType.choices,
        default=MaterialInfoAttachmentType.ANY,
    )
    valid_from = models.DateField()
    valid_to = models.DateField(null=True, blank=True)
    optional = models.BooleanField(default=False)
    file = FileFieldWithHash(
        upload_to='production_margins/material_info/%Y/%m',
        storage=private_media_storage,
        blank=True,
    )

    def valid_today(self):
        today = datetime.date.today()
        if not self.valid_to:
            return self.valid_from <= today
        return self.valid_from <= today <= self.valid_to

    def soon_to_expire(self):
        if not self.valid_to:
            return False
        today = datetime.date.today()
        expiration_margin = today + datetime.timedelta(days=90)
        return expiration_margin > self.valid_to >= today >= self.valid_from

    def clean(self):
        super().clean()

        if self.valid_to and self.valid_to < self.valid_from:
            raise ValidationError(
                {
                    'valid_from': 'valid_from can\'t be bigger than valid_to.',
                    'valid_to': 'valid_to can\'t be smaller than valid_from',
                }
            )

        if not self.optional and not self.file:
            raise ValidationError(
                {
                    'file': 'This field is required if not explicitly marked as "optional"',
                }
            )


class TelmexMagazineCode(models.Model):
    magazine_code = models.CharField(max_length=20)
    codename = models.CharField(max_length=255)

    class Meta:
        unique_together = ['magazine_code', 'codename']


class GalaStockLevelMaterial(models.Model):
    index = models.CharField(max_length=50, verbose_name='Material index')
    invoice = models.CharField(max_length=100, verbose_name='Invoice number')
    warehouse = models.CharField(max_length=50, verbose_name='Storage location')
    quantity = models.DecimalField(
        max_digits=10, decimal_places=2, verbose_name='Current stock'
    )
    entry_date = models.DateTimeField(verbose_name='Date of entry')
    defect = models.BooleanField(default=False, verbose_name='Defect')
    defect_description = models.CharField(
        max_length=255, blank=True, verbose_name='Defect description'
    )
    supplier = models.CharField(max_length=50, blank=True, verbose_name='Supplier code')

    class Meta:
        verbose_name = 'Gala stock level material'
        verbose_name_plural = 'Gala stock level materials'
        unique_together = ('index', 'invoice')

    def __str__(self):
        return f'{self.index} - {self.invoice}'
