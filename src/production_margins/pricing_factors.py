from dataclasses import dataclass
from datetime import datetime
from decimal import Decimal
from types import MappingProxyType
from typing import Mapping

from django.db.models import F

from producers.enums import Manufacturers
from production_margins.models import (
    CustomPricingFactor,
    PricingFactors,
)


@dataclass(frozen=True, slots=True)
class PricingFactor:
    """Class to represent the pricing factor for a material."""

    price_per_unit: Decimal
    loss_factor: Decimal  # e.g. 1.2 for 20% loss


PricingFactorsMapping = Mapping[str, PricingFactor]


class PricingFactorsService:
    """Service to provide pricing factors for everyone who needs it."""

    @staticmethod
    def get_pricing_factors(
        manufacturer: Manufacturers = None, for_date: datetime = None
    ) -> PricingFactorsMapping:
        pricing_factors = _get_common_pricing()
        if manufacturer:
            pricing_factors |= _get_custom_pricing(manufacturer, for_date)

        return MappingProxyType(pricing_factors)


def _get_common_pricing() -> dict:
    pricing_df = PricingFactors.get_solo().df[['price_per_unit', 'loss_factor']]
    pricing_dict = pricing_df.T.to_dict('dict')
    return {
        element_code: PricingFactor(
            price_per_unit=Decimal(values['price_per_unit']),
            loss_factor=Decimal(values['loss_factor']),
        )
        for element_code, values in pricing_dict.items()
    }


def _get_custom_pricing(manufacturer: Manufacturers, for_date: datetime = None) -> dict:
    custom_pricing_qs = CustomPricingFactor.objects.filter(
        manufactor_id=manufacturer,
    ).annotate(
        codename=F('pricing_factor_item__codename'),
    )

    if for_date is None:
        custom_pricing_qs = custom_pricing_qs.valid_for_today()
    else:
        custom_pricing_qs = custom_pricing_qs.valid_for_date(for_date)

    custom_pricing_qs = custom_pricing_qs.values(
        'codename',
        'price',
        'loss_factor',
    )

    return {
        pricing_factor['codename']: PricingFactor(
            price_per_unit=Decimal(pricing_factor['price']),
            loss_factor=Decimal(pricing_factor['loss_factor'] / 100) + 1,
        )
        for pricing_factor in custom_pricing_qs
    }
