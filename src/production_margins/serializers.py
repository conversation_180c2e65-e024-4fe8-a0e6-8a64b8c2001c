from rest_framework import serializers

from production_margins.models import CustomPricingFactor


class FittingInstructionsCPFSerializer(serializers.ModelSerializer):
    codename = serializers.CharField(source='pricing_factor_item.codename')
    ty_code = serializers.CharField(source='manufacturer_code.code', default='')
    description = serializers.CharField(source='pricing_factor_item.description')

    class Meta:
        model = CustomPricingFactor
        fields = (
            'codename',
            'ty_code',
            'description',
        )
