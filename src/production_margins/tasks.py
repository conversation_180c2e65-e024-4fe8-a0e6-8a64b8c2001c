import io

from datetime import datetime
from typing import (
    Any,
    Dict,
    Optional,
)

from django.conf import settings
from django.core.cache import cache

import pandas as pd
import requests

from celery import shared_task
from celery.utils.log import get_task_logger

from cstm_be.media_storage import private_media_storage
from custom.enums import LanguageEnum
from mailing.templates import (
    CodenamesReportReadyMail,
    EcotaxReportReadyMail,
    ElectrotaxReportReadyMail,
)
from producers.models import Product
from producers.production_system_utils.client import ProductionSystemClient
from production_margins.choices import CodenamesRecalculationChoice
from production_margins.codenames_report import generate_codenames_report
from production_margins.data_management.exporters import AveragePricingFactorsExporter
from production_margins.data_management.importers import (
    CustomPricingFactorsImporter,
    ElementManagedInfoImporter,
    ManufacturerCodeImporter,
    MaterialManagementCostImporter,
    PricingFactorItemsImporter,
)
from production_margins.ecotax.ecotax import calculate_ecotax
from production_margins.electrotax import calculate_electrotax
from production_margins.exceptions import CodenamesReportError
from production_margins.models import (
    CustomPricingFactor,
    ElementManagedInfo,
    ManufacturerCode,
    MaterialManagementCost,
    PricingFactorItem,
    PricingFactors,
)

task_logger = get_task_logger('celery_task')


def get_report_filename(report_name: str) -> str:
    return (
        f'production_margins/{report_name}/'
        + f'{report_name}_{datetime.now().strftime("%Y_%m_%d-%H_%M_%S")}.csv'
    )


def save_report_file_to_media_dir(
    filename: str,
    report_dataframe: pd.DataFrame,
    extra_lines: list[bytes] = None,
) -> str:
    with io.BytesIO() as buffer:
        report_dataframe.to_csv(buffer)
        if extra_lines:
            buffer.writelines(extra_lines)
        buffer.seek(0)
        saved_file = private_media_storage.save(filename, buffer)
    if settings.USE_AWS_S3_MEDIA_STORAGE:
        # It has to be less than a week
        time_to_expire = 3600 * 24 * 6
        return private_media_storage.url(saved_file, expire=time_to_expire)
    return private_media_storage.url(saved_file)


@shared_task
def generate_codenames_report_and_send_email_task(
    product_ids: list[int],
    pricing_factors: int,
    is_aggregated: Optional[bool] = False,
    send_to: Optional[str] = None,
    ppv_overrides: Optional[dict] = None,
) -> Dict[str, Any]:
    products = Product.objects.filter(id__in=product_ids)
    is_original_serialization = pricing_factors == CodenamesRecalculationChoice.ORIGINAL
    try:
        codenames_report, products_with_errors = generate_codenames_report(
            products=products,
            is_original_serialization=is_original_serialization,
            is_aggregated=is_aggregated,
            override_ppv=ppv_overrides,
        )
    except AttributeError as ex:
        task_logger.error(
            'Margins report: every product out of %s has failed', len(product_ids)
        )
        cache.set('codenames_report_task_id', None)
        raise CodenamesReportError('Every product has failed') from ex
    filename = save_report_file_to_media_dir(
        get_report_filename('codenames_report'),
        codenames_report,
    )
    codenames_report_details = {
        'filename': filename,
        'pricing_factors': CodenamesRecalculationChoice(pricing_factors).label,
        'errors': products_with_errors,
    }
    if send_to:
        mail = CodenamesReportReadyMail(
            send_to,
            data_html=codenames_report_details,
        )
        mail.send(language=LanguageEnum.EN)
    cache.set('codenames_report_task_id', None)
    return codenames_report_details


@shared_task
def generate_ecotax_report(
    product_ids: list[int],
    sample_boxes: list[dict[str, Any]],
    send_to: Optional[str] = None,
    start_date: str = '',
    end_date: str = '',
) -> Dict[str, Any]:
    pricing_factors = PricingFactors.get_solo().df
    products = (
        Product.objects.filter(id__in=product_ids)
        .prefetch_related(
            'order_item',
            'order_item__order_item',
            'order_item__region',
            'product_status_history',
            'product_details_jetty',
            'product_details_watty',
        )
        .select_related(
            'order',
        )
    )
    try:
        codenames_report, products_with_errors = generate_codenames_report(
            products=products,
            is_original_serialization=True,
            is_aggregated=False,
        )
    except AttributeError as ex:
        task_logger.error(
            'Margins report: every product out of %s has failed',
            len(product_ids),
        )
        raise CodenamesReportError(
            'Every product has failed while generating '
            + 'codenames_report for ecotax_report',
        ) from ex
    ecotax_report = calculate_ecotax(
        codenames_report,
        pricing_factors,
    )

    filename = save_report_file_to_media_dir(
        get_report_filename('ecotax_report'),
        ecotax_report,
        _get_extra_lines_for_ecotax(sample_boxes, start_date, end_date),
    )
    ecotax_report_details = {
        'filename': filename,
        'errors': products_with_errors,
    }
    if send_to:
        mail = EcotaxReportReadyMail(
            send_to,
            data_html=ecotax_report_details,
        )
        mail.send(language=LanguageEnum.EN)
    return ecotax_report_details


@shared_task
def generate_electrotax_report(
    start_date: str,
    end_date: str,
    send_to: Optional[str] = None,
) -> Dict[str, Any]:
    start_date = datetime.fromisoformat(start_date).date()
    end_date = datetime.fromisoformat(end_date).date()
    report, products_with_errors = calculate_electrotax(start_date, end_date)

    filename = save_report_file_to_media_dir(
        get_report_filename('electrotax_report'),
        report,
    )
    report_details = {
        'filename': filename,
        'errors': products_with_errors,
    }
    if send_to:
        mail = ElectrotaxReportReadyMail(
            send_to,
            data_html=report_details,
        )
        mail.send(language=LanguageEnum.EN)
    return report_details


def _get_extra_lines_for_ecotax(
    sample_boxes: Optional[list[dict[str, Any]]] = None,
    start_date: str = '',
    end_date: str = '',
) -> list[bytes]:
    header_line = 'Ecotax report \n'
    if start_date and end_date:
        header_line = f'Ecotax report ({start_date} - {end_date}) \n'

    lines = ['\n', header_line]
    if sample_boxes:
        lines.append('\n')
        lines.append('Sample Boxes \n')
        for line in sample_boxes:
            lines.append(
                '{country} - {quantity} \n'.format(
                    country=line.get('order__country', ''),
                    quantity=line.get('items_count', ''),
                ),
            )
    lines.append('\n')
    return [line.encode() for line in lines]


@shared_task
def update_pfs_and_send_to_ps(clean: bool = False):
    if settings.IS_PRODUCTION:
        return
    if clean:
        MaterialManagementCost.objects.all().delete()
        ElementManagedInfo.objects.all().delete()
        ManufacturerCode.objects.all().delete()
        CustomPricingFactor.objects.all().delete()
        PricingFactorItem.objects.all().delete()

    for item_kind in ('pfi', 'mmc', 'code', 'emi', 'cpf'):
        base_url = f'https://tylko.com/api/v1/serialized_items/{item_kind}/'
        resp = requests.get(
            base_url,
            headers={'Authorization': f'Token {settings.CSTM_PROD_TOKEN}'},
        )
        resp.raise_for_status()
        data = resp.json()
        importer = {
            'pfi': PricingFactorItemsImporter,
            'cpf': CustomPricingFactorsImporter,
            'emi': ElementManagedInfoImporter,
            'code': ManufacturerCodeImporter,
            'mmc': MaterialManagementCostImporter,
        }[item_kind]
        # TODO: error logging
        importer(data).save_valid_items()

    average_pricing_factors_dict = AveragePricingFactorsExporter().to_dict()
    with ProductionSystemClient(suppress_errors=True) as ps_client:
        ps_client.price_factors_create(
            pricing_factors_serialized_data=average_pricing_factors_dict,
        )
