{% extends "admin/change_list.html" %}
{% load static static %}

{% block extrastyle %}
  {{ block.super }}

  <style>
    .statistic__container {
      font-size: 1.25em;
      padding-top: 10px;
      padding-bottom: 20px;
    }

    .statistic__table__caption {
      padding-bottom: 10px;
      white-space: nowrap;
    }
  </style>
{% endblock %}

{% block object-tools-items %}
    {{ block.super }}

    {% for action_url, action_name in button_actions %}
    <li style="--object-tools-bg: orangered;" >
        <form action="{{ action_url }}/" method="POST">
            {% csrf_token %}
            <a href="" onclick="this.closest('form').submit();return false;">{{ action_name }}</a>
        </form>
    </li>
    {% endfor %}
{% endblock %}


{% block content %}
  <div class="statistic__container">
      <caption class="statistic__table__caption">
          <p>Total price per filter: {{ total_cost }}</p>
          <p>Service price per filter: {{ service_cost }}</p>
      </caption>
  </div>
  {{ block.super }}
{% endblock %}
