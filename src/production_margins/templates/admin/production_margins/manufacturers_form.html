{% extends "admin/base_site.html" %}
{% load i18n l10n %}
{% load admin_urls %}

{% block breadcrumbs %}
    <div class="breadcrumbs">
        <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
        &rsaquo; <a href="{% url 'admin:app_list' app_label=app_label %}">{{ app_label|capfirst|escape }}</a>
        &rsaquo; <a href="{% url opts|admin_urlname:'changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
        &rsaquo; Add to cart selected items
    </div>
{% endblock %}

{% block content %}

    <p>Set Manufacturer objects {{ items }}:</p>

    <ul>
        {% for item in items %}
            <li>Item id: {{ item }}</li>
        {% endfor %}
    </ul>

    <form action="" method="post">
        {% csrf_token %}
        {{ status_form.as_p }}
        <input type="hidden" name="action" value="get_summary_for_manufacturer" />
        <input type="submit" name="apply" value="Download XLS" />
    </form>

{% endblock %}