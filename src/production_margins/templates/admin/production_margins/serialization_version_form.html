{% extends "admin/base_site.html" %}
{% load i18n l10n %}
{% load admin_urls %}

{% block breadcrumbs %}
    <div class="breadcrumbs">
        <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a>
        &rsaquo; <a href="{% url 'admin:app_list' app_label=app_label %}">{{ app_label|capfirst|escape }}</a>
        &rsaquo; <a href="{% url opts|admin_urlname:'changelist' %}">{{ opts.verbose_name_plural|capfirst }}</a>
        &rsaquo; Add to cart selected items
    </div>
{% endblock %}

{% block content %}

    <p>Serialization version (e.g. "19.19") {{ text }}:</p>

    <form action="" method="post">
        {% csrf_token %}
        {{ select_form.as_p }}
        <input type="hidden" name="action" value="duplicate_based_on_current_serialization" />
        <input type="submit" name="apply" value="Proceed" />
    </form>

{% endblock %}