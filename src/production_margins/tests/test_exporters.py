import datetime

from decimal import Decimal

import pytest

from production_margins.choices import MeasurementUnit
from production_margins.data_management.exporters import (
    AveragePricingFactorsExporter,
    CustomPricingFactorsExporter,
)
from production_margins.tests.factories import CustomPricingFactorFactory


@pytest.mark.django_db
class TestCustomPricingFactorsExporter:
    def test_export_to_csv(
        self, custom_pricing_factor_factory: CustomPricingFactorFactory
    ):
        custom_pricing_factor_factory.create(
            pricing_factor_item__codename='test_codename',
            date_from=datetime.date(2021, 1, 1),
            date_to=datetime.date(2022, 1, 1),
            price=Decimal(150),
            loss_factor=Decimal(20),
            note='n1',
        )
        exporter = CustomPricingFactorsExporter()
        expected_headers = 'id,codename,manufacturer,price,loss_factor,manufacturer_code,date_from,date_to,description,conversion,note'
        expected_row = '1,test_codename,test_manufacturer,150.00000,20.00,,2021-01-01,2022-01-01,d,c,n1'
        assert exporter.to_csv() == f'{expected_headers}\r\n{expected_row}\r\n'


@pytest.mark.django_db
class TestAveragePricingFactorsExporter:
    def test_export_to_csv(
        self,
        pricing_factor_item_factory,
        custom_pricing_factor_factory,
    ):
        pfi = pricing_factor_item_factory(
            codename='test_codename',
            length=1000,
            thickness=15,
            weight=1,
            measurement_unit=MeasurementUnit.PIECES,
        )
        custom_pricing_factor_factory.create(
            pricing_factor_item=pfi,
            price=Decimal(150),
            loss_factor=Decimal(20),
        )
        custom_pricing_factor_factory.create(
            pricing_factor_item=pfi,
            price=Decimal(50),
            loss_factor=Decimal(10),
        )
        exporter = AveragePricingFactorsExporter()
        expected_headers = 'codename,unit,length,thickness,loss_factor,price_per_unit,weight_per_unit,management_cost'
        expected_row = 'test_codename,szt,1000,15,1.150,100.000,1.000,0.00'
        assert exporter.to_csv() == f'{expected_headers}\r\n{expected_row}\r\n'
