import pytest

from production_margins.choices import (
    CodenamesRecalculationChoice,
    ReportDataType,
)
from production_margins.forms import (
    CodenamesReportForm,
    TaxReportForm,
)


class TestCodenamesReportForm:
    def test_reporting_month_must_be_filled_if_monthly_report_selected(self):
        form = CodenamesReportForm(
            {
                'report_type': ReportDataType.MONTHLY,
                'reporting_date': None,
                'pricing_factors': CodenamesRecalculationChoice.CURRENT,
            }
        )
        assert not form.is_valid()
        assert form.errors == {
            '__all__': ['Reporting month must be filled for monthly report.']
        }

    def test_product_ids_must_be_filled_if_id_report_selected(self):
        form = CodenamesReportForm(
            {
                'report_type': ReportDataType.PRODUCT_IDS,
                'pricing_factors': CodenamesRecalculationChoice.CURRENT,
                'product_ids': '',
            }
        )
        assert not form.is_valid()
        assert form.errors == {
            '__all__': ['Product ids cannot be empty for ids report.']
        }

    @pytest.mark.parametrize(
        'product_ids',
        [
            '1,2,3,4',
            '1, 2, 3, 4',
            '1,2, 3,4,',
        ],
    )
    def test_product_ids_correctly_parses_list_of_comma_separated_integers(
        self,
        product_ids,
    ):
        form = CodenamesReportForm(
            {
                'report_type': ReportDataType.PRODUCT_IDS,
                'pricing_factors': CodenamesRecalculationChoice.CURRENT,
                'product_ids': product_ids,
            }
        )
        assert form.is_valid()
        assert form.cleaned_data['product_ids'] == [1, 2, 3, 4]

    @pytest.mark.parametrize(
        ('product_ids', 'invalid_value'),
        [
            ('1,2,a,4', 'a'),
            ('1, 2, , 4', ' '),
            ('1, 2,3df, 4,', '3df'),
        ],
    )
    def test_product_ids_raises_validation_error_when_a_non_integer_present(
        self,
        product_ids,
        invalid_value,
    ):
        form = CodenamesReportForm(
            {
                'report_type': ReportDataType.PRODUCT_IDS,
                'pricing_factors': CodenamesRecalculationChoice.CURRENT,
                'product_ids': product_ids,
            }
        )
        assert not form.is_valid()
        assert form.errors['product_ids'] == [f'{invalid_value} is not a valid integer']


class TestEcoTaxReportForm:
    @pytest.mark.parametrize(
        ('start_date', 'end_date'),
        [
            ('2020-12-01', '2020-12-01'),
            ('2020-12-01', '2021-12-01'),
        ],
    )
    def test_form_with_correct_dates(self, start_date, end_date):
        form = TaxReportForm(
            {
                'start_date': start_date,
                'end_date': end_date,
            }
        )
        assert form.is_valid()

    @pytest.mark.parametrize(
        ('start_date', 'end_date'),
        [
            ('2020-12-01', '2020-11-01'),
            ('2020-12-10', '2020-12-09'),
        ],
    )
    def test_form_is_not_valid_when_end_date_lower_than_start_date(
        self,
        start_date,
        end_date,
    ):
        form = TaxReportForm(
            {
                'start_date': start_date,
                'end_date': end_date,
            }
        )
        assert not form.is_valid()
        assert form.errors == {
            '__all__': [
                'The end date must be greater than or equal to the start date.',
            ],
        }
