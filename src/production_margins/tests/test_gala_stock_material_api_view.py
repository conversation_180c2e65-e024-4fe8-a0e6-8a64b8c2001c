from django.urls import reverse

import pytest

from rest_framework import status

from production_margins.models import GalaStockLevelMaterial


@pytest.mark.django_db
def test_update_gala_stock_level_material(api_client, token_factory):
    token = token_factory(is_producer=True)
    url = reverse('gala-stock-level-materials')
    input_data = [
        {
            'index': 'M144 K96 77',
            'invoice': 'KWADRAT PZ242 17,03,25',
            'warehouse': '3G2',
            'quantity': 79.71,
            'entry_date': '2025-03-17T12:55:00',
            'defect': False,
            'defect_description': '',
            'supplier': 'LA00125',
        },
        {
            'index': 'M145 139 00',
            'invoice': 'KWADRAT PZ242 17,03,25',
            'warehouse': '3G2',
            'quantity': 30.34,
            'entry_date': '2025-03-17T12:55:00',
            'defect': False,
            'defect_description': '',
            'supplier': 'LA00125',
        },
    ]
    response = api_client.post(
        url,
        data={'data': input_data},
        HTTP_AUTHORIZATION=f'Token {token.key}',
        format='json',
    )
    assert response.status_code == status.HTTP_201_CREATED
    assert response.json() == {'created': 2, 'updated': 0}
    assert GalaStockLevelMaterial.objects.count() == 2

    # now check if update works correctly:
    response = api_client.post(
        url,
        data={'data': input_data},
        HTTP_AUTHORIZATION=f'Token {token.key}',
        format='json',
    )
    assert response.status_code == status.HTTP_201_CREATED
    assert response.json() == {'created': 0, 'updated': 2}
    assert GalaStockLevelMaterial.objects.count() == 2
