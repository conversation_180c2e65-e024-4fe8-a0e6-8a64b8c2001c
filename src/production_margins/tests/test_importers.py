import datetime

from decimal import Decimal
from io import StringIO

import pytest

from producers.tests.factories import ManufactorFactory
from production_margins.data_management.importers import (
    CustomPricingFactorsImporter,
    LegacyPricingFactorItemImporter,
)
from production_margins.data_management.importers.pricing_factors_item import (
    float_multiplier_to_percentage,
    percentage_to_float_multiplier,
)
from production_margins.models import (
    CustomPricingFactor,
    PricingFactorItem,
)
from production_margins.tests.factories import (
    CustomPricingFactorFactory,
    PricingFactorItemFactory,
)


def custom_pricing_factors_csv(
    codename: str,
    manufacturer: str,
    date_from: datetime.date = datetime.date(2021, 1, 1),
    date_to: datetime.date = datetime.date(2022, 1, 1),
    price: Decimal = Decimal(150),
    loss_factor: Decimal = Decimal(20),
) -> StringIO:
    headers = 'id,codename,manufacturer,price,loss_factor,date_from,date_to,description,conversion'
    row = f'1,{codename},{manufacturer},{price},{loss_factor},{date_from},{date_to},d,c'
    return StringIO(f'{headers}\r\n{row}\r\n')


@pytest.mark.django_db
class TestCustomPricingFactorsImporter:
    def test_import_valid(
        self,
        pricing_factor_item_factory: PricingFactorItemFactory,
        manufactor_factory: ManufactorFactory,
    ):
        codename = 'test_codename'
        manufacturer = 'test_manufacturer'

        pricing_factor_item_factory.create(codename=codename)
        manufactor_factory.create(name=manufacturer)

        csv = custom_pricing_factors_csv(
            codename=codename,
            manufacturer=manufacturer,
        )
        importer = CustomPricingFactorsImporter.from_csv(csv)
        importer.save_valid_items()
        assert importer.valid_items
        assert not importer.invalid_items
        assert not importer.duplicated_items
        assert not importer.conflicting_items

    def test_import_duplicate(
        self,
        custom_pricing_factor_factory: CustomPricingFactorFactory,
    ):
        existing_cpf = custom_pricing_factor_factory.create()

        csv = custom_pricing_factors_csv(
            codename=existing_cpf.pricing_factor_item.codename,
            manufacturer=existing_cpf.manufactor.name,
            date_from=existing_cpf.date_from,
            date_to=existing_cpf.date_to,
            price=existing_cpf.price,
            loss_factor=existing_cpf.loss_factor,
        )
        importer = CustomPricingFactorsImporter.from_csv(csv)
        importer.save_valid_items()
        assert not importer.valid_items
        assert not importer.invalid_items
        assert importer.duplicated_items
        assert not importer.conflicting_items

    @pytest.mark.parametrize(
        ('price_diff', 'loss_factor_diff'),
        (
            (Decimal(0), Decimal(20)),
            (Decimal(20), Decimal(0)),
        ),
    )
    def test_import_conflicting(
        self,
        custom_pricing_factor_factory: CustomPricingFactorFactory,
        price_diff,
        loss_factor_diff,
    ):
        existing_cpf = custom_pricing_factor_factory.create()

        csv = custom_pricing_factors_csv(
            codename=existing_cpf.pricing_factor_item.codename,
            manufacturer=existing_cpf.manufactor.name,
            date_from=existing_cpf.date_from,
            date_to=existing_cpf.date_to,
            price=existing_cpf.price + price_diff,
            loss_factor=existing_cpf.loss_factor + loss_factor_diff,
        )
        importer = CustomPricingFactorsImporter.from_csv(csv)
        importer.save_valid_items()
        assert not importer.valid_items
        assert not importer.invalid_items
        assert not importer.duplicated_items
        assert importer.conflicting_items

    def test_import_invalid(
        self,
        custom_pricing_factor_factory: CustomPricingFactorFactory,
    ):
        existing_cpf = custom_pricing_factor_factory.create()

        csv = custom_pricing_factors_csv(
            codename='nonexistent_codename',
            manufacturer=existing_cpf.manufactor.name,
            date_from=existing_cpf.date_from,
            date_to=existing_cpf.date_to,
            price=existing_cpf.price,
            loss_factor=existing_cpf.loss_factor,
        )
        importer = CustomPricingFactorsImporter.from_csv(csv)
        importer.save_valid_items()
        assert not importer.valid_items
        assert importer.invalid_items
        assert not importer.duplicated_items
        assert not importer.conflicting_items

    def test_import_valid_created(
        self,
        custom_pricing_factor_factory: CustomPricingFactorFactory,
    ):
        existing_cpf = custom_pricing_factor_factory.create()
        new_date_from = existing_cpf.date_to + datetime.timedelta(2)
        new_date_to = existing_cpf.date_to + datetime.timedelta(10)

        csv = custom_pricing_factors_csv(
            codename=existing_cpf.pricing_factor_item.codename,
            manufacturer=existing_cpf.manufactor.name,
            date_from=new_date_from,
            date_to=new_date_to,
            price=existing_cpf.price,
            loss_factor=existing_cpf.loss_factor,
        )
        importer = CustomPricingFactorsImporter.from_csv(csv)
        importer.save_valid_items()
        assert importer.valid_items
        assert CustomPricingFactor.objects.count() == 2
        assert CustomPricingFactor.objects.last().date_from == new_date_from
        assert CustomPricingFactor.objects.last().date_to == new_date_to


@pytest.mark.django_db
class TestPricingFactorItemImporter:
    @pytest.fixture()
    def sample_pricing_factor_item_data(self):
        return {
            'C1_category': 'fitting',
            'unit': 'szt',
            'price_per_unit': 1.0,
            'loss_factor': 1.0,
            'weight_per_unit': 0,
            'thickness': 0,
            'length': 0,
            'polish_desc': 'polski opis',
            'conversion': 'conversion',
        }

    def test_update_pricing_factor_item_updates_when_price_changed(
        self,
        sample_pricing_factor_item_data,
        pricing_factor_item,
    ):
        codename = pricing_factor_item.codename
        price = 0.5

        data = {
            **sample_pricing_factor_item_data,
            'price_per_unit': price,
            'loss_factor': float(pricing_factor_item.loss_factor),
        }

        LegacyPricingFactorItemImporter.update_pricing_factor_item(codename, data)
        pricing_factor_item.refresh_from_db()

        assert pricing_factor_item.price == price

    def test_update_pricing_factor_item_updates_when_loss_factor_changed(
        self,
        sample_pricing_factor_item_data,
        pricing_factor_item,
    ):
        codename = pricing_factor_item.codename
        loss_factor = 2.0

        data = {
            **sample_pricing_factor_item_data,
            'price_per_unit': float(pricing_factor_item.price),
            'loss_factor': loss_factor,
        }

        LegacyPricingFactorItemImporter.update_pricing_factor_item(codename, data)
        pricing_factor_item.refresh_from_db()

        assert pricing_factor_item.loss_factor == loss_factor

    def test_update_pricing_factor_item_updates_when_both_changed(
        self,
        sample_pricing_factor_item_data,
        pricing_factor_item,
    ):
        codename = pricing_factor_item.codename
        price = 0.5
        loss_factor = 2.0

        data = {
            **sample_pricing_factor_item_data,
            'price_per_unit': price,
            'loss_factor': loss_factor,
        }

        LegacyPricingFactorItemImporter.update_pricing_factor_item(codename, data)
        pricing_factor_item.refresh_from_db()

        assert pricing_factor_item.price == price
        assert pricing_factor_item.loss_factor == loss_factor

    def test_update_pricing_factor_item_not_updates_when_nothing_changed(
        self,
        mocker,
        sample_pricing_factor_item_data,
        pricing_factor_item,
    ):
        codename = pricing_factor_item.codename

        price = float(pricing_factor_item.price)
        loss_factor_percentage = Decimal('1.5')
        loss_factor_pf_format = float(pricing_factor_item.loss_factor / 100 + 1)
        data = {
            'C1_category': pricing_factor_item.category,
            'unit': pricing_factor_item.measurement_unit,
            'weight_per_unit': float(pricing_factor_item.weight),
            'thickness': pricing_factor_item.thickness,
            'length': pricing_factor_item.length,
            'polish_desc': pricing_factor_item.description,
            'conversion': pricing_factor_item.conversion,
            'price_per_unit': price,
            'loss_factor': loss_factor_pf_format,
        }

        save_mock = mocker.patch('production_margins.models.PricingFactorItem.save')
        LegacyPricingFactorItemImporter.update_pricing_factor_item(codename, data)
        pricing_factor_item.refresh_from_db()

        save_mock.assert_not_called()
        assert pricing_factor_item.price == price
        assert pricing_factor_item.loss_factor == loss_factor_percentage

    def test_load_creates_new_item(self, sample_pricing_factor_item_data, mocker):
        test_codename = 'test_codename'
        pricing_factor_dict = {
            test_codename: sample_pricing_factor_item_data,
        }

        importer = LegacyPricingFactorItemImporter()
        mocker.patch.object(
            importer,
            'get_pricing_factor_dict',
            return_value=pricing_factor_dict,
        )

        importer.load()

        assert PricingFactorItem.objects.count() == 1
        assert PricingFactorItem.objects.filter(codename=test_codename).exists()

    def test_load_updates_when_item_exists(
        self,
        sample_pricing_factor_item_data,
        pricing_factor_item,
        mocker,
    ):
        pricing_factor_dict = {
            pricing_factor_item.codename: sample_pricing_factor_item_data
        }

        importer = LegacyPricingFactorItemImporter()
        mocker.patch.object(
            importer,
            'get_pricing_factor_dict',
            return_value=pricing_factor_dict,
        )
        update_pricing_factor_item_mock = mocker.patch.object(
            importer,
            'update_pricing_factor_item',
        )

        importer.load()

        assert PricingFactorItem.objects.count() == 1
        assert PricingFactorItem.objects.filter(
            codename=pricing_factor_item.codename,
        ).exists()
        update_pricing_factor_item_mock.assert_called_once_with(
            pricing_factor_item.codename,
            pricing_factor_dict[pricing_factor_item.codename],
        )


@pytest.mark.parametrize(
    ('value', 'expected_decimal'),
    [
        (1.015, Decimal('1.5')),
        (1.22, Decimal('22')),
        (1.00, Decimal('0')),
        (0, Decimal('0')),
        ('', Decimal('0')),
        (None, Decimal('0')),
    ],
)
def test_float_multiplier_to_percentage(value, expected_decimal):
    assert float_multiplier_to_percentage(value) == expected_decimal


@pytest.mark.parametrize(
    ('value', 'expected_float'),
    [
        (Decimal('1.5'), 1.015),
        (Decimal('22'), 1.22),
        (Decimal('0'), 1.0),
        (None, 1.0),
    ],
)
def test_percentage_to_float_multiplier(value, expected_float):
    assert percentage_to_float_multiplier(value) == pytest.approx(expected_float)
