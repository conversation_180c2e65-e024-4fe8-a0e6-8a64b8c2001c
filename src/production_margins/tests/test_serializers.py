import pytest

from rest_framework.exceptions import ValidationError

from producers.tests.factories import ManufactorFactory
from production_margins.data_management.serializers.custom_pricing_factors import (
    CustomPricingFactorsExportSerializer,
)
from production_margins.tests.factories import PricingFactorItemFactory


@pytest.fixture()
def pricing_factor_item():
    return PricingFactorItemFactory(
        codename='sample_codename',
    )


@pytest.fixture()
def manufacturer_drewtur():
    return ManufactorFactory(
        name='Drewtur',
    )


@pytest.mark.django_db
class TestCustomPricingFactorsSerializer:
    @pytest.fixture(scope='class')
    def sample_data(self):
        return {
            'codename': 'sample_codename',
            'manufacturer': 'Drewtur',
            'price': '750.00',
            'loss_factor': '25.00',
            'manufacturer_code': None,
            'date_from': '2022-4-12',
            'date_to': '2022-4-15',
        }

    def test_no_errors_when_serialized_first_time(
        self,
        sample_data,
        manufacturer_drewtur,
        pricing_factor_item,
    ):
        serializer = CustomPricingFactorsExportSerializer(data=sample_data)
        assert serializer.is_valid()

    def test_raises_validation_error_when_duplicated(
        self,
        sample_data,
        manufacturer_drewtur,
        pricing_factor_item,
    ):
        serializer = CustomPricingFactorsExportSerializer(data=sample_data)
        serializer.is_valid()
        serializer.save()

        serializer2 = CustomPricingFactorsExportSerializer(data=sample_data)
        with pytest.raises(ValidationError, match='duplicate'):
            serializer2.is_valid(raise_exception=True)

    def test_raises_validation_error_when_conflicting(
        self,
        sample_data,
        manufacturer_drewtur,
        pricing_factor_item,
    ):
        serializer = CustomPricingFactorsExportSerializer(data=sample_data)
        serializer.is_valid()
        serializer.save()

        conflicting_data = sample_data.copy()
        conflicting_data['price'] = '100.00'
        serializer2 = CustomPricingFactorsExportSerializer(data=conflicting_data)
        with pytest.raises(ValidationError, match='conflict'):
            serializer2.is_valid(raise_exception=True)

    def test_raises_validation_error_when_dates_range_is_incorrect(
        self,
        manufacturer_drewtur,
        pricing_factor_item,
    ):
        serializer = CustomPricingFactorsExportSerializer(
            data={
                'codename': 'sample_codename',
                'manufacturer': 'Drewtur',
                'price': '750.00',
                'loss_factor': '25.00',
                'date_from': '2022-4-16',
                'date_to': '2022-4-15',
            }
        )
        with pytest.raises(ValidationError, match='incorrect_range'):
            serializer.is_valid(raise_exception=True)

    # First item date range is: 2022-4-12 to 2022-4-15
    # s1 - start date 1, e1 - end date 1
    # s2 - start date 2, e2 - end date 2
    @pytest.mark.parametrize(
        ('previous_date_to', 'date_from', 'date_to'),
        [
            ('2022-4-15', '2022-4-10', '2022-4-12'),  # --s2--s1e2--e1----
            ('2022-4-15', '2022-4-10', '2022-4-13'),  # --s2--s1--e2--e1--
            ('2022-4-15', '2022-4-12', '2022-4-15'),  # --s1s2--e1e2------
            ('2022-4-15', '2022-4-13', '2022-4-14'),  # --s1--s2--e2--e1--
            ('2022-4-15', '2022-4-13', '2022-4-15'),  # --s1--s2--e1e2----
            ('2022-4-15', '2022-4-13', '2022-4-16'),  # --s1--s2--e1--e2--
            ('2022-4-15', '2022-4-15', '2022-4-16'),  # --s1--e1s2--e2----
            (None, '2022-4-12', '2022-4-15'),  # --s1s2--e2------
            (None, '2022-4-13', '2022-4-14'),  # --s1--s2--e2----
            (None, '2022-4-10', '2022-4-12'),  # --s2--s1e2------
            (None, '2022-4-10', '2022-4-13'),  # --s2--s1--e2----
            ('2022-4-15', '2022-4-10', None),  # --s2--s1--e1----
            ('2022-4-15', '2022-4-12', None),  # --s1s2--e1------
            ('2022-4-15', '2022-4-13', None),  # --s1--s2--e1----
            ('2022-4-15', '2022-4-15', None),  # --s1--e1s1------
            (None, '2022-4-10', None),  # --s2--s1------
            (None, '2022-4-12', None),  # --s1s2--------
            (None, '2022-4-13', None),  # --s1--s2------
        ],
    )
    def test_raises_validation_error_when_overlapping_dates(
        self,
        previous_date_to,
        date_from,
        date_to,
        sample_data,
        manufacturer_drewtur,
        pricing_factor_item,
    ):
        serializer_data = sample_data.copy()
        serializer_data['date_to'] = previous_date_to
        serializer = CustomPricingFactorsExportSerializer(data=sample_data)
        serializer.is_valid()
        serializer.save()

        conflicting_data = sample_data.copy()
        conflicting_data['date_from'] = date_from
        conflicting_data['date_to'] = date_to
        serializer2 = CustomPricingFactorsExportSerializer(data=conflicting_data)
        with pytest.raises(ValidationError, match='duplicate'):
            serializer2.is_valid(raise_exception=True)
