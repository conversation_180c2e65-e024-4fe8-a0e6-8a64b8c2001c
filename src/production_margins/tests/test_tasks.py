import pytest

from production_margins.choices import CodenamesRecalculationChoice
from production_margins.exceptions import CodenamesReportError
from production_margins.tasks import generate_codenames_report_and_send_email_task


@pytest.mark.django_db
class TestGenerateCodenamesReport:
    def test_selects_products_with_provided_ids(
        self,
        product_factory,
        mocker,
    ):
        products = product_factory.create_batch(8)
        mocked_generate = mocker.patch(
            'production_margins.tasks.generate_codenames_report',
            return_value=(1, [2]),
        )
        mocker.patch('production_margins.tasks.save_report_file_to_media_dir')
        products_ids = [product.id for product in products][:4]
        generate_codenames_report_and_send_email_task(
            product_ids=products_ids,
            pricing_factors=CodenamesRecalculationChoice.CURRENT,
        )
        first_argument_of_generate = mocked_generate.call_args.kwargs['products']
        assert len(first_argument_of_generate) == 4

    def test_saves_report_csv_to_media_directory(self, mocker):
        mocker.patch(
            'production_margins.tasks.generate_codenames_report',
            return_value=(1, [2]),
        )
        mocked_save = mocker.patch(
            'production_margins.tasks.save_report_file_to_media_dir'
        )
        generate_codenames_report_and_send_email_task(
            product_ids=[1, 2, 3],
            pricing_factors=CodenamesRecalculationChoice.CURRENT,
        )
        assert mocked_save.called

    def test_sends_email_with_report_data_to_recipients(self, mocker):
        mocker.patch(
            'production_margins.tasks.generate_codenames_report',
            return_value=(1, 2),
        )
        mocker.patch('production_margins.tasks.save_report_file_to_media_dir')
        mock_mail = mocker.patch('production_margins.tasks.CodenamesReportReadyMail')
        generate_codenames_report_and_send_email_task(
            product_ids=[1, 2, 3],
            pricing_factors=CodenamesRecalculationChoice.CURRENT,
            send_to='<EMAIL>',
        )
        first_argument_to_mock_mail = mock_mail.call_args.args[0]
        assert first_argument_to_mock_mail == '<EMAIL>'

    def test_catches_attributerror_and_reraises_as_custom_error(self, mocker):
        mocker.patch(
            'production_margins.tasks.generate_codenames_report',
            side_effect=AttributeError,
        )
        with pytest.raises(CodenamesReportError):
            generate_codenames_report_and_send_email_task(
                product_ids=[1, 2, 3],
                pricing_factors=CodenamesRecalculationChoice.CURRENT,
            )
