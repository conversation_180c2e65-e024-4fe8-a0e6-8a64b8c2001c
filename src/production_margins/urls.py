from django.urls import path

from production_margins.views import (
    CodenamesReportApiView,
    CodenamesReportStatusView,
    CPFManufacturerCodeView,
    PFUpdater,
    PFUpdaterClean,
    PotentialCodenamesReportDifferences,
    SerializedItemsView,
)

urlpatterns = [
    path(
        'codenames_report/',
        CodenamesReportApiView.as_view(),
        name='codenames-report-api',
    ),
    path(
        'codenames_report/results/',
        CodenamesReportStatusView.as_view(),
        name='codenames-report-status',
    ),
    path(
        'codenames_report_compare/',
        PotentialCodenamesReportDifferences.as_view(),
        name='current-pricing-factors',
    ),
    path(
        'serialized_items/<str:items_kind>/',
        SerializedItemsView.as_view(),
        name='serialized-items',
    ),
    path(
        'update_pfs/',
        PFUpdater.as_view(),
        name='update-pfs',
    ),
    path(
        'update_pfs/clean/',
        PFUpdaterClean.as_view(),
        name='update-pfs',
    ),
    path(
        'ty_codes/',
        CPFManufacturerCodeView.as_view(),
        name='ty-codes',
    ),
]
