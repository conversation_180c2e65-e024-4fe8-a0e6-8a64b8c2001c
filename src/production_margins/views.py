import json

from collections import defaultdict

from django.conf import settings
from django.contrib import messages
from django.core.cache import cache
from django.urls import reverse
from django.views.generic import FormView

from celery.result import AsyncResult
from rest_framework import status
from rest_framework.authentication import (
    BasicAuthentication,
    TokenAuthentication,
)
from rest_framework.permissions import AllowAny
from rest_framework.request import Request
from rest_framework.response import Response
from rest_framework.throttling import AnonRateThrottle
from rest_framework.views import APIView

from custom.enums import ShelfType
from orders.enums import OrderType
from producers.models import Product
from producers.production_system_utils.client import get_active_price_factors_from_ps
from production_margins.choices import CodenamesRecalculationChoice
from production_margins.codenames_report import (
    get_product_ids_for_ecotax_report,
    get_product_ids_for_report,
    get_sample_box_ecotax_info,
)
from production_margins.data_management.exporters import (
    CustomPricingFactorsExporter,
    ElementManagedInfoExporter,
    ManufacturerCodeExporter,
    MaterialManagementCostExporter,
    PricingFactorItemExporter,
)
from production_margins.forms import (
    CodenamesReportForm,
    TaxReportForm,
)
from production_margins.models import (
    CustomPricingFactor,
    ElementManagedInfo,
    MaterialManagementCost,
    PricingFactors,
)
from production_margins.serializers import FittingInstructionsCPFSerializer
from production_margins.tasks import (
    generate_codenames_report_and_send_email_task,
    generate_ecotax_report,
    generate_electrotax_report,
    update_pfs_and_send_to_ps,
)


class CodenamesReportView(FormView):
    form_class = CodenamesReportForm
    template_name = 'admin/codenames_report.html'

    def get_success_url(self):
        messages.add_message(
            self.request,
            messages.SUCCESS,
            'Report will be sent to the recipients emails.',
        )
        return reverse('admin:codenames-report')

    def form_valid(self, form):
        report_type = int(form.cleaned_data['report_type'])
        reporting_date = form.cleaned_data.get('reporting_date')
        pricing_factors = int(form.cleaned_data['pricing_factors'])
        product_ids = form.cleaned_data.get('product_ids')
        is_aggregated = form.cleaned_data['is_aggregated']
        product_ids = get_product_ids_for_report(
            report_type,
            product_ids,
            reporting_date,
        )
        ppv_overrides = form.cleaned_data.get('ppv_overrides', {})
        generate_codenames_report_and_send_email_task.delay(
            product_ids=product_ids,
            send_to=self.request.user.username,
            pricing_factors=pricing_factors,
            is_aggregated=is_aggregated,
            ppv_overrides=ppv_overrides,
        )
        return super().form_valid(form)


class CodenamesReportApiView(APIView):
    authentication_classes = (
        BasicAuthentication,
        TokenAuthentication,
    )

    def get(self, request: Request, *args, **kwargs) -> Response:
        sample_size = request.query_params.get('sample_size')
        ppv_overrides = {}
        for param_name, param_value in request.query_params.items():
            if param_name.endswith('_PPV'):
                ppv_overrides[param_name.rsplit('_', 1)[0]] = int(param_value)
        force = request.query_params.get('force')
        pricing_factors = CodenamesRecalculationChoice.CURRENT
        products = Product.objects.all().exclude(order__order_type=OrderType.COMPLAINT)
        if sample_size:
            products = products[: int(sample_size)]
        product_ids = list(products.values_list('id', flat=True))

        if force == 'true':
            cache.set('codenames_report_task_id', None)
        async_task_id = cache.get('codenames_report_task_id')
        if not async_task_id:
            async_task_id = generate_codenames_report_and_send_email_task.delay(
                product_ids=product_ids,
                pricing_factors=pricing_factors,
                ppv_overrides=ppv_overrides,
            ).id
            cache.set('codenames_report_task_id', async_task_id)
        return Response(
            {'task_id': async_task_id},
            status=status.HTTP_202_ACCEPTED,
        )

    def _get_ppv_overrides(self, request_data):
        data = {
            ShelfType.TYPE01.production_code: request_data.get('type01_ppv'),
            ShelfType.VENEER_TYPE01.production_code: request_data.get('type01v_ppv'),
            ShelfType.TYPE02.production_code: request_data.get('type02_ppv'),
            ShelfType.TYPE03.production_code: request_data.get('type03_ppv'),
            ShelfType.TYPE13.production_code: request_data.get('type13_ppv'),
        }
        return {shelf_type: int(ppv) for shelf_type, ppv in data.items() if ppv}


class CodenamesReportStatusView(APIView):
    authentication_classes = (
        BasicAuthentication,
        TokenAuthentication,
    )

    def get(self, request, *args, **kwargs):
        task_id = request.query_params['task_id']
        task = AsyncResult(task_id)
        if task.status == 'SUCCESS':
            result = task.result
            report_url = result['filename']
            if not report_url.startswith('http'):
                # report is not saved to s3 - let's create a local url
                site_url = (
                    settings.SITE_URL[:-1]
                    if settings.SITE_URL.endswith('/')
                    else settings.SITE_URL
                )
                report_url = f'{site_url}{report_url}'
            return Response(
                {
                    'report_url': report_url,
                    'errors': result['errors'],
                },
            )
        if task.status == 'FAILURE':
            if cache.get('codenames_report_task_id') == task_id:
                cache.set('code_names_report_task_id', None)
            return Response(
                status=status.HTTP_400_BAD_REQUEST,
                data={'error': str(task.result)},
            )
        if task.status == 'PENDING':
            return Response(status=status.HTTP_202_ACCEPTED)


class PotentialCodenamesReportDifferences(APIView):
    """
    Endpoint used in Margin Tests.

    Gets data that has impact on the results. In the test it compares said data
    between two environments.
    """

    authentication_classes = (
        BasicAuthentication,
        TokenAuthentication,
    )

    @staticmethod
    def extract_and_parse_important_pf_entries_from_ps(pf_from_ps):
        important_pfs = {}
        important_keys = {
            'unit',
            'length',
            'thickness',
            'loss_factor',
            'price_per_unit',
            'weight_per_unit',
        }
        for key in important_keys:
            flattened_codenames = {}
            codenames_dict = pf_from_ps.get(key, {})
            flattened_codenames.update(codenames_dict)
            important_pfs[key] = flattened_codenames
        return important_pfs

    def get(self, request, *args, **kwargs):
        pf_from_ps = get_active_price_factors_from_ps()
        important_pfs_from_ps = self.extract_and_parse_important_pf_entries_from_ps(
            pf_from_ps,
        )

        pf = PricingFactors.get_solo()
        pf_from_cstm = json.loads(pf.data)

        all_managed_info = ElementManagedInfo.objects.all()
        managed_info_dict = defaultdict(dict)
        for single_managed_info in all_managed_info:
            managed_info_dict[single_managed_info.codename].update(
                {single_managed_info.manufactor.name: single_managed_info.managed_by_us}
            )

        management_costs = MaterialManagementCost.objects.all()
        management_costs_dict = {
            management_cost.manufactor.name: management_cost.management_cost
            for management_cost in management_costs
        }

        return Response(
            {
                'pricing_factors_cstm': pf_from_cstm,
                'pricing_factors_ps': important_pfs_from_ps,
                'element_managed_infos': managed_info_dict,
                'management_costs': management_costs_dict,
            }
        )


class EcoTaxReportView(FormView):
    form_class = TaxReportForm
    template_name = 'admin/tax_report.html'

    def get_success_url(self):
        messages.add_message(
            self.request,
            messages.SUCCESS,
            'Report will be sent to the recipients emails.',
        )
        return reverse('admin:ecotax-report')

    def form_valid(self, form):
        start_date = form.cleaned_data['start_date']
        end_date = form.cleaned_data['end_date']
        product_ids = get_product_ids_for_ecotax_report(start_date, end_date)
        sample_boxes = get_sample_box_ecotax_info(start_date, end_date)
        generate_ecotax_report.delay(
            product_ids=product_ids,
            sample_boxes=sample_boxes,
            send_to=self.request.user.username,
            start_date=str(start_date),
            end_date=str(end_date),
        )
        return super().form_valid(form)


class ElectroTaxReportView(FormView):
    form_class = TaxReportForm
    template_name = 'admin/tax_report.html'

    def get_success_url(self):
        messages.add_message(
            self.request,
            messages.SUCCESS,
            'Report will be sent to the recipients emails.',
        )
        return reverse('admin:electrotax-report')

    def form_valid(self, form):
        start_date = form.cleaned_data['start_date']
        end_date = form.cleaned_data['end_date']
        generate_electrotax_report.delay(
            send_to=self.request.user.username,
            start_date=start_date.isoformat(),
            end_date=end_date.isoformat(),
        )
        return super().form_valid(form)


class SerializedItemsView(APIView):
    authentication_classes = (TokenAuthentication,)

    def get(self, request, *args, **kwargs):
        exporter = {
            'cpf': CustomPricingFactorsExporter,
            'pfi': PricingFactorItemExporter,
            'emi': ElementManagedInfoExporter,
            'code': ManufacturerCodeExporter,
            'mmc': MaterialManagementCostExporter,
        }[kwargs['items_kind']]
        return Response(exporter().to_dict())


class DontDDOSUsThrottle(AnonRateThrottle):
    rate = '1/minute'


class PFUpdater(APIView):
    # this one doesn't need permissions, as it will do nothing on prod
    # and displays nothing. I'm adding a really heavy throttle in case some wannabe
    # hacker wants to DDOS this view.
    permission_classes = (AllowAny,)
    throttle_classes = (DontDDOSUsThrottle,)

    def post(self, request, *args, **kwargs):
        update_pfs_and_send_to_ps.delay()
        return Response()


class PFUpdaterClean(PFUpdater):
    def post(self, request, *args, **kwargs):
        update_pfs_and_send_to_ps.delay(clean=True)
        return Response()


class CPFManufacturerCodeView(APIView):
    authentication_classes = (TokenAuthentication,)

    def get(self, request, *args, **kwargs):
        requested_cpfs = request.GET.get('codenames').split(',')
        manufacturer = request.GET.get('manufacturer')
        cpfs = (
            CustomPricingFactor.objects.valid_for_today()
            .prefetch_related('pricing_factor_item', 'manufacturer_code')
            .filter(
                manufactor=manufacturer,
                pricing_factor_item__codename__in=requested_cpfs,
            )
        )
        serializer = FittingInstructionsCPFSerializer(cpfs, many=True)
        return Response(serializer.data)
