import logging

from django.utils import translation

from promotions.utils import (
    get_active_promotion_config,
    strikethrough_promo,
)
from regions.cached_region import get_region_data_from_request

logger = logging.getLogger('cstm')


def promotion_cp(request):
    language = translation.get_language()
    region = get_region_data_from_request(request)

    context = {'is_global_promo_on': bool(strikethrough_promo(region=region))}
    actual_promotion_config = get_active_promotion_config(region)

    # sale enabled
    context['promotion'] = actual_promotion_config
    if not actual_promotion_config:
        return context

    context['promo_code'] = getattr(
        actual_promotion_config.promotion.promo_code, 'code', ''
    )

    # copy
    context |= actual_promotion_config.get_copy(language, region)
    for key, value in list(context.items()):
        if isinstance(value, str) and "'" in value:
            context[key] = value.replace("'", "\'")

    return context
