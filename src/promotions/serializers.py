from decimal import Decimal

from rest_framework import serializers

from promotions.fields import PromotionConfigTranslatedField
from promotions.models import (
    CountdownConfig,
    Promotion,
    PromotionConfig,
    PromotionConfigCopy,
    RibbonLine,
)
from promotions.services.categories_in_promo import get_categories_in_promo
from vouchers.models import (
    ItemDiscount,
    Voucher,
    VoucherGroup,
    VoucherRegionEntry,
)
from vouchers.serializers import PromoVoucherSerializer


class RibbonLineSerializer(serializers.ModelSerializer):
    copy_header_mobile = serializers.SerializerMethodField()

    class Meta:
        model = RibbonLine
        fields = (
            'copy_header',
            'copy_header_mobile',
            'copy_link',
            'show_timer',
        )

    def get_copy_header_mobile(self, obj: RibbonLine) -> str:
        return obj.copy_header_mobile or obj.copy_header


class PromotionConfigCopySerializer(serializers.ModelSerializer):
    ribbon = RibbonLineSerializer(source='ribbon_lines', many=True)

    class Meta:
        model = PromotionConfigCopy
        fields = (
            'language',
            'cart_ribbon_copy_header_1',
            'cart_ribbon_copy_header_mobile_1',
            'configurator_copy_promo_alert_1',
            'configurator_copy_promo_alert_mobile_1',
            'grid_copy_slot_1_header_1',
            'ribbon',
        )


class CountdownConfigSerializer(serializers.ModelSerializer):
    end_date = serializers.SerializerMethodField()

    class Meta:
        model = CountdownConfig
        fields = (
            'show_on_pdp',
            'start_date',
            'chevron_theme',
            'text_color',
            'end_date',
        )

    @staticmethod
    def get_end_date(obj: CountdownConfig) -> str:
        if obj.end_date:
            return obj.end_date.isoformat()
        return obj.promotion_config.promotion.end_date.isoformat()


class PromotionConfigSerializer(serializers.ModelSerializer):
    copies = PromotionConfigCopySerializer(many=True)
    countdown = CountdownConfigSerializer(allow_null=True, required=False)

    class Meta:
        model = PromotionConfig
        fields = (
            'enabled_regions',
            'created_at',
            'theme',
            'theme_light',
            'hp_promo',
            'hp_promo_video',
            'ribbon_enabled',
            'ribbon_text_color',
            'ribbon_background_color',
            'cart_ribbon_enabled',
            'configurator_new_alert_enabled',
            'configurator_alert_enabled',
            'grid_show_promo_value',
            'grid_show_category_promotion',
            'copies',
            'countdown',
            'extra_data',
        )


class PromotionImportExportSerializer(serializers.ModelSerializer):
    configs = PromotionConfigSerializer(many=True)
    promo_code = PromoVoucherSerializer(allow_null=True, required=False)

    class Meta:
        model = Promotion
        fields = (
            'configs',
            'promo_code',
            'start_date',
            'end_date',
            'created_at',
            'strikethrough_pricing',
        )

    def create(self, validated_data: dict) -> Promotion:
        configs_data = validated_data.pop('configs', [])
        promo_code_data = validated_data.pop('promo_code', None)

        voucher = self.create_voucher(promo_code_data)
        promo = Promotion.objects.create(**validated_data, promo_code=voucher)

        self.create_promotion_configs(promo, configs_data)

        return promo

    @staticmethod
    def create_voucher(promo_code_data: dict) -> Voucher:
        discounts_data = promo_code_data.pop('discounts', [])
        group_data = promo_code_data.pop('group', {})
        region_entries_data = promo_code_data.pop('region_entries', [])

        group = None
        if group_data:
            regions = group_data.pop('region')
            group = VoucherGroup.objects.create(**group_data)
            if regions:
                group.region.add(*regions)

        voucher = Voucher.objects.create(**promo_code_data, group=group)
        for discount in discounts_data:
            item_discount = ItemDiscount.objects.create(**discount)
            voucher.discounts.add(item_discount)

        for region_entry in region_entries_data:
            VoucherRegionEntry.objects.create(**region_entry, voucher=voucher)

        return voucher

    @staticmethod
    def create_promotion_configs(promo: Promotion, configs_data: list[dict]) -> None:
        for config_data in configs_data:
            enabled_regions = config_data.pop('enabled_regions', [])
            copies = config_data.pop('copies', [])
            countdown = config_data.pop('countdown', None)

            promotion_config = PromotionConfig.objects.create(
                **config_data,
                promotion=promo,
            )
            promotion_config.enabled_regions.add(*enabled_regions)

            if countdown:
                CountdownConfig.objects.create(
                    **countdown,
                    promotion_config=promotion_config,
                )

            for copy in copies:
                ribbon_lines_data = copy.pop('ribbon_lines')
                copy_config = PromotionConfigCopy.objects.create(
                    **copy, config=promotion_config
                )
                ribbon_objects = [
                    RibbonLine(config_copy=copy_config, **line)
                    for line in ribbon_lines_data
                ]
                RibbonLine.objects.bulk_create(ribbon_objects)


class RibbonLineSimpleSerializer(serializers.Serializer):
    copy_header = serializers.CharField()
    copy_header_mobile = serializers.CharField()
    copy_link = serializers.URLField()
    show_timer = serializers.BooleanField()


class RibbonSerializer(serializers.ModelSerializer):
    enabled = serializers.BooleanField(source='ribbon_enabled', default=False)
    text_color = serializers.CharField(source='ribbon_text_color', default='')
    background_color = serializers.CharField(
        source='ribbon_background_color',
        default='',
    )
    lines = serializers.SerializerMethodField()

    class Meta:
        model = PromotionConfig
        field_prefix = 'ribbon_copy'
        fields = (
            'enabled',
            'text_color',
            'background_color',
            'lines',
        )

    def get_lines(self, obj: PromotionConfig) -> list[dict]:
        return RibbonLineSimpleSerializer(
            self.context.get('promotion_copy_data', {}).get('ribbon_copy_lines'),
            many=True,
        ).data


class CartRibbonSerializer(serializers.ModelSerializer):
    enabled = serializers.BooleanField(source='cart_ribbon_enabled', default=False)
    header_mobile = PromotionConfigTranslatedField(source='header_mobile_1')
    header = PromotionConfigTranslatedField(source='header_1')

    class Meta:
        model = PromotionConfig
        field_prefix = 'cart_ribbon_copy'
        fields = (
            'enabled',
            'header',
            'header_mobile',
        )


class SimplePromotionSerializer(serializers.ModelSerializer):
    code = serializers.CharField(source='promo_code.code')
    value = serializers.CharField(source='promo_code.value')
    percentage = serializers.BooleanField(source='promo_code.is_percentage')
    min_threshold = serializers.SerializerMethodField()
    max_threshold = serializers.SerializerMethodField()
    categories = serializers.SerializerMethodField()

    class Meta:
        model = Promotion
        fields = (
            'code',
            'value',
            'percentage',
            'strikethrough_pricing',
            'end_date',
            'min_threshold',
            'max_threshold',
            'categories',
        )

    def get_min_threshold(self, obj: Promotion) -> Decimal:
        region_entry = self.context['promotion_region_data']
        return region_entry.amount_starts

    def get_max_threshold(self, obj: Promotion) -> Decimal:
        region_entry = self.context['promotion_region_data']
        return region_entry.amount_limit

    def get_categories(self, _) -> list:
        return get_categories_in_promo(region=self.context['region'])


class PromotionGlobalSerializer(serializers.ModelSerializer):
    cart_ribbon = CartRibbonSerializer(source='*')
    ribbon = RibbonSerializer(source='*')
    countdown = CountdownConfigSerializer(allow_null=True)
    promotion_data = SimplePromotionSerializer(source='promotion', allow_null=True)

    class Meta:
        model = PromotionConfig
        fields = (
            'cart_ribbon',
            'ribbon',
            'countdown',
            'promotion_data',
            'extra_data',
        )

    def to_representation(self, instance):
        representation = super().to_representation(instance)
        if hasattr(instance, 'countdown') and not instance.countdown.is_active():
            representation['countdown'] = None
        return representation
