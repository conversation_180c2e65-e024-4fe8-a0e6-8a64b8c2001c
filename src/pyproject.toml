[tool.poetry]
name = "cstm"
version = "0.1.0"
description = ""
authors = []
package-mode = false
requires-poetry = ">=2.1"

[tool.poetry.dependencies]
python = "3.11.*"

adyen = "10.0.*"
Babel = "2.11.*"
api-client = "^1.3.1"
bleach = "4.0.*"
boto3 = "^1.34"
botocore = "^1.34"
cached-property = "~1.5.2"
celery = "5.5.3"
cloudinary = "^1.28.0"
cryptography = "^42.0.4"
djangorestframework-dataclasses = "^1.2.0"
datadog = "0.44.*"
dateparser = "1.0.*"
ddtrace = "^1.13.4"
deepl = "~1.18"
Django = "~4.2"
django-activity-stream = "1.4.*"
django-admin-sortable2 = "2.0"
django-admin-lightweight-date-hierarchy = "1.1.0"
django-admin-rangefilter = "0.9.*"
django-allauth = "0.51.0"
django-cors-headers = "3.13.0"
# fixed version due to: https://github.com/django-ckeditor/django-ckeditor/issues/761
# TODO: check best way to keep it update
django-ckeditor = "6.7.0"
django-crispy-forms = "1.14.*"
django-environ = "0.9.*"
django-extensions = "3.2.*"
django-filter = "22.1.*"
django-fsm = "~2.8.1"
django-fsm-log = "~3.0.0"
django-health-check = "~3.17.0"
django-inline-svg = "0.1.*"
django-json-widget = "1.1.1"
django-jsonform = "2.23.*"
django-nested-admin = "~4.1.1"
django-redis = "5.2.*"
django-requestlogging-redux = "1.2.1" # last version from 2017 - do we still want it?
django-simple-history = "~3.1.1"
django-safedelete = "~1.3.1"
django-smart-selects = "~1.6.0"
django-sortedm2m = "^4.0.0"
django-storages = "1.13" # consult bump with platform team
django-taggit = "^6"
django-object-actions = "^4.2.0"
django-webpack-loader = "~1.8.0"
djangorestframework = "3.14.*"
djangorestframework-camel-case = "~1.3.0"
dj-rest-auth = "^2.2.6"
drf-spectacular = "^0.27.1"
drf-writable-nested = "~0.7.0"
ezdxf = "0.16.*"  # consider removing - dxfs were moved to PS?
facebook_business = "11.0.*"  # should work, but I am not 100% sure
flower = "~2.0.1"
fuzzywuzzy = "0.18.0"
geoip2 = "4.6.*"
google-api-python-client = "1.12.*"
google-cloud-bigquery = "3.6.*"
google-cloud-language = "1.3.0"
gunicorn = "20.1.0"
hashids = "^1.3.1"
hiredis = "2.2.*"
holidays = "~0.19"
isodate = "0.6.1"
Jinja2 = "3.1.*"
jsonfield = "3.1.0"
jsonpointer = "2.3"
markupsafe = "~2.1.2"
newrelic = "8.5.*"
nltk = "3.8.*"
numpy = "1.24.*"
openpyxl = "3.1.*"
pandas = "1.3.*"
paramiko = "^2.12"
pdfrw = "0.4"
pendulum = "2.1.*"
Pillow = "9.4.*"
psycopg2-binary = "2.9.*"
pychrome = {git = "https://github.com/tylkocom/pychrome.git"}
pycountry = "22.3.*"
pydantic = "2.3.0"
pygments = "~2.15"
pymupdf = "1.22.*"
pynonymizer = "1.24.*"
py-money = "0.5.0"
python-barcode = "0.14.*"
python-json-logger = "2.0.*"
python-magic = "0.4.*"
python-Levenshtein = "0.21.0"  # it's indirect dependency of fuzzywuzzy
python-slugify = "8.0.*"
pytz = "2022.7"
qrcode = "7.3.1"
requests = "~2.31"
sentry-sdk = "^1.14"
setuptools = "^69.2.0"
simplejson = "3.19.2"
slack-sdk = "^3.20.2"
sorl-thumbnail = "12.9.*"
statsd = "4.0.*"
# NOTE: Be careful with this package.
#  It can be cause of wrong PDFs for labels.
#  It can't be updated to 1.4.*, because it is not compatible
#  with previous version and raises errors.
svgwrite = "1.3.*"
tqdm = "~4.64.1"
Unidecode = "1.3.6"
ujson = "5.7.*"
urllib3 = "~1.26"
user-agents = "2.2.*"
zeep = "~4.2.1"

# TODO: migrate to packages from PyPI
django-fixture-magic = { git = "https://github.com/tylkocom/django-fixture-magic" }

# DO NOT PUT STUFF AT THE END. Let's keep the alphabetical order.

[tool.poetry.dev-dependencies]
coverage = "^7.0.5"
django-debug-toolbar = "^3.8.1"
django-silk = "^5.0.3"
django-test-migrations = "^1.2.0"
factory-boy = "^3.2.0"
Faker = "^16.6.0"
freezegun = "~1.2.2"
ipython = "^8.8.0"
pre-commit = "2.21.0"
pytest = "^7.2.1"
pytest-cov = "^4.0.0"
pytest-django = "^4.8.0"
pytest-mock = "^3.10.0"
pytest-socket = "^0.5.1"
pytest-subtests = "^0.9.0"
pytest-sugar = "^0.9"
pytest-xdist = "^3.1.0"
pytest-cases = "^3.8.6"
requests-mock = "^1.10.0"
tach = "^0.29.0"
Werkzeug = "^2.2.2"

[tool.black]
line-length = 88
target-version = ['py310']
skip-string-normalization = true
isort = true


[tool.isort]
line_length = 88
# use black code style
profile = "black"
# add one line between `import abc` and `from abc import ABC`
lines_between_types = 1
# split imports to multline, when 2 or more imports in one line
force_grid_wrap = 2
# Django related settings
known_django = "django"
# add projects module to first party category
known_first_party = '''
    abtests,accounting,admin_customization,automating_batching,
    b2b,catalogue,checkout,complaints,cstm_be,custom,
    custom_audiences,customer_service,dixa,dynamic_delivery,
    ecommerce_api,events,feeds,free_returns,frontend_cms,gallery,
    gallery_editor,internal_api,invoice,items_for_render,
    kpi,logger,loose_ends,mailing,material_recovery,
    model_transfers,orders,payments,pricing_v3,
    producers,product_feeds,production_margins,promotions,
    rating_tool,regions,render_tasks,rest_auth,reviews,
    shortener,taskapp,user_consents,user_profile,vouchers,waiting_list,warehouse
'''
sections = ['FUTURE','STDLIB','DJANGO','THIRDPARTY','FIRSTPARTY','LOCALFOLDER']

[build-system]
requires = ["poetry>=2.1"]
build-backend = "poetry.masonry.api"

