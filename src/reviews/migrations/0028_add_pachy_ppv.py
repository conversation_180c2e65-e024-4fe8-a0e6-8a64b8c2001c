# Generated by Django 3.2.16 on 2023-02-06 15:10

from django.db import (
    migrations,
    models,
)

import custom.enums.enums


class Migration(migrations.Migration):

    dependencies = [
        ('reviews', '0027_add_new_languages'),
    ]

    operations = [
        migrations.AlterField(
            model_name='review',
            name='physical_product_version',
            field=models.PositiveSmallIntegerField(
                choices=[
                    (1, 'TREX'),
                    (2, 'RAPTOR'),
                    (3, 'DIPLO'),
                    (4, 'PTERO'),
                    (5, 'BAMBI'),
                    (6, 'STEGO'),
                    (7, 'BRONTO'),
                    (8, 'PACHY'),
                ],
                default=custom.enums.enums.PhysicalProductVersion['TREX'],
                help_text=(
                    'TREX - old shelves, RAPTOR - 2020 Sideboard+ T02, '
                    'DIPLO - early 2021, PTERO - drawers 3.0, '
                    'BAMBI - chipboard supports T01, '
                    'STEGO - chipboard drawer T02,'
                    'BRONTO - non-spring pins in backs and supports'
                ),
            ),
        ),
    ]
