import pytest

from custom.enums import (
    Furniture,
    ShelfType,
)
from orders.enums import OrderStatus
from reviews import utils


@pytest.mark.django_db
class TestMatchReviewWithOrder:
    def test_match_review_with_order_when_wrong_email(
        self,
        user,
        order_factory,
        watty_factory,
        order_item_factory,
        review_factory,
    ):
        email = '<EMAIL>'
        order = order_factory(
            owner=user,
            items=[],
            email=email,
            status=OrderStatus.DELIVERED,
        )
        watty = watty_factory(owner_id=user.id)
        order_item_factory(order=order, order_item=watty)
        review = review_factory.build(
            email='<EMAIL>', order=None, questions={'13': 8}
        )

        utils.match_review_with_order(review)

        assert review.order is None

    def test_match_review_when_one_order_item_and_client_marked_correct_category(
        self,
        user,
        order_factory,
        watty_factory,
        order_item_factory,
        review_factory,
    ):
        email = '<EMAIL>'
        order = order_factory(
            owner=user,
            items=[],
            email=email,
            status=OrderStatus.DELIVERED,
        )
        watty = watty_factory(owner_id=user.id)
        order_item_factory(order=order, order_item=watty)
        review = review_factory.build(email=email, order=None, questions={'13': 8})

        utils.match_review_with_order(review)

        assert review.order == order

    def test_match_review_with_order_when_client_marked_wrong_category(
        self,
        user,
        order_factory,
        jetty_factory,
        order_item_factory,
        review_factory,
    ):
        email = '<EMAIL>'
        order = order_factory(
            owner=user,
            items=[],
            email=email,
            status=OrderStatus.DELIVERED,
        )
        jetty = jetty_factory(owner_id=user.id)
        order_item_factory(order=order, order_item=jetty)
        review = review_factory.build(email=email, order=None, questions={'13': 8})

        utils.match_review_with_order(review)

        assert review.order == order

    @pytest.mark.parametrize('category_number', [8, 2])
    def test_match_review_with_order_when_two_order_items_in_order(
        self,
        user,
        order_factory,
        watty_factory,
        jetty_factory,
        order_item_factory,
        review_factory,
        category_number,
    ):
        email = '<EMAIL>'
        order = order_factory(
            owner=user,
            items=[],
            email=email,
            status=OrderStatus.DELIVERED,
        )
        watty = watty_factory(owner_id=user.id)
        jetty = jetty_factory(owner_id=user.id)
        order_item_factory(order=order, order_item=watty)
        order_item_factory(order=order, order_item=jetty)
        review = review_factory.build(
            email=email, order=None, questions={'13': category_number}
        )

        utils.match_review_with_order(review)

        assert review.order == order

    def test_match_review_with_order_when_only_sample_box_ordered(
        self,
        user,
        order_factory,
        sample_box_factory,
        order_item_factory,
        review_factory,
    ):
        email = '<EMAIL>'
        order = order_factory(
            owner=user,
            items=[],
            email=email,
            status=OrderStatus.DELIVERED,
        )
        sample_box = sample_box_factory(owner_id=user.id)
        order_item_factory(order=order, order_item=sample_box)
        review = review_factory.build(email=email, order=None, questions={'13': 2})

        utils.match_review_with_order(review)

        assert review.order is None

    @pytest.mark.parametrize(
        'order_status',
        [
            OrderStatus.CANCELLED,
            OrderStatus.DRAFT,
            OrderStatus.PAYMENT_PENDING,
            OrderStatus.IN_PRODUCTION,
            OrderStatus.SHIPPED,
            OrderStatus.PAYMENT_FAILED,
            OrderStatus.TO_BE_SHIPPED,
            OrderStatus.CART,
        ],
    )
    def test_match_review_with_order_when_order_not_yet_delivered(
        self,
        user,
        order_factory,
        order_status,
        watty_factory,
        order_item_factory,
        review_factory,
    ):
        email = '<EMAIL>'
        order = order_factory(
            owner=user,
            items=[],
            email=email,
            status=order_status,
        )
        watty = watty_factory(owner_id=user.id)
        order_item_factory(order=order, order_item=watty)
        review = review_factory.build(email=email, order=None, questions={'13': 8})

        utils.match_review_with_order(review)

        assert review.order is None


@pytest.mark.django_db
def test_fill_review_data_with_order_item(
    user,
    order_factory,
    watty_factory,
    order_item_factory,
    review_factory,
):
    order = order_factory(owner=user, items=[])
    watty = watty_factory(
        owner_id=user.id,
        shelf_type=ShelfType.TYPE03.value,
    )
    order_item = order_item_factory(order=order, order_item=watty)
    review = review_factory.build(
        order=order,
        shelf_type=None,
    )

    utils.fill_review_data_from_order_item(review, order_item)

    assert review.shelf_type == ShelfType.TYPE03.value
    assert review.furniture_type == Furniture.watty.value
