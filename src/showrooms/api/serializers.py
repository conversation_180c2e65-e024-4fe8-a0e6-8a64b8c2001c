from typing import Optional

from rest_framework import serializers

from custom.fields import ShortT<PERSON>Field
from showrooms.enums import ShowRoomItemEnum
from showrooms.models import ShowRoom


class ShowRoomItemsSerializer(serializers.Serializer):
    name = serializers.CharField()
    is_visible = serializers.BooleanField()


class ShowRoomSerializer(serializers.ModelSerializer):
    showroom_items = serializers.SerializerMethodField()
    phone = serializers.SerializerMethodField()
    region = serializers.CharField(source='region.name')

    mon_open_time = ShortTimeField()
    mon_close_time = ShortTimeField()
    tue_open_time = ShortTimeField()
    tue_close_time = ShortTimeField()
    wed_open_time = ShortTimeField()
    wed_close_time = ShortTimeField()
    thu_open_time = ShortTimeField()
    thu_close_time = ShortTimeField()
    fri_open_time = ShortTimeField()
    fri_close_time = ShortTimeField()
    sat_open_time = ShortTimeField()
    sat_close_time = ShortTimeField()
    sun_open_time = ShortTimeField()
    sun_close_time = ShortTimeField()

    class Meta:
        model = ShowRoom
        fields = (
            'postal_code',
            'city',
            'street_address',
            'phone',
            'email',
            'region',
            'latitude',
            'longitude',
            'name',
            'link',
            'kind',
            'display_image',
            'mon_open_time',
            'mon_close_time',
            'tue_open_time',
            'tue_close_time',
            'wed_open_time',
            'wed_close_time',
            'thu_open_time',
            'thu_close_time',
            'fri_open_time',
            'fri_close_time',
            'sat_open_time',
            'sat_close_time',
            'sun_open_time',
            'sun_close_time',
            'showroom_items',
        )

    def get_showroom_items(self, obj: ShowRoom) -> list[dict[str, any]]:
        visible_items = set(obj.showroom_items)
        all_items = [
            {'name': choice.label, 'is_visible': choice.value in visible_items}
            for choice in ShowRoomItemEnum
        ]
        return all_items

    def get_phone(self, obj: ShowRoom) -> Optional[str]:
        if obj.phone_prefix and obj.phone:
            return f'{obj.phone_prefix}{obj.phone}'
        elif obj.phone:
            return obj.phone
        else:
            return
