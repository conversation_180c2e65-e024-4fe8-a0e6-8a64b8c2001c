from django_filters.rest_framework import Django<PERSON>ilterBackend
from djangorestframework_camel_case.parser import CamelC<PERSON><PERSON><PERSON><PERSON>ars<PERSON>
from djangorestframework_camel_case.render import CamelCaseJ<PERSON><PERSON>enderer
from rest_framework import mixins
from rest_framework.permissions import AllowAny
from rest_framework.viewsets import GenericViewSet

from showrooms.api.serializers import ShowRoomSerializer
from showrooms.models import ShowRoom
from showrooms.utils import showroom_available_regions


class ShowRoomViewSet(mixins.ListModelMixin, GenericViewSet):
    queryset = ShowRoom.objects.all().select_related('region')
    serializer_class = ShowRoomSerializer
    renderer_classes = (CamelCaseJSONRenderer,)
    parser_classes = (CamelCaseJSONParser,)
    authentication_classes = ()
    permission_classes = (AllowAny,)
    filter_backends = [DjangoFilterBackend]
    filterset_fields = ['region__name']

    def list(self, request, *args, **kwargs):
        response = super().list(request, *args, **kwargs)
        response.data = {
            'results': response.data,
            'availableRegions': showroom_available_regions(),
        }
        return response
