{% extends "admin/change_form.html" %}
{% load defaulttags %}
{% load i18n %}
{% load admin_urls %}
{% load activity_tags %}
{% load humanize %}
{% load region_tags %}
{% load static %}

{% block extrahead %}
    <link href="{% static 'css/bootstrap.min.css' %}" rel="stylesheet">

    <link href="{% static 'css/bootcards-desktop.css' %}" rel="stylesheet">

    <script src="{% static 'js/bootcards.min.js' %}"></script>

    <!-- MetisMenu CSS -->
    <link href="{% static 'css/plugins/metisMenu/metisMenu.min.css' %}" rel="stylesheet">

    <!-- Timeline CSS -->
    <link href="{% static 'css/plugins/timeline.css' %}" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'css/sb-admin-2.css' %}" rel="stylesheet">

    <!-- <PERSON> Charts CSS -->
    <link href="{% static 'css/plugins/morris.css' %}" rel="stylesheet">

    <!-- Custom Fonts -->
    <link href="{% static 'font-awesome-4.1.0/css/font-awesome.min.css' %}" rel="stylesheet" type="text/css">
    <script src="{% static 'js/plugins/morris/raphael.min.js' %}"></script>
    <script src="{% static 'js/plugins/morris/morris.js' %}"></script>
    <!-- HTML5 Shim and Respond.js IE8 support of HTML5 elements and media queries -->
    <!-- WARNING: Respond.js doesn't work if you view the page via file:// -->
    <!--[if lt IE 9]>
        <script src="https://oss.maxcdn.com/libs/html5shiv/3.7.0/html5shiv.js"></script>
        <script src="https://oss.maxcdn.com/libs/respond.js/1.4.2/respond.min.js"></script>
    <![endif]-->

{% endblock %}

{% block breadcrumbs %}
    <div class="breadcrumbs">
        <a href="{% url 'admin:index' %}">{% trans 'Home' %}</a> &rsaquo;
        <a href="../">{{ app_label|capfirst|escape }}</a> &rsaquo;
        {{ opts.verbose_name|capfirst }}
    </div>
{% endblock %}
{% block content %}
    {% activity_stream 'actor' original %}
    <div class="container bootcards-container push-right">
        <!-- This is where you come in... -->
        <!-- I've added some sample data below so you can get a feel for the required markup -->

        <div class="row">

            <div class="col-sm-6 bootcards-cards hidden-xs">

                <!--contact details -->
                <div id="contactCard">

                    <div class="panel panel-default">
                        <div class="panel-heading clearfix">
                            <h3 class="panel-title pull-left">{{ original.user.username }} details</h3>
                        </div>
                        <div class="list-group">
                            <div class="list-group-item">
                                <label>Name</label>
                                <h4 class="list-group-item-heading">{{ original.first_name|default:"Not set" }} {{ original.last_name|default:"Not set" }}</h4>
                            </div>
                            <div class="list-group-item">
                                <label>Account type</label>
                                <h4 class="list-group-item-heading">{{ original.get_user_type_display }}</h4>
                            </div>

                            <div class="list-group-item">
                                <label>Email</label>
                                <h4 class="list-group-item-heading">{{ original.email }}</h4>
                            </div>

                            <div class="list-group-item">
                                <label>Gender</label>
                                <h4 class="list-group-item-heading">{{ original.get_gender_display }}</h4>
                            </div>

                            <div class="list-group-item">
                                <label>Language</label>
                                <h4 class="list-group-item-heading">{{ original.language }}</h4>
                            </div>

                            <div class="list-group-item">
                                <label>Phone</label>
                                <h4 class="list-group-item-heading">{{ original.phone|default:"Not set" }}</h4>
                            </div>
                            <div class="list-group-item">
                                <label>First contacted with webpage</label>
                                <h4 class="list-group-item-heading">{{ original.registration_first_contact }}</h4>
                            </div>
                            <div class="list-group-item">
                                <label>Registered</label>
                                <h4 class="list-group-item-heading">{{ original.user.date_joined }}</h4>
                            </div>
                            <div class="list-group-item">
                                <label>Last login</label>
                                <h4 class="list-group-item-heading">{{ original.user.last_login }}</h4>
                            </div>
                            <div class="list-group-item">
                                <label>Registration source</label>
                                <h4 class="list-group-item-heading">{{ original.get_registration_source_display }}</h4>
                            </div>
                            <div class="list-group-item">
                                <label>Registration utm</label>
                                <h4 class="list-group-item-heading">{{ original.registration_referrer }}</h4>
                            </div>
                            <div class="list-group-item">
                                <label>Registration referrer</label>
                                <h4 class="list-group-item-heading">{{ original.registration_referrer_uri }}</h4>
                            </div>
                        </div>

                        <div class="panel-footer">
                            <a class="btn btn-link btn-xs pull-right" href="/admin/auth/user/{{ original.user.id }}/">
                                <small class="pull-left">Go to user edit/profile view</small>
                                </a>
                        </div>
                    </div>

                </div><!--contact card-->
            </div><!--list-details-->

            <div class="col-sm-6 bootcards-cards hidden-xs">

                <!--contact details -->
                <div id="contactHistoryCard">

                    <div class="panel panel-default">
                        <div class="panel-heading clearfix">
                            <h3 class="panel-title pull-left">Contact History</h3>
                        </div>
                        {% for contact in original.contact_history %}
                        <div class="list-group">
                            <div class="list-group-item">
                                <label>{{ contact.get_contact_channel_display }} @ {{ contact.created_at }} by {{ contact.contacted_by }}</label>
                                <h4 class="list-group-item-heading">{{ contact.description }}</h4>
                            </div>
                        </div>
                        {% endfor %}
                    </div>
                </div><!--contact card-->

            </div><!--list-details-->

        </div><!--row-->

        <div class="row">

            <div class="col-sm-12 bootcards-cards hidden-xs">

                <!--contact details -->
                <div id="ConnectedList">

                    <div class="panel panel-default">
                        <div class="panel-heading clearfix">
                            <h3 class="panel-title pull-left">Connected accounts (registration/logins)</h3>
                        </div>
                        <div class="list-group">
                            {% for account in original.get_connected_accounts %}
                                <a class="list-group-item" href="/admin/user_profile/userprofile/{{ account.id }}/">
                                    <div class="row">
                                        <div class="col-sm-6">
                                            <p class="list-group-item-text">Username: {{ account.user.username }} / {{ account.get_user_type_display }}</p>
                                            <p class="list-group-item-text">First contact: {{ account.registration_first_contact }}</p>
                                            <p class="list-group-item-text">Registration date: {{ account.user.date_joined }}</p>
                                            <p class="list-group-item-text">Last login: {{ account.user.last_login }}</p>
                                        </div>
                                        <div class="col-sm-6">
                                            <p class="list-group-item-text">Registration source: {{ account.get_registration_source_display }}</p>
                                            <p class="list-group-item-text">Registration utm: {{ account.registration_referrer }} </p>
                                            <p class="list-group-item-text">Registration referrer: {{ account.registration_referrer_uri }} </p>
                                        </div>
                                    </div>
                                </a>
                            {% endfor %}
                        </div>

                        <div class="panel-footer">
                            <a class="btn btn-link btn-xs pull-right" href="/admin/auth/orders/">
                                <small class="pull-left">Go to orders</small>
                            </a>
                        </div>
                    </div>

                </div><!--contact card-->
            </div><!--list-details-->

        </div>


        <div class="row">

            <div class="col-sm-12 bootcards-cards hidden-xs">

                <!--contact details -->
                <div id="LibraryList">

                    <div class="panel panel-default">
                        <div class="panel-heading clearfix">
                            <h3 class="panel-title pull-left">Library</h3>
                        </div>
                        <div class="list-group">
                            {% for item in original.library_jetty_set %}
                                    <a class="list-group-item" href="/admin/gallery/jetty/{{ item.id }}/">
                                        <div class="row">
                                            <div class="col-sm-3">
                                                <p class="list-group-item-text">Matertials: {{ item.translated_material_name }} / {{ item.get_material }}</p>
                                                <p class="list-group-item-text">Dimensions: {% for dimension in item.get_dimensions %}{{ dimension.label }}&nbsp;{{ dimension.value|floatformat }}{% if not forloop.last %} {% endif %}{% endfor %} cm</p>
                                            </div>
                                            <div class="col-sm-3">
                                                <p class="list-group-item-text"> Added: {{ item.created_at|naturalday }}</p>
                                                <p class="list-group-item-text"> Price: {% regionalize_and_display_price item original.region %} </p>
                                            </div>
                                            <div class="col-sm-6">
                                                    <img style="width: 270px; height:200px" src="{{ item.preview | file_url}}">
                                            </div>
                                        </div>
                                    </a>
                            {% endfor %}
                        </div>

                        <div class="panel-footer">
                            <a class="btn btn-link btn-xs pull-right" href="/admin/auth/orders/">
                                <small class="pull-left">Go to orders</small>
                            </a>
                        </div>
                    </div>

                </div><!--contact card-->
            </div><!--list-details-->

        </div>

        <div class="row">

            <div class="col-sm-12 bootcards-cards hidden-xs">

                <!--contact details -->
                <div id="CartList">

                    <div class="panel panel-default">
                        <div class="panel-heading clearfix">
                            <h3 class="panel-title pull-left">Cart</h3>
                        </div>
                        <div class="list-group">
                            {% for order in original.user.order_set.all %}
                                {% if order.status != 9 %}

                                {% else %}
                                    <a class="list-group-item" href="/admin/orders/order/{{ order.id }}/">
                                        <div class="row">
                                            <div class="col-sm-3">
                                                <p class="list-group-item-text"> {{ order.created_at|naturalday }}</p>
                                                <p class="list-group-item-text">Order: <span class="btn btn-success">{{ order.get_status_display }}</span</p>
                                                <p class="list-group-item-text">Production: {{ order.get_status_display }}</p>
                                            </div>
                                            <div class="col-sm-3">
                                                <p class="list-group-item-text"> Total: {{ order.get_total_value_display }}</p>
                                                <p class="list-group-item-text"> Items: {{ order.get_items_as_string|safe }}</p>
                                            </div>
                                            <div class="col-sm-6">
                                                {% for item in order.items.all %}
                                                    <img style="width: 135px; height:100px" src="{{ item.order_item.preview | file_url }}">
                                                {% endfor %}
                                            </div>
                                        </div>
                                    </a>
                                {% endif %}
                            {% endfor %}
                        </div>

                        <div class="panel-footer">
                            <a class="btn btn-link btn-xs pull-right" href="/admin/auth/orders/">
                                <small class="pull-left">Go to orders</small>
                            </a>
                        </div>
                    </div>

                </div><!--contact card-->
            </div><!--list-details-->

        </div>

        <div class="row">

            <div class="col-sm-12 bootcards-cards hidden-xs">

                <!--contact details -->
                <div id="orderList">

                    <div class="panel panel-default">
                        <div class="panel-heading clearfix">
                            <h3 class="panel-title pull-left">Orders</h3>
                        </div>
                        <div class="list-group">
                            {% for order in original.user.order_set.all %}
                                {% if order.status == 9 %}

                                {% else %}
                            <a class="list-group-item" href="/admin/orders/order/{{ order.id }}/">
                                <div class="row">
                                    <div class="col-sm-3">
                                        <p class="list-group-item-text"> {{ order.created_at|naturalday }}</p>
                                        <p class="list-group-item-text">Order: <span class="btn btn-success">{{ order.get_status_display }}</span</p>
                                        <p class="list-group-item-text">Production: {{ order.get_status_display }}</p>
                                    </div>
                                    <div class="col-sm-3">
                                        <p class="list-group-item-text"> Total: {{ order.get_total_value_display }}</p>
                                        <p class="list-group-item-text"> Items: {{ order.get_items_as_string|safe }}</p>
                                    </div>
                                    <div class="col-sm-6">
                                        {% for item in order.items.all %}
                                            <img style="width: 135px; height:100px" src="{{ item.order_item.preview | file_url }}">
                                        {% endfor %}
                                    </div>
                                </div>
                            </a>
                                {% endif %}
                            {% endfor %}
                        </div>

                        <div class="panel-footer">
                            <a class="btn btn-link btn-xs pull-right" href="/admin/auth/orders/">
                                <small class="pull-left">Go to orders</small>
                            </a>
                        </div>
                    </div>

                </div><!--contact card-->
            </div><!--list-details-->

        </div>

        <div class="row">
            <div class="col-sm-12">
                <h2>User stream:</h2>
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <i class="fa fa-clock-o fa-fw"></i> User actions
                    </div>
                    <!-- /.panel-heading -->
                    <div class="panel-body">
                        <ul class="timeline">
                            {% for action in stream %}
                                <li {% cycle '' 'class="timeline-inverted"' %}>
                                    <div class="timeline-badge {% if action.data.action_status %} {{ action.data.action_status }} {% endif %}"><i class="fa fa-check"></i>
                                    </div>
                                    <div class="timeline-panel">
                                        <div class="timeline-heading">
                                            <h4 class="timeline-title">User {{ action.verb }} {% if action.target %}{{ action.target }}{% endif %}</h4>
                                            <p><small class="text-muted"><i class="fa fa-clock-o"></i> {{ action.timestamp|naturaltime }} </small>
                                            </p>
                                        </div>
                                        <div class="timeline-body">
                                            <p>{{ action.description|safe }}</p>
                                        </div>
                                    </div>
                                </li>
                            {% endfor %}
                        </ul>
                    </div>
                    <!-- /.panel-body -->
                </div>
            </div>
        </div>

    </div><!--container-->
{% endblock %}
