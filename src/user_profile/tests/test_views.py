import urllib.parse

from unittest.mock import patch

from django.contrib.auth.hashers import make_password
from django.contrib.auth.models import User
from django.core import mail
from django.urls import reverse

import pytest

from rest_framework import status

from carts.choices import CartStatusChoices
from custom.context_processors.settings_cp import (
    get_user_email_hash,
    get_user_hash,
)
from custom.enums import (
    Furniture,
    LanguageEnum,
    SampleBoxVariantEnum,
    ShelfType,
)
from events.choices import BrazeSubscriptionGroupStatus
from events.models import Event
from events.utils import hash_normalized_string
from gallery.enums import FurnitureStatusEnum
from orders.enums import OrderStatus
from regions.models import Country
from user_profile.choices import SubscriptionSources
from user_profile.models import (
    AccessToken,
    LoginAccessToken,
    PasswordResetToken,
)


@pytest.fixture()
def user_password() -> str:
    return 'testpassword'


@pytest.fixture()
def user_(user_password, user_factory) -> User:
    return user_factory(password=make_password(user_password))


@pytest.mark.django_db
class TestForgottenPasswordInit:
    def test_forgotten_password_init_sends_email(
        self,
        api_client,
        user_factory,
        guest_user,
    ):
        url = reverse('rest_reset_password_init')
        api_client.force_authenticate(user=guest_user)
        user2 = user_factory(username='<EMAIL>')

        response = api_client.post(url, {'email': user2.username}, format='json')
        email_sent = mail.outbox[0]

        assert response.status_code == status.HTTP_200_OK
        assert len(mail.outbox) == 1
        assert email_sent.subject == 'Pssst...get your new password!'

    def test_emit_change_password_request_event(
        self,
        api_client,
        guest_user,
        user_factory,
        settings,
    ):
        url = reverse('rest_reset_password_init')
        api_client.force_authenticate(user=guest_user)
        user2 = user_factory(username='<EMAIL>')

        response = api_client.post(url, {'email': user2.username}, format='json')
        assert response.status_code == status.HTTP_200_OK

        forgotten_password_events = Event.objects.filter(
            event_name='ChangePasswordRequestEvent',
        )
        assert forgotten_password_events.exists()

        event = forgotten_password_events.last()
        token = PasswordResetToken.objects.filter(user_id=user2.id).last().token
        assert event.properties['password_reset_url'] == urllib.parse.urljoin(
            settings.SITE_URL,
            reverse('front-changepassword-token', args=(token,)),
        )


@pytest.fixture()
def order_owner(user_profile_factory, currency_rate_factory, region_rate_factory):
    user_profile = user_profile_factory(german=True)
    region = user_profile.region
    currency_rate_factory(currency=region.currency, rate=1)
    region_rate_factory(region=region, rate=1)
    return user_profile.user


@pytest.fixture()
def order_owner_client(order_owner, client):
    client.force_login(user=order_owner)
    return client


@pytest.fixture()
def cart_order(
    order_owner,
    order_factory,
    order_item_factory,
    sample_box_factory,
    sample_box_variant_factory,
):
    region = order_owner.profile.region
    order = order_factory(
        owner=order_owner, status=OrderStatus.CART, items=[], region=region
    )

    sample_box = sample_box_factory(
        owner=order_owner,
        box_variant=sample_box_variant_factory(
            variant_type=SampleBoxVariantEnum.TYPE02.value
        ),
    )
    order_item_factory(order_item=sample_box, order=order)
    return order


@pytest.mark.django_db
class TestLoginAccessTokenCreateView:
    url = reverse('login-access-token')

    def test_correct_token_is_created(self, user, api_client):
        user_data = self._get_user_data(user)
        headers = {
            'HTTP_BRAZE_API_KEY': 'api_key',
            'HTTP_BRAZE_APP_ID': 'app_id',
        }

        api_client.force_authenticate(user)
        response = api_client.post(self.url, data=user_data, format='json', **headers)

        token = response.data.get('LoginAccessToken')

        assert response.status_code == status.HTTP_201_CREATED
        assert 'LoginAccessToken' in response.data.keys()
        assert token == LoginAccessToken.objects.last().token

    def test_no_permission_when_braze_headers_are_missing(self, user, api_client):
        user_data = self._get_user_data(user)
        api_client.force_authenticate(user)
        response = api_client.post(self.url, data=user_data, format='json')

        assert response.status_code == status.HTTP_403_FORBIDDEN

    def test_no_permission_when_braze_headers_dont_match_with_settings(
        self,
        user,
        api_client,
    ):
        user_data = self._get_user_data(user)
        api_client.force_authenticate(user)
        headers = {
            'HTTP_BRAZE_API_KEY': 'wrong_api_key',
            'HTTP_BRAZE_APP_ID': 'wrong_app_id',
        }
        response = api_client.post(self.url, data=user_data, format='json', **headers)

        assert response.status_code == status.HTTP_403_FORBIDDEN

    @staticmethod
    def _get_user_data(user):
        return {'user_id': user.id, 'email': user.email}


@pytest.mark.django_db
class TestUserAccountProfileView:
    url = reverse('account-profile')

    def test_returns_user_data(self, api_client, user_factory, user_profile_factory):
        user = user_factory(email='<EMAIL>')
        user_profile_factory(user=user)
        api_client.force_authenticate(user)

        response = api_client.get(self.url, format='json')

        assert response.status_code == status.HTTP_200_OK
        assert response.data['email'] == '<EMAIL>'

    def test_updates_user_data(self, api_client, user_profile):
        user = user_profile.user
        api_client.force_authenticate(user)

        response = api_client.put(
            self.url,
            data={'city': 'Warsaw', 'postal_code': '02-495'},
            format='json',
        )

        assert response.status_code == status.HTTP_200_OK
        assert user.profile.city == 'Warsaw'
        assert user.profile.postal_code == '02-495'

    def test_language_update_triggers_event(self, api_client, user_profile):
        api_client.force_authenticate(user_profile.user)

        response = api_client.patch(self.url, data={'language': 'fr'}, format='json')

        assert response.status_code == status.HTTP_200_OK
        assert user_profile.language == 'fr'

        language_update_events = Event.objects.filter(event_name='LanguageUpdateEvent')
        assert language_update_events.count() == 1
        assert language_update_events.last().properties == {
            'external_id': hash_normalized_string(user_profile.user.email),
            'language': 'fr',
        }

    def test_updates_order_data_after_updates_profile(
        self,
        api_client,
        user_profile,
        order_factory,
    ):
        user = user_profile.user
        order = order_factory(
            owner=user,
            status=OrderStatus.CART,
            city='Test',
            postal_code='11-222',
        )
        api_client.force_authenticate(user)

        response = api_client.put(
            self.url,
            data={'city': 'Warsaw', 'postal_code': '02-495'},
            format='json',
        )

        assert response.status_code == status.HTTP_200_OK
        assert user.profile.city == 'Warsaw'
        assert user.profile.postal_code == '02-495'
        order.refresh_from_db()
        assert order.city == 'Warsaw'
        assert order.postal_code == '02-495'


@pytest.mark.django_db
class TestUserOrderListView:
    url = reverse('account-orders')

    def test_returns_correct_user_orders_data(
        self, api_client, user_profile, order_factory, order_item_factory
    ):
        user = user_profile.user
        order_factory(
            owner=user, status=OrderStatus.CART, cart__status=CartStatusChoices.DRAFT
        )  # excluded
        order_factory(
            owner=user,
            status=OrderStatus.PAYMENT_PENDING,
            cart__status=CartStatusChoices.ACTIVE,
        )  # excluded
        order_delivered = order_factory(
            owner=user,
            status=OrderStatus.DELIVERED,
            cart__status=CartStatusChoices.ORDERED,
            items=None,
        )
        order_item_factory(order=order_delivered, is_jetty=True)
        order_item_factory(order=order_delivered, is_sample_box=True)
        order_item_factory(order=order_delivered, is_sotty=True)
        order_item_factory(order=order_delivered, is_watty=True)
        expected_keys = [
            'id',
            'created_at',
            'visual_status',
            'order_status',
            'check_status_link',
            'payment_link',
            'invoices',
            'items',
            'cart_id',
        ]
        expected_items_keys = ['title', 'preview', 'dimensions', 'manual']
        api_client.force_authenticate(user)

        response = api_client.get(self.url, format='json')

        assert response.status_code == status.HTTP_200_OK
        response_json = response.json().pop()
        assert response_json['id'] == order_delivered.id
        assert list(response_json.keys()) == expected_keys
        response_items = response_json['items']
        assert list(response_items[0].keys()) == expected_items_keys
        assert {response_item['title'] for response_item in response_items} == {
            'Sample box',
            'Tylko Shelf',
            'Tylko Sofa',
            'Tylko Wardrobe',
        }


@pytest.mark.django_db
class TestMailingUserWishlistAPIView:
    @patch('gallery.models.Jetty.get_compartment_max_load', return_value=1)
    def test_response_200(
        self,
        _,
        jetty_factory,
        sotty_factory,
        watty_factory,
        user_factory,
        api_client,
        country_factory,
    ):
        country_de: Country = country_factory(germany=True)
        region_de = country_de.region
        cached_region_data = region_de.cached_region_data
        user = user_factory(
            profile__language=LanguageEnum.EN,
            profile__region=region_de,
        )
        saved_jetty = jetty_factory(
            furniture_status=FurnitureStatusEnum.SAVED,
            owner=user,
        )
        saved_sotty = sotty_factory(
            furniture_status=FurnitureStatusEnum.SAVED,
            owner=user,
        )
        saved_watty = watty_factory(
            furniture_status=FurnitureStatusEnum.SAVED,
            owner=user,
        )

        url = reverse('mailing-user-wishlist', kwargs={'pk': user.id})
        headers = {'HTTP_BRAZE_APP_ID': 'app_id', 'HTTP_BRAZE_API_KEY': 'api_key'}
        api_client.force_authenticate(user)
        response = api_client.get(url, **headers)

        assert response.status_code == status.HTTP_200_OK
        response_json = response.json()
        assert len(response_json) == 3
        assert response_json == [
            {
                'title': (
                    f'{saved_watty.furniture_category.translated_name} '
                    f'{ShelfType(saved_watty.shelf_type).translated_name}'
                ),
                'furniture_type': Furniture.watty.value,
                'region_price_display': cached_region_data.get_format_price(
                    saved_watty.get_regionalized_price(region=region_de)
                ),
                'image_url': f'{saved_watty.preview.url}',
                'item_url': f'/en-de/furniture/wardrobe/{saved_watty.id},w,/',
                'description': {
                    'name': 'Wardrobe',
                    'material': 'White',
                    'dimensions': 'H30cm, W20cm, D32cm',
                },
                'image_type': 'preview',
            },
            {
                'title': (
                    f'{saved_sotty.furniture_category.translated_name} '
                    f'{ShelfType(saved_sotty.shelf_type).translated_name}'
                ),
                'furniture_type': Furniture.sotty.value,
                'region_price_display': cached_region_data.get_format_price(
                    saved_sotty.get_regionalized_price(region=region_de)
                ),
                'image_url': f'{saved_sotty.preview.url}',
                'item_url': saved_sotty.get_url_with_region(region=region_de),
                'description': {
                    'name': saved_sotty.furniture_category.translated_name,
                    'material': saved_sotty.color.translated_material_description,
                    'dimensions': 'H83cm, W350cm, D160cm',
                },
                'image_type': 'preview',
            },
            {
                'title': (
                    f'{saved_jetty.furniture_category.translated_name} '
                    f'{ShelfType(saved_jetty.shelf_type).translated_name}'
                ),
                'furniture_type': Furniture.jetty.value,
                'region_price_display': cached_region_data.get_format_price(
                    saved_jetty.get_regionalized_price(region=region_de)
                ),
                'image_url': f'{saved_jetty.preview.url}',
                'item_url': f'/en-de/furniture/bookcases/{saved_jetty.id},j,/',
                'description': {
                    'name': 'Tylko Shelf',
                    'material': 'White Plywood',
                    'dimensions': 'H0cm, W0, D32cm',
                },
                'image_type': 'preview',
            },
        ]

    def test_returns_only_furniture_with_status_saved(
        self,
        jetty_factory,
        user_factory,
        country_factory,
        api_client,
    ):
        country_de: Country = country_factory(germany=True)
        region_de = country_de.region
        cached_region_data = region_de.cached_region_data
        user = user_factory(
            profile__language=LanguageEnum.EN,
            profile__region=region_de,
        )
        saved_jetty = jetty_factory(
            furniture_status=FurnitureStatusEnum.SAVED,
            owner=user,
        )
        jetty_factory(
            furniture_status=FurnitureStatusEnum.DRAFT,
            owner=user,
        )

        url = reverse('mailing-user-wishlist', kwargs={'pk': user.id})
        headers = {'HTTP_BRAZE_APP_ID': 'app_id', 'HTTP_BRAZE_API_KEY': 'api_key'}
        api_client.force_authenticate(user)
        response = api_client.get(url, **headers)

        assert response.status_code == status.HTTP_200_OK
        assert len(response.json()) == 1
        assert response.json() == [
            {
                'title': (
                    f'{saved_jetty.furniture_category.translated_name} '
                    f'{ShelfType(saved_jetty.shelf_type).translated_name}'
                ),
                'furniture_type': Furniture.jetty.value,
                'region_price_display': cached_region_data.get_format_price(
                    saved_jetty.get_regionalized_price(region=region_de)
                ),
                'image_url': f'{saved_jetty.preview.url}',
                'item_url': f'/en-de/furniture/bookcases/{saved_jetty.id},j,/',
                'description': {
                    'name': 'Tylko Shelf',
                    'material': 'White Plywood',
                    'dimensions': 'H0cm, W0, D32cm',
                },
                'image_type': 'preview',
            }
        ]


@pytest.mark.django_db
class TestEmailCheckAPIView:
    url = reverse('users-check-email')

    @pytest.mark.parametrize('is_user_registered', [True, False])
    def test_should_check_user_existence(
        self, api_client, is_user_registered, user_factory
    ):
        email = '<EMAIL>'
        if is_user_registered:
            user_factory(username=email)

        response = api_client.post(self.url, data={'email': email})

        assert response.status_code == status.HTTP_200_OK
        response_json = response.json()
        assert response_json == {'is_user_registered': is_user_registered}

    def test_should_return_error_email_not_valid(self, api_client):
        response = api_client.post(self.url, data={'email': 'email_not_valid'})

        assert response.status_code == status.HTTP_400_BAD_REQUEST
        response_json = response.json()
        assert response_json['email'][0] == 'Enter a valid email address.'


@pytest.mark.django_db
class TestInsiderUserAPIView:
    def test_insider_update_user(self, user_factory, api_client):
        user = user_factory(email='', profile__email='')
        new_email = '<EMAIL>'
        api_client.force_authenticate(user)
        response = api_client.patch(
            path=reverse('insider-user-view', kwargs={'pk': user.pk}),
            data={'email': new_email},
        )
        user.refresh_from_db()
        assert response.status_code == status.HTTP_200_OK
        assert user.email == new_email
        assert user.profile.email == new_email

    def test_insider_update_user_with_email(self, user_factory, api_client):
        user = user_factory(email='<EMAIL>', profile__email='<EMAIL>')
        new_email = '<EMAIL>'
        api_client.force_authenticate(user)
        response = api_client.patch(
            path=reverse('insider-user-view', kwargs={'pk': user.pk}),
            data={'email': new_email},
        )
        user.refresh_from_db()
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert user.email == '<EMAIL>'
        assert user.profile.email == '<EMAIL>'


@pytest.mark.django_db
class TestPasswordResetAPIView:
    def _create_token(self, user):
        token = PasswordResetToken(user=user)
        token.token = AccessToken.create_token()
        token.save()
        return token.token

    def test_password_reset_all_good(self, user_factory, api_client):
        user = user_factory()
        token = self._create_token(user)
        new_password = 'avantipopolo1905'
        url = reverse('reset-password')
        response = api_client.post(
            url,
            data={
                'token': token,
                'password_1': new_password,
                'password_2': new_password,
            },
        )
        user.refresh_from_db()
        assert response.status_code == status.HTTP_200_OK
        assert user.check_password(new_password)

    def test_wrong_token(self, user_factory, api_client):
        user = user_factory()
        token = self._create_token(user)
        new_password = 'avantipopolo1905'
        url = reverse('reset-password')
        response = api_client.post(
            url,
            data={
                'token': token + '1',
                'password_1': new_password,
                'password_2': new_password,
            },
        )
        user.refresh_from_db()
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'Invalid token' in str(response.data[0])
        assert not user.check_password(new_password)

    def test_password_reset_wrong_password(self, user_factory, api_client):
        user = user_factory()
        token = self._create_token(user)
        new_password = 'avantipopolo1905'
        url = reverse('reset-password')
        response = api_client.post(
            url,
            data={
                'token': token,
                'password_1': new_password,
                'password_2': 'fischia_il_vento',
            },
        )
        user.refresh_from_db()
        assert response.status_code == status.HTTP_400_BAD_REQUEST
        assert 'Passwords do not match' in response.data['non_field_errors']
        assert not user.check_password(new_password)


@pytest.mark.django_db
class TestNewsletterAddView:
    @patch('user_profile.views.NewsletterAddView.process_subscription')
    @patch(
        'user_profile.serializers.BrazeClient.get_email_subscription_group_status',
        return_value=BrazeSubscriptionGroupStatus.UNKNOWN,
    )
    def test_normal_subscription(
        self,
        _,
        subscription_mock,
        user,
        api_client,
    ):
        url = reverse('rest_add_newsletter')
        api_client.force_authenticate(user)
        response = api_client.post(
            url,
            data={
                'email': '<EMAIL>',
                'source': SubscriptionSources.CHECKOUT,
            },
        )

        assert response.status_code == status.HTTP_200_OK
        subscription_mock.assert_called_once()

    @patch(
        'user_profile.serializers.BrazeClient.get_email_subscription_group_status',
        return_value=BrazeSubscriptionGroupStatus.UNKNOWN,
    )
    def test_emits_subscription_events(
        self,
        _,
        api_client,
        user,
    ):
        url = reverse('rest_add_newsletter')

        api_client.force_login(user)
        api_client.post(
            url,
            data={
                'email': '<EMAIL>',
                'source': SubscriptionSources.CHECKOUT,
            },
        )

        events = Event.objects.all()
        assert events.count() == 3
        assert events.filter(event_name='CreateExternalMarketingProfile').exists()
        assert events.filter(event_name='EmailSubscriptionEvent').exists()
        assert events.filter(event_name='EmailSubscriptionStatusUpdateEvent').exists()


@pytest.mark.django_db
class TestUserGlobalAPIView:
    url = reverse('user-global')

    def test_response_for_authenticated_user(
        self,
        api_client,
        cart,
        requests_mock,
    ):
        session_id = api_client.session.session_key
        requests_mock.user = cart.owner

        api_client.force_authenticate(user=cart.owner)
        response = api_client.get(self.url)
        assert response.status_code == status.HTTP_200_OK

        response_data = response.json()
        assert response_data['globalSessionId'] == session_id
        assert response_data['userId'] == cart.owner.id
        assert response_data['userType'] == cart.owner.profile.user_type
        assert response_data['userHashId'] == get_user_hash(requests_mock)
        assert response_data['userHashEmail'] == get_user_email_hash(requests_mock)
        assert response_data['userLanguage'] == cart.owner.profile.language
        assert response_data['isSignedIn'] is True
        assert response_data['cartId'] == cart.id
        assert response_data['orderId'] == cart.order.id
        assert response_data['hasAssembly'] is False
        assert response_data['hasCorduroy'] is False
        assert response_data['hasS01'] is False
        assert response_data['hasT03'] is False
        assert response_data['cartItemsCount'] == 2
        assert response_data['libraryItemsCount'] == 0
        assert response_data['abIds'] == ''
        assert response_data['abTestsList'] == []
        assert response_data['featureFlagsIds'] == ''
        assert response_data['featureFlagsList'] == []

    def test_has_item_or_service_in_cart(
        self,
        api_client,
        cart_factory,
        cart_item_factory,
    ):
        cart = cart_factory(
            assembly=True,
            items=[
                cart_item_factory(is_sofa_corduroy=True),
                cart_item_factory(is_sotty=True),
                cart_item_factory(is_tone_wardrobe=True),
            ],
        )
        api_client.force_authenticate(user=cart.owner)

        response = api_client.get(self.url)

        assert response.status_code == status.HTTP_200_OK
        response_data = response.json()
        assert response_data['hasAssembly'] is True
        assert response_data['hasCorduroy'] is True
        assert response_data['hasS01'] is True
        assert response_data['hasT03'] is True

    def test_db_performance_for_authenticated_user(
        self,
        api_client,
        cart,
        django_assert_max_num_queries,
    ):
        api_client.force_authenticate(user=cart.owner)
        with django_assert_max_num_queries(23):
            api_client.get(self.url)

        with django_assert_max_num_queries(16):
            api_client.get(self.url)

    def test_response_for_anonymous_user(
        self,
        api_client,
    ):
        session_id = api_client.session.session_key

        response = api_client.get(self.url)
        assert response.status_code == status.HTTP_200_OK

        response_data = response.json()
        assert response_data['globalSessionId'] == session_id
        assert response_data['userId'] is None
        assert response_data['userType'] == ''
        assert response_data['userHashId'] == ''
        assert response_data['userHashEmail'] == ''
        assert response_data['userLanguage'] == api_client.session['cached_language']
        assert response_data['isSignedIn'] is False
        assert response_data['cartId'] == ''
        assert response_data['hasT03'] is False
        assert response_data['cartItemsCount'] == 0
        assert response_data['libraryItemsCount'] == 0
        assert response_data['abIds'] == ''
        assert response_data['abTestsList'] == []
        assert response_data['featureFlagsIds'] == ''
        assert response_data['featureFlagsList'] == []

    def test_db_performance_for_anonymous_user(
        self, api_client, django_assert_max_num_queries
    ):
        with django_assert_max_num_queries(14):
            api_client.get(self.url)

        with django_assert_max_num_queries(4):
            api_client.get(self.url)
